#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动license_client测试图标
"""

import os
import sys
import subprocess

def main():
    """启动license_client.py"""
    print("🚀 启动license_client.py测试图标")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists("license_client.py"):
        print("❌ 未找到license_client.py文件")
        return
    
    if not os.path.exists("icon.ico"):
        print("❌ 未找到icon.ico文件")
        return
    
    print("✅ 找到license_client.py和icon.ico文件")
    print("🔄 正在启动程序...")
    print("\n💡 测试说明:")
    print("1. 检查主窗口标题栏是否显示图标")
    print("2. 点击'🔄 检测更新'按钮")
    print("3. 检查更新对话框标题栏是否显示相同图标")
    print("4. 更新对话框应该只显示进度条，无其他弹窗")
    print("\n" + "=" * 50)
    
    try:
        # 启动license_client.py
        subprocess.run([sys.executable, "license_client.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"❌ 启动过程出错: {e}")

if __name__ == "__main__":
    main()
