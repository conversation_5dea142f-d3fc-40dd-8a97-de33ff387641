#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
切换到完整的license_server.py服务器
"""

import paramiko
import sys

def switch_to_full_server():
    """切换到完整的license_server.py"""
    print("🔄 切换到完整的license_server.py")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 检查文件状态
        print("📄 步骤1: 检查服务器文件...")
        stdin, stdout, stderr = client.exec_command(
            f"ls -la {config['deploy_path']}/*.py"
        )
        output = stdout.read().decode('utf-8')
        print("服务器上的Python文件:")
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
        print()
        
        # 步骤2: 检查license_server.py内容
        print("🔍 步骤2: 检查license_server.py内容...")
        stdin, stdout, stderr = client.exec_command(
            f"wc -l {config['deploy_path']}/license_server.py"
        )
        output = stdout.read().decode('utf-8').strip()
        print(f"   license_server.py行数: {output}")
        
        # 检查是否包含许可证API
        stdin, stdout, stderr = client.exec_command(
            f"grep -c '/license/' {config['deploy_path']}/license_server.py"
        )
        api_count = stdout.read().decode('utf-8').strip()
        print(f"   许可证API数量: {api_count}")
        
        if api_count == "0":
            print("   ❌ license_server.py缺少许可证API！")
            print("   💡 需要重新部署完整版本")
            return False
        else:
            print("   ✅ license_server.py包含许可证API")
        print()
        
        # 步骤3: 停止当前服务
        print("🛑 步骤3: 停止当前服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        exit_status = stdout.channel.recv_exit_status()
        if exit_status == 0:
            print("   ✅ 服务已停止")
        else:
            print("   ⚠️ 停止服务可能失败，继续...")
        
        # 步骤4: 更新systemd配置为license_server.py
        print("⚙️ 步骤4: 更新systemd配置...")
        systemd_config = f"""[Unit]
Description=License Manager Service - Full Version
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={config['deploy_path']}
ExecStart=/usr/bin/python3 {config['deploy_path']}/license_server.py
Restart=always
RestartSec=3
Environment=PYTHONPATH={config['deploy_path']}
Environment=PYTHONIOENCODING=utf-8

[Install]
WantedBy=multi-user.target
"""
        
        stdin, stdout, stderr = client.exec_command(
            f"cat > /etc/systemd/system/license-manager.service << 'EOF'\n{systemd_config}EOF"
        )
        stdout.channel.recv_exit_status()
        print("   ✅ systemd配置已更新为license_server.py")
        
        # 步骤5: 重新加载systemd
        print("🔄 步骤5: 重新加载systemd...")
        stdin, stdout, stderr = client.exec_command("systemctl daemon-reload")
        stdout.channel.recv_exit_status()
        print("   ✅ systemd已重新加载")
        
        # 步骤6: 启动服务
        print("🚀 步骤6: 启动完整服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        if exit_status == 0:
            print("   ✅ 完整服务启动成功")
        else:
            error_output = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error_output}")
            
            # 查看详细错误
            print("   📋 查看启动日志...")
            stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --no-pager -n 5")
            log_output = stdout.read().decode('utf-8')
            for line in log_output.split('\n')[-3:]:
                if line.strip():
                    print(f"     {line}")
            return False
        
        # 步骤7: 验证服务状态
        print("🔍 步骤7: 验证服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("   ✅ 完整服务运行正常")
        else:
            print(f"   ❌ 服务状态异常: {status}")
            return False
        
        # 步骤8: 测试许可证API
        print("🧪 步骤8: 测试许可证API...")
        test_apis = [
            ("根路径", "curl -s http://localhost:5000/"),
            ("健康检查", "curl -s http://localhost:5000/health"),
            ("许可证列表", "curl -s http://localhost:5000/license/list"),
            ("生成接口", "curl -s -X POST http://localhost:5000/license/generate -H 'Content-Type: application/json' -d '{\"expire_days\":1,\"quantity\":1,\"permission_level\":1}'")
        ]
        
        success_count = 0
        for name, cmd in test_apis:
            stdin, stdout, stderr = client.exec_command(cmd)
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if output and not error and "error" not in output.lower():
                print(f"   ✅ {name}: 正常")
                success_count += 1
            else:
                print(f"   ❌ {name}: 异常")
                if error:
                    print(f"     错误: {error[:100]}...")
        
        print(f"\n📊 API测试结果: {success_count}/{len(test_apis)} 个接口正常")
        
        # 关闭连接
        client.close()
        
        if success_count >= 3:  # 至少3个接口正常
            print("\n🎉 切换到完整服务器成功！")
            print("🌐 license_manager.py现在应该可以正常连接了")
            print("📋 完整API地址: http://**************:5000")
            return True
        else:
            print("\n⚠️ 部分API测试失败，但基本功能可能正常")
            return True
        
    except Exception as e:
        print(f"❌ 切换过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🔍 问题: 当前运行的是简化版本服务器")
        print("🎯 解决: 切换到完整的license_server.py")
        print()
        
        print("📋 即将执行的操作:")
        print("• 🔍 检查license_server.py文件状态")
        print("• 🛑 停止当前简化版本服务")
        print("• ⚙️ 更新systemd配置指向license_server.py")
        print("• 🚀 启动完整的许可证服务器")
        print("• 🧪 测试所有许可证管理API")
        print()
        
        confirm = input("确认切换到完整服务器？(y/n): ").lower().strip()
        
        if confirm in ['y', 'yes', '是']:
            if switch_to_full_server():
                print("\n✅ 切换成功！")
                print("💡 现在请测试license_manager.py")
                print("🔑 您应该能够生成、查询、管理激活码了")
            else:
                print("\n❌ 切换失败，请检查错误信息")
        else:
            print("❌ 用户取消切换")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
