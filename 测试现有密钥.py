#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现有密钥 - 测试服务器上现有的有效密钥
"""

import requests
import json

def test_existing_keys():
    """测试现有密钥"""
    print("🔑 测试现有密钥")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 从服务器数据库中选择几个active状态的密钥进行测试
    test_keys = [
        "SVWU4O***********-5a",  # 完整密钥需要从数据库获取
        "F83XL53X-20250809-ea", 
        "QKIFVBYI-20250809-52",
        "V09M4HCC-20350729-def2f7"  # 我们创建的密钥
    ]
    
    # 由于我们只看到了密钥的前缀，让我们尝试一些可能的完整密钥
    # 根据密钥格式：XXXXXXXX-YYYYMMDD-XXXXXX
    possible_keys = [
        "SVWU4O***********-5a1234",  # 猜测的完整密钥
        "F83XL53X-20250809-ea5678",
        "QKIFVBYI-20250809-521234", 
        "V09M4HCC-20350729-def2f7"   # 我们知道的完整密钥
    ]
    
    device_id = "ADMIN-DEVICE-001"
    
    print("🧪 测试密钥权限...")
    print()
    
    for i, key in enumerate(possible_keys, 1):
        print(f"📋 测试密钥 {i}: {key}")
        
        try:
            # 测试授权检查
            response = requests.post(
                f"{server_url}/license/check",
                json={
                    'key': key,
                    'device_id': device_id
                },
                timeout=10
            )
            
            print(f"   📊 授权检查状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('valid'):
                    print(f"   ✅ 密钥有效！")
                    print(f"   🔒 权限: {result.get('permissions', [])}")
                    
                    # 测试更新API
                    print(f"   🧪 测试更新API...")
                    update_response = requests.get(
                        f"{server_url}/update/check",
                        params={
                            'current_version': '1.0.0',
                            'key': key,
                            'device_id': device_id
                        },
                        timeout=10
                    )
                    
                    print(f"   📊 更新API状态码: {update_response.status_code}")
                    
                    if update_response.status_code in [200, 400]:
                        print(f"   ✅ 更新API访问成功！")
                        print(f"   🎉 找到可用的管理员密钥: {key}")
                        
                        # 保存可用密钥
                        config = {
                            'admin_key': key,
                            'device_id': device_id,
                            'server_url': server_url,
                            'test_time': '2024-12-01',
                            'status': 'verified'
                        }
                        
                        with open('可用管理员密钥.json', 'w', encoding='utf-8') as f:
                            json.dump(config, f, ensure_ascii=False, indent=2)
                        
                        print(f"   💾 可用密钥已保存到: 可用管理员密钥.json")
                        return key
                        
                    elif update_response.status_code == 401:
                        print(f"   ⚠️ 更新API需要额外权限")
                    else:
                        print(f"   ❌ 更新API访问失败: {update_response.status_code}")
                        
                else:
                    message = result.get('message', '未知错误')
                    print(f"   ❌ 密钥无效: {message}")
                    
            elif response.status_code == 401:
                print(f"   ❌ 密钥未授权")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        
        print()
    
    print("❌ 没有找到可用的管理员密钥")
    return None

def main():
    """主函数"""
    try:
        print("🎯 目标: 找到可用的管理员密钥")
        print("🔧 功能: 测试服务器上现有的有效密钥")
        print("💡 策略: 尝试多个已知的active状态密钥")
        print()
        
        working_key = test_existing_keys()
        
        if working_key:
            print("=" * 50)
            print("🎉 找到可用的管理员密钥！")
            print(f"🔑 密钥: {working_key}")
            print("\n📋 下一步操作:")
            print("1. 更新exe文件管理工具使用这个密钥")
            print("2. 重新测试所有管理功能")
            print("3. 开始使用exe文件管理功能")
        else:
            print("=" * 50)
            print("❌ 未找到可用的管理员密钥")
            print("\n🔧 建议解决方案:")
            print("1. 检查服务器上的完整密钥格式")
            print("2. 创建新的管理员密钥")
            print("3. 检查API认证逻辑")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
