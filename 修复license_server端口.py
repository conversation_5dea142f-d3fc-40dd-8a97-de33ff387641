#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复license_server.py的端口配置
"""

import paramiko
import sys

def fix_license_server_port():
    """修复license_server.py的端口配置"""
    print("🔧 修复license_server.py的端口配置")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 停止当前服务
        print("🛑 步骤1: 停止当前服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        stdout.channel.recv_exit_status()
        print("   ✅ 服务已停止")
        
        # 步骤2: 检查当前端口配置
        print("🔍 步骤2: 检查当前端口配置...")
        stdin, stdout, stderr = client.exec_command(
            f"grep -n 'app.run.*port' {config['deploy_path']}/license_server.py"
        )
        output = stdout.read().decode('utf-8')
        
        if output.strip():
            print("   📊 当前端口配置:")
            for line in output.split('\n'):
                if line.strip():
                    print(f"     {line}")
        else:
            print("   ❌ 未找到端口配置")
        
        # 步骤3: 备份原文件
        print("📁 步骤3: 备份原文件...")
        from datetime import datetime
        backup_name = f"license_server.py.backup_port_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        stdin, stdout, stderr = client.exec_command(
            f"cp {config['deploy_path']}/license_server.py {config['deploy_path']}/{backup_name}"
        )
        stdout.channel.recv_exit_status()
        print(f"   ✅ 已备份到: {backup_name}")
        
        # 步骤4: 修改端口配置
        print("🔧 步骤4: 修改端口配置...")
        
        # 使用sed命令替换端口
        sed_command = f"sed -i 's/app.run(host=\"0.0.0.0\", port=44285)/app.run(host=\"0.0.0.0\", port=5000)/g' {config['deploy_path']}/license_server.py"
        stdin, stdout, stderr = client.exec_command(sed_command)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 端口配置已修改 (44285 → 5000)")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 修改失败: {error}")
            return False
        
        # 步骤5: 验证修改结果
        print("🔍 步骤5: 验证修改结果...")
        stdin, stdout, stderr = client.exec_command(
            f"grep -n 'app.run.*port' {config['deploy_path']}/license_server.py"
        )
        output = stdout.read().decode('utf-8')
        
        if "port=5000" in output:
            print("   ✅ 端口配置修改成功")
            for line in output.split('\n'):
                if line.strip():
                    print(f"     {line}")
        else:
            print("   ❌ 端口配置修改失败")
            return False
        
        # 步骤6: 启动服务
        print("🚀 步骤6: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error}")
            return False
        
        # 等待服务启动
        import time
        time.sleep(5)
        
        # 步骤7: 验证服务状态
        print("🔍 步骤7: 验证服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("   ✅ 服务运行正常")
        else:
            print(f"   ❌ 服务状态异常: {status}")
            
            # 查看日志
            print("   📋 查看启动日志...")
            stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --no-pager -n 5")
            log_output = stdout.read().decode('utf-8')
            for line in log_output.split('\n')[-3:]:
                if line.strip():
                    print(f"     {line}")
            return False
        
        # 步骤8: 测试API接口
        print("🧪 步骤8: 测试API接口...")
        test_commands = [
            ("根路径", "curl -s -w '%{http_code}' http://localhost:5000/"),
            ("许可证列表", "curl -s -w '%{http_code}' http://localhost:5000/license/list"),
            ("健康检查", "curl -s -w '%{http_code}' http://localhost:5000/health")
        ]
        
        success_count = 0
        for name, cmd in test_commands:
            stdin, stdout, stderr = client.exec_command(cmd)
            output = stdout.read().decode('utf-8')
            
            if "200" in output:
                print(f"   ✅ {name}: 正常")
                success_count += 1
            else:
                print(f"   ❌ {name}: 异常 ({output[-10:]})")
        
        print(f"\n📊 API测试结果: {success_count}/{len(test_commands)} 个接口正常")
        
        # 关闭连接
        client.close()
        
        if success_count >= 2:
            print("\n🎉 端口修复成功！")
            print("🌐 license_manager.py现在应该可以正常连接了")
            print("📋 服务地址: http://**************:5000")
            return True
        else:
            print("\n⚠️ 端口修复完成，但API测试部分失败")
            return True
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🔍 问题: license_server.py配置的端口是44285，不是5000")
        print("🎯 解决: 修改license_server.py的端口配置为5000")
        print()
        
        print("📋 即将执行的操作:")
        print("• 🛑 停止当前服务")
        print("• 📁 备份原文件")
        print("• 🔧 修改端口配置 (44285 → 5000)")
        print("• 🚀 重启服务")
        print("• 🧪 测试API接口")
        print()
        
        confirm = input("确认修复端口配置？(y/n): ").lower().strip()
        
        if confirm in ['y', 'yes', '是']:
            if fix_license_server_port():
                print("\n✅ 端口修复成功！")
                print("💡 现在请测试license_manager.py")
            else:
                print("\n❌ 端口修复失败，请检查错误信息")
        else:
            print("❌ 用户取消修复")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
