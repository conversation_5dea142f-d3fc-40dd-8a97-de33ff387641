#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动更新管理工具
用于管理版本发布、文件上传和配置更新
"""

import os
import json
import hashlib
import shutil
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path
from datetime import datetime
import subprocess
import requests
import sys

class UpdateManager:
    """更新管理器"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🔄 自动更新管理工具")
        self.root.geometry("800x600")
        
        # 配置路径
        self.update_dir = Path("update_server")
        self.files_dir = self.update_dir / "files"
        self.versions_dir = self.update_dir / "versions"
        self.version_info_path = self.update_dir / "version_info.json"
        
        # 确保目录存在
        self.update_dir.mkdir(exist_ok=True)
        self.files_dir.mkdir(exist_ok=True)
        self.versions_dir.mkdir(exist_ok=True)
        
        # 创建界面
        self.create_widgets()
        
        # 加载当前版本信息
        self.load_current_version()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 版本发布标签页
        self.create_release_tab(notebook)
        
        # 版本管理标签页
        self.create_manage_tab(notebook)
        
        # 服务器配置标签页
        self.create_config_tab(notebook)
    
    def create_release_tab(self, notebook):
        """创建版本发布标签页"""
        release_frame = ttk.Frame(notebook)
        notebook.add(release_frame, text="🚀 发布新版本")
        
        # 主框架
        main_frame = ttk.Frame(release_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 版本信息
        info_frame = ttk.LabelFrame(main_frame, text="版本信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text="版本号:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.version_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.version_var, width=20).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(info_frame, text="发布日期:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(info_frame, textvariable=self.date_var, width=20).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # 文件选择
        file_frame = ttk.LabelFrame(main_frame, text="更新文件", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(file_frame, text="exe文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E))
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2, padx=(10, 0))
        
        file_frame.columnconfigure(1, weight=1)
        
        # 更新说明
        changelog_frame = ttk.LabelFrame(main_frame, text="更新说明", padding="10")
        changelog_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.changelog_text = tk.Text(changelog_frame, height=8, wrap=tk.WORD)
        changelog_scrollbar = ttk.Scrollbar(changelog_frame, orient=tk.VERTICAL, command=self.changelog_text.yview)
        self.changelog_text.configure(yscrollcommand=changelog_scrollbar.set)
        
        self.changelog_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        changelog_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 默认更新说明
        default_changelog = """🎉 版本更新内容:

✨ 新功能:
• 

🔧 改进:
• 

🐛 修复:
• """
        self.changelog_text.insert('1.0', default_changelog)
        
        # 发布选项
        options_frame = ttk.LabelFrame(main_frame, text="发布选项", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.force_update_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="强制更新", variable=self.force_update_var).pack(anchor=tk.W)
        
        self.backup_recommended_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="建议备份", variable=self.backup_recommended_var).pack(anchor=tk.W)
        
        # 发布按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="🚀 发布版本", command=self.release_version).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="📋 预览配置", command=self.preview_config).pack(side=tk.LEFT)
    
    def create_manage_tab(self, notebook):
        """创建版本管理标签页"""
        manage_frame = ttk.Frame(notebook)
        notebook.add(manage_frame, text="📦 版本管理")
        
        main_frame = ttk.Frame(manage_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 当前版本信息
        current_frame = ttk.LabelFrame(main_frame, text="当前版本", padding="10")
        current_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.current_version_label = ttk.Label(current_frame, text="版本: 未知")
        self.current_version_label.pack(anchor=tk.W)
        
        self.current_date_label = ttk.Label(current_frame, text="发布日期: 未知")
        self.current_date_label.pack(anchor=tk.W)
        
        # 版本历史
        history_frame = ttk.LabelFrame(main_frame, text="版本历史", padding="10")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 版本列表
        columns = ("版本", "发布日期", "文件大小", "状态")
        self.version_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.version_tree.heading(col, text=col)
            self.version_tree.column(col, width=150)
        
        version_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.version_tree.yview)
        self.version_tree.configure(yscrollcommand=version_scrollbar.set)
        
        self.version_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        version_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 管理按钮
        manage_button_frame = ttk.Frame(main_frame)
        manage_button_frame.pack(fill=tk.X)
        
        ttk.Button(manage_button_frame, text="🔄 刷新列表", command=self.refresh_version_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(manage_button_frame, text="📁 打开文件夹", command=self.open_files_folder).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(manage_button_frame, text="🗑️ 删除版本", command=self.delete_version).pack(side=tk.LEFT)
        
        # 加载版本列表
        self.refresh_version_list()
    
    def create_config_tab(self, notebook):
        """创建服务器配置标签页"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ 服务器配置")
        
        main_frame = ttk.Frame(config_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 服务器配置
        server_frame = ttk.LabelFrame(main_frame, text="服务器设置", padding="10")
        server_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(server_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.server_url_var = tk.StringVar(value="http://198.23.135.176/")
        ttk.Entry(server_frame, textvariable=self.server_url_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(server_frame, text="更新路径:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.update_path_var = tk.StringVar(value="/updates/")
        ttk.Entry(server_frame, textvariable=self.update_path_var, width=40).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(5, 0))
        
        server_frame.columnconfigure(1, weight=1)
        
        # 测试连接
        test_frame = ttk.Frame(main_frame)
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(test_frame, text="🔗 测试连接", command=self.test_connection).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_frame, text="💾 保存配置", command=self.save_config).pack(side=tk.LEFT)
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD, state='disabled')
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state='normal')
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')
        self.root.update()
    
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if file_path:
            self.file_var.set(file_path)
    
    def calculate_file_hash(self, file_path):
        """计算文件哈希"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def release_version(self):
        """发布新版本"""
        try:
            # 验证输入
            version = self.version_var.get().strip()
            file_path = self.file_var.get().strip()
            
            if not version:
                messagebox.showerror("错误", "请输入版本号")
                return
            
            if not file_path or not os.path.exists(file_path):
                messagebox.showerror("错误", "请选择有效的exe文件")
                return
            
            self.log_message(f"开始发布版本 {version}...")
            
            # 计算文件信息
            file_size = os.path.getsize(file_path)
            file_hash = self.calculate_file_hash(file_path)
            
            self.log_message(f"文件大小: {file_size / (1024*1024):.1f} MB")
            self.log_message(f"文件哈希: {file_hash}")
            
            # 复制文件到更新目录
            target_filename = f"amazon_blueprint_v{version}.exe"
            target_path = self.files_dir / target_filename
            
            shutil.copy2(file_path, target_path)
            self.log_message(f"文件已复制到: {target_path}")
            
            # 创建版本信息
            version_info = {
                "version": version,
                "download_url": f"{self.server_url_var.get().rstrip('/')}{self.update_path_var.get()}files/{target_filename}",
                "file_size": file_size,
                "file_hash": file_hash,
                "changelog": self.changelog_text.get('1.0', tk.END).strip(),
                "release_date": self.date_var.get(),
                "min_version": "1.0.0",
                "force_update": self.force_update_var.get(),
                "update_notes": {
                    "critical": self.force_update_var.get(),
                    "backup_recommended": self.backup_recommended_var.get(),
                    "restart_required": True
                }
            }
            
            # 保存主版本信息文件
            with open(self.version_info_path, 'w', encoding='utf-8') as f:
                json.dump(version_info, f, indent=2, ensure_ascii=False)
            
            # 保存版本历史文件
            version_history_path = self.versions_dir / f"v{version}.json"
            with open(version_history_path, 'w', encoding='utf-8') as f:
                json.dump(version_info, f, indent=2, ensure_ascii=False)
            
            self.log_message("版本信息已保存")
            self.log_message(f"✅ 版本 {version} 发布成功！")
            
            # 刷新界面
            self.load_current_version()
            self.refresh_version_list()
            
            messagebox.showinfo("成功", f"版本 {version} 发布成功！")
            
        except Exception as e:
            self.log_message(f"❌ 发布失败: {str(e)}")
            messagebox.showerror("错误", f"发布失败: {str(e)}")
    
    def preview_config(self):
        """预览配置"""
        version = self.version_var.get().strip()
        if not version:
            messagebox.showerror("错误", "请输入版本号")
            return
        
        config = {
            "version": version,
            "release_date": self.date_var.get(),
            "changelog": self.changelog_text.get('1.0', tk.END).strip(),
            "force_update": self.force_update_var.get(),
            "backup_recommended": self.backup_recommended_var.get()
        }
        
        preview_window = tk.Toplevel(self.root)
        preview_window.title("配置预览")
        preview_window.geometry("600x400")
        
        text_widget = tk.Text(preview_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget.insert('1.0', json.dumps(config, indent=2, ensure_ascii=False))
        text_widget.config(state='disabled')
    
    def load_current_version(self):
        """加载当前版本信息"""
        try:
            if self.version_info_path.exists():
                with open(self.version_info_path, 'r', encoding='utf-8') as f:
                    version_info = json.load(f)
                
                self.current_version_label.config(text=f"版本: {version_info.get('version', '未知')}")
                self.current_date_label.config(text=f"发布日期: {version_info.get('release_date', '未知')}")
            else:
                self.current_version_label.config(text="版本: 无")
                self.current_date_label.config(text="发布日期: 无")
        except Exception as e:
            self.log_message(f"加载版本信息失败: {e}")
    
    def refresh_version_list(self):
        """刷新版本列表"""
        # 清空列表
        for item in self.version_tree.get_children():
            self.version_tree.delete(item)
        
        # 加载版本文件
        for version_file in self.versions_dir.glob("v*.json"):
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_info = json.load(f)
                
                version = version_info.get('version', '未知')
                release_date = version_info.get('release_date', '未知')
                file_size = version_info.get('file_size', 0)
                file_size_mb = f"{file_size / (1024*1024):.1f} MB" if file_size > 0 else "未知"
                
                # 检查文件是否存在
                exe_filename = f"amazon_blueprint_v{version}.exe"
                exe_path = self.files_dir / exe_filename
                status = "✅ 存在" if exe_path.exists() else "❌ 缺失"
                
                self.version_tree.insert('', 'end', values=(version, release_date, file_size_mb, status))
                
            except Exception as e:
                self.log_message(f"加载版本文件 {version_file} 失败: {e}")
    
    def open_files_folder(self):
        """打开文件夹"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(str(self.files_dir))
            else:  # macOS/Linux
                subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', str(self.files_dir)])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹: {e}")
    
    def delete_version(self):
        """删除选中的版本"""
        selected_item = self.version_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请选择要删除的版本")
            return
        
        version = self.version_tree.item(selected_item[0])['values'][0]
        
        if messagebox.askyesno("确认删除", f"确定要删除版本 {version} 吗？\n这将删除版本文件和配置信息。"):
            try:
                # 删除exe文件
                exe_path = self.files_dir / f"amazon_blueprint_v{version}.exe"
                if exe_path.exists():
                    exe_path.unlink()
                
                # 删除版本配置文件
                version_config_path = self.versions_dir / f"v{version}.json"
                if version_config_path.exists():
                    version_config_path.unlink()
                
                self.log_message(f"版本 {version} 已删除")
                self.refresh_version_list()
                
            except Exception as e:
                messagebox.showerror("错误", f"删除失败: {e}")
    
    def test_connection(self):
        """测试服务器连接"""
        try:
            url = self.server_url_var.get().rstrip('/')
            self.log_message(f"测试连接到: {url}")
            
            response = requests.get(f"{url}/update/check", timeout=10, params={
                'key': 'test',
                'device_id': 'test',
                'current_version': '1.0.0'
            })
            
            if response.status_code == 200 or response.status_code == 400:  # 400是因为测试参数无效，但说明服务器在运行
                self.log_message("✅ 服务器连接成功")
                messagebox.showinfo("成功", "服务器连接正常")
            else:
                self.log_message(f"⚠️ 服务器响应异常: {response.status_code}")
                messagebox.showwarning("警告", f"服务器响应异常: {response.status_code}")
                
        except Exception as e:
            self.log_message(f"❌ 连接失败: {str(e)}")
            messagebox.showerror("错误", f"连接失败: {str(e)}")
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                "server_url": self.server_url_var.get(),
                "update_path": self.update_path_var.get()
            }
            
            config_path = Path("update_config.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.log_message("配置已保存")
            messagebox.showinfo("成功", "配置保存成功")
            
        except Exception as e:
            self.log_message(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")

def main():
    """主函数"""
    root = tk.Tk()
    app = UpdateManager(root)
    root.mainloop()

if __name__ == "__main__":
    main()
