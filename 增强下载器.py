#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的下载器，支持断点续传和更好的错误处理
"""

import os
import sys
import requests
import time
import hashlib
from pathlib import Path

class EnhancedDownloader:
    """增强的下载器"""
    
    def __init__(self, max_retries=5, chunk_size=65536, timeout=(10, 600)):
        """
        初始化下载器
        
        Args:
            max_retries: 最大重试次数
            chunk_size: 下载块大小（64KB）
            timeout: 超时设置 (连接超时, 读取超时)
        """
        self.max_retries = max_retries
        self.chunk_size = chunk_size
        self.timeout = timeout
        
    def download_with_resume(self, url, file_path, params=None, progress_callback=None, expected_size=None):
        """
        支持断点续传的下载
        
        Args:
            url: 下载URL
            file_path: 保存路径
            params: 请求参数
            progress_callback: 进度回调函数
            expected_size: 预期文件大小
            
        Returns:
            bool: 下载是否成功
        """
        file_path = Path(file_path)
        temp_path = file_path.with_suffix(file_path.suffix + '.tmp')
        
        # 检查是否有未完成的下载
        resume_pos = 0
        if temp_path.exists():
            resume_pos = temp_path.stat().st_size
            print(f"发现未完成的下载，从 {resume_pos} 字节处继续...")
        
        headers = {}
        if resume_pos > 0:
            headers['Range'] = f'bytes={resume_pos}-'
        
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                print(f"开始下载... (尝试 {retry_count + 1}/{self.max_retries})")
                
                # 发送请求
                response = requests.get(
                    url, 
                    params=params, 
                    headers=headers,
                    stream=True, 
                    timeout=self.timeout
                )
                
                # 检查响应状态
                if response.status_code == 416:  # Range Not Satisfiable
                    print("服务器不支持断点续传，重新开始下载...")
                    resume_pos = 0
                    headers = {}
                    temp_path.unlink(missing_ok=True)
                    continue
                elif response.status_code not in [200, 206]:
                    response.raise_for_status()
                
                # 获取文件总大小
                if 'content-length' in response.headers:
                    content_length = int(response.headers['content-length'])
                    if response.status_code == 206:  # 部分内容
                        total_size = resume_pos + content_length
                    else:
                        total_size = content_length
                elif expected_size:
                    total_size = expected_size
                else:
                    total_size = 0
                
                print(f"文件大小: {total_size // 1024 // 1024}MB")
                
                # 下载文件
                downloaded_size = resume_pos
                mode = 'ab' if resume_pos > 0 else 'wb'
                
                with open(temp_path, mode) as f:
                    start_time = time.time()
                    last_update = start_time
                    
                    for chunk in response.iter_content(chunk_size=self.chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # 更新进度（每秒最多更新一次）
                            current_time = time.time()
                            if current_time - last_update >= 1.0 or downloaded_size >= total_size:
                                if progress_callback and total_size > 0:
                                    progress = (downloaded_size / total_size) * 100
                                    speed = downloaded_size / (current_time - start_time) / 1024 / 1024  # MB/s
                                    progress_callback(progress, downloaded_size, total_size, speed)
                                last_update = current_time
                
                # 下载完成，重命名文件
                if file_path.exists():
                    file_path.unlink()
                temp_path.rename(file_path)
                
                print(f"下载完成: {file_path}")
                return True
                
            except requests.exceptions.Timeout as e:
                retry_count += 1
                print(f"下载超时 (尝试 {retry_count}/{self.max_retries}): {e}")
                if retry_count < self.max_retries:
                    wait_time = min(2 ** retry_count, 30)  # 指数退避，最大30秒
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                
            except requests.exceptions.ConnectionError as e:
                retry_count += 1
                print(f"连接错误 (尝试 {retry_count}/{self.max_retries}): {e}")
                if retry_count < self.max_retries:
                    wait_time = min(2 ** retry_count, 30)
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                
            except Exception as e:
                retry_count += 1
                print(f"下载错误 (尝试 {retry_count}/{self.max_retries}): {e}")
                if retry_count < self.max_retries:
                    wait_time = min(2 ** retry_count, 30)
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
        
        print(f"下载失败，已重试 {self.max_retries} 次")
        return False
    
    def verify_file(self, file_path, expected_hash=None, expected_size=None):
        """
        验证下载的文件
        
        Args:
            file_path: 文件路径
            expected_hash: 预期的MD5哈希
            expected_size: 预期的文件大小
            
        Returns:
            bool: 验证是否通过
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            print("文件不存在")
            return False
        
        # 检查文件大小
        actual_size = file_path.stat().st_size
        if expected_size and actual_size != expected_size:
            print(f"文件大小不匹配: 期望 {expected_size}, 实际 {actual_size}")
            return False
        
        # 检查文件哈希
        if expected_hash:
            print("正在验证文件完整性...")
            actual_hash = self._calculate_md5(file_path)
            if actual_hash.lower() != expected_hash.lower():
                print(f"文件哈希不匹配: 期望 {expected_hash}, 实际 {actual_hash}")
                return False
        
        print("文件验证通过")
        return True
    
    def _calculate_md5(self, file_path):
        """计算文件MD5哈希"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

def test_enhanced_downloader():
    """测试增强下载器"""
    print("🧪 测试增强下载器")
    print("=" * 50)
    
    # 创建下载器
    downloader = EnhancedDownloader(
        max_retries=3,
        chunk_size=65536,  # 64KB
        timeout=(10, 300)  # 连接10秒，读取5分钟
    )
    
    # 测试URL（使用一个小文件测试）
    test_url = "http://198.23.135.176:5000/update/download"
    test_file = "test_download.exe"
    
    # 进度回调函数
    def progress_callback(progress, downloaded, total, speed):
        print(f"\r进度: {progress:.1f}% ({downloaded//1024//1024}MB/{total//1024//1024}MB) 速度: {speed:.2f}MB/s", end="")
    
    try:
        # 模拟下载参数
        params = {
            'key': 'ADMIN_BYPASS',
            'device_id': 'ADMIN-DEVICE-001',
            'version': '2.1.1'
        }
        
        print("开始测试下载...")
        success = downloader.download_with_resume(
            test_url, 
            test_file, 
            params=params,
            progress_callback=progress_callback,
            expected_size=54321000  # 约54MB
        )
        
        print(f"\n下载结果: {'成功' if success else '失败'}")
        
        if success:
            # 验证文件
            if os.path.exists(test_file):
                file_size = os.path.getsize(test_file)
                print(f"下载文件大小: {file_size // 1024 // 1024}MB")
                
                # 清理测试文件
                os.remove(test_file)
                print("清理测试文件完成")
        
    except Exception as e:
        print(f"\n测试失败: {e}")

def main():
    """主函数"""
    print("🔧 增强下载器")
    print("=" * 60)
    
    try:
        test_enhanced_downloader()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")

if __name__ == "__main__":
    main()
