#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的依赖安装脚本 - 忽略编码错误
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包，忽略编码错误"""
    print(f"正在安装: {package}")
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 运行pip安装
        cmd = [sys.executable, "-m", "pip", "install", package]
        result = subprocess.run(cmd, env=env, capture_output=True)
        
        if result.returncode == 0:
            print(f"✓ {package} 安装成功")
            return True
        else:
            print(f"✗ {package} 安装失败")
            return False
    except Exception as e:
        print(f"⚠ {package} 安装时出现异常，但可能已成功: {str(e)}")
        return True  # 假设成功

def main():
    """主安装流程"""
    print("=" * 60)
    print("    亚马逊授权系统 - 简化依赖安装")
    print("=" * 60)
    
    # 升级pip
    print("\n1. 升级pip...")
    install_package("--upgrade pip")
    
    # 核心依赖
    print("\n2. 安装核心依赖...")
    core_packages = [
        "requests>=2.31.0",
        "urllib3>=2.0.0",
        "certifi>=2023.0.0",
        "charset-normalizer>=3.0.0",
        "idna>=3.4"
    ]
    
    for package in core_packages:
        install_package(package)
    
    # 数据处理
    print("\n3. 安装数据处理库...")
    data_packages = [
        "numpy>=1.24.0",
        "pandas>=2.0.0",
        "openpyxl>=3.1.0",
        "xlsxwriter>=3.1.0",
        "lxml>=4.9.0"
    ]
    
    for package in data_packages:
        install_package(package)
    
    # Web爬虫
    print("\n4. 安装Web爬虫库...")
    web_packages = [
        "beautifulsoup4>=4.12.0",
        "selenium>=4.15.0",
        "webdriver_manager>=4.0.0",
        "fake_useragent>=1.4.0"
    ]
    
    for package in web_packages:
        install_package(package)
    
    # 系统依赖
    print("\n5. 安装系统依赖...")
    system_packages = [
        "psutil>=5.9.0",
        "cryptography>=41.0.0",
        "pillow>=10.0.0"
    ]
    
    for package in system_packages:
        install_package(package)
    
    # 构建工具
    print("\n6. 安装构建工具...")
    build_packages = [
        "pyinstaller>=6.0.0"
    ]
    
    for package in build_packages:
        install_package(package)
    
    # 验证安装
    print("\n7. 验证关键包...")
    test_imports = [
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('selenium', 'selenium'),
        ('requests', 'requests'),
        ('cryptography', 'cryptography'),
        ('PIL', 'pillow'),
        ('PyInstaller', 'pyinstaller')
    ]
    
    failed = []
    for import_name, package_name in test_imports:
        try:
            __import__(import_name)
            print(f"✓ {package_name} 可用")
        except ImportError:
            print(f"✗ {package_name} 不可用")
            failed.append(package_name)
    
    # 测试Excel功能
    print("\n8. 测试Excel功能...")
    try:
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Product': ['Test1', 'Test2'],
            'Price': [10.99, 20.99]
        })
        
        # 测试写入
        test_file = 'install_test.xlsx'
        test_data.to_excel(test_file, index=False, engine='openpyxl')
        
        # 测试读取
        read_data = pd.read_excel(test_file, engine='openpyxl')
        
        if len(read_data) == len(test_data):
            print("✓ Excel读写功能正常")
        else:
            print("✗ Excel功能异常")
        
        # 清理
        if os.path.exists(test_file):
            os.remove(test_file)
            
    except Exception as e:
        print(f"✗ Excel功能测试失败: {str(e)}")
    
    # 总结
    print("\n" + "=" * 60)
    if failed:
        print(f"⚠ 以下包可能需要手动检查: {', '.join(failed)}")
    else:
        print("🎉 所有关键包安装成功！")
    
    print("\n现在可以运行以下命令:")
    print("python build_gui.py        # 启动图形界面")
    print("python build_license_system.py  # 直接构建")
    
    print("\n注意：")
    print("- amazoncaptcha已移除（存在依赖冲突）")
    print("- 如果看到编码错误，可以忽略（不影响安装）")
    print("- 主要功能（Excel、Web爬虫、加密）都可正常使用")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
