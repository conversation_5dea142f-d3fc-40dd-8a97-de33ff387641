#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版exe管理工具 - 智能版本号管理和上传
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import requests
import json
import os
import hashlib
from datetime import datetime
import threading
import re

class EnhancedExeManagerGUI:
    """增强版exe文件管理界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 亚马逊蓝图工具 - 智能exe管理")
        self.root.geometry("900x700")

        # 服务器配置
        self.server_url = "http://198.23.135.176:5000"
        self.admin_key = "ADMIN_BYPASS"
        self.device_id = "ADMIN-DEVICE-001"
        
        self.selected_file = None
        self.current_server_version = None

        self.create_widgets()
        self.refresh_versions()
        self.check_server_connection()

    def create_widgets(self):
        """创建界面控件"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="🚀 亚马逊蓝图工具 - 智能exe管理",
            font=("微软雅黑", 16, "bold"),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        # 主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧 - 版本信息
        left_frame = tk.LabelFrame(main_frame, text="📊 当前版本信息", font=("微软雅黑", 10, "bold"))
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 服务器版本信息
        self.server_info_frame = tk.Frame(left_frame)
        self.server_info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.server_version_label = tk.Label(
            self.server_info_frame, 
            text="服务器版本: 检查中...", 
            font=("微软雅黑", 12, "bold"),
            fg="#2c3e50"
        )
        self.server_version_label.pack(anchor='w')
        
        # 右侧 - 智能上传面板
        right_frame = tk.LabelFrame(main_frame, text="🎯 智能上传面板", font=("微软雅黑", 10, "bold"))
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # 文件选择
        file_frame = tk.LabelFrame(right_frame, text="📁 文件选择")
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.file_label = tk.Label(file_frame, text="未选择文件", fg='gray', wraplength=200)
        self.file_label.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Button(
            file_frame,
            text="📁 选择exe文件",
            command=self.select_file,
            bg='#3498db',
            fg='white',
            font=("微软雅黑", 9)
        ).pack(fill=tk.X, padx=5, pady=2)
        
        # 智能版本号设定
        version_frame = tk.LabelFrame(right_frame, text="🎯 智能版本号")
        version_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 版本号输入
        tk.Label(version_frame, text="版本号:", font=("微软雅黑", 9, "bold")).pack(anchor='w', padx=5, pady=2)
        
        version_input_frame = tk.Frame(version_frame)
        version_input_frame.pack(fill=tk.X, padx=5, pady=2)
        
        self.version_entry = tk.Entry(version_input_frame, width=15, font=("Consolas", 10))
        self.version_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 智能版本号按钮
        smart_version_frame = tk.Frame(version_frame)
        smart_version_frame.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Button(
            smart_version_frame,
            text="🧠 智能递增",
            command=self.auto_increment_version,
            bg='#9b59b6',
            fg='white',
            font=("微软雅黑", 8),
            width=10
        ).pack(side=tk.LEFT, padx=(0, 2))
        
        tk.Button(
            smart_version_frame,
            text="📅 日期版本",
            command=self.generate_date_version,
            bg='#e67e22',
            fg='white',
            font=("微软雅黑", 8),
            width=10
        ).pack(side=tk.LEFT, padx=2)
        
        # 版本建议
        self.version_suggestion_label = tk.Label(
            version_frame, 
            text="💡 建议版本: 检查中...", 
            font=("微软雅黑", 8),
            fg="#7f8c8d"
        )
        self.version_suggestion_label.pack(anchor='w', padx=5, pady=2)
        
        # 更新说明
        changelog_frame = tk.LabelFrame(right_frame, text="📝 更新说明")
        changelog_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.changelog_text = tk.Text(changelog_frame, height=4, width=25, font=("微软雅黑", 9))
        self.changelog_text.pack(fill=tk.X, padx=5, pady=2)
        
        # 快速更新说明按钮
        quick_changelog_frame = tk.Frame(changelog_frame)
        quick_changelog_frame.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Button(
            quick_changelog_frame,
            text="🐛 修复",
            command=lambda: self.add_changelog_template("修复bug"),
            bg='#e74c3c',
            fg='white',
            font=("微软雅黑", 8),
            width=8
        ).pack(side=tk.LEFT, padx=(0, 2))
        
        tk.Button(
            quick_changelog_frame,
            text="✨ 新功能",
            command=lambda: self.add_changelog_template("新增功能"),
            bg='#27ae60',
            fg='white',
            font=("微软雅黑", 8),
            width=8
        ).pack(side=tk.LEFT, padx=2)
        
        tk.Button(
            quick_changelog_frame,
            text="⚡ 优化",
            command=lambda: self.add_changelog_template("性能优化"),
            bg='#f39c12',
            fg='white',
            font=("微软雅黑", 8),
            width=8
        ).pack(side=tk.LEFT, padx=2)
        
        # 上传按钮
        upload_button_frame = tk.Frame(right_frame)
        upload_button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        self.upload_button = tk.Button(
            upload_button_frame,
            text="🚀 智能上传",
            command=self.smart_upload,
            bg='#27ae60',
            fg='white',
            font=("微软雅黑", 11, "bold"),
            height=2
        )
        self.upload_button.pack(fill=tk.X)
        
        # 状态栏
        status_frame = tk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = tk.Label(
            status_frame, 
            text="就绪", 
            relief=tk.SUNKEN, 
            anchor=tk.W,
            font=("微软雅黑", 9)
        )
        self.status_label.pack(fill=tk.X, padx=5, pady=2)

    def check_server_connection(self):
        """检查服务器连接并获取当前版本"""
        def check_thread():
            try:
                # 获取当前版本信息
                response = requests.get(f"{self.server_url}/update/stats", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    current_version = data.get('current_version', {})
                    if current_version:
                        version = current_version.get('version', 'Unknown')
                        self.current_server_version = version
                        
                        self.root.after(0, lambda: self.server_version_label.config(
                            text=f"服务器当前版本: {version}",
                            fg="#27ae60"
                        ))
                        
                        # 更新版本建议
                        self.root.after(0, self.update_version_suggestion)
                    else:
                        self.root.after(0, lambda: self.server_version_label.config(
                            text="服务器版本: 无版本信息",
                            fg="#e67e22"
                        ))
                else:
                    self.root.after(0, lambda: self.server_version_label.config(
                        text="服务器版本: 连接失败",
                        fg="#e74c3c"
                    ))
            except Exception as e:
                self.root.after(0, lambda: self.server_version_label.config(
                    text=f"服务器版本: 错误 ({str(e)[:20]}...)",
                    fg="#e74c3c"
                ))

        thread = threading.Thread(target=check_thread)
        thread.daemon = True
        thread.start()

    def update_version_suggestion(self):
        """更新版本建议"""
        if self.current_server_version:
            try:
                # 解析当前版本号
                parts = self.current_server_version.split('.')
                if len(parts) >= 3:
                    major, minor, patch = int(parts[0]), int(parts[1]), int(parts[2])
                    suggested_version = f"{major}.{minor}.{patch + 1}"
                    
                    self.version_suggestion_label.config(
                        text=f"💡 建议版本: {suggested_version}",
                        fg="#27ae60"
                    )
                    
                    # 如果版本号输入框为空，自动填入建议版本
                    if not self.version_entry.get().strip():
                        self.version_entry.insert(0, suggested_version)
                else:
                    self.version_suggestion_label.config(
                        text="💡 建议版本: 无法解析当前版本",
                        fg="#e67e22"
                    )
            except Exception as e:
                self.version_suggestion_label.config(
                    text="💡 建议版本: 解析错误",
                    fg="#e74c3c"
                )
        else:
            self.version_suggestion_label.config(
                text="💡 建议版本: 等待服务器响应...",
                fg="#7f8c8d"
            )

    def select_file(self):
        """选择exe文件"""
        file_path = filedialog.askopenfilename(
            title="选择exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.selected_file = file_path
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            size_mb = file_size / (1024 * 1024)
            
            self.file_label.config(
                text=f"📁 {filename}\n💾 大小: {size_mb:.1f} MB",
                fg='#2c3e50'
            )
            
            # 尝试从文件名提取版本号
            self.extract_version_from_filename(filename)

    def extract_version_from_filename(self, filename):
        """从文件名提取版本号"""
        try:
            # 常见的版本号模式
            patterns = [
                r'v?(\d+\.\d+\.\d+)',
                r'version[_\-]?(\d+\.\d+\.\d+)',
                r'(\d+\.\d+\.\d+)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, filename, re.IGNORECASE)
                if match:
                    version = match.group(1)
                    if not self.version_entry.get().strip():
                        self.version_entry.delete(0, tk.END)
                        self.version_entry.insert(0, version)
                    break
        except:
            pass

    def auto_increment_version(self):
        """自动递增版本号"""
        if self.current_server_version:
            try:
                parts = self.current_server_version.split('.')
                if len(parts) >= 3:
                    major, minor, patch = int(parts[0]), int(parts[1]), int(parts[2])
                    new_version = f"{major}.{minor}.{patch + 1}"
                    
                    self.version_entry.delete(0, tk.END)
                    self.version_entry.insert(0, new_version)
                    
                    self.status_label.config(text=f"版本号已自动递增为: {new_version}")
            except Exception as e:
                messagebox.showerror("错误", f"无法递增版本号: {e}")
        else:
            messagebox.showwarning("警告", "无法获取当前服务器版本，请手动输入版本号")

    def generate_date_version(self):
        """生成基于日期的版本号"""
        now = datetime.now()
        date_version = f"2.{now.strftime('%m%d')}.{now.hour:02d}{now.minute:02d}"
        
        self.version_entry.delete(0, tk.END)
        self.version_entry.insert(0, date_version)
        
        self.status_label.config(text=f"已生成日期版本号: {date_version}")

    def add_changelog_template(self, template_type):
        """添加更新说明模板"""
        current_text = self.changelog_text.get("1.0", tk.END).strip()
        
        templates = {
            "修复bug": "- 修复已知问题\n- 提升稳定性",
            "新增功能": "- 新增功能\n- 优化用户体验",
            "性能优化": "- 性能优化\n- 提升运行效率"
        }
        
        template = templates.get(template_type, template_type)
        
        if current_text:
            self.changelog_text.insert(tk.END, f"\n{template}")
        else:
            self.changelog_text.insert("1.0", template)

    def smart_upload(self):
        """智能上传"""
        if not self.selected_file:
            messagebox.showerror("错误", "请先选择exe文件")
            return
        
        version = self.version_entry.get().strip()
        if not version:
            messagebox.showerror("错误", "请输入版本号")
            return
        
        # 版本号验证
        if not re.match(r'^\d+\.\d+\.\d+', version):
            if not messagebox.askyesno("确认", f"版本号格式可能不标准: {version}\n是否继续上传？"):
                return
        
        # 版本号重复检查
        if version == self.current_server_version:
            if not messagebox.askyesno("确认", f"版本号与当前服务器版本相同: {version}\n是否覆盖？"):
                return
        
        changelog = self.changelog_text.get("1.0", tk.END).strip()
        if not changelog:
            changelog = f"版本 {version} 更新"
        
        def upload_thread():
            try:
                self.status_label.config(text="正在上传...")
                self.upload_button.config(state='disabled', text="上传中...")
                self.root.update()
                
                # 准备文件
                with open(self.selected_file, 'rb') as f:
                    files = {'file': f}
                    data = {
                        'version': version,
                        'changelog': changelog,
                        'key': self.admin_key,
                        'device_id': self.device_id
                    }

                    # 上传文件
                    response = requests.post(
                        f"{self.server_url}/update/upload",
                        files=files,
                        data=data,
                        timeout=300
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        self.root.after(0, lambda: messagebox.showinfo("成功", f"版本 {version} 上传成功！"))
                        
                        # 清空表单
                        self.root.after(0, self.clear_form)
                        
                        # 刷新版本信息
                        self.root.after(0, self.check_server_connection)
                    else:
                        self.root.after(0, lambda: messagebox.showerror("失败", result.get('message', '上传失败')))
                else:
                    self.root.after(0, lambda: messagebox.showerror("失败", f"上传失败: HTTP {response.status_code}"))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"上传过程中发生错误: {e}"))
            finally:
                self.root.after(0, lambda: self.status_label.config(text="就绪"))
                self.root.after(0, lambda: self.upload_button.config(state='normal', text="🚀 智能上传"))
        
        # 在后台线程中上传
        thread = threading.Thread(target=upload_thread)
        thread.daemon = True
        thread.start()

    def clear_form(self):
        """清空表单"""
        self.version_entry.delete(0, tk.END)
        self.changelog_text.delete("1.0", tk.END)
        self.selected_file = None
        self.file_label.config(text="未选择文件", fg='gray')

    def refresh_versions(self):
        """刷新版本信息"""
        self.check_server_connection()

    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    app = EnhancedExeManagerGUI()
    app.run()

if __name__ == "__main__":
    main()
