#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的API - 验证所有路由是否正常
"""

import requests
import json

def test_fixed_api():
    """测试修复后的API"""
    print("🧪 测试修复后的API")
    print("=" * 50)
    
    base_url = "http://**************:5000"
    
    test_cases = [
        {
            "name": "根路径",
            "url": f"{base_url}/",
            "method": "GET",
            "expected_status": 200,
            "description": "应该返回API信息"
        },
        {
            "name": "更新统计",
            "url": f"{base_url}/update/stats",
            "method": "GET", 
            "expected_status": [200, 404, 500],
            "description": "应该返回更新统计信息"
        },
        {
            "name": "检查更新",
            "url": f"{base_url}/update/check?current_version=1.0.0",
            "method": "GET",
            "expected_status": [200, 400],
            "description": "应该返回更新检查结果"
        },
        {
            "name": "授权列表",
            "url": f"{base_url}/license/list",
            "method": "GET",
            "expected_status": [200, 500],
            "description": "应该返回授权列表"
        },
        {
            "name": "下载图标",
            "url": f"{base_url}/license/download_icon",
            "method": "GET",
            "expected_status": [200, 404],
            "description": "应该返回图标文件或404"
        }
    ]
    
    print("🌐 开始测试API...")
    print()
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test in enumerate(test_cases, 1):
        print(f"📋 测试 {i}/{total_count}: {test['name']}")
        print(f"   🌐 URL: {test['url']}")
        print(f"   💡 期望: {test['description']}")
        
        try:
            if test['method'] == 'GET':
                response = requests.get(test['url'], timeout=10)
            else:
                response = requests.post(test['url'], timeout=10)
            
            status_code = response.status_code
            print(f"   📊 状态码: {status_code}")
            
            # 检查状态码是否符合预期
            expected = test['expected_status']
            if isinstance(expected, list):
                status_ok = status_code in expected
            else:
                status_ok = status_code == expected
            
            if status_ok:
                success_count += 1
                print(f"   ✅ 状态码正常")
                
                # 尝试解析JSON响应
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = response.json()
                        
                        if isinstance(data, dict):
                            if 'message' in data:
                                print(f"   📄 消息: {data['message'][:50]}...")
                            if 'success' in data:
                                print(f"   📄 成功: {data['success']}")
                            if 'version' in data:
                                print(f"   📄 版本: {data['version']}")
                        elif isinstance(data, list):
                            print(f"   📄 返回列表，共{len(data)}项")
                            
                except Exception as e:
                    print(f"   📄 响应: {response.text[:100]}...")
                    
            else:
                print(f"   ❌ 状态码异常 (期望: {expected})")
                print(f"   📄 错误: {response.text[:100]}...")
            
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        
        print()
    
    # 总结
    print("=" * 50)
    print(f"🎯 测试完成: {success_count}/{total_count} 个API正常")
    
    if success_count == total_count:
        print("🎉 所有API测试通过！")
        return True
    elif success_count >= total_count * 0.8:
        print("✅ 大部分API正常工作")
        return True
    else:
        print("⚠️ 多个API仍有问题，需要进一步检查")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 测试修复后的license_server.py")
        print("🔧 修复内容: 添加了根路由 @app.route('/')")
        print("💡 验证: 检查所有API是否正常响应")
        print()
        
        if test_fixed_api():
            print("\n✅ API修复成功！")
            print("\n📋 现在你可以:")
            print("1. 将修复后的license_server.py上传到服务器")
            print("2. 重启license-manager服务")
            print("3. 使用自动更新功能")
        else:
            print("\n⚠️ 部分API仍有问题")
            print("建议检查服务器日志获取更多信息")
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
