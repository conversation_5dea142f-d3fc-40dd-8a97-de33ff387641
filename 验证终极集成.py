#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证终极下载器集成 - 确保license_client.py能正常使用
"""

import ast
import sys

def check_syntax(filename):
    """检查语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, "语法正确"
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"检查失败: {e}"

def test_import():
    """测试导入"""
    try:
        # 清除缓存
        if 'auto_updater' in sys.modules:
            del sys.modules['auto_updater']
        
        import auto_updater
        
        # 检查关键类和方法
        if hasattr(auto_updater, 'AutoUpdater'):
            updater_class = auto_updater.AutoUpdater
            if hasattr(updater_class, 'download_update'):
                return True, "导入成功，download_update方法存在"
            else:
                return False, "download_update方法不存在"
        else:
            return False, "AutoUpdater类不存在"
    except Exception as e:
        return False, f"导入失败: {e}"

def test_license_client_compatibility():
    """测试license_client.py兼容性"""
    try:
        # 清除缓存
        modules_to_clear = ['auto_updater', 'license_client']
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        # 测试导入
        from auto_updater import check_and_update_silent as check_and_update
        
        return True, "license_client.py兼容性测试通过"
    except Exception as e:
        return False, f"兼容性测试失败: {e}"

def check_ultimate_features():
    """检查终极下载器特性"""
    try:
        with open('auto_updater.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = {
            "断点续传": "Range" in content and "bytes=" in content,
            "智能重试": "max_retries = 10" in content,
            "小块下载": "chunk_size = 4096" in content,
            "宽松验证": "completion >= 99.0" in content,
            "缓冲区刷新": "os.fsync" in content,
            "终极标识": "终极下载器" in content
        }
        
        return features
    except Exception as e:
        return {"错误": str(e)}

def test_quick_download():
    """快速下载测试"""
    try:
        # 清除缓存
        if 'auto_updater' in sys.modules:
            del sys.modules['auto_updater']
        
        from auto_updater import AutoUpdater
        
        # 创建实例
        updater = AutoUpdater(
            current_version="2.1.0",
            license_server_url="http://198.23.135.176:5000",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        # 检查更新
        update_info = updater.check_for_updates()
        
        if update_info:
            return True, f"更新检查成功: {update_info.get('version', 'Unknown')}"
        else:
            return True, "当前已是最新版本"
            
    except Exception as e:
        return False, f"快速测试失败: {e}"

def main():
    """主函数"""
    print("🔍 验证终极下载器集成")
    print("=" * 60)
    
    # 1. 语法检查
    print("1️⃣ 检查auto_updater.py语法...")
    syntax_ok, syntax_msg = check_syntax('auto_updater.py')
    if syntax_ok:
        print(f"   ✅ {syntax_msg}")
    else:
        print(f"   ❌ {syntax_msg}")
        return
    
    # 2. 导入测试
    print("\n2️⃣ 测试模块导入...")
    import_ok, import_msg = test_import()
    if import_ok:
        print(f"   ✅ {import_msg}")
    else:
        print(f"   ❌ {import_msg}")
        return
    
    # 3. license_client.py兼容性
    print("\n3️⃣ 测试license_client.py兼容性...")
    compat_ok, compat_msg = test_license_client_compatibility()
    if compat_ok:
        print(f"   ✅ {compat_msg}")
    else:
        print(f"   ❌ {compat_msg}")
    
    # 4. 检查终极特性
    print("\n4️⃣ 检查终极下载器特性...")
    features = check_ultimate_features()
    for feature, exists in features.items():
        status = "✅" if exists else "❌"
        print(f"   {status} {feature}: {'已集成' if exists else '未找到'}")
    
    # 5. 快速功能测试
    print("\n5️⃣ 快速功能测试...")
    test_ok, test_msg = test_quick_download()
    if test_ok:
        print(f"   ✅ {test_msg}")
    else:
        print(f"   ❌ {test_msg}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 验证结果总结")
    print("=" * 60)
    
    all_features_ok = all(features.values()) if isinstance(features, dict) else False
    
    if syntax_ok and import_ok and compat_ok and all_features_ok and test_ok:
        print("🎉 所有测试都通过!")
        print("\n✅ 终极下载器已成功集成到license_client.py")
        print("✅ 现在可以解决99.8%下载问题")
        print("✅ 支持断点续传和智能重试")
        
        print("\n🚀 使用方法:")
        print("1. 运行 python license_client.py")
        print("2. 点击'检查更新'按钮")
        print("3. 享受稳定的更新体验!")
        
    else:
        print("⚠️ 部分测试未通过，可能需要进一步调试")
        
        if not syntax_ok:
            print("- 语法错误需要修复")
        if not import_ok:
            print("- 导入问题需要解决")
        if not compat_ok:
            print("- 兼容性问题需要处理")
        if not all_features_ok:
            print("- 某些终极特性未正确集成")
        if not test_ok:
            print("- 功能测试失败")

if __name__ == "__main__":
    main()
