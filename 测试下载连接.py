#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载功能
"""

import requests
import time
import os
from pathlib import Path

def test_download_connection():
    """测试下载连接"""
    print("🔍 测试服务器连接...")
    
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': '2.1.1'
    }
    
    try:
        # 测试HEAD请求，获取文件信息
        response = requests.head(url, params=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if 'content-length' in response.headers:
            size = int(response.headers['content-length'])
            print(f"文件大小: {size // 1024 // 1024}MB")
        
        if 'accept-ranges' in response.headers:
            print(f"支持断点续传: {response.headers['accept-ranges']}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

def test_small_download():
    """测试小文件下载"""
    print("\n📥 测试小文件下载...")
    
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': '2.1.1'
    }
    
    try:
        # 只下载前1MB测试
        headers = {'Range': 'bytes=0-1048575'}  # 1MB
        
        response = requests.get(
            url, 
            params=params, 
            headers=headers,
            stream=True, 
            timeout=(10, 60)
        )
        
        if response.status_code in [200, 206]:
            downloaded = 0
            start_time = time.time()
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    downloaded += len(chunk)
                    if downloaded >= 1048576:  # 1MB
                        break
            
            elapsed = time.time() - start_time
            speed = downloaded / elapsed / 1024 / 1024  # MB/s
            
            print(f"下载速度: {speed:.2f}MB/s")
            print(f"下载 {downloaded} 字节，用时 {elapsed:.2f} 秒")
            
            return speed > 0.1  # 至少100KB/s
        else:
            print(f"下载失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"下载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 下载功能测试")
    print("=" * 40)
    
    # 测试连接
    connection_ok = test_download_connection()
    
    if connection_ok:
        # 测试下载速度
        download_ok = test_small_download()
        
        print("\n" + "=" * 40)
        print("📊 测试结果:")
        print(f"   - 服务器连接: {'✅' if connection_ok else '❌'}")
        print(f"   - 下载速度: {'✅' if download_ok else '❌'}")
        
        if connection_ok and download_ok:
            print("\n🎉 网络连接正常，可以进行更新下载")
        else:
            print("\n⚠️ 网络连接有问题，可能影响更新下载")
    else:
        print("\n❌ 无法连接到更新服务器")

if __name__ == "__main__":
    main()
