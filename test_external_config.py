#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试外部配置文件机制
"""

import os
import json
from datetime import datetime

def test_external_config():
    """测试外部配置文件的读写"""
    print("=== 测试外部配置文件机制 ===")
    
    try:
        from update_config import get_external_version, update_version, get_config
        
        # 1. 测试初始状态
        print(f"1. 初始版本号: {get_external_version()}")
        
        # 2. 测试更新版本号
        test_version = "2.1.5"
        print(f"2. 更新版本号到: {test_version}")
        result = update_version(test_version)
        print(f"   更新结果: {result}")
        
        # 3. 测试读取更新后的版本号
        updated_version = get_external_version()
        print(f"3. 更新后版本号: {updated_version}")
        
        # 4. 测试配置获取
        config = get_config()
        print(f"4. 配置中的版本号: {config.get('current_version')}")
        
        # 5. 检查外部配置文件
        config_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
        external_config_file = os.path.join(config_dir, "version_config.json")
        
        if os.path.exists(external_config_file):
            with open(external_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            print(f"5. 外部配置文件内容: {config_data}")
        else:
            print("5. 外部配置文件不存在")
        
        # 6. 验证版本比较
        if updated_version == test_version:
            print("✅ 外部配置文件机制工作正常")
        else:
            print("❌ 外部配置文件机制有问题")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_version_persistence():
    """测试版本号持久化"""
    print("\n=== 测试版本号持久化 ===")
    
    try:
        from update_config import update_version, get_external_version
        
        # 模拟多次更新
        versions = ["2.1.6", "2.1.7", "2.1.8"]
        
        for version in versions:
            print(f"更新到版本: {version}")
            update_version(version)
            current = get_external_version()
            print(f"读取到版本: {current}")
            
            if current == version:
                print(f"✅ 版本 {version} 持久化成功")
            else:
                print(f"❌ 版本 {version} 持久化失败")
        
    except Exception as e:
        print(f"❌ 持久化测试失败: {e}")

def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    try:
        config_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
        external_config_file = os.path.join(config_dir, "version_config.json")
        
        if os.path.exists(external_config_file):
            os.remove(external_config_file)
            print("✅ 测试配置文件已清理")
        else:
            print("ℹ️ 没有需要清理的文件")
            
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def main():
    """主测试函数"""
    print("🔧 外部配置文件机制测试")
    print("=" * 50)
    
    test_external_config()
    test_version_persistence()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    
    # 询问是否清理测试文件
    try:
        choice = input("\n是否清理测试文件？(y/n): ").lower().strip()
        if choice == 'y':
            cleanup_test_files()
    except:
        pass
    
    print("\n📋 外部配置文件机制说明:")
    print("1. ✅ 版本号保存在用户目录的外部文件中")
    print("2. ✅ 兼容exe打包环境（不依赖修改内部文件）")
    print("3. ✅ 支持版本号持久化和读取")
    print("4. ✅ 更新脚本会正确更新外部配置文件")
    
    print("\n🚀 现在重新构建exe文件，版本号更新应该能正常工作！")

if __name__ == "__main__":
    main()
