#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试更新功能 - 详细日志版本
"""

import tkinter as tk
from tkinter import messagebox, scrolledtext
import requests
import json
import traceback
import sys
import os

class DebugUpdater:
    """调试版本的更新器"""
    
    def __init__(self, log_callback=None):
        self.log_callback = log_callback or print
        self.license_key = "ADMIN_BYPASS"
        self.device_id = "ADMIN-DEVICE-001"
        self.server_url = "http://198.23.135.176:5000"
    
    def log(self, message):
        """记录日志"""
        self.log_callback(message)
    
    def check_update(self, current_version="2.1.0"):
        """检查更新"""
        self.log(f"🔍 开始检查更新...")
        self.log(f"📋 当前版本: {current_version}")
        self.log(f"🔑 授权密钥: {self.license_key}")
        self.log(f"📱 设备ID: {self.device_id}")
        
        try:
            url = f"{self.server_url}/update/check"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'current_version': current_version
            }
            
            self.log(f"📡 请求URL: {url}")
            self.log(f"📋 请求参数: {params}")
            
            response = requests.get(url, params=params, timeout=15)
            
            self.log(f"📊 响应状态码: {response.status_code}")
            self.log(f"📄 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log(f"✅ JSON解析成功")
                    self.log(f"📊 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    if result.get('success'):
                        if result.get('has_update'):
                            self.log("🎉 发现可用更新")
                            return result.get('update_info')
                        else:
                            self.log("ℹ️ 没有可用更新")
                            return None
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log(f"❌ 服务器返回错误: {error_msg}")
                        return None
                        
                except json.JSONDecodeError as e:
                    self.log(f"❌ JSON解析失败: {e}")
                    self.log(f"📄 原始响应: {response.text}")
                    return None
            else:
                self.log(f"❌ HTTP请求失败: {response.status_code}")
                self.log(f"📄 错误内容: {response.text}")
                return None
                
        except requests.exceptions.Timeout as e:
            self.log(f"❌ 请求超时: {e}")
            return None
        except requests.exceptions.ConnectionError as e:
            self.log(f"❌ 连接错误: {e}")
            return None
        except Exception as e:
            self.log(f"❌ 检查更新异常: {e}")
            self.log(f"📄 详细错误: {traceback.format_exc()}")
            return None
    
    def test_download(self, version="2.1.1"):
        """测试下载"""
        self.log(f"📥 测试下载版本: {version}")
        
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': version
            }
            
            # 只下载前1KB测试
            headers = {'Range': 'bytes=0-1023'}
            
            self.log(f"📡 下载URL: {url}")
            self.log(f"📋 下载参数: {params}")
            self.log(f"📋 请求头: {headers}")
            
            response = requests.get(url, params=params, headers=headers, timeout=30)
            
            self.log(f"📊 下载响应状态: {response.status_code}")
            self.log(f"📄 响应头: {dict(response.headers)}")
            
            if response.status_code in [200, 206]:
                content_length = response.headers.get('Content-Length', '未知')
                content_type = response.headers.get('Content-Type', '未知')
                
                self.log(f"✅ 下载测试成功")
                self.log(f"📦 内容长度: {content_length}")
                self.log(f"📄 内容类型: {content_type}")
                self.log(f"📊 实际下载: {len(response.content)} 字节")
                return True
            else:
                self.log(f"❌ 下载失败: {response.status_code}")
                self.log(f"📄 错误内容: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ 下载测试异常: {e}")
            self.log(f"📄 详细错误: {traceback.format_exc()}")
            return False

def create_debug_gui():
    """创建调试界面"""
    root = tk.Tk()
    root.title("更新功能调试器")
    root.geometry("800x600")
    
    # 设置图标
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 日志显示区域
    log_frame = tk.Frame(root)
    log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    tk.Label(log_frame, text="调试日志", font=("微软雅黑", 12, "bold")).pack(anchor=tk.W)
    
    log_text = scrolledtext.ScrolledText(log_frame, height=25, width=100)
    log_text.pack(fill=tk.BOTH, expand=True, pady=5)
    
    def log_message(msg):
        """记录日志消息"""
        log_text.insert(tk.END, msg + "\n")
        log_text.see(tk.END)
        root.update()
    
    # 创建调试器
    debugger = DebugUpdater(log_callback=log_message)
    
    # 按钮区域
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    def test_check_update():
        """测试检查更新"""
        log_text.delete(1.0, tk.END)
        log_message("=" * 60)
        log_message("🧪 测试检查更新功能")
        log_message("=" * 60)
        
        update_info = debugger.check_update("2.1.0")
        
        if update_info:
            log_message("✅ 检查更新成功，有可用更新")
            messagebox.showinfo("成功", "检查更新成功，有可用更新")
        else:
            log_message("ℹ️ 检查更新完成，没有可用更新或出现错误")
            messagebox.showinfo("信息", "没有可用更新或检查失败")
    
    def test_download():
        """测试下载"""
        log_message("\n" + "=" * 60)
        log_message("🧪 测试下载功能")
        log_message("=" * 60)
        
        success = debugger.test_download("2.1.1")
        
        if success:
            log_message("✅ 下载测试成功")
            messagebox.showinfo("成功", "下载测试成功")
        else:
            log_message("❌ 下载测试失败")
            messagebox.showerror("失败", "下载测试失败")
    
    def test_full_flow():
        """测试完整流程"""
        log_text.delete(1.0, tk.END)
        log_message("=" * 60)
        log_message("🧪 测试完整更新流程")
        log_message("=" * 60)
        
        # 1. 检查更新
        log_message("1️⃣ 检查更新...")
        update_info = debugger.check_update("2.1.0")
        
        if not update_info:
            log_message("❌ 检查更新失败，停止测试")
            messagebox.showerror("失败", "检查更新失败")
            return
        
        # 2. 测试下载
        log_message("\n2️⃣ 测试下载...")
        download_success = debugger.test_download("2.1.1")
        
        if download_success:
            log_message("✅ 完整流程测试通过")
            messagebox.showinfo("成功", "完整更新流程测试通过")
        else:
            log_message("❌ 下载测试失败")
            messagebox.showerror("失败", "下载测试失败")
    
    def test_real_update():
        """测试真实更新功能"""
        log_message("\n" + "=" * 60)
        log_message("🧪 测试真实更新功能")
        log_message("=" * 60)
        
        try:
            from auto_updater import check_and_update
            
            log_message("📦 导入auto_updater成功")
            log_message("🚀 调用check_and_update...")
            
            result = check_and_update(
                parent_window=root,
                current_version="2.1.0",
                license_key="ADMIN_BYPASS",
                device_id="ADMIN-DEVICE-001"
            )
            
            log_message(f"📊 更新结果: {result}")
            
            if result:
                log_message("✅ 更新功能执行成功")
                messagebox.showinfo("成功", "更新功能执行成功")
            else:
                log_message("ℹ️ 更新功能返回False（没有更新或用户取消）")
                messagebox.showinfo("信息", "没有更新或用户取消")
                
        except Exception as e:
            error_msg = f"❌ 真实更新测试失败: {e}"
            log_message(error_msg)
            log_message(f"📄 详细错误: {traceback.format_exc()}")
            messagebox.showerror("错误", error_msg)
    
    # 创建按钮
    buttons = [
        ("🔍 测试检查更新", test_check_update, "#3498db"),
        ("📥 测试下载", test_download, "#27ae60"),
        ("🔄 测试完整流程", test_full_flow, "#e67e22"),
        ("🚀 测试真实更新", test_real_update, "#e74c3c"),
        ("🗑️ 清空日志", lambda: log_text.delete(1.0, tk.END), "#95a5a6")
    ]
    
    for text, command, color in buttons:
        btn = tk.Button(
            button_frame,
            text=text,
            command=command,
            font=("微软雅黑", 10),
            bg=color,
            fg="white",
            padx=15,
            pady=5
        )
        btn.pack(side=tk.LEFT, padx=5)
    
    # 初始化日志
    log_message("🔧 更新功能调试器已启动")
    log_message("💡 点击按钮开始测试各项功能")
    log_message("📋 服务器地址: http://198.23.135.176:5000")
    log_message("🔑 使用管理员绕过认证")
    
    root.mainloop()

if __name__ == "__main__":
    create_debug_gui()
