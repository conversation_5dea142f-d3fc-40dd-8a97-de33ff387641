#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户端连接修复效果
"""

import requests
import json
import sys
import os

def test_client_connection():
    """测试客户端连接"""
    print("🔧 测试license_client.py连接修复效果")
    print("=" * 50)
    
    # 读取修复后的配置
    try:
        with open("license_client.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 查找服务器URL
        for line in content.split('\n'):
            if 'self.server_url = ' in line and 'http' in line:
                print(f"📋 客户端配置: {line.strip()}")
                # 提取URL
                url = line.split('"')[1]
                break
        else:
            print("❌ 未找到服务器URL配置")
            return
            
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return
    
    print(f"🌐 测试服务器: {url}")
    print()
    
    # 测试各个API端点
    test_cases = [
        {
            "name": "根路径",
            "endpoint": "/",
            "method": "GET",
            "expected": [200]
        },
        {
            "name": "License检查API",
            "endpoint": "/license/check",
            "method": "POST",
            "data": {
                "key": "TEST-KEY-123",
                "device_id": "TEST-DEVICE-001",
                "timestamp": "**********",
                "nonce": "test-nonce",
                "signature": "test-signature"
            },
            "expected": [200, 400, 401]  # 可能的正常响应
        },
        {
            "name": "License生成API",
            "endpoint": "/license/generate",
            "method": "POST",
            "data": {
                "days": 30,
                "permission_level": 1
            },
            "expected": [200, 400, 401]
        },
        {
            "name": "Update检查API",
            "endpoint": "/update/check",
            "method": "GET",
            "params": {
                "current_version": "2.0.0",
                "key": "ADMIN_BYPASS",
                "device_id": "ADMIN-DEVICE-001"
            },
            "expected": [200, 400]
        },
        {
            "name": "Update统计API",
            "endpoint": "/update/stats",
            "method": "GET",
            "expected": [200]
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test in enumerate(test_cases, 1):
        print(f"🧪 测试 {i}/{total_count}: {test['name']}")
        
        try:
            if test['method'] == 'GET':
                if 'params' in test:
                    response = requests.get(
                        f"{url}{test['endpoint']}", 
                        params=test['params'],
                        timeout=10
                    )
                else:
                    response = requests.get(f"{url}{test['endpoint']}", timeout=10)
            else:  # POST
                response = requests.post(
                    f"{url}{test['endpoint']}", 
                    json=test.get('data', {}),
                    timeout=10
                )
            
            status_code = response.status_code
            print(f"   📊 状态码: {status_code}")
            
            if status_code in test['expected']:
                print(f"   ✅ 响应正常")
                success_count += 1
                
                # 尝试解析JSON响应
                try:
                    data = response.json()
                    if 'message' in data:
                        print(f"   📄 消息: {data['message']}")
                    if 'success' in data:
                        print(f"   🎯 成功: {data['success']}")
                except:
                    print(f"   📄 响应: {response.text[:50]}...")
            else:
                print(f"   ⚠️ 意外状态码 (期望: {test['expected']})")
                try:
                    data = response.json()
                    print(f"   📄 错误: {data.get('message', 'Unknown')}")
                except:
                    print(f"   📄 响应: {response.text[:50]}...")
                    
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败")
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        
        print()
    
    # 总结
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 个API正常响应")
    
    if success_count == total_count:
        print("🎉 所有API测试通过！客户端连接修复成功！")
        print()
        print("✅ 现在可以正常使用license_client.py了")
        print("✅ 激活码验证应该可以正常工作")
        print("✅ 自动更新功能应该可以正常工作")
    elif success_count > total_count // 2:
        print("⚠️ 大部分API正常，但仍有一些问题")
        print("💡 建议检查服务器配置和API实现")
    else:
        print("❌ 多数API测试失败")
        print("💡 请检查服务器是否正常运行")
    
    return success_count == total_count

def main():
    """主函数"""
    print("🎯 目标: 验证license_client.py连接修复效果")
    print("🔧 修复: 将端口从44285改为5000")
    print()
    
    if test_client_connection():
        print("\n🚀 修复成功！现在可以启动license_client.py测试了")
        
        # 询问是否启动客户端
        try:
            choice = input("\n是否现在启动license_client.py进行测试？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                print("🚀 启动license_client.py...")
                import subprocess
                subprocess.run([sys.executable, "license_client.py"])
        except KeyboardInterrupt:
            print("\n👋 用户取消")
    else:
        print("\n❌ 仍有问题需要解决")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
