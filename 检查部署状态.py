#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查CentOS服务器部署状态
详细验证每个组件的运行情况
"""

import paramiko
import sys

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def check_deployment():
    """检查部署状态"""
    print("🔍 检查CentOS服务器部署状态")
    print("=" * 50)
    
    # 服务器配置
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"👤 用户: {config['username']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    checks_passed = 0
    total_checks = 6
    
    # 检查1: 基本连接
    print("🔗 检查1: SSH连接测试")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "echo 'SSH连接成功' && uname -a")
    if success:
        print("✅ SSH连接正常")
        print(f"   系统: {output.strip()}")
        checks_passed += 1
    else:
        print(f"❌ SSH连接失败: {error}")
    print()
    
    # 检查2: 文件部署
    print("📁 检查2: 文件部署状态")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"ls -la {config['deploy_path']}/")
    if success:
        print("✅ 部署目录存在")
        files = output.split('\n')
        for file in files:
            if any(name in file for name in ['license_server.py', 'auto_updater.py', 'version_info.json']):
                print(f"   {file}")
        checks_passed += 1
    else:
        print(f"❌ 部署目录检查失败: {error}")
    print()
    
    # 检查3: license-manager服务
    print("🔧 检查3: license-manager服务状态")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl status license-manager")
    if success:
        if "active (running)" in output:
            print("✅ license-manager服务运行中")
            checks_passed += 1
        else:
            print("⚠️ license-manager服务未运行")
        print(f"   状态: {output.split('Active:')[1].split('\\n')[0] if 'Active:' in output else '未知'}")
    else:
        print(f"❌ 无法检查license-manager服务: {error}")
    print()
    
    # 检查4: nginx服务
    print("🌐 检查4: nginx服务状态")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl status nginx")
    if success:
        if "active (running)" in output:
            print("✅ nginx服务运行中")
            checks_passed += 1
        else:
            print("⚠️ nginx服务未运行")
        print(f"   状态: {output.split('Active:')[1].split('\\n')[0] if 'Active:' in output else '未知'}")
    else:
        print(f"❌ 无法检查nginx服务: {error}")
    print()
    
    # 检查5: 端口监听
    print("🔌 检查5: 端口监听状态")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "netstat -tlnp | grep -E ':(80|5000)' || ss -tlnp | grep -E ':(80|5000)'")
    if success and output.strip():
        print("✅ 端口监听正常")
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
        checks_passed += 1
    else:
        print("❌ 端口监听检查失败")
        print("   可能原因: 服务未启动或端口配置错误")
    print()
    
    # 检查6: HTTP服务测试
    print("🌍 检查6: HTTP服务响应")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "curl -s -o /dev/null -w 'HTTP状态码: %{http_code}' http://localhost:5000/ 2>/dev/null || echo '连接失败'")
    if success and "200" in output:
        print("✅ HTTP服务响应正常")
        print(f"   {output}")
        checks_passed += 1
    else:
        print("❌ HTTP服务响应异常")
        print(f"   响应: {output}")
        
        # 检查进程
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], 
                                               "ps aux | grep python3 | grep license_server | head -1")
        if output2.strip():
            print(f"   进程: {output2.strip()}")
        else:
            print("   进程: license_server进程未找到")
    print()
    
    # 总结
    print("📊 检查结果总结")
    print("=" * 30)
    print(f"✅ 通过检查: {checks_passed}/{total_checks}")
    
    if checks_passed == total_checks:
        print("🎉 部署完全成功！所有服务运行正常")
        print(f"🌐 服务地址: http://{config['host']}:5000/")
    elif checks_passed >= 4:
        print("⚠️ 部署基本成功，但有些服务需要调整")
        print("💡 建议:")
        if checks_passed < total_checks:
            print("   • 检查服务配置")
            print("   • 重启相关服务")
            print("   • 检查防火墙设置")
    else:
        print("❌ 部署存在问题，需要手动修复")
        print("💡 建议:")
        print("   • 检查服务日志: journalctl -u license-manager")
        print("   • 重新运行部署脚本")
        print("   • 手动启动服务")
    
    return checks_passed >= 4

def main():
    """主函数"""
    try:
        success = check_deployment()
        
        if success:
            print("\n🎯 下一步操作:")
            print("1. 在浏览器中访问: http://**************:5000/")
            print("2. 测试授权功能")
            print("3. 配置域名和SSL证书 (可选)")
        else:
            print("\n🔧 故障排除:")
            print("1. 重新运行部署工具")
            print("2. 手动启动服务: systemctl start license-manager")
            print("3. 检查日志: journalctl -u license-manager -f")
        
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
