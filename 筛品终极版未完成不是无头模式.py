import os
import time
import pandas as pd
import re
import random
import logging
import json
import threading
import queue
import requests
import tempfile
import platform
import sys
import shutil # 新增导入
from datetime import datetime
import urllib.parse
import traceback

# 检查并安装必要的库
def check_and_install_dependencies():
    """检查并安装必要的依赖库"""
    required_packages = {
        'selenium': 'selenium',
        'amazoncaptcha': 'amazoncaptcha',
        'beautifulsoup4': 'bs4',
        'pandas': 'pandas',
        'requests': 'requests',
        'webdriver_manager': 'webdriver_manager'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"缺少必要的库: {', '.join(missing_packages)}")
        try:
            import subprocess
            for package in missing_packages:
                print(f"正在安装 {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print("所有依赖库安装完成，重新导入...")
            # 重新导入所有模块
            for package_name, import_name in required_packages.items():
                if package_name in missing_packages:
                    __import__(import_name)
        except Exception as e:
            print(f"安装依赖库时出错: {str(e)}")
            print("请手动安装以下库:")
            for package in missing_packages:
                print(f"    pip install {package}")
            sys.exit(1)

# 检查并安装依赖
try:
    check_and_install_dependencies()
except Exception as e:
    print(f"依赖检查失败: {str(e)}")

# 导入其他必要的库
try:
    from selenium import webdriver
    from selenium.webdriver.edge.service import Service
    from selenium.webdriver.edge.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    import amazoncaptcha
    from bs4 import BeautifulSoup
    import tkinter as tk
    from tkinter import ttk, messagebox, filedialog
    from webdriver_manager.microsoft import EdgeChromiumDriverManager # 新增导入
except ImportError as e:
    print(f"导入库失败: {str(e)}")
    print("请确保所有必要的库都已正确安装")
    sys.exit(1)

# 全局变量用于在GUI关闭时引用正在运行的爬虫实例和线程
running_scraper_instance = None
processing_thread_ref = None

# 设置日志
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
logging.basicConfig(
    level=logging.INFO,
    format=LOG_FORMAT,
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LogLevel:
    SIMPLE = 0    # 简易日志（只显示关键信息）
    NORMAL = 1    # 正常日志（默认级别）
    DETAILED = 2  # 详细日志（包含所有细节）

class AmazonAsinScraper:
    def __init__(self, config=None):
        """初始化Amazon ASIN处理器"""
        # 初始化配置
        self.config = config or {}
        
        # 配置文件保存在用户文件夹
        user_folder = os.path.join(os.path.expanduser('~'), '智赢账户配置')
        # 确保文件夹存在
        os.makedirs(user_folder, exist_ok=True)
        self.config_file = os.path.join(user_folder, "config.json")
        # 新增：加载现有配置，并合并传入的config
        self._load_config() # 优先加载文件中的配置
        self.config.update(config or {}) # 合并传入的config，覆盖文件中的同名配置
        
        # 新增：从配置中获取登录凭据
        self.login_username = self.config.get('login_username', '')
        self.login_password = self.config.get('login_password', '')
        
        # 设置日志级别
        self.log_level = self.config.get('log_level', LogLevel.SIMPLE)  # 默认为SIMPLE级别
        
        # 初始化已处理的ASIN集合，用于防止重复处理
        self.processed_asins = set()
        
        # 设置国家相关属性
        self.countries = {
            "美国": {"domain": "com", "zipcode": "98032", "zipcode_parts": None},
            "日本": {"domain": "co.jp", "zipcode": "060-8588", "zipcode_parts": ["060", "8588"]},
            "加拿大": {"domain": "ca", "zipcode": "B3H 0A9", "zipcode_parts": ["B3H", "0A9"]}
        }
        self.country = "美国"  # 默认国家
        self.country_domain = "com"  # 默认域名
        self.country_zipcode = "98032"  # 默认邮编
        
        # 初始化结果列表和品牌类型分类
        self.results = []
        self.timeout_asins = []
        self.visit_the_brand_asins = []
        self.other_brand_asins = []
        self.non_compliant_asins = []  # 添加不符合条件的ASIN列表
        
        # 品牌分类
        self.compliant_brands = []      # 符合条件的品牌
        self.noncompliant_brands = []   # 不符合条件的品牌
        self.brand_compliant_count = 0  # 符合条件的品牌数量
        self.brand_noncompliant_count = 0  # 不符合条件的品牌数量
        
        # 创建一个webdriver空对象，稍后再初始化
        self.driver = None
        
        # 计数器和标志
        self.asin_counter = 0
        self.processed_count = 0
        self.skip_summary = False
        self.headless_mode = True # 默认显示浏览器界面
        
        # 断点续传和实时保存相关
        self.created_result_files = {}
        self.non_unique_brand_asins = []
        
        # 品牌结果分类
        self.brand_results = {
            "visit_the": {
                "compliant": {},
                "non_compliant": []
            },
            "brand": {
                "compliant": {},
                "non_compliant": []
            },
            "no_brand": {
                "compliant": {},
                "non_compliant": []
            }
        }
        
        # 初始化品牌结果分类字典
        for brand_type in self.brand_results.keys():
            for i in range(7):  # 0-6 七种分类
                self.brand_results[brand_type]["compliant"][i] = []
        
        # 系统信息检测
        self.system_info = self.check_system_compatibility()
        
        # 多线程相关设置
        self.asin_queue = queue.Queue()  # 初始化ASIN队列
        self.lock = threading.Lock()  # 线程同步锁
        
        # 新增：用于临时用户数据目录
        self.user_data_dir = None
        # 新增：记录上次浏览器重启时间，用于定时重启
        self.last_browser_restart_time = time.time()
        
        # 新增：连续"商品不存在"错误计数器
        self.consecutive_not_found_errors = 0
        
        # 新增：连续发现zyCardWrap但无品牌元素的计数器
        self.consecutive_no_brand_in_zycard = 0
        
        # 结果文件夹将在setup_results_folder中初始化
        
        # 新增：控制线程停止的标志
        self._should_stop = False
        
    # 定义503错误处理和搜索重试的常量
    MAX_503_REFRESH_ATTEMPTS = 5
    RETRY_INTERVAL_503_SECONDS = 10
    MAX_SEARCH_ATTEMPTS = 3
    
    # 新增：定义浏览器启动重试的常量
    MAX_BROWSER_SETUP_ATTEMPTS = 3
    BROWSER_SETUP_RETRY_INTERVAL_SECONDS = 5
    
    # 新增：定义基于时间的浏览器重启间隔（分钟）
    BROWSER_RESTART_TIME_INTERVAL_MINUTES = 999999 # 设置一个很大的值来禁用定时重启
    
    # 新增：定义内容加载失败时的页面刷新常量
    MAX_REFRESH_ATTEMPTS_FOR_CONTENT = 3
    REFRESH_INTERVAL_SECONDS = 5
    
    # 新增：定义连续"商品不存在"错误的阈值
    MAX_CONSECUTIVE_NOT_FOUND_ERRORS = 5
    
    def _load_config(self):
        """加载配置文件，不输出日志"""
        # 使用指定的配置文件路径（在用户文件夹中）
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
                    # 不输出日志
            except Exception as e:
                # 仅在出错时输出日志
                self.log_message(f"加载配置文件 {self.config_file} 时出错: {str(e)}", always_show=True)
        # 不再输出文件不存在的日志

    def _save_config(self):
        """保存当前配置到文件，不输出日志"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            # 确保保存的配置包含登录凭据和GUI设置相关项
            config_to_save = {
                'log_level': self.log_level,
                'headless_mode': self.headless_mode,
                'country': self.country,
                'login_username': self.login_username,
                'login_password': self.login_password
            }
            # 合并其他可能存在的配置项
            config_to_save.update(self.config)
            
            # 直接使用配置文件路径，而不是基于当前工作目录
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=4)
            # 不输出日志
        except Exception as e:
            # 仅在出错时输出日志
            self.log_message(f"保存配置文件 {self.config_file} 时出错: {str(e)}", always_show=True)
    
    def check_system_compatibility(self):
        """检查系统兼容性并返回系统信息"""
        system_info = {
            "os": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "architecture": platform.architecture(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "python_version": sys.version,
            "is_64bit": sys.maxsize > 2**32,
            "is_win10": False,
            "is_win7": False
        }
        
        # 判断是否为Win10系统
        if system_info["os"] == "Windows" and "10" in system_info["version"]:
            system_info["is_win10"] = True
            self.log_message("检测到Windows 10系统，将使用自动更新驱动模式", always_show=True)
        
        # 判断是否为Win7系统
        if system_info["os"] == "Windows" and "7" in system_info["release"]:
            system_info["is_win7"] = True
            self.log_message("检测到Windows 7系统，将应用特殊兼容性配置", always_show=True)
            
        # 如果是32位系统，发出警告
        if not system_info["is_64bit"]:
            self.log_message("警告: 检测到32位系统，某些功能可能不正常工作。将采用兼容模式。", always_show=True)
            
        # 记录系统信息
        self.log_message(f"系统信息: {system_info}", always_show=True)
            
        return system_info

    def setup_results_folder(self, country_name):
        """设置结果保存的文件夹结构"""
        self.log_message("正在设置结果文件夹结构...", always_show=True)
        
        # 创建主结果文件夹
        results_folder = os.path.join(os.getcwd(), "筛选结果")
        if not os.path.exists(results_folder):
            os.makedirs(results_folder)
            self.log_message(f"创建主结果文件夹: {results_folder}", always_show=True)
        
        # 创建国家文件夹
        country_folder = os.path.join(results_folder, country_name)
        if not os.path.exists(country_folder):
            os.makedirs(country_folder)
            self.log_message(f"创建国家文件夹: {country_folder}", always_show=True)
        
        # 创建时间戳文件夹，确保每次运行都有独立的结果目录
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_folder = os.path.join(country_folder, current_time)
        if not os.path.exists(run_folder):
            os.makedirs(run_folder)
            self.log_message(f"创建运行时间文件夹: {run_folder}", always_show=True)
        
        # 创建"符合"、"不符合"、"品牌为空"和"网络超时"文件夹
        compliant_folder = os.path.join(run_folder, "符合")
        non_compliant_folder = os.path.join(run_folder, "不符合")
        empty_brand_folder = os.path.join(run_folder, "品牌为空")
        timeout_folder = os.path.join(run_folder, "网络超时")
        
        for folder in [compliant_folder, non_compliant_folder, empty_brand_folder, timeout_folder]:
            if not os.path.exists(folder):
                os.makedirs(folder)
                self.log_message(f"创建文件夹: {folder}", always_show=True)
        
        # 定义库存状态和品牌类型
        stock_statuses = ["缺货", "有货"]
        brand_types = ["Visit_the类型", "Brand类型", "无品牌类型"]
        
        # 创建符合条件的文件夹结构
        self.condition_folders = {
            "符合": compliant_folder,
            "不符合": non_compliant_folder,
            "品牌为空": empty_brand_folder,
            "网络超时": timeout_folder
        }
        
        # 在符合条件文件夹下创建库存状态子文件夹
        for stock_status in stock_statuses:
            stock_folder_compliant = os.path.join(compliant_folder, stock_status)
            if not os.path.exists(stock_folder_compliant):
                os.makedirs(stock_folder_compliant)
                self.log_message(f"创建文件夹: {stock_folder_compliant}", always_show=True)
            
            # 在库存状态文件夹下创建品牌类型文件夹
            for brand_type in brand_types:
                brand_folder = os.path.join(stock_folder_compliant, brand_type)
                if not os.path.exists(brand_folder):
                    os.makedirs(brand_folder)
                    self.log_message(f"创建品牌类型文件夹: {brand_folder}", always_show=True)
        
        # 在品牌为空文件夹下创建库存状态子文件夹
        for stock_status in stock_statuses:
            stock_folder_empty = os.path.join(empty_brand_folder, stock_status)
            if not os.path.exists(stock_folder_empty):
                os.makedirs(stock_folder_empty)
                self.log_message(f"创建文件夹: {stock_folder_empty}", always_show=True)
            
            # 在品牌为空的库存状态文件夹下创建品牌类型文件夹
            for brand_type in brand_types:
                brand_folder = os.path.join(stock_folder_empty, brand_type)
                if not os.path.exists(brand_folder):
                    os.makedirs(brand_folder)
                    self.log_message(f"创建品牌类型文件夹: {brand_folder}", always_show=True)
                    
        # 在网络超时文件夹下创建库存状态子文件夹
        for stock_status in stock_statuses:
            stock_folder_timeout = os.path.join(timeout_folder, stock_status)
            if not os.path.exists(stock_folder_timeout):
                os.makedirs(stock_folder_timeout)
                self.log_message(f"创建文件夹: {stock_folder_timeout}", always_show=True)
            
            # 在网络超时的库存状态文件夹下创建品牌类型文件夹
            for brand_type in brand_types:
                brand_folder = os.path.join(stock_folder_timeout, brand_type)
                if not os.path.exists(brand_folder):
                    os.makedirs(brand_folder)
                    self.log_message(f"创建品牌类型文件夹: {brand_folder}", always_show=True)
        
        # 保存路径信息
        self.results_folder = run_folder
        
        # 设置断点文件和超时文件路径
        # 断点文件保存在国家文件夹中，而不是时间戳文件夹，便于续传
        self.checkpoint_file = os.path.join(country_folder, "checkpoint.json")
        # 超时文件仍保存在当前运行文件夹中
        self.timeout_file = os.path.join(self.results_folder, "timeout_asins.xlsx")
        
        self.log_message(f"结果保存路径: {self.results_folder}", always_show=True)
        self.log_message(f"断点文件保存路径: {self.checkpoint_file}", always_show=True)
        self.log_message("文件夹结构设置完成", always_show=True)
        
        return self.results_folder

    def set_log_level(self, level):
        """设置日志级别"""
        if level in [LogLevel.SIMPLE, LogLevel.NORMAL, LogLevel.DETAILED]:
            self.log_level = level
            level_names = {
                LogLevel.SIMPLE: "简易",
                LogLevel.NORMAL: "正常",
                LogLevel.DETAILED: "详细"
            }
            self.log_message(f"日志级别已设置为: {level_names[level]}", always_show=True)
        else:
            self.log_message(f"无效的日志级别: {level}，使用默认级别", always_show=True)

    def log_message(self, message, always_show=False, level=LogLevel.NORMAL):
        """记录日志消息，根据设置的日志级别控制输出"""
        # 始终显示的日志，不考虑级别（仅用于关键错误和程序状态）
        if always_show:
            print(message)
            logger.info(message)
            return
            
        # 根据日志级别控制输出
        if level <= self.log_level:
            # 简易日志：仅记录到文件，不打印到控制台
            if level == LogLevel.SIMPLE:
                logger.info(message)
                # 简易模式下，仅打印重要状态信息
                if "处理品牌" in message or "处理ASIN" in message or "已保存" in message or "完成" in message:
                    print(message)
            # 正常日志：记录到文件并打印到控制台
            elif level == LogLevel.NORMAL:
                logger.info(message)
                print(message)
            # 详细日志：记录详细信息到文件和控制台
            elif level == LogLevel.DETAILED:
                logger.debug(message)
                print(message)
    
    def get_random_user_agent(self):
        """返回一个随机的用户代理，支持从固定列表选择或动态生成"""
        # 添加一个概率选择是使用固定列表还是动态生成
        use_dynamic = random.random() > 0.5  # 50%概率使用动态生成
        
        if use_dynamic:
            # 动态生成用户代理，参考队列.py的实现
            v1 = random.randint(100, 134)  # Chrome/Edge主版本
            v2 = random.randint(10, 25)    # 品牌版本
            v3 = random.randint(400, 600)  # WebKit版本
            
            # 随机选择浏览器类型
            browser_type = random.choice(['Chrome', 'Edge', 'Firefox', 'Safari'])
            os_type = random.choice(['Windows', 'Macintosh'])
            
            if browser_type == 'Chrome':
                return f"Mozilla/5.0 ({os_type} NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36"
            elif browser_type == 'Edge':
                return f"Mozilla/5.0 ({os_type} NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.{random.randint(1000, 9999)}.{random.randint(10, 99)}"
            elif browser_type == 'Firefox':
                return f"Mozilla/5.0 ({os_type} NT 10.0; Win64; x64; rv:{v1}.0) Gecko/20100101 Firefox/{v1}.0"
            elif browser_type == 'Safari' and os_type == 'Macintosh':
                return f"Mozilla/5.0 (Macintosh; Intel Mac OS X 12_{random.randint(0, 6)}_{random.randint(0, 9)}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.{random.randint(0, 6)} Safari/605.1.15"
        
        # 从固定列表中选择
        user_agents = [
            # Windows 10 + Chrome
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36",
            
            # Windows 10 + Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 Edg/98.0.1108.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36 Edg/99.0.1150.39",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36 Edg/100.0.1185.36",
            
            # Windows 10 + Firefox
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/20100101 Firefox/96.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:97.0) Gecko/20100101 Firefox/97.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:98.0) Gecko/20100101 Firefox/98.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:100.0) Gecko/20100101 Firefox/100.0",
            
            # macOS + Chrome
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_0_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_2_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_3_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_4_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36",
            
            # macOS + Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_0_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_2_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_3_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.3 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_4_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15"
        ]
        return random.choice(user_agents)
    
    def setup_selenium_browser(self):
        """设置Microsoft Edge浏览器，支持Win10和Win7系统"""
        try:
            # 获取系统类型
            is_win7 = self.system_info.get("is_win7", False)
            is_win10 = self.system_info.get("is_win10", False)
            is_64bit = self.system_info.get("is_64bit", True)
            
            edge_options = Options()
            
            # 新增：创建临时用户数据目录，用于每次启动都清空缓存和Cookie
            self.user_data_dir = tempfile.mkdtemp()
            edge_options.add_argument(f"--user-data-dir={self.user_data_dir}")
            self.log_message(f"创建临时用户数据目录: {self.user_data_dir}", always_show=True)

            # 基本配置
            edge_options.add_argument("--disable-gpu")
            edge_options.add_argument("--disable-dev-shm-usage")
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-browser-side-navigation")
            edge_options.add_argument("--disable-notifications")
            
            # 禁用图片加载，提高性能
            edge_options.add_argument("--blink-settings=imagesEnabled=false")
            edge_options.add_experimental_option("prefs", {"profile.managed_default_content_settings.images": 2})
            self.log_message("已禁用图片加载，提高性能", always_show=True)

            # 添加更多反爬虫检测参数
            edge_options.add_argument("--disable-blink-features=AutomationControlled")
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            edge_options.add_experimental_option("useAutomationExtension", False)
            
            # 设置更真实的用户代理，随机选择一个
            user_agent = self.get_random_user_agent()
            edge_options.add_argument(f"--user-agent={user_agent}")
            self.log_message(f"设置用户代理: {user_agent}", level=LogLevel.DETAILED)
            
            # 无头模式开关，可通过参数控制是否启用
            if hasattr(self, 'headless_mode') and self.headless_mode:
                edge_options.add_argument("--headless=new")  # 使用新版无头模式
                # 无头模式下的额外设置
                edge_options.add_argument("--window-size=1920,1080")
                edge_options.add_argument("--start-maximized")
                edge_options.add_argument("--disable-blink-features=AutomationControlled")
                # 设置WebGL和Canvas指纹信息，减少被检测风险
                edge_options.add_argument("--disable-webgl")
                edge_options.add_argument("--disable-canvas-aa")
                edge_options.add_argument("--disable-2d-canvas-clip-aa")
                edge_options.add_argument("--disable-gl-drawing-for-tests")
                self.log_message("已启用无头模式，浏览器将在后台运行", always_show=True)
            
            # 更全面的Edge浏览器路径检测
            possible_edge_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                # Edge Beta版本路径
                r"C:\Program Files (x86)\Microsoft\Edge Beta\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge Beta\Application\msedge.exe"
            ]
            
            edge_path = None
            for path in possible_edge_paths:
                if os.path.exists(path):
                    edge_path = path
                    self.log_message(f"找到Edge浏览器: {path}", always_show=True)
                    break
            
            # 如果找到Edge浏览器，设置二进制路径
            if edge_path:
                edge_options.binary_location = edge_path
            else:
                self.log_message("未找到Edge浏览器，尝试使用系统默认设置", always_show=True)
            
            # 加载欧鹭扩展插件
            current_dir = os.path.abspath(os.path.dirname(__file__))
            extension_path = os.path.join(current_dir, "oalur-extension-V1.9.3_3")
            
            # 检查扩展是否存在
            if os.path.exists(extension_path):
                edge_options.add_argument(f"--load-extension={extension_path}")
                self.log_message(f"加载欧陆扩展: {extension_path}", always_show=True)
            else:
                self.log_message(f"未找到欧陆扩展: {extension_path}，请确保扩展目录存在", always_show=True)
                # 尝试查找扩展目录
                self.log_message("正在查找可能的扩展目录...", always_show=True)
                dirs = os.listdir(current_dir)
                for dir_name in dirs:
                    full_path = os.path.join(current_dir, dir_name)
                    if os.path.isdir(full_path) and ("sellersprite" in dir_name.lower() or "extension" in dir_name.lower()):
                        self.log_message(f"找到可能的扩展目录: {full_path}", always_show=True)
                        edge_options.add_argument(f"--load-extension={full_path}")
                        self.log_message(f"尝试加载扩展: {full_path}", always_show=True)
                        break
            
            # 根据系统类型使用不同的驱动策略
            if is_win7:
                # Win7系统使用固定路径的驱动
                driver_path = r"C:\Program Files (x86)\Microsoft\Edge\Application\msedgedriver.exe"
                if os.path.exists(driver_path):
                    self.log_message(f"Win7系统使用固定路径驱动: {driver_path}", always_show=True)
                    service = Service(driver_path)
                    self.driver = webdriver.Edge(service=service, options=edge_options)
                    self.driver.maximize_window()
                    
                    # 执行CDP命令绕过检测
                    self.execute_cdp_commands()
                    
                    # 记录浏览器进程ID
                    self.browser_pid = self.find_browser_pid()
                    if self.browser_pid:
                        self.log_message(f"记录Edge浏览器进程ID: {self.browser_pid}", always_show=True)
                    
                    self.log_message("使用固定路径驱动启动Edge浏览器成功", always_show=True)
                    return True # 新增：成功启动后返回True
                    # 验证扩展是否成功加载（为Win7系统也添加这一调用）
                    # {{ delete_line }}
                    
                else:
                    self.log_message(f"错误：Win7系统未找到固定驱动路径: {driver_path}", always_show=True)
                    raise Exception("Edge浏览器驱动未找到。请检查路径或安装Edge浏览器。")
            else:
                # 其他系统尝试自动查找驱动
                self.log_message("尝试自动查找Edge浏览器驱动...", always_show=True)
                try:
                    service = Service(EdgeChromiumDriverManager().install()) # 将 EdgeService() 修改为 Service() 并添加webdriver_manager
                    self.driver = webdriver.Edge(service=service, options=edge_options)
                    self.driver.maximize_window()
                    
                    # 执行CDP命令绕过检测
                    self.execute_cdp_commands()
                    
                    # 记录浏览器进程ID
                    self.browser_pid = self.find_browser_pid()
                    if self.browser_pid:
                        self.log_message(f"记录Edge浏览器进程ID: {self.browser_pid}", always_show=True)
                    
                    self.log_message("使用自动查找驱动启动Edge浏览器成功", always_show=True)
                    return True # 新增：成功启动后返回True
                    # 验证扩展是否成功加载
                    # {{ delete_line }}
                    
                except Exception as e:
                    self.log_message(f"自动查找Edge浏览器驱动失败: {e}", always_show=True)
                    
                    # 尝试使用本地Edge目录中的驱动
                    local_driver_path = r"C:\Program Files (x86)\Microsoft\Edge\Application\msedgedriver.exe"
                    if os.path.exists(local_driver_path):
                        self.log_message(f"尝试使用本地Edge驱动: {local_driver_path}", always_show=True)
                        try:
                            service = Service(local_driver_path)
                            self.driver = webdriver.Edge(service=service, options=edge_options)
                            self.driver.maximize_window()
                            
                            # 执行CDP命令绕过检测
                            self.execute_cdp_commands()
                            
                            # 记录浏览器进程ID
                            self.browser_pid = self.find_browser_pid()
                            if self.browser_pid:
                                self.log_message(f"记录Edge浏览器进程ID: {self.browser_pid}", always_show=True)
                            
                            self.log_message("使用本地驱动启动Edge浏览器成功", always_show=True)
                            return True
                        except Exception as local_e:
                            self.log_message(f"使用本地Edge驱动失败: {local_e}", always_show=True)
                            raise local_e
                    else:
                        self.log_message(f"本地Edge驱动不存在: {local_driver_path}", always_show=True)
                        raise e
                
        except Exception as e:
            self.log_message(f"启动Edge浏览器时出错: {str(e)}", always_show=True)
            return False
    
    def check_extension_loaded(self):
        """
        检查欧鹭扩展是否成功加载。
        对于 sellersprite 插件，不再需要验证扩展是否成功加载，
        此方法现在只包含一个 pass 语句，以符合新的需求。
        """
        pass
    
    def close_selenium_browser(self):
        """关闭浏览器"""
        if self.driver:
            browser_pid = None
            try:
                # 尝试获取浏览器进程ID
                try:
                    # 首先尝试从browser_pid属性获取进程ID（如果之前已保存）
                    browser_pid = getattr(self, 'browser_pid', None)
                    
                    # 如果没有保存的进程ID，尝试通过执行JavaScript获取
                    if browser_pid is None and hasattr(self.driver, 'execute_script'):
                        try:
                            # 发送一个简单的心跳检测，确认浏览器仍然响应
                            self.driver.execute_script("return navigator.userAgent")
                            
                            # 在Windows系统上，使用命令行获取Edge浏览器进程
                            if platform.system() == "Windows":
                                import subprocess
                                import re
                                
                                # 查找msedge.exe进程
                                try:
                                    result = subprocess.check_output("wmic process where name='msedge.exe' get processid", shell=True)
                                    result_str = result.decode('utf-8', errors='ignore')
                                    # 提取所有进程ID
                                    pids = re.findall(r'\d+', result_str)
                                    if pids and len(pids) > 0:
                                        # 取最新的进程ID（通常是最后一个）
                                        browser_pid = int(pids[-1])
                                        self.log_message(f"找到Edge浏览器进程ID: {browser_pid}", always_show=True)
                                except Exception as e:
                                    self.log_message(f"获取Edge浏览器进程ID时出错: {str(e)}", level=LogLevel.DETAILED)
                        except Exception as e:
                            self.log_message(f"浏览器JavaScript执行失败，可能已经无响应: {str(e)}", level=LogLevel.DETAILED)
                except Exception as e:
                    self.log_message(f"获取浏览器进程ID时出错: {str(e)}", level=LogLevel.DETAILED)
                
                # 尝试通过WebDriver正常关闭
                self.log_message("尝试正常关闭浏览器...", always_show=True)
                
                # 先关闭所有标签页
                try:
                    if hasattr(self.driver, 'window_handles') and len(self.driver.window_handles) > 1:
                        # 保存当前窗口句柄
                        original_window = self.driver.current_window_handle
                        
                        # 逐个关闭其他标签页
                        for handle in self.driver.window_handles:
                            if handle != original_window:
                                try:
                                    self.driver.switch_to.window(handle)
                                    self.driver.close()
                                except:
                                    pass
                        
                        # 切回主窗口
                        try:
                            self.driver.switch_to.window(original_window)
                        except:
                            pass
                except Exception as e:
                    self.log_message(f"关闭多余标签页时出错: {str(e)}", level=LogLevel.DETAILED)
                
                # 正常关闭浏览器
                try:
                    self.driver.quit()
                    self.log_message("成功正常关闭浏览器", always_show=True)
                except Exception as e:
                    self.log_message(f"正常关闭浏览器失败: {str(e)}", always_show=True)
                    
                    # 如果正常关闭失败，尝试终止进程
                    if browser_pid:
                        self.log_message(f"尝试强制终止浏览器进程 PID: {browser_pid}", always_show=True)
                        try:
                            if platform.system() == "Windows":
                                # os已在文件头部导入，不需要再次导入
                                os.system(f"taskkill /F /PID {browser_pid}")
                                self.log_message(f"已强制终止浏览器进程 PID: {browser_pid}", always_show=True)
                            else:
                                # os已在文件头部导入，不需要再次导入
                                import signal
                                os.kill(browser_pid, signal.SIGKILL)
                                self.log_message(f"已强制终止浏览器进程 PID: {browser_pid}", always_show=True)
                        except Exception as kill_error:
                            self.log_message(f"强制终止浏览器进程失败: {str(kill_error)}", always_show=True)
                
                # 终止可能残留的msedgedriver.exe进程
                try:
                    if platform.system() == "Windows":
                        # os已在文件顶部导入
                        os.system("taskkill /F /IM msedgedriver.exe /T 2>nul")
                        self.log_message("已清理可能残留的msedgedriver.exe进程", level=LogLevel.DETAILED)
                except:
                    pass
            
            except Exception as e:
                self.log_message(f"关闭浏览器时出错: {str(e)}", always_show=True)
            
            finally:
                # 重置driver对象
                self.driver = None
                
                # 删除临时用户数据目录
                if self.user_data_dir and os.path.exists(self.user_data_dir):
                    try:
                        # 确保目录存在
                        if os.path.isdir(self.user_data_dir):
                            # 先等待一小段时间，确保浏览器进程完全退出
                            time.sleep(1)
                            
                            # 尝试删除目录
                            shutil.rmtree(self.user_data_dir, ignore_errors=True)
                            self.log_message(f"已删除临时用户数据目录: {self.user_data_dir}", always_show=True)
                            
                            # 二次检查目录是否仍然存在
                            if os.path.exists(self.user_data_dir):
                                self.log_message(f"临时用户数据目录仍然存在，尝试使用系统命令删除: {self.user_data_dir}", level=LogLevel.DETAILED)
                                try:
                                    if platform.system() == "Windows":
                                        os.system(f'rd /s /q "{self.user_data_dir}"')
                                    else:
                                        os.system(f'rm -rf "{self.user_data_dir}"')
                                except:
                                    pass
                    except Exception as e:
                        self.log_message(f"删除临时用户数据目录时出错: {str(e)}", always_show=True)
                    finally:
                        self.user_data_dir = None
    
    def is_captcha_present(self):
        """检查是否存在验证码"""
        try:
            captcha_elements = [
                "//input[@id='captchacharacters']",  # 验证码输入框
                "//button[contains(text(), 'Continue shopping')]",  # 继续购物按钮
                "//form[contains(@action, '/errors/validateCaptcha')]",  # 验证码表单
                "//div[contains(text(), 'Enter the characters you see below')]",  # 验证码提示文本
                "//div[contains(text(), 'Type the characters you see in this image')]",  # 美国版验证码提示
                "//input[@name='cvf_captcha_input']",  # 另一种验证码输入框
                "//span[contains(text(), 'Enter the characters you see')]",  # 另一种验证码提示
                "//span[contains(text(), 'Type the characters you see')]"  # 另一种美国版验证码提示
            ]

            for element in captcha_elements:
                if self.driver.find_elements(By.XPATH, element):
                    self.log_message("检测到验证码页面", always_show=True)
                    return True
                    
            # 检查URL是否包含验证码相关关键词
            current_url = self.driver.current_url
            captcha_url_indicators = ['captcha', 'validateCaptcha', 'verify', 'puzzle']
            for indicator in captcha_url_indicators:
                if indicator in current_url:
                    self.log_message(f"从URL检测到验证码页面: {current_url}", always_show=True)
                    return True

            return False
        except Exception as e:
            self.log_message(f"检查验证码时出错: {str(e)}")
            return False
    
    def handle_captcha(self):
        """处理验证码"""
        try:
            logger.info("检测到验证码，开始处理...")

            # 优先级1: 检查是否存在 "Click the button below to continue shopping" 文字
            try:
                WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Click the button below to continue shopping')]"))
                )
                self.log_message("检测到 'Click the button below to continue shopping' 提示，尝试点击通用按钮", always_show=True)
                
                # 如果找到该文字，直接尝试点击通用继续按钮
                try:
                    continue_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[@class='a-button-text']"))
                    )
                    continue_button.click()
                    self.log_message("已点击通用继续按钮", always_show=True)
                    time.sleep(3) # 等待页面响应

                    # 再次检查是否还在验证码页面
                    if not self.is_captcha_present():
                        logger.info("通过点击通用按钮成功解决验证码")
                        return True
                    else:
                        logger.warning("点击通用按钮后仍检测到验证码页面，处理失败")
                        return False
                except Exception as e:
                    logger.error(f"点击通用继续按钮失败或按钮不存在: {str(e)}")
                    return False # 无法点击按钮，直接返回失败

            except TimeoutException:
                self.log_message("未检测到 'Click the button below to continue shopping' 提示，继续尝试图片验证码处理", always_show=True)
                # 如果没有该文字，则继续执行原有的图片验证码处理逻辑
                pass # 继续执行下面的代码
            
            # 优先级2: 如果没有特定文字提示，尝试获取验证码图片并进行识别
            captcha_img = None
            img_xpath_patterns = [
                "//form//div/img",  # 标准验证码图片
                "//img[contains(@src, 'captcha')]",  # 包含captcha的img标签
                "//img[contains(@src, 'Captcha')]"   # 包含Captcha的img标签（大写）
            ]
            
            for xpath in img_xpath_patterns:
                try:
                    captcha_img = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    if captcha_img:
                        logger.debug(f"使用 {xpath} 找到验证码图片")
                        break
                except:
                    continue
            
            if not captcha_img:
                logger.error("未找到验证码图片，无法继续处理")
                return False # 即使是尝试图片验证码，如果图片仍未找到，也返回失败
                
            img_url = captcha_img.get_attribute("src")
            
            # 下载验证码图片到临时文件
            self.log_message(f"下载验证码图片: {img_url}", always_show=True)
            temp_dir = tempfile.gettempdir()
            temp_captcha_path = os.path.join(temp_dir, f"amazon_captcha_{int(time.time())}.jpg")
            
            # 最多尝试3次下载
            max_download_attempts = 3
            download_success = False
            
            for attempt in range(max_download_attempts):
                try:
                    # 使用requests下载图片
                    response = requests.get(img_url, stream=True, timeout=10)
                    if response.status_code == 200:
                        with open(temp_captcha_path, 'wb') as f:
                            f.write(response.content)
                        download_success = True
                        break
                    else:
                        self.log_message(f"下载验证码图片失败，状态码: {response.status_code}，尝试 {attempt+1}/{max_download_attempts}", always_show=True)
                        time.sleep(2)  # 等待2秒后重试
                except Exception as e:
                    self.log_message(f"下载验证码图片异常: {str(e)}，尝试 {attempt+1}/{max_download_attempts}", always_show=True)
                    time.sleep(2)  # 等待2秒后重试
            
            if not download_success:
                self.log_message("验证码图片下载多次失败，无法继续处理验证码", always_show=True)
                return False
                
            try:
                # 使用amazoncaptcha解析本地验证码图片
                captcha = amazoncaptcha.AmazonCaptcha(temp_captcha_path)
                captcha_code = captcha.solve()
                
                self.log_message(f"验证码识别结果: {captcha_code}", always_show=True)
                
                # 删除临时文件
                try:
                    os.remove(temp_captcha_path)
                except:
                    pass
                
                # 如果验证码为空或非法，返回失败
                if not captcha_code or len(captcha_code) < 4:
                    self.log_message(f"验证码识别结果无效: {captcha_code}", always_show=True)
                    return False
                
                # 查找验证码输入框
                input_found = False
                input_selectors = [
                    (By.ID, "captchacharacters"),
                    (By.NAME, "cvf_captcha_input"),
                    (By.NAME, "captchacharacters"),
                    (By.CSS_SELECTOR, "input[type='text'][name*='captcha']")
                ]
                
                captcha_input = None
                for by, selector in input_selectors:
                    try:
                        captcha_input = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((by, selector))
                        )
                        input_found = True
                        self.log_message(f"使用选择器 {selector} 找到验证码输入框", always_show=True)
                        break
                    except:
                        continue
                
                if not input_found or not captcha_input:
                    self.log_message("未找到验证码输入框，无法继续处理", always_show=True)
                    return False
                
                # 输入验证码
                captcha_input.clear()
                captcha_input.send_keys(captcha_code)
                self.log_message("已输入验证码", always_show=True)
                
                # 查找提交按钮
                button_found = False
                button_selectors = [
                    (By.XPATH, "//button[contains(text(), 'Continue shopping')]"),
                    (By.XPATH, "//input[@type='submit']"),
                    (By.XPATH, "//button[@type='submit']"),
                    (By.XPATH, "//button[contains(text(), 'Submit')]"),
                    (By.XPATH, "//button[contains(@class, 'submit')]"),
                    (By.XPATH, "//input[@aria-labelledby='captchaSubmit']"),
                    (By.ID, "captchaSubmit")
                ]
                
                for by, selector in button_selectors:
                    try:
                        submit_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((by, selector))
                        )
                        submit_button.click()
                        button_found = True
                        self.log_message(f"使用选择器 {selector} 找到并点击提交按钮", always_show=True)
                        break
                    except:
                        continue
                
                if not button_found:
                    self.log_message("未找到提交按钮，尝试使用回车键提交", always_show=True)
                    try:
                        from selenium.webdriver.common.keys import Keys
                        captcha_input.send_keys(Keys.RETURN)
                    except Exception as e:
                        self.log_message(f"使用回车键提交失败: {str(e)}", always_show=True)
                        return False
                
                # 等待页面加载
                time.sleep(3)
                
                # 验证是否仍在验证码页面
                if self.is_captcha_present():
                    self.log_message("验证码未能正确解决，重试...", always_show=True)
                    return False
                else:
                    self.log_message("验证码已成功解决", always_show=True)
                    return True
            except Exception as e:
                self.log_message(f"处理验证码时出现错误: {str(e)}", always_show=True)
                # 确保删除临时文件
                try:
                    if os.path.exists(temp_captcha_path):
                        os.remove(temp_captcha_path)
                except:
                    pass
                return False
                
        except Exception as e:
            self.log_message(f"处理验证码时出现错误: {str(e)}", always_show=True)
            return False

    def load_asins_from_excel(self, filename="amazon_results_美国.xlsx"):
        """从Excel文件加载ASIN列表"""
        try:
            df = pd.read_excel(filename)
            asins = []
            
            # 检查列名 - 不区分大小写
            columns = [col.lower() for col in df.columns]
            
            if 'asin' in columns:
                # 获取原始列名（保留大小写）
                original_col = df.columns[columns.index('asin')]
                asins = df[original_col].dropna().tolist()
                self.log_message(f"成功从Excel的'{original_col}'列加载了{len(asins)}个ASIN", always_show=True)
            elif 'amazon links' in columns:
                # 获取原始列名（保留大小写）
                original_col = df.columns[columns.index('amazon links')]
                # 从URL中提取ASIN
                links = df[original_col].dropna().tolist()
                for link in links:
                    # 从URL中提取ASIN，格式可能是各种国家的亚马逊链接
                    # 支持美国(amazon.com)、日本(amazon.co.jp)、加拿大(amazon.ca)
                    asin_match = re.search(r'/dp/([A-Z0-9]{10})', link)
                    if asin_match:
                        asins.append(asin_match.group(1))
                    else:
                        # 尝试其他可能的URL格式
                        alt_match = re.search(r'/gp/product/([A-Z0-9]{10})', link)
                        if alt_match:
                            asins.append(alt_match.group(1))
                
                # 注释掉从Excel文件覆盖用户国家设置的代码
                """
                # 如果有"国家"列，记录每个ASIN对应的国家
                if '国家' in df.columns and not df['国家'].empty:
                    # 如果Excel中有国家列，则使用它来设置当前国家
                    country = df['国家'].iloc[0]
                    if country in self.countries:
                        self.country = country
                        self.country_domain = self.countries[country]["domain"]
                        self.country_zipcode = self.countries[country]["zipcode"]
                        self.log_message(f"根据Excel文件设置国家为: {country}, 域名: {self.country_domain}, 邮编: {self.country_zipcode}", always_show=True)
                """
                    
                self.log_message(f"成功从Excel的'{original_col}'列提取了{len(asins)}个ASIN", always_show=True)
            else:
                # 查找任何包含"asin"的列（不区分大小写）
                asin_cols = [col for col in df.columns if 'asin' in col.lower()]
                if asin_cols:
                    # 使用第一个匹配的列
                    asins = df[asin_cols[0]].dropna().tolist()
                    self.log_message(f"成功从Excel的'{asin_cols[0]}'列加载了{len(asins)}个ASIN", always_show=True)
                else:
                    self.log_message(f"Excel文件中未找到包含'asin'或'Amazon Links'的列", always_show=True)
                    return []
            
            # 去除重复的ASIN并转换为大写
            asins = [asin.upper() for asin in asins if isinstance(asin, str)]
            asins = list(dict.fromkeys(asins))  # 保持顺序的去重方法
            
            self.log_message(f"最终加载了{len(asins)}个唯一的ASIN", always_show=True)
            return asins
        except Exception as e:
            self.log_message(f"从Excel文件加载ASIN时出错: {str(e)}", always_show=True)
            return []
    
    def search_brand_on_amazon(self, brand):
        """在Amazon上搜索品牌并确定其类别"""
        if not brand:
            self.log_message("品牌名称为空，无法搜索", always_show=True)
            return None
            
        # 去除特殊字符，只保留字母、数字和空格，以提高搜索准确性
        clean_brand = re.sub(r'[^\w\s]', '', brand).strip()
        if not clean_brand:
            self.log_message(f"清理后的品牌名为空: {brand} -> {clean_brand}，无法搜索", always_show=True)
            return None
            
        self.log_message(f"开始在亚马逊{self.country}站点搜索品牌: {brand}", always_show=True)
        
        # 使用类中定义的常量作为最大搜索重试次数
        for retry in range(self.MAX_SEARCH_ATTEMPTS):
            try:
                # 构建搜索URL
                search_url = f"https://www.amazon.{self.country_domain}/s?k={brand}&language=en_US"
                self.driver.get(search_url)
                
                # 新增：在加载页面后增加随机延迟
                time.sleep(random.uniform(10, 15))
                
                # 处理验证码
                if self.is_captcha_present():
                    if not self.handle_captcha():
                        self.log_message("无法处理验证码，搜索失败", always_show=True)
                        return {"status": "error", "brand": brand, "error": "无法处理验证码"}
                
                # 添加对503错误页面的检查和刷新逻辑
                max_503_refresh_attempts = self.MAX_503_REFRESH_ATTEMPTS # 使用常量
                retry_interval_503 = self.RETRY_INTERVAL_503_SECONDS # 使用常量
                
                for refresh_attempt in range(max_503_refresh_attempts):
                    try:
                        # 检查是否存在 503 错误页面的图片元素
                        error_img_elements = self.driver.find_elements(By.XPATH, "//a[contains(@href, 'cs_503_link')]/img")
                        
                        if error_img_elements:
                            self.log_message(f"检测到503/500错误页面，正在刷新页面 ({refresh_attempt+1}/{max_503_refresh_attempts})", always_show=True)
                            self.driver.refresh()
                            time.sleep(retry_interval_503) # 等待一段时间后重试
                            
                            # 刷新后再次处理可能出现的验证码
                            if self.is_captcha_present():
                                if not self.handle_captcha():
                                    self.log_message("刷新后出现验证码且无法处理，继续尝试刷新或重启", always_show=True)
                                    # 如果验证码无法处理，继续下一次刷新尝试
                                    continue
                        else:
                            # 没有检测到503错误，跳出刷新循环
                            break
                    except Exception as e:
                        self.log_message(f"检查503错误或刷新时出错: {str(e)}", always_show=True)
                        # 如果刷新过程中出现异常，也尝试继续刷新
                        time.sleep(retry_interval_503)

                # 如果达到最大刷新次数后仍然是503页面，则重启浏览器
                error_img_after_refreshes = self.driver.find_elements(By.XPATH, "//a[contains(@href, 'cs_503_link')]/img")
                if error_img_after_refreshes:
                    self.log_message(f"经过{max_503_refresh_attempts}次刷新后仍检测到503/500错误，正在关闭并重新打开浏览器，然后重试 ({retry+1}/{self.MAX_SEARCH_ATTEMPTS})", always_show=True)
                    self.close_selenium_browser() # 关闭当前浏览器
                    time.sleep(2) # 等待浏览器完全关闭
                    
                    # 重新打开浏览器，增加重试机制
                    browser_restarted_after_503 = False
                    for setup_attempt in range(self.MAX_BROWSER_SETUP_ATTEMPTS):
                        self.log_message(f"尝试重新打开浏览器 ({setup_attempt+1}/{self.MAX_BROWSER_SETUP_ATTEMPTS})...", always_show=True)
                        if self.setup_selenium_browser():
                            browser_restarted_after_503 = True
                            self.log_message("浏览器重新打开成功", always_show=True)
                            break
                        else:
                            self.log_message(f"浏览器重新打开失败，等待 {self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS} 秒后重试...", always_show=True)
                            self.close_selenium_browser() # 尝试关闭可能未完全启动的浏览器
                            time.sleep(self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS)

                    if not browser_restarted_after_503:
                        self.log_message("多次尝试重新打开浏览器失败，搜索失败", always_show=True)
                        return {"status": "error", "brand": brand, "error": "重新打开浏览器失败"}
                    
                    # 新增：503错误重启后，更新时间戳，避免与定时重启冲突
                    self.last_browser_restart_time = time.time()

                    # 重新设置位置，因为是新浏览器实例
                    self.log_message(f"在新浏览器中重新设置{self.country}位置...", always_show=True)
                    initial_url = f"https://www.amazon.{self.country_domain}/?language=en_US"
                    self.driver.get(initial_url)
                    time.sleep(3)
                    # 处理可能出现的验证码
                    if self.is_captcha_present():
                        if not self.handle_captcha():
                            self.log_message(f"在503错误后重新打开浏览器时出现验证码且无法处理，搜索品牌 '{brand}' 失败。", always_show=True)
                            return {"status": "error", "brand": brand, "error": "503错误后验证码处理失败", "brands_found": [], "sellers": [], "category": 6}
                    self.check_and_fix_location()
                    
                    time.sleep(5) # 等待新浏览器和位置设置稳定
                    continue # 继续最外层循环，在新浏览器中重新尝试加载搜索URL
                
                # 新增：用于跟踪内容是否成功加载的标志
                page_content_fully_loaded_after_refresh = False

                # 新增：外层循环用于刷新页面并重新尝试加载内容
                # 只有当quick-view元素存在时才进行内部内容加载检查
                elements_present_after_initial_check = False
                elements = [] # Initialize elements list here for scope
                for initial_element_check_attempt in range(2): # 0: initial, 1: after one refresh
                    current_page_source = self.driver.page_source
                    current_soup = BeautifulSoup(current_page_source, 'html.parser')
                    temp_elements = current_soup.find_all('div', id='zyCardWrap') # Use temp_elements to avoid confusion
                    
                    if temp_elements:
                        elements = temp_elements # Assign to main elements variable if found
                        elements_present_after_initial_check = True
                        self.log_message(f"在 {initial_element_check_attempt+1} 次尝试后找到品牌 '{brand}' 的快速视图元素。", level=LogLevel.DETAILED)
                        break
                    elif initial_element_check_attempt == 0:
                        self.log_message(f"首次加载时未找到品牌 '{brand}' 的快速视图元素。尝试刷新页面后再次检查。", always_show=True)
                        self.driver.refresh()
                        time.sleep(self.REFRESH_INTERVAL_SECONDS)
                        # 刷新后处理可能出现的验证码
                        if self.is_captcha_present():
                            if not self.handle_captcha():
                                self.log_message(f"在初始元素检查期间刷新后出现验证码且无法处理，无法获取品牌 '{brand}' 的详细信息。", always_show=True)
                                return {"status": "error", "brand": brand, "error": "验证码处理失败", "brands_found": [], "sellers": [], "category": 6}
                    else: # initial_element_check_attempt == 1 and still no elements
                        self.log_message(f"刷新后仍未找到品牌 '{brand}' 的快速视图元素。判断该品牌没有其他卖家。", always_show=True)
                        return {"status": "success", "brand": brand, "brands_found": [], "sellers": []}

                # 新增：用于跟踪内容是否成功加载的标志
                page_content_fully_loaded_after_refresh = False

                # 新增：外层循环用于刷新页面并重新尝试加载内容
                # 只有当quick-view元素存在时才进行内部内容加载检查
                if elements_present_after_initial_check: # Only proceed if elements were found after initial checks
                    for refresh_attempt_idx in range(self.MAX_REFRESH_ATTEMPTS_FOR_CONTENT):
                        if refresh_attempt_idx > 0:
                            self.log_message(f"内容加载不完整，尝试刷新页面 ({refresh_attempt_idx+1}/{self.MAX_REFRESH_ATTEMPTS_FOR_CONTENT})...", always_show=True)
                            self.driver.refresh()
                            time.sleep(self.REFRESH_INTERVAL_SECONDS)
                            # 刷新后处理可能出现的验证码
                            if self.is_captcha_present():
                                if not self.handle_captcha():
                                    self.log_message("刷新后出现验证码且无法处理，继续尝试刷新", always_show=True)
                                    continue # 跳过当前刷新尝试，进入下一次刷新循环

                        try:
                            # Step 0: Wait for Amazon's native search results container to be present.
                            # This is crucial to ensure the core page content has loaded before expecting extension injected elements.
                            WebDriverWait(self.driver, 20).until( # 增加超时时间以应对初次页面加载
                                EC.presence_of_element_located((By.CSS_SELECTOR, "div.s-search-results"))
                            )
                            self.log_message("确认亚马逊搜索结果主容器已加载", level=LogLevel.DETAILED)

                            # Step 1: Wait for the primary quick-view container to be visible (injected by extension)
                            WebDriverWait(self.driver, 15).until( # 给扩展注入元素的时间，比初次页面加载短
                                EC.visibility_of_element_located((By.ID, "zyCardWrap")) # 修改为新的ID选择器
                            )
                            self.log_message(f"找到并等待主 quick-view 元素可见", level=LogLevel.DETAILED)

                            # 定义内容加载验证的常量 (这里可以重复定义，也可以作为类属性)
                            MAX_CONTENT_CHECK_ATTEMPTS = 10
                            CONTENT_CHECK_INTERVAL_SECONDS = 2

                            # 步骤2: 循环检查quick-view元素内部内容是否加载完成
                            content_fully_loaded = False
                            for check_attempt in range(MAX_CONTENT_CHECK_ATTEMPTS):
                                current_page_source = self.driver.page_source
                                current_soup = BeautifulSoup(current_page_source, 'html.parser')
                                all_quick_view_elements = current_soup.find_all('div', id='zyCardWrap') # 修改为新的ID选择器

                                if not all_quick_view_elements: # 如果在本次迭代中没有找到quick-view元素，可能尚未注入，继续等待
                                    self.log_message(f"尝试 {check_attempt+1}/{MAX_CONTENT_CHECK_ATTEMPTS}: 未找到 quick-view 元素，等待重试...", level=LogLevel.DETAILED)
                                    time.sleep(CONTENT_CHECK_INTERVAL_SECONDS)
                                    continue

                                all_elements_populated = True
                                for qv_elem in all_quick_view_elements[:20]: # 只检查前20个元素
                                    # 尝试提取ASIN
                                    asin_value_elem = qv_elem.find('span', id='zying-nodeitem-asin') # 新的ASIN元素ID
                                    asin_text = asin_value_elem.text.strip() if asin_value_elem else ''

                                    # 尝试提取品牌
                                    brand_title_elem = qv_elem.find('span', class_='color-text-label', string=re.compile(r'品牌[：:]')) # 新的品牌标签和中文冒号，使用正则匹配
                                    brand_text_check = '' # 默认值为空字符串

                                    if brand_title_elem:
                                        brand_value_elem = brand_title_elem.find_next_sibling('a') # 品牌值在a标签中
                                        if brand_value_elem and brand_value_elem.text.strip():
                                            brand_text_check = brand_value_elem.text.strip().lower().replace(' ', '')
                                        else:
                                            # 品牌标题元素存在，但品牌值为空或不存在
                                            brand_text_check = 'empty_value' # 明确标记为品牌值为空
                                    else:
                                        # 没有找到品牌标题元素
                                        brand_text_check = 'no' # 明确标记为没有品牌元素

                                    # 尝试提取卖家
                                    seller_value_elem = qv_elem.find('a', class_='zying-tag-ship', string=re.compile(r'卖家:')) # 卖家值在a标签中，包含"卖家:"
                                    seller_text_check = seller_value_elem.text.strip().lower().replace(' ', '') if seller_value_elem else ''

                                    # 新增：检查"近30天销量"元素是否存在
                                    sales_30_days_elem = qv_elem.find('span', class_='color-text-label', string='近30天销量：')

                                    # 检查"近30天销量"元素是否加载完成
                                    if not sales_30_days_elem:
                                        all_elements_populated = False
                                        break # 发现有未加载完整的元素，跳出当前quick-view循环
                                
                                if all_elements_populated:
                                    # 如果检查的前20个元素（或所有元素，如果不足20个）都已加载完整，则认为内容已就绪
                                    content_fully_loaded = True
                                    self.log_message(f"内容加载完成，前 {min(len(all_quick_view_elements), 20)} 个 quick-view 元素的数据已就绪。", level=LogLevel.DETAILED)
                                    break # 所有内容都已加载，跳出重试循环
                                else:
                                    self.log_message(f"尝试 {check_attempt+1}/{MAX_CONTENT_CHECK_ATTEMPTS}: 部分 quick-view 元素内容未加载完成，等待重试...", level=LogLevel.DETAILED)
                                    time.sleep(CONTENT_CHECK_INTERVAL_SECONDS)
                            
                            # 如果内层内容检查循环成功，则设置标志并跳出外层刷新循环
                            if content_fully_loaded:
                                page_content_fully_loaded_after_refresh = True
                                break # 跳出刷新循环，因为内容已成功加载

                        except TimeoutException:
                            self.log_message(f"在刷新尝试 {refresh_attempt_idx+1}/{self.MAX_REFRESH_ATTEMPTS_FOR_CONTENT} 中，等待 quick-view 元素超时。将尝试刷新页面...", always_show=True)
                            # 这里继续下一次刷新尝试
                            continue # 进入外层 for 循环的下一次迭代，执行 driver.refresh()
                        except Exception as e:
                            self.log_message(f"在等待或检查 quick-view 内容时发生错误 during refresh attempt {refresh_attempt_idx+1}: {str(e)}", always_show=True)
                            self.log_message("将尝试刷新页面...", always_show=True)
                            continue # 进入外层 for 循环的下一次迭代，执行 driver.refresh()
                    
                    # 在所有刷新尝试结束后，检查内容是否最终加载成功
                    if not page_content_fully_loaded_after_refresh:
                        self.log_message("警告: 达到最大页面刷新尝试次数，quick-view 元素内容可能仍未完全加载。将使用当前可用数据。", always_show=True)
                else: # If quick-view elements were NOT initially found after the one refresh.
                    # This path should ideally not be reached as the initial check returns early.
                    # But as a safety, or if the initial check leads to this branch unexpectedly,
                    # it means we really found no elements.
                    self.log_message(f"No quick-view elements were present after initial checks for brand '{brand}'. Concluding no other sellers.", always_show=True)
                    return {"status": "success", "brand": brand, "brands_found": [], "sellers": []}

                # Removed original MAX_EMPTY_ELEMENT_RETRIES block (lines 431-460)

                # 重新解析页面以获取最终的quick-view元素，确保它是最新的
                page_source = self.driver.page_source
                soup = BeautifulSoup(page_source, 'html.parser')
                elements = soup.find_all('div', id='zyCardWrap')
                
                # 如果所有检查后仍然没有找到quick-view元素，则返回无卖家信息
                if not elements:
                    self.log_message(f"最终检查：quick-view 元素在所有内容加载尝试后仍然未找到，判断品牌'{brand}'没有其他卖家。", always_show=True)
                    return {"status": "success", "brand": brand, "brands_found": [], "sellers": []}

                # 初始化品牌数列和卖家数列
                brands = []
                sellers = []
                asins = []
                login_required_brand_count = 0  # 用于计算需要登录查看的品牌数量
                login_required_seller_count = 0  # 用于计算需要登录查看的卖家数量
                empty_brand_count = 0  # 用于计算品牌信息为空的数量
                
                # 遍历元素获取品牌和卖家文本内容
                for element in elements:
                    # 获取ASIN
                    asin_value_elem = element.find('span', id='zying-nodeitem-asin') # 新的ASIN元素ID
                    asin = asin_value_elem.text.strip() if asin_value_elem else 'no'
                    asins.append(asin)
                    
                    # 获取品牌文本内容 - 改进的品牌提取逻辑
                    brand_text = 'no' # 默认值，表示没有品牌元素
                    try:
                        # 首先尝试查找品牌标签和对应的值
                        brand_title_elem = element.find('span', class_='color-text-label', string=re.compile(r'品牌[：:]'))
                        
                        if brand_title_elem:
                            # 尝试多种可能的品牌值位置
                            brand_value_elem = brand_title_elem.find_next_sibling('a')
                            if not brand_value_elem:
                                # 如果a标签不是直接的兄弟，可能在父元素的下一个div中
                                parent_div = brand_title_elem.parent
                                if parent_div:
                                    brand_value_elem = parent_div.find('a')
                            
                            if brand_value_elem and brand_value_elem.text.strip():
                                brand_text = brand_value_elem.text.strip().lower().replace(' ', '')
                            else:
                                # 品牌标题元素存在，但品牌值为空或不存在
                                brand_text = 'empty_value' # 明确标记为品牌值为空
                    except Exception as e:
                        self.log_message(f"提取品牌时出错: {str(e)}", level=LogLevel.DETAILED)
                        brand_text = 'error' # 标记为提取错误
                        
                    brands.append(brand_text)
                    
                    # 检查品牌信息是否为空
                    if brand_text == 'empty_value':
                        self.log_message(f"ASIN {asin}: 品牌信息为空（值为空）", level=LogLevel.DETAILED)
                        empty_brand_count += 1
                    elif brand_text == 'no':
                        self.log_message(f"ASIN {asin}: 没有品牌元素", level=LogLevel.DETAILED)
                    elif brand_text == 'error':
                        self.log_message(f"ASIN {asin}: 提取品牌信息时出错", level=LogLevel.DETAILED)
                    
                    # 检查品牌是否需要登录查看 - 改进的登录提示检测
                    login_indicator = element.find('span', string='登录', class_='_1wyo2vd1h')
                    if not login_indicator:
                        # 尝试其他可能的登录提示
                        login_indicator = element.find(string=re.compile(r'登录'))
                        
                    if brand_text == 'empty_value' and login_indicator:
                        login_required_brand_count += 1
                    
                    # 获取卖家文本内容 - 改进的卖家提取逻辑
                    seller = 'no'
                    seller_count = 0
                    
                    try:
                        # 尝试多种可能的卖家元素定位方式
                        # 1. 首先尝试直接找到卖家标签和值
                        seller_label = element.find('span', class_='color-text-label', string=re.compile(r'卖家[：:]'))
                        if seller_label:
                            seller_value_elem = seller_label.find_next_sibling('a')
                            if seller_value_elem and seller_value_elem.text.strip():
                                seller = seller_value_elem.text.strip().lower().replace(' ', '')
                        
                        # 2. 尝试查找带有"卖家:"文本的zying-tag-ship类的元素
                        if seller == 'no':
                            seller_tag = element.find('a', class_='zying-tag-ship', string=re.compile(r'卖家:'))
                            if seller_tag:
                                seller_text = seller_tag.text.strip()
                                # 提取卖家数量
                                seller_count_match = re.search(r'卖家:(\d+)', seller_text)
                                if seller_count_match:
                                    try:
                                        seller_count = int(seller_count_match.group(1))
                                        self.log_message(f"ASIN {asin}: 找到 {seller_count} 个卖家", level=LogLevel.DETAILED)
                                    except ValueError:
                                        pass
                        
                        # 3. 尝试找到任何可能的卖家元素
                        if seller == 'no':
                            # 找到所有的a标签，然后查找可能的卖家信息
                            all_links = element.find_all('a')
                            for link in all_links:
                                if link.has_attr('href') and '/sp?seller=' in link.get('href', ''):
                                    seller = link.text.strip().lower().replace(' ', '')
                                    break
                    except Exception as e:
                        self.log_message(f"提取卖家时出错: {str(e)}", level=LogLevel.DETAILED)
                    
                    # 移除卖家ID格式检查代码，将在classify_brand_results方法中对匹配品牌的卖家进行检查
                    
                    sellers.append(seller)
                    
                    # 检查卖家是否需要登录查看
                    if seller == 'no' and login_indicator:
                        login_required_seller_count += 1
                
                # 计算总的需要登录查看的数量
                total_login_required = login_required_brand_count # 仅根据品牌登录查看数量判断
                
                # 检查需要登录查看的元素数量
                if total_login_required >= 5:
                    self.log_message(f"发现需要登录后查看的品牌数量:{login_required_brand_count}，卖家数量:{login_required_seller_count}，总计:{total_login_required}，尝试等待10秒后重新获取", always_show=True)
                    
                    # 计数当前页面重载次数
                    page_reload_count = 0
                    max_page_reloads = 3
                    
                    while total_login_required >= 5 and page_reload_count < max_page_reloads:
                        # 等待后重试
                        time.sleep(retry_interval_503)
                        
                        # 解析页面并重新检查
                        page_source = self.driver.page_source
                        soup = BeautifulSoup(page_source, 'html.parser')
                        elements = soup.find_all('div', id='zyCardWrap') # 修改为新的ID选择器
                        
                        # 重置计数
                        login_required_brand_count = 0
                        login_required_seller_count = 0
                        empty_brand_count = 0  # 重置空品牌计数
                        brands = []
                        sellers = []
                        asins = []
                        
                        # 重新遍历元素
                        for element in elements:
                            # 获取ASIN
                            asin_value_elem = element.find('span', id='zying-nodeitem-asin') # 新的ASIN元素ID
                            asin = asin_value_elem.text.strip() if asin_value_elem else 'no'
                            asins.append(asin)
                            
                            # 获取品牌文本内容 - 改进的品牌提取逻辑
                            brand_text = 'no' # 默认值，表示没有品牌元素
                            try:
                                # 首先尝试查找品牌标签和对应的值
                                brand_title_elem = element.find('span', class_='color-text-label', string=re.compile(r'品牌[：:]'))
                                
                                if brand_title_elem:
                                    # 尝试多种可能的品牌值位置
                                    brand_value_elem = brand_title_elem.find_next_sibling('a')
                                    if not brand_value_elem:
                                        # 如果a标签不是直接的兄弟，可能在父元素的下一个div中
                                        parent_div = brand_title_elem.parent
                                        if parent_div:
                                            brand_value_elem = parent_div.find('a')
                                    
                                    if brand_value_elem and brand_value_elem.text.strip():
                                        brand_text = brand_value_elem.text.strip().lower().replace(' ', '')
                                    else:
                                        # 品牌标题元素存在，但品牌值为空或不存在
                                        brand_text = 'empty_value' # 明确标记为品牌值为空
                            except Exception as e:
                                self.log_message(f"提取品牌时出错: {str(e)}", level=LogLevel.DETAILED)
                                brand_text = 'error' # 标记为提取错误
                                
                            brands.append(brand_text)
                            
                            # 检查品牌信息是否为空
                            if brand_text == 'empty_value':
                                self.log_message(f"ASIN {asin}: 品牌信息为空（值为空）", level=LogLevel.DETAILED)
                                empty_brand_count += 1
                            elif brand_text == 'no':
                                self.log_message(f"ASIN {asin}: 没有品牌元素", level=LogLevel.DETAILED)
                            elif brand_text == 'error':
                                self.log_message(f"ASIN {asin}: 提取品牌信息时出错", level=LogLevel.DETAILED)
                            
                            # 检查品牌是否需要登录查看 - 改进的登录提示检测
                            login_indicator = element.find('span', string='登录', class_='_1wyo2vd1h')
                            if not login_indicator:
                                # 尝试其他可能的登录提示
                                login_indicator = element.find(string=re.compile(r'登录'))
                                
                            if brand_text == 'empty_value' and login_indicator:
                                login_required_brand_count += 1
                            
                            # 获取卖家文本内容 - 改进的卖家提取逻辑
                            seller = 'no'
                            seller_count = 0
                            
                            try:
                                # 尝试多种可能的卖家元素定位方式
                                # 1. 首先尝试直接找到卖家标签和值
                                seller_label = element.find('span', class_='color-text-label', string=re.compile(r'卖家[：:]'))
                                if seller_label:
                                    seller_value_elem = seller_label.find_next_sibling('a')
                                    if seller_value_elem and seller_value_elem.text.strip():
                                        seller = seller_value_elem.text.strip().lower().replace(' ', '')
                                        self.log_message(f"ASIN {asin}: 通过方法1找到卖家 '{seller}'", level=LogLevel.DETAILED)
                                
                                # 2. 尝试查找带有"卖家:"文本的元素，使用部分类名匹配
                                if seller == 'no':
                                    # 先查找所有a标签
                                    all_a_tags = element.find_all('a')
                                    for a_tag in all_a_tags:
                                        # 检查class属性是否存在，并且包含zying-tag-ship，并且文本包含"卖家:"
                                        if a_tag.has_attr('class') and 'zying-tag-ship' in a_tag.get('class', '') and re.search(r'卖家[:：]', a_tag.text):
                                            seller_text = a_tag.text.strip()
                                            # 提取卖家数量
                                            seller_count_match = re.search(r'卖家[:：](\d+)', seller_text)
                                            if seller_count_match:
                                                try:
                                                    seller_count = int(seller_count_match.group(1))
                                                    self.log_message(f"ASIN {asin}: 找到卖家数量 {seller_count}", level=LogLevel.DETAILED)
                                                except ValueError:
                                                    pass
                                            break
                                
                                # 3. 尝试找到任何可能的卖家元素
                                if seller == 'no':
                                    # 找到所有的a标签，然后查找可能的卖家信息
                                    all_links = element.find_all('a')
                                    for link in all_links:
                                        if link.has_attr('href') and '/sp?seller=' in link.get('href', ''):
                                            seller = link.text.strip().lower().replace(' ', '')
                                            self.log_message(f"ASIN {asin}: 通过href属性找到卖家 '{seller}'", level=LogLevel.DETAILED)
                                            break
                            except Exception as e:
                                self.log_message(f"提取卖家时出错: {str(e)}", level=LogLevel.DETAILED)
                            
                            # 移除卖家ID格式检查代码，将在classify_brand_results方法中对匹配品牌的卖家进行检查
                            
                            sellers.append(seller)
                            
                            # 检查卖家是否需要登录查看
                            if seller == 'no' and login_indicator:
                                login_required_seller_count += 1
                        
                        # 更新总需要登录查看的数量
                        total_login_required = login_required_brand_count # 仅根据品牌登录查看数量判断
                        
                        # 如果仍然需要登录，尝试重新加载页面
                        if total_login_required >= 5:
                            page_reload_count += 1
                            if page_reload_count < max_page_reloads:
                                self.log_message(f"第{page_reload_count}次页面刷新尝试，仍有品牌:{login_required_brand_count}个、卖家:{login_required_seller_count}个需要登录查看，总计:{total_login_required}个", always_show=True)
                                self.driver.refresh()
                                time.sleep(5)  # 等待页面加载
                                
                                # 处理验证码
                                if self.is_captcha_present():
                                    if not self.handle_captcha():
                                        self.log_message("重新加载页面后出现验证码，无法处理", always_show=True)
                                        continue
                            else:
                                self.log_message(f"经过{max_page_reloads}次页面刷新，仍有品牌:{login_required_brand_count}个、卖家:{login_required_seller_count}个需要登录查看，总计:{total_login_required}个，保存到超时表格", always_show=True)
                                self.save_timeout_asin(brand, brand)
                                return {"status": "error", "brand": brand, "error": "需要登录后查看内容"}
                        else:
                            self.log_message(f"经过{page_reload_count+1}次尝试，登录查看的元素已减少到品牌:{login_required_brand_count}个、卖家:{login_required_seller_count}个，总计:{total_login_required}个", always_show=True)
                            break

                # 如果存在品牌信息为空的情况，记录下来
                has_empty_brands = empty_brand_count > 0
                if has_empty_brands:
                    self.log_message(f"发现{empty_brand_count}个品牌信息为空的产品", always_show=True)

                # 确定品牌类型和是否符合条件
                # 默认为无品牌类型，符合条件
                brand_type = "no_brand"
                is_compliant = True
                
                # 这里可以根据实际业务逻辑设置brand_type和is_compliant
                # 例如，根据品牌名或其他条件判断
                if any("visit the" in b.lower() for b in brands if b != "no"):
                    brand_type = "visit_the"
                elif any(b != "no" for b in brands):
                    brand_type = "brand"
                
                # 返回提取的数据
                return {
                    "status": "success",
                    "brand": brand,
                    "brands_found": brands,
                    "sellers": sellers,
                    "has_empty_brands": has_empty_brands,  # 添加空品牌标志
                    "empty_brand_count": empty_brand_count,  # 添加空品牌数量
                    "brand_type": brand_type,
                    "is_compliant": is_compliant,
                    "seller_count": len(sellers),  # 总卖家数
                    "unique_seller_count": len(set([s for s in sellers if s != 'no'])),  # 唯一有效卖家数
                    "unique_sellers": list(set([s for s in sellers if s != 'no']))  # 唯一卖家列表
                }
                
            except Exception as e:
                self.log_message(f"搜索品牌时出错: {str(e)}", always_show=True)
                if retry < self.MAX_SEARCH_ATTEMPTS - 1:
                    self.log_message(f"第{retry + 1}次尝试失败，{retry_interval_503}秒后重试", always_show=True)
                    time.sleep(retry_interval_503)
                else:
                    self.log_message(f"第{self.MAX_SEARCH_ATTEMPTS}次尝试失败，保存到超时表格", always_show=True)
                    self.save_timeout_asin(brand, brand)
                    return {"status": "error", "brand": brand, "error": str(e)}
        
        return {"status": "error", "brand": brand, "error": "达到最大重试次数"}
    
    def get_brand(self, asin):
        """获取指定ASIN的品牌信息"""
        try:
            product_url = f"https://www.amazon.{self.country_domain}/dp/{asin}?language=en_US"
            self.log_message(f"获取品牌信息: {product_url}")
            
            # 访问产品页面
            self.driver.get(product_url)
            time.sleep(random.uniform(1, 2))  # 随机等待时间
            
            # 处理验证码
            if self.is_captcha_present():
                if not self.handle_captcha():
                    self.log_message(f"无法处理验证码: {asin}")
                    return None, None, 0
            
            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "productTitle"))
                )
            except TimeoutException:
                self.log_message(f"页面加载超时: {asin}")
                return None, None, 0
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            brand_type = None
            brand = None
            
            # 提取品牌信息
            brand_element = soup.find('a', id='bylineInfo')
            if brand_element:
                brand_text = brand_element.text.strip()
                if "Visit the" in brand_text:
                    brand = brand_text.replace("Visit the", "").replace("Store", "").strip()
                    brand_type = "Visit the"
                else:
                    # 处理多种品牌前缀：英文的"Brand:"和中文/日文的"品牌："
                    brand = brand_text.replace("Brand:", "").replace("品牌：", "").strip()
                    brand_type = "Brand:"
            
            # 获取卖家数量信息
            seller_count = 0
            seller_info = soup.find('div', {'id': 'olp_feature_div'}) or soup.find('div', {'id': 'mbc'})
            if seller_info:
                seller_text = seller_info.text
                # 匹配卖家数量
                seller_match = re.search(r'(\d+)\s+(?:new|used)(?:\s+from)', seller_text)
                if seller_match:
                    seller_count = int(seller_match.group(1))
                    self.log_message(f"ASIN {asin}: 找到 {seller_count} 个卖家", level=LogLevel.DETAILED)
                else:
                    # 可能只有一个卖家
                    if '1 new' in seller_text.lower() or 'new from' in seller_text.lower():
                        seller_count = 1
                        self.log_message(f"ASIN {asin}: 找到 1 个卖家", level=LogLevel.DETAILED)
            
            self.log_message(f"ASIN {asin} 品牌类型: {brand_type}, 品牌: {brand}, 卖家数: {seller_count}", level=LogLevel.DETAILED)
            return brand_type, brand, seller_count
            
        except Exception as e:
            self.log_message(f"获取品牌信息时出错: {str(e)}", level=LogLevel.NORMAL)
            return None, None, 0
    
    def check_and_fix_location(self):
        """检查并修复位置设置，直接在当前页面进行操作"""
        try:
            # 获取当前位置文本
            current_location = "" # Initialize to empty string
            try:
                # 使用 WebDriverWait 确保元素存在后再获取其文本
                location_element = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "glow-ingress-line2"))
                )
                current_location = location_element.text.strip()
                self.log_message(f"当前位置: {current_location}", always_show=True)
            except TimeoutException:
                self.log_message("获取位置信息超时，将尝试设置位置", always_show=True)
                # current_location remains ""
            except Exception as e:
                self.log_message(f"获取位置信息出错: {str(e)}，将尝试设置位置", always_show=True)
                # current_location remains ""
            
            # 根据不同国家检查位置是否正确
            if self.country == "美国":
                if "98032" in current_location or "Kent 98032" in current_location:
                    self.log_message(f"当前位置已经是正确的美国位置: {current_location}", always_show=True)
                    return True
                else:
                    self.log_message(f"当前美国位置不正确，需要重新设置", always_show=True)
                    self.set_us_location(stay_on_page=True)
            elif self.country == "日本":
                if "060-8588" in current_location:
                    self.log_message(f"当前位置已经是正确的日本位置: {current_location}", always_show=True)
                    return True
                else:
                    self.log_message(f"当前日本位置不正确，需要重新设置", always_show=True)
                    self.set_japan_location(stay_on_page=True)
            elif self.country == "加拿大":
                if "B3H" in current_location and "0A" in current_location:
                    self.log_message(f"当前位置已经是正确的加拿大位置: {current_location}", always_show=True)
                    return True
                else:
                    self.log_message(f"当前加拿大位置不正确，需要重新设置", always_show=True)
                    self.set_canada_location(stay_on_page=True)
            else:
                self.log_message(f"未知国家: {self.country}，使用默认的美国位置", always_show=True)
                self.set_us_location(stay_on_page=True)
                
            return True
        except Exception as e:
            self.log_message(f"检查和修复位置时出错: {str(e)}", always_show=True)
            return False
    
    def get_product_info(self, asin):
        """获取Amazon产品详细信息"""
        result = {
            "asin": asin,
            "brand": None,
            "brand_type": None,
            "title": None,
            "has_variants": False,
            "is_out_of_stock": False,
            "is_unique_brand": False,
            "image_url": None,
            "dimensions": None,
            "weight": None,
            "rating": None,
            "rating_count": None,
            "sales_rank": [],
            "timeout": False,
            "seller_count": 0,  # 添加卖家数量字段
            "reason": None      # 保存不符合条件的原因
        }
        
        try:
            product_url = f"https://www.amazon.{self.country_domain}/dp/{asin}?language=en_US"
            self.driver.get(product_url)
            time.sleep(random.uniform(1, 2))  # 随机等待时间
            
            # 处理验证码
            if self.is_captcha_present():
                if not self.handle_captcha():
                    self.log_message(f"无法处理验证码: {asin}")
                    result["timeout"] = True
                    result["reason"] = "无法处理验证码"
                    self.save_timeout_asin(asin, None)
                    return result
            
            # 检查商品是否存在（检测错误页面）
            try:
                not_found_elem = self.driver.find_element(By.ID, "g")
                if not_found_elem:
                    self.log_message(f"ASIN {asin}: 商品不存在（检测到错误页面）", always_show=True)
                    result["reason"] = "商品不存在"
                    # 不将此ASIN记录为超时，因为它确实是不存在的
                    return result
            except Exception:
                # 元素不存在，说明不是错误页面，可以继续处理
                pass
                
            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.ID, "productTitle"))
                )
            except TimeoutException:
                self.log_message(f"页面加载超时，跳过ASIN: {asin}", always_show=True)
                result["timeout"] = True
                result["reason"] = "页面加载超时"
                self.save_timeout_asin(asin, "")
                self.save_result_realtime(result, is_qualified=False)
                return result
                
            # 检查商品不存在页面 - 方法1：检查ID为g的元素
            if self.driver.find_elements(By.ID, "g"):
                self.log_message(f"ASIN {asin}: 商品不存在(方法1)，跳过", always_show=True)
                result["reason"] = "商品不存在"
                self.save_result_realtime(result, is_qualified=False)
                return result
                
            # 检查商品不存在页面 - 方法2：检查带有猫图片的错误消息
            try:
                kitty_images = self.driver.find_elements(By.XPATH, "//img[contains(@src, 'kailey-kitty')]")
                if kitty_images:
                    if "在寻找某商品" in self.driver.page_source or "Looking for something" in self.driver.page_source:
                        self.log_message(f"ASIN {asin}: 商品不存在(方法2)，跳过", always_show=True)
                        result["reason"] = "商品不存在"
                        self.save_result_realtime(result, is_qualified=False)
                        return result
            except Exception:
                # 如果检查失败，继续处理
                pass
                
            # 每2个ASIN检查一次位置设置，增加检查频率
            if random.randint(1, 2) == 1:
                self.log_message(f"在产品页面上检查{self.country}位置设置", always_show=True)
                # 直接检查和修复位置，不需要跳转到主页
                self.check_and_fix_location()
            
            # 检查是否需要登录
            try:
                login_elements = self.driver.find_elements(By.XPATH, "//span[contains(@class, '_1wyo2vd6') and contains(text(), '登录')]")
                if login_elements:
                    self.log_message("检测到登录提示，尝试登录", always_show=True)
                    # 使用现有的登录处理函数
                    self._handle_login_popup()
            except Exception as e:
                self.log_message(f"处理登录时出错: {str(e)}", always_show=True)
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 提取产品标题
            title_elem = soup.select_one("#productTitle")
            if title_elem:
                result["title"] = title_elem.text.strip()
            
            # 提取品牌和品牌类型
            brand_element = soup.find('a', id='bylineInfo')
            if not brand_element:
                self.log_message(f"ASIN {asin}: 无法找到品牌元素，跳过", always_show=True)
                result["reason"] = "无法找到品牌元素"
                self.save_result_realtime(result, is_qualified=False)
                return result
                
            brand_text = brand_element.text.strip()
            if "Visit the" in brand_text:
                result["brand"] = brand_text.replace("Visit the", "").replace("Store", "").strip()
                result["brand_type"] = "Visit the"
                self.visit_the_brand_asins.append({
                    "asin": asin,
                    "brand": result["brand"],
                    "title": result["title"]
                })
            else:
                # 处理多种品牌前缀：英文的"Brand:"和中文/日文的"品牌："
                result["brand"] = brand_text.replace("Brand:", "").replace("品牌：", "").strip()
                result["brand_type"] = "Brand:"
                self.other_brand_asins.append({
                    "asin": asin,
                    "brand": result["brand"],
                    "title": result["title"]
                })
            
            self.log_message(f"找到品牌: {result['brand']} (类型: {result['brand_type']})", always_show=True)
            
            # --- 新增：从zyCardWrap提取卖家信息 ---
            zy_card_wrap = soup.find('div', id='zyCardWrap')
            zy_seller_name = None
            if zy_card_wrap:
                # 尝试查找卖家信息，可能是a标签，也可能是span标签
                seller_span = zy_card_wrap.find('span', class_='_1wyo2vd18 _1wyo2vdw', string='卖家：')
                if seller_span:
                    next_sibling = seller_span.find_next_sibling()
                    if next_sibling and next_sibling.name == 'a':
                        zy_seller_name = next_sibling.text.strip()
                    elif next_sibling and next_sibling.name == 'span':
                        # 如果卖家是span，尝试获取其文本，例如"Amazon.com"
                        zy_seller_name = next_sibling.text.strip()
                
                # 兼容另一种可能的卖家显示方式，例如"卖家:1"这种，但我们需要的是卖家名称
                # 如果没有从上面的方法获取到，则尝试更宽泛的匹配
                if not zy_seller_name:
                    seller_link_in_zy = zy_card_wrap.find('a', href=re.compile(r'/sp\?seller='))
                    if seller_link_in_zy:
                        zy_seller_name = seller_link_in_zy.text.strip()
                
                # 检查zyCardWrap中是否缺少品牌元素
                has_brand_element = False
                
                # 方法1：先检查是否有"品牌："或"Brand:"的span标签
                brand_label_spans = zy_card_wrap.find_all('span', string=lambda s: s and ('Brand:' in s or '品牌：' in s) if s else False)
                
                # 方法2：检查是否有带有"品牌："文本的span后面紧跟着a标签（更符合实际HTML结构）
                for span in zy_card_wrap.find_all('span', class_='_1wyo2vd18 _1wyo2vdw color-text-label'):
                    if span.string and ('Brand:' in span.string or '品牌：' in span.string):
                        next_sibling = span.find_next_sibling()
                        if next_sibling and next_sibling.name == 'a':
                            # 找到了品牌名称
                            has_brand_element = True
                            break
                
                # 如果上面的方法都没找到，或者结果中已有品牌，则认为有品牌
                if brand_label_spans or has_brand_element or result.get('brand'):
                    has_brand_element = True
                    self.consecutive_no_brand_in_zycard = 0  # 重置计数器
                else:
                    self.consecutive_no_brand_in_zycard += 1
                    self.log_message(f"发现zyCardWrap但无品牌元素，连续次数：{self.consecutive_no_brand_in_zycard}", level=LogLevel.NORMAL)
                    
                    # 如果连续2次发现此情况，重启浏览器
                    if self.consecutive_no_brand_in_zycard >= 2:
                        self.log_message("连续2次发现zyCardWrap但无品牌元素，重启浏览器", always_show=True)
                        self.consecutive_no_brand_in_zycard = 0  # 重置计数器
                        self.close_selenium_browser()
                        time.sleep(2)
                        self.setup_selenium_browser()
                        self.log_message("浏览器已重启", always_show=True)
            else:
                # 如果页面上没有zyCardWrap元素，不计数
                pass
            
            # 检查是否断货
            out_of_stock_elem = soup.find('div', id='outOfStock')
            if out_of_stock_elem and "Currently unavailable" in out_of_stock_elem.text:
                self.log_message(f"ASIN {asin}: 产品断货", always_show=True)
                result["is_out_of_stock"] = True
                
                # 检查断货产品是否有变体
                variant_elements = soup.find_all('div', id=lambda x: x and x.startswith('dimension-slot-info-'))
                if len(variant_elements) > 1:
                    self.log_message(f"ASIN {asin}: 断货产品有多个变体，归类为不符合条件", always_show=True)
                    result["has_variants"] = True
                    result["reason"] = "断货产品有多个变体"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 提取其他额外信息
                self.extract_additional_info(soup, result)
                
                # 断货状态下，检查评论数是否符合条件
                if result["rating_count"] is None or result["rating_count"] < 20 or result["rating_count"] > 2000:
                    self.log_message(f"ASIN {asin}: 断货产品评论数 {result['rating_count']} 不在要求范围内(20-2000)，归类为不符合条件", always_show=True)
                    result["reason"] = f"断货产品评论数 {result['rating_count']} 不在要求范围内"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 符合条件，搜索并判断品牌
                self.log_message(f"ASIN {asin}: 断货产品评论数符合条件，开始搜索判断品牌", always_show=True)
                
                # 调用analyze_brand_on_amazon搜索品牌
                brand_search_result = self.analyze_brand_on_amazon(result["brand"], result["brand_type"])
                if brand_search_result and "category" in brand_search_result:
                    result["brand_category"] = brand_search_result["category"]
                    result["search_result"] = brand_search_result
                    self.log_message(f"ASIN {asin}: 断货产品品牌分类结果: {result['brand_category']}", always_show=True)
                    
                    # 添加判断，如果分类为0（未找到匹配品牌），设置is_unique_brand为True，避免后续再进行品牌唯一性检查
                    if result["brand_category"] == 0:
                        result["is_unique_brand"] = True
                        self.log_message(f"ASIN {asin}: 未找到匹配品牌，自动设置为唯一品牌", always_show=True)
                    
                    self.save_result_realtime(result, is_qualified=True)
                    return result
                else:
                    self.log_message(f"ASIN {asin}: 断货产品品牌搜索失败", always_show=True)
                    result["reason"] = "品牌搜索失败"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
            else:
                self.log_message(f"ASIN {asin}: 产品有货", always_show=True)
                result["is_out_of_stock"] = False
                
                # 检查是否有变体
                variant_elements = soup.find_all('div', id=lambda x: x and x.startswith('dimension-slot-info-'))
                if len(variant_elements) > 1:
                    self.log_message(f"ASIN {asin}: 有货产品有多个变体，归类为不符合条件", always_show=True)
                    result["has_variants"] = True
                    result["reason"] = "有货产品有多个变体"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 检查卖家是否与品牌匹配
                seller_name = None
                # 优先使用从zyCardWrap提取的卖家名称
                if zy_seller_name:
                    seller_name = zy_seller_name
                    self.log_message(f"ASIN {asin}: 从zyCardWrap获取到卖家: {seller_name}", always_show=True)
                else:
                    # 否则，尝试从标准Amazon元素获取
                    seller_element = None
                    for elem in soup.find_all("span", id="sellerProfileTriggerId"):
                        seller_element = elem
                        break
                    if seller_element:
                        seller_name = seller_element.text.strip()
                        self.log_message(f"ASIN {asin}: 从标准元素获取到卖家: {seller_name}", always_show=True)

                if seller_name:
                    brand_name = result["brand"].lower()
                    
                    # 清理品牌名和卖家名用于比较（去除特殊字符和空格）
                    seller_name_clean = re.sub(r'[^a-zA-Z0-9]', '', seller_name.lower()) # 确保卖家名称也被清理
                    brand_name_clean = re.sub(r'[^a-zA-Z0-9]', '', brand_name)
                    
                    self.log_message(f"ASIN {asin}: 卖家清理后: {seller_name_clean}, 品牌清理后: {brand_name_clean}", always_show=True)
                    
                    if seller_name_clean in brand_name_clean or brand_name_clean in seller_name_clean:
                        self.log_message(f"ASIN {asin}: 卖家名与品牌名匹配，归类为不符合条件", always_show=True)
                        result["reason"] = f"有货产品卖家({seller_name})与品牌({brand_name})匹配"
                        self.save_result_realtime(result, is_qualified=False)
                        return result
                
                # 提取其他额外信息
                self.extract_additional_info(soup, result)
                
                # 有货状态下，检查评论数是否符合条件
                if result["rating_count"] is None or result["rating_count"] < 20 or result["rating_count"] > 2000:
                    self.log_message(f"ASIN {asin}: 有货产品评论数 {result['rating_count']} 不在要求范围内(20-2000)，归类为不符合条件", always_show=True)
                    result["reason"] = f"有货产品评论数 {result['rating_count']} 不在要求范围内"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 符合条件，搜索并判断品牌
                self.log_message(f"ASIN {asin}: 有货产品评论数符合条件，开始搜索判断品牌", always_show=True)
                
                # 调用analyze_brand_on_amazon搜索品牌
                brand_search_result = self.analyze_brand_on_amazon(result["brand"], result["brand_type"])
                if brand_search_result and "category" in brand_search_result:
                    result["brand_category"] = brand_search_result["category"]
                    result["search_result"] = brand_search_result
                    self.log_message(f"ASIN {asin}: 有货产品品牌分类结果: {result['brand_category']}", always_show=True)
                    
                    # 添加判断，如果分类为0（未找到匹配品牌），设置is_unique_brand为True，避免后续再进行品牌唯一性检查
                    if result["brand_category"] == 0:
                        result["is_unique_brand"] = True
                        self.log_message(f"ASIN {asin}: 未找到匹配品牌，自动设置为唯一品牌", always_show=True)
                    
                    self.save_result_realtime(result, is_qualified=True)
                    return result
                else:
                    self.log_message(f"ASIN {asin}: 有货产品品牌搜索失败", always_show=True)
                    result["reason"] = "品牌搜索失败"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
            
        except Exception as e:
            self.log_message(f"处理ASIN {asin}时出错: {str(e)}", level=LogLevel.NORMAL)
            result["timeout"] = True
            result["reason"] = f"处理出错: {str(e)}"
            # 实时保存超时的结果
            self.save_timeout_asin(asin, result.get("brand", ""))
            self.save_result_realtime(result, is_qualified=False)
            return result

    def extract_additional_info(self, soup, result):
        """从页面提取其他额外信息"""
        try:
            # 提取主图URL
            try:
                # 尝试从页面上的JavaScript数据中提取图片URL
                image_script = None
                for script in soup.find_all('script', type='text/javascript'):
                    if 'ImageBlockATF' in script.text and 'colorImages' in script.text:
                        image_script = script.text
                        break
                
                if image_script:
                    # 提取colorImages部分
                    match = re.search(r'colorImages\':\s*{\s*\'initial\':\s*(\[.*?\])', image_script, re.DOTALL)
                    if match:
                        image_data_str = match.group(1)
                        # 清理JavaScript对象使其成为有效的JSON
                        image_data_str = re.sub(r'(\w+):', r'"\1":', image_data_str)
                        image_data_str = re.sub(r'\'', '"', image_data_str)
                        
                        try:
                            image_data = json.loads(image_data_str)
                            if image_data and len(image_data) > 0 and 'hiRes' in image_data[0]:
                                result['image_url'] = image_data[0]['hiRes']
                            elif image_data and len(image_data) > 0 and 'large' in image_data[0]:
                                result['image_url'] = image_data[0]['large']
                        except json.JSONDecodeError:
                            # 尝试提取直接URL
                            img_url_match = re.search(r'"hiRes":\s*"(https://[^"]+)"', image_script)
                            if img_url_match:
                                result['image_url'] = img_url_match.group(1)
                            else:
                                img_url_match = re.search(r'"large":\s*"(https://[^"]+)"', image_script)
                                if img_url_match:
                                    result['image_url'] = img_url_match.group(1)
                
                # 如果上面方法失败，尝试从图片标签获取
                if not result['image_url']:
                    main_image = soup.find('img', id='landingImage') or soup.find('img', id='imgBlkFront')
                    if main_image and 'src' in main_image.attrs:
                        result['image_url'] = main_image['src']
                    elif main_image and 'data-old-hires' in main_image.attrs:
                        result['image_url'] = main_image['data-old-hires']
                
            except Exception as e:
                self.log_message(f"提取图片URL时出错: {str(e)}")
                
            # 提取尺寸信息
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and ('Dimensions' in th.text or 'Product Dimensions' in th.text):
                    dimensions_elem = tr.find('td')
                    if dimensions_elem:
                        result["dimensions"] = dimensions_elem.text.strip()
                    break
            
            # 提取重量信息
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and 'Weight' in th.text:
                    weight_elem = tr.find('td')
                    if weight_elem:
                        result["weight"] = weight_elem.text.strip()
                    break
            
            # 提取评分
            rating_elem = soup.select_one("span[data-hook='rating-out-of-text'], span.a-icon-alt")
            if rating_elem:
                rating_text = rating_elem.text
                rating_match = re.search(r'([\d.]+) out of', rating_text)
                if rating_match:
                    result["rating"] = float(rating_match.group(1))
                else:
                    # 尝试其他可能的格式
                    rating_match = re.search(r'([\d.]+)', rating_text)
                    if rating_match:
                        result["rating"] = float(rating_match.group(1))
            
            # 如果上面的方法没找到评分，尝试其他选择器
            if result["rating"] is None:
                for span in soup.find_all('span'):
                    if span.get('aria-label') and 'out of 5 stars' in span.get('aria-label'):
                        rating_text = span.get('aria-label')
                        rating_match = re.search(r'([\d.]+) out of', rating_text)
                        if rating_match:
                            result["rating"] = float(rating_match.group(1))
                            break
            
            # 提取评论数量
            reviews_elem = soup.select_one("span#acrCustomerReviewText")
            if reviews_elem:
                reviews_text = reviews_elem.text
                reviews_match = re.search(r'([\d,]+)', reviews_text)
                if reviews_match:
                    result["rating_count"] = int(reviews_match.group(1).replace(',', ''))
            
            # 如果上面的方法没找到评论数，尝试其他选择器
            if result["rating_count"] is None:
                for a in soup.find_all('a', {'id': 'acrCustomerReviewLink'}):
                    reviews_text = a.text
                    reviews_match = re.search(r'([\d,]+)', reviews_text)
                    if reviews_match:
                        result["rating_count"] = int(reviews_match.group(1).replace(',', ''))
                        break
            
            # 检查评论数是否在要求范围内
            if result["rating_count"] is not None:
                if result["rating_count"] < 20 or result["rating_count"] > 2000:
                    self.log_message(f"ASIN {result['asin']}: 评论数 {result['rating_count']} 不在要求范围内(20-2000)，跳过", always_show=True)
                    self.non_compliant_asins.append({
                        "asin": result["asin"],
                        "reason": f"评论数 {result['rating_count']} 不在要求范围内"
                    })
                    return False
            # 如果评论数为None，仍然继续处理
            
            # 提取销售排名
            rank_section = None
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and ('Best Sellers Rank' in th.text or 'Sellers Rank' in th.text):
                    rank_section = tr.find('td')
                    break
                    
            if rank_section:
                # 提取排名文本
                rank_text = rank_section.text.strip()
                self.log_message(f"销售排名原始文本: {rank_text}")
                # 提取每个排名
                rank_matches = re.finditer(r'#([\d,]+) in ([^#\(]+)', rank_text)
                for match in rank_matches:
                    rank_num = int(match.group(1).replace(',', ''))
                    category = match.group(2).strip()
                    result["sales_rank"].append({
                        "rank": rank_num,
                        "category": category
                    })
                    
                if result["sales_rank"]:
                    self.log_message(f"找到销售排名: {result['sales_rank']}")
                    
            return True
                    
        except Exception as e:
            self.log_message(f"提取附加信息时出错: {str(e)}")
            return False 

    def save_brand_results_by_category(self, only_save_new_results=False):
        """根据品牌类型、符合条件情况和分类，将结果保存到不同的Excel文件中
           only_save_new_results: 为True时只保存新处理的结果，不读取并合并已有文件的内容
        """
        try:
            # 定义品牌分类名称
            category_names = {
                0: "品牌找不到",
                1: "品牌独占",
                2: "两个不重复卖家",
                3: "3-5个不重复卖家",
                4: "其他情况",
                5: "品牌信息为空",
                6: "网络超时或加载失败"
            }
            
            # 定义品牌类型中文名称
            brand_type_names = {
                "visit_the": "Visit_the类型",
                "brand": "Brand类型",
                "no_brand": "无品牌类型"
            }
            
            # 保存符合条件的结果（按品牌类型和分类保存）
            for brand_type, categories in self.brand_results.items():
                # 确保categories是字典类型
                if not isinstance(categories, dict):
                    self.log_message(f"跳过非字典类型的分类数据: {brand_type}", always_show=True)
                    continue
                    
                brand_type_cn = brand_type_names.get(brand_type, f"未知类型-{brand_type}")
                
                # 检查compliant键是否存在和是否为字典类型
                if "compliant" in categories and isinstance(categories["compliant"], dict):
                    for category, items in categories["compliant"].items():
                        if items:  # 只保存有数据的类别
                            category_name = category_names.get(category, f"未知分类-{category}")
                            
                            # 根据是否缺货，选择对应的文件夹
                            has_stock_items = []
                            out_of_stock_items = []
                            
                            # 按库存状态分类
                            for item in items:
                                if item.get("is_out_of_stock", False):
                                    out_of_stock_items.append(item)
                                else:
                                    has_stock_items.append(item)
                            
                            # 判断分类是否为"品牌信息为空"（分类 5）
                            if category == 5:
                                # 按原始分类分组
                                empty_brand_by_original = {}
                                for item in out_of_stock_items + has_stock_items:
                                    original_cat = item.get("original_category", 4)  # 默认为4如果没有原始分类
                                    if original_cat not in empty_brand_by_original:
                                        empty_brand_by_original[original_cat] = {"out_of_stock": [], "has_stock": []}
                                    
                                    if item.get("is_out_of_stock", False):
                                        empty_brand_by_original[original_cat]["out_of_stock"].append(item)
                                    else:
                                        empty_brand_by_original[original_cat]["has_stock"].append(item)
                                
                                # 保存到"品牌为空"文件夹下，使用与"符合"文件夹相同的逻辑
                                for original_cat, stock_items in empty_brand_by_original.items():
                                    original_cat_name = category_names.get(original_cat, f"未知分类-{original_cat}")
                                    
                                    # 保存缺货项目
                                    if stock_items["out_of_stock"]:
                                        folder = os.path.join(self.condition_folders["品牌为空"], "缺货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["out_of_stock"], filename, brand_type_cn, category_name, True, True, only_save_new_results)
                                    
                                    # 保存有货项目
                                    if stock_items["has_stock"]:
                                        folder = os.path.join(self.condition_folders["品牌为空"], "有货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["has_stock"], filename, brand_type_cn, category_name, False, True, only_save_new_results)
                            # 判断分类是否为"网络超时或加载失败"（分类 6）
                            elif category == 6:
                                # 按原始分类分组（如果没有原始分类，默认为6本身）
                                timeout_by_original = {}
                                for item in out_of_stock_items + has_stock_items:
                                    original_cat = item.get("original_category", 6)  # 默认为6如果没有原始分类
                                    if original_cat not in timeout_by_original:
                                        timeout_by_original[original_cat] = {"out_of_stock": [], "has_stock": []}
                                    
                                    if item.get("is_out_of_stock", False):
                                        timeout_by_original[original_cat]["out_of_stock"].append(item)
                                    else:
                                        timeout_by_original[original_cat]["has_stock"].append(item)
                                
                                # 保存到"网络超时"文件夹下
                                for original_cat, stock_items in timeout_by_original.items():
                                    original_cat_name = category_names.get(original_cat, f"未知分类-{original_cat}")
                                    
                                    # 保存缺货项目
                                    if stock_items["out_of_stock"]:
                                        folder = os.path.join(self.condition_folders["网络超时"], "缺货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["out_of_stock"], filename, brand_type_cn, category_name, True, True, only_save_new_results)
                                    
                                    # 保存有货项目
                                    if stock_items["has_stock"]:
                                        folder = os.path.join(self.condition_folders["网络超时"], "有货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["has_stock"], filename, brand_type_cn, category_name, False, True, only_save_new_results)
                            else:
                                # 保存到"符合"文件夹下，正常逻辑
                                # 保存缺货项目
                                if out_of_stock_items:
                                    # 使用文件夹结构
                                    folder = os.path.join(self.condition_folders["符合"], "缺货", brand_type_cn)
                                    os.makedirs(folder, exist_ok=True)
                                    filename = os.path.join(folder, f"{category_name}.xlsx")
                                    
                                    self._save_items_to_excel(out_of_stock_items, filename, brand_type_cn, category_name, True, True, only_save_new_results)
                                
                                # 保存有货项目
                                if has_stock_items:
                                    # 使用文件夹结构
                                    folder = os.path.join(self.condition_folders["符合"], "有货", brand_type_cn)
                                    os.makedirs(folder, exist_ok=True)
                                    filename = os.path.join(folder, f"{category_name}.xlsx")
                                    
                                    self._save_items_to_excel(has_stock_items, filename, brand_type_cn, category_name, False, True, only_save_new_results)
            
            # 收集所有不符合条件的结果
            all_non_compliant_items = []
            
            # 收集各品牌类型下的不符合条件结果
            for brand_type, categories in self.brand_results.items():
                if "non_compliant" in categories and isinstance(categories["non_compliant"], list):
                    brand_type_cn = brand_type_names.get(brand_type, f"未知类型-{brand_type}")
                    for item in categories["non_compliant"]:
                        # 给每个项目添加品牌类型标记
                        item["品牌类型"] = brand_type_cn
                        all_non_compliant_items.append(item)
            
            # 保存所有不符合条件的结果到一个文件
            if all_non_compliant_items:
                folder = os.path.join(self.condition_folders["不符合"])
                os.makedirs(folder, exist_ok=True)
                filename = os.path.join(folder, "不符合条件ASIN.xlsx")
                
                self._save_items_to_excel(all_non_compliant_items, filename, "", "", None, False, only_save_new_results)
                self.log_message(f"已将所有不符合条件的ASIN({len(all_non_compliant_items)}个)保存到一个文件", level=LogLevel.SIMPLE)
            
            # 保存完整结果到一个文件（方便总览）
            self.save_comprehensive_results()
            
            # 保存超时ASIN
            if self.timeout_asins:
                timeout_df = pd.DataFrame(self.timeout_asins)
                output_file = os.path.join(self.results_folder, "超时ASIN.xlsx")
                timeout_df.to_excel(output_file, index=False)
                self.log_message(f"已保存{len(self.timeout_asins)}个超时ASIN到: {output_file}", level=LogLevel.SIMPLE)
            
            # 保存非唯一品牌的ASIN
            if self.non_unique_brand_asins:
                non_unique_df = pd.DataFrame(self.non_unique_brand_asins)
                output_file = os.path.join(self.results_folder, "非唯一品牌ASIN.xlsx")
                non_unique_df.to_excel(output_file, index=False)
                self.log_message(f"已保存{len(self.non_unique_brand_asins)}个非唯一品牌ASIN到: {output_file}", level=LogLevel.SIMPLE)
                    
        except Exception as e:
            self.log_message(f"保存结果到Excel时出错: {str(e)}", always_show=True)
            traceback.print_exc()
    
    def _save_items_to_excel(self, items, filename, brand_type_cn, category_name, is_out_of_stock, is_compliant, only_save_new_results=False):
        """辅助方法：保存项目到Excel文件"""
        try:
            # 检查是否仅保存新结果
            if only_save_new_results and os.path.exists(filename):
                # 读取已有的Excel文件
                try:
                    existing_df = pd.read_excel(filename)
                    # 如果存在ASIN列，提取已有的ASIN列表
                    if 'asin' in existing_df.columns:
                        existing_asins = set(existing_df['asin'].str.upper().tolist())
                        # 过滤出新的ASIN
                        new_items = [item for item in items if item.get('asin', '').upper() not in existing_asins]
                        
                        if not new_items:
                            self.log_message(f"所有结果已存在于文件中，无需更新: {filename}", level=LogLevel.SIMPLE)
                            return
                            
                        self.log_message(f"文件已存在，将追加{len(new_items)}个新结果到: {filename}", level=LogLevel.SIMPLE)
                        items = new_items
                except Exception as e:
                    self.log_message(f"读取已有Excel文件出错，将覆盖文件: {str(e)}", level=LogLevel.NORMAL)
            
            # 使用深拷贝避免修改原始数据
            import copy
            export_items = copy.deepcopy(items)
            
            # 处理复杂数据类型为字符串格式
            for item in export_items:
                # 销售排名处理
                if 'sales_rank' in item and item['sales_rank']:
                    # 提取排名和类别信息到单独的列
                    ranks = []
                    categories_list = []
                    for rank_info in item['sales_rank']:
                        if isinstance(rank_info, dict):
                            if 'rank' in rank_info:
                                ranks.append(str(rank_info['rank']))
                            if 'category' in rank_info:
                                categories_list.append(rank_info['category'])
                    
                    item['sales_rank_numbers'] = ';'.join(ranks)
                    item['sales_rank_categories'] = ';'.join(categories_list)
                    item['sales_rank'] = str(item['sales_rank'])
                
                # 品牌分析结果处理
                if 'brand_analysis' in item and item['brand_analysis']:
                    # 提取品牌分析的关键字段
                    if isinstance(item['brand_analysis'], dict):
                        analysis = item['brand_analysis']
                        item['brand_total_products'] = analysis.get('total_products', 0)
                        item['brand_sellers_count'] = len(analysis.get('unique_sellers', []))
                        item['brand_sellers'] = ';'.join(analysis.get('unique_sellers', []))
                        item['brand_category'] = analysis.get('category', 0)
                        # 将完整的分析结果转为字符串保存
                        item['brand_analysis'] = str(analysis)
                
                # 确保所有字段都有值
                for field in ['title', 'brand', 'brand_type', 'image_url', 
                            'dimensions', 'weight', 'rating', 'rating_count']:
                    if field not in item or item[field] is None:
                        item[field] = ""
                        
                # 添加分类分组信息
                item['品牌类型'] = brand_type_cn
                item['品牌分类'] = category_name if category_name else ""
                item['符合条件'] = "是" if is_compliant else "否"
                if is_out_of_stock is not None:
                    item['库存状态'] = "缺货" if is_out_of_stock else "有货"
                if not is_compliant and 'non_compliant_reason' in item:
                    item['不符合原因'] = item['non_compliant_reason']
            
            # 创建DataFrame
            df = pd.DataFrame(export_items)
            
            # 将新数据追加到现有文件或创建新文件
            if only_save_new_results and os.path.exists(filename) and 'existing_df' in locals():
                # 合并数据框
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                # 保存合并后的数据框
                combined_df.to_excel(filename, index=False)
                self.log_message(f"已将{len(items)}个新结果追加到: {filename}", level=LogLevel.SIMPLE)
            else:
                # 保存为新文件
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                df.to_excel(filename, index=False)
                self.log_message(f"已将{len(items)}个结果保存到: {filename}", level=LogLevel.SIMPLE)
                
        except Exception as e:
            self.log_message(f"保存Excel时出错: {str(e)}", always_show=True)
            traceback.print_exc()
    
    def save_comprehensive_results(self):
        """创建汇总结果"""
        try:
            # 创建汇总文件夹
            comprehensive_folder = self.results_folder
                
            # 创建全部结果文件
            all_results_file = os.path.join(comprehensive_folder, "全部结果.xlsx")
            
            # 收集所有结果到一个列表中
            all_results = []
            
            # 遍历所有品牌类型和分类
            for brand_type, categories in self.brand_results.items():
                # 符合条件的结果
                for category, results in categories.get("compliant", {}).items():
                    for result in results:
                        all_results.append({
                            "asin": result.get("asin"),
                            "title": result.get("title"),
                            "brand": result.get("brand"),
                            "brand_type": result.get("brand_type"),
                            "rating": result.get("rating"),
                            "rating_count": result.get("rating_count"),
                            "is_out_of_stock": result.get("is_out_of_stock", False),
                            "has_variants": result.get("has_variants", False),
                            "is_unique_brand": result.get("is_unique_brand", False),
                            "品牌类型": "Visit the" if brand_type == "visit_the" else ("Brand" if brand_type == "brand" else "无品牌"),
                            "符合条件": "是",
                            "库存状态": "缺货" if result.get("is_out_of_stock", False) else "有货",
                            "品牌分类": category
                        })
                
                # 不符合条件的结果
                for result in categories.get("non_compliant", []):
                    all_results.append({
                        "asin": result.get("asin"),
                        "title": result.get("title"),
                        "brand": result.get("brand"),
                        "brand_type": result.get("brand_type"),
                        "rating": result.get("rating"),
                        "rating_count": result.get("rating_count"),
                        "is_out_of_stock": result.get("is_out_of_stock", False),
                        "has_variants": result.get("has_variants", False),
                        "is_unique_brand": result.get("is_unique_brand", False),
                        "品牌类型": "Visit the" if brand_type == "visit_the" else ("Brand" if brand_type == "brand" else "无品牌"),
                        "符合条件": "否",
                        "不符合原因": result.get("non_compliant_reason", ""),
                        "品牌分类": ""
                    })
            
            # 创建数据框
            if all_results:
                # 将结果列表转换为DataFrame
                df = pd.DataFrame(all_results)
                
                # 保存到Excel
                with pd.ExcelWriter(all_results_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='全部结果', index=False)
                
                print(f"已保存{len(all_results)}个结果到汇总文件: {all_results_file}")
                self.log_message(f"已保存{len(all_results)}个结果到汇总文件: {all_results_file}", level=LogLevel.SIMPLE)
                
                # 创建数据分析文件
                pivot_file = os.path.join(comprehensive_folder, "数据分析.xlsx")
                with pd.ExcelWriter(pivot_file, engine='openpyxl') as writer:
                    # 总体分类统计
                    pivot_df = pd.pivot_table(
                        df, 
                        index=['品牌类型', '符合条件', '库存状态', '品牌分类'],
                        values=['asin'], 
                        aggfunc='count',
                        observed=False # 添加此行以保留当前行为并消除FutureWarning
                    )
                    pivot_df.columns = ['数量']
                    pivot_df.to_excel(writer, sheet_name='分类统计')
                    
                    # 评论数量分布 - 使用副本避免SettingWithCopyWarning
                    if 'rating_count' in df.columns:
                        # 创建完整的数据副本
                        df_copy = df.copy()  
                        # 筛选有评论数的行
                        df_with_rating = df_copy[df_copy['rating_count'].notna()].copy()
                        
                        if not df_with_rating.empty:
                            # 确保rating_count是数值类型 - 使用安全的方式修改副本
                            df_with_rating['rating_count'] = pd.to_numeric(df_with_rating['rating_count'], errors='coerce')
                            
                            # 定义评论数量分组 - 使用安全的方式修改副本
                            df_with_rating['评论数分组'] = pd.cut(
                                df_with_rating['rating_count'],
                                bins=[0, 20, 50, 100, 200, 500, 1000, 2000, float('inf')],
                                labels=['<20', '20-50', '50-100', '100-200', '200-500', '500-1000', '1000-2000', '>2000']
                            )
                            
                            # 根据评论数量分组统计
                            ratings_pivot = pd.pivot_table(
                                df_with_rating,
                                index=['品牌类型', '符合条件', '库存状态', '评论数分组'],
                                values=['asin'],
                                aggfunc='count',
                                observed=False # 添加此行以保留当前行为并消除FutureWarning
                            )
                            ratings_pivot.columns = ['数量']
                            ratings_pivot.to_excel(writer, sheet_name='评论数量分布')
                    
                self.log_message(f"已创建数据分析文件: {pivot_file}", level=LogLevel.SIMPLE)
                
        except Exception as e:
            self.log_message(f"创建汇总结果时出错: {str(e)}", always_show=True)

    def process_asin(self, asin):
        """处理单个ASIN，获取相关信息并进行分析"""
        self.log_message(f"开始处理ASIN: {asin}", level=LogLevel.NORMAL)
        
        try:
            # 获取产品信息
            result = self.get_product_info(asin)
            
            # 标记此ASIN已处理
            self.asin_counter += 1
            
            # 确定是否符合条件
            is_qualified = True
            non_compliant_reason = ""
            
            # 条件1: 产品必须缺货
            if not result.get("is_out_of_stock", False):
                is_qualified = False
                non_compliant_reason = "产品有货"
                self.log_message(f"ASIN {asin}: 产品有货，不符合条件", level=LogLevel.NORMAL)
            
            # 条件2: 产品不能有变体 - 检测到变体立即返回，不再进行其他条件检查
            elif result.get("has_variants", False):
                is_qualified = False
                non_compliant_reason = "产品有变体"
                self.log_message(f"ASIN {asin}: 产品有变体，不符合条件，跳过后续分析", level=LogLevel.NORMAL)
                
                # 记录不符合原因
                result["non_compliant_reason"] = non_compliant_reason
                
                # 实时保存结果
                self.save_result_realtime(result, is_qualified=False)
                
                # 每处理10个ASIN就保存一次断点
                if self.asin_counter % 10 == 0:
                    self.save_checkpoint()
                    self.log_message(f"已处理{self.asin_counter}个ASIN，保存断点", level=LogLevel.NORMAL)
                
                return result
            
            # 条件3: 评论数必须在20-2000之间
            elif result.get("rating_count") is not None and (result.get("rating_count") < 20 or result.get("rating_count") > 2000):
                is_qualified = False
                non_compliant_reason = f"评论数 {result.get('rating_count')} 不在要求范围内(20-2000)"
                self.log_message(f"ASIN {asin}: {non_compliant_reason}，不符合条件", level=LogLevel.NORMAL)
            elif result.get("rating_count") is None:
                # 如果评论数为None，仍然可以继续处理，不影响合格判断
                self.log_message(f"ASIN {asin}: 评论数未获取到，继续处理", level=LogLevel.NORMAL)
            
            # 条件4: 品牌必须唯一 - 除非品牌分类为0（未找到匹配品牌）
            elif not result.get("is_unique_brand", False):
                # 检查品牌分类结果，如果是0（未找到匹配品牌），则认为是唯一的
                if result.get("brand_category") == 0:
                    # 品牌分类为0，自动视为唯一品牌
                    result["is_unique_brand"] = True
                    self.log_message(f"ASIN {asin}: 品牌分类为0（未找到匹配品牌），自动视为唯一品牌", level=LogLevel.NORMAL)
                else:
                    # 品牌分类不为0，且不是唯一品牌，则不符合条件
                    is_qualified = False
                    non_compliant_reason = f"品牌 {result.get('brand', '')} 不是唯一的"
                    self.log_message(f"ASIN {asin}: {non_compliant_reason}，不符合条件", level=LogLevel.NORMAL)
            
            # 记录不符合原因
            if not is_qualified:
                result["non_compliant_reason"] = non_compliant_reason
            else:
                self.log_message(f"ASIN {asin}: 符合所有筛选条件", level=LogLevel.NORMAL)
            
            # 实时保存结果（使用新的方法，会按分类保存）
            self.save_result_realtime(result, is_qualified=is_qualified)
            
            # 每处理10个ASIN就保存一次断点
            if self.asin_counter % 10 == 0:
                self.save_checkpoint()
                self.log_message(f"已处理{self.asin_counter}个ASIN，保存断点", level=LogLevel.NORMAL)
            
            return result
        except Exception as e:
            self.log_message(f"处理ASIN {asin}时出错: {str(e)}", level=LogLevel.NORMAL)
            # 记录到超时ASIN列表
            self.timeout_asins.append({"asin": asin, "error": str(e)})
            
            # 标记为已处理
            self.asin_counter += 1
            
            # 创建最小结果对象
            error_result = {
                "asin": asin, 
                "error": str(e),
                "timeout": True,
                "brand_category": 6  # 网络超时或加载失败
            }
            
            # 实时保存错误结果
            self.save_result_realtime(error_result, is_qualified=False)
            
            return error_result

    def show_brand_seller_details(self, search_result):
        """显示详细的品牌和卖家信息"""
        try:
            if not search_result:
                return
                
            brand = search_result["brand"]
            brands_found = search_result["brands_found"]
            sellers = search_result["sellers"] # 保持，但不再用于日志输出
            
            self.log_message(f"\n品牌 '{brand}' 的详细搜索结果:", level=LogLevel.DETAILED)
            self.log_message("="*50, level=LogLevel.DETAILED)
            
            # 统计每个品牌出现的次数
            brand_counts = {}
            for b in brands_found:
                brand_counts[b] = brand_counts.get(b, 0) + 1
                
            # 统计每个卖家出现的次数
            seller_counts = {}
            for s in sellers:
                seller_counts[s] = seller_counts.get(s, 0) + 1
                
            # 显示品牌统计
            self.log_message("\n品牌统计:", level=LogLevel.DETAILED)
            for b, count in brand_counts.items():
                self.log_message(f"品牌: {b}, 出现次数: {count}", level=LogLevel.DETAILED)
                
            # 显示卖家统计
            self.log_message("\n卖家统计:", level=LogLevel.DETAILED)
            for s, count in seller_counts.items():
                self.log_message(f"卖家: {s}, 出现次数: {count}", level=LogLevel.DETAILED)
                
            # 显示品牌和卖家的对应关系
            self.log_message("\n品牌和卖家对应关系:", level=LogLevel.DETAILED)
            for i, (b, s) in enumerate(zip(brands_found, sellers)):
                self.log_message(f"第{i+1}条: 品牌={b}, 卖家={s}", level=LogLevel.DETAILED)
                
            self.log_message("="*50, level=LogLevel.DETAILED)
            
        except Exception as e:
            self.log_message(f"显示品牌卖家详细信息时出错: {str(e)}", level=LogLevel.NORMAL)

    def classify_brand_results(self, search_result):
        """根据搜索结果对品牌进行分类
        0: 品牌找不到
        1: 品牌独占
        2: 两个不重复卖家
        3: 3-5个不重复卖家
        4: 其他情况
        5: 包含空品牌信息
        """
        try:
            # 初始化原始分类变量
            original_category = None
            
            if not search_result or "status" not in search_result or search_result["status"] != "success":
                self.log_message(f"搜索失败或结果无效，归类为品牌未找到(0)", level=LogLevel.NORMAL)
                return 0  # 品牌未找到
                
            # 提取品牌和卖家信息
            brands_found = search_result.get("brands_found", [])
            sellers = search_result.get("sellers", [])
            brand = search_result.get("brand", "").lower().replace(" ", "")  # 当前品牌，转小写并删除空格
            
            # 清理品牌名，去除特殊字符
            brand_clean = re.sub(r'[^a-zA-Z0-9]', '', brand)
            
            # 处理所有找到的品牌，转小写并删除空格
            cleaned_brands = []
            for b in brands_found:
                if b == "no" or b == "error" or b == "empty_value":
                    cleaned_brands.append(b)
                else:
                    cleaned_brands.append(re.sub(r'[^a-zA-Z0-9]', '', b.lower().replace(" ", "")))
                    
            # 处理所有卖家，转小写并删除空格和特殊字符
            cleaned_sellers = []
            for s in sellers:
                if s == "no":
                    cleaned_sellers.append("no")
                else:
                    cleaned_sellers.append(re.sub(r'[^a-zA-Z0-9]', '', s.lower()))
            
            # 显示详细的品牌和卖家信息
            self.show_brand_seller_details(search_result)
            
            # 获取与当前品牌匹配的所有索引（对应JavaScript中的indices）
            matching_indices = [i for i, b in enumerate(cleaned_brands) if b != "no" and b != "error" and b != "empty_value" and b == brand_clean]
            
            self.log_message(f"找到 {len(matching_indices)} 个匹配的品牌", level=LogLevel.NORMAL)
            
            # 检查是否有匹配品牌
            if len(matching_indices) == 0:
                self.log_message(f"未找到匹配品牌，归类为无匹配(0)", level=LogLevel.NORMAL)
                return 0  # 品牌找不到
                
            # 如果匹配品牌数量大于等于5个，直接归为类别4
            if len(matching_indices) >= 5:
                self.log_message(f"匹配品牌数量大于等于5个({len(matching_indices)}个)，归类为其他情况(4)", level=LogLevel.NORMAL)
                return 4  # 其他情况：匹配品牌过多
                
            else:
                # 获取匹配品牌对应的卖家
                matching_sellers = [cleaned_sellers[i] for i in matching_indices]
                
                # 判断卖家列表中是否包含品牌名称
                if brand_clean in matching_sellers:
                    self.log_message(f"匹配品牌的卖家列表包含品牌名 '{brand_clean}'，归类为其他情况(4)", level=LogLevel.NORMAL)
                    return 4  # 其他情况：卖家包含品牌名
                else:
                    # 检查匹配品牌的卖家中是否包含"Amazon"字样
                    has_amazon_seller = False
                    amazon_sellers = []
                    for idx, seller in enumerate(matching_sellers):
                        if seller != 'no' and 'amazon' in seller.lower():
                            has_amazon_seller = True
                            amazon_sellers.append(seller)
                            self.log_message(f"匹配品牌的卖家 '{seller}' 包含Amazon字样", level=LogLevel.NORMAL)
                    
                    if has_amazon_seller:
                        self.log_message(f"匹配品牌有Amazon卖家 {amazon_sellers}，归类为其他情况(4)", level=LogLevel.NORMAL)
                        return 4  # 有Amazon卖家，归类为其他情况
                    else:
                        # 获取唯一卖家数量（排除"no"）
                        unique_sellers = set([s for s in matching_sellers if s != "no"])
                        unique_seller_count = len(unique_sellers)
                        
                        # 根据唯一卖家数量进行分类
                        if unique_seller_count == 0:
                            self.log_message(f"匹配品牌没有有效卖家，归类为品牌独占(1)", level=LogLevel.NORMAL)
                            original_category = 1  # 品牌独占（没有有效卖家）
                        elif unique_seller_count == 1:
                            self.log_message(f"匹配品牌只有一个唯一卖家 '{list(unique_sellers)[0]}'，归类为品牌独占(1)", level=LogLevel.NORMAL)
                            original_category = 1  # 品牌独占（一个唯一卖家）
                        elif unique_seller_count == 2:
                            self.log_message(f"匹配品牌有两个不重复卖家，归类为两个不重复卖家(2)", level=LogLevel.NORMAL)
                            original_category = 2  # 两个不重复卖家
                        elif unique_seller_count <= 5:
                            self.log_message(f"匹配品牌有{unique_seller_count}个不重复卖家，归类为3-5个不重复卖家(3)", level=LogLevel.NORMAL)
                            original_category = 3  # 3-5个不重复卖家
                        else:
                            self.log_message(f"匹配品牌有{unique_seller_count}个不重复卖家，归类为其他情况(4)", level=LogLevel.NORMAL)
                            original_category = 4  # 其他情况（超过5个不重复卖家）
                        
                        # 只有当类别为1、2或3时，才检查卖家ID格式
                        if original_category in [1, 2, 3]:
                            # 检查匹配品牌的卖家是否有ID格式
                            has_id_format_sellers = False
                            id_format_sellers = []
                            for idx, seller in enumerate(matching_sellers):
                                if seller != 'no' and self.is_seller_id_format(seller):
                                    has_id_format_sellers = True
                                    id_format_sellers.append(seller)
                                    self.log_message(f"匹配品牌的卖家 '{seller}' 是ID格式", level=LogLevel.NORMAL)
                            
                            # 如果有ID格式卖家，直接归类为5
                            if has_id_format_sellers:
                                self.log_message(f"匹配品牌有ID格式卖家 {id_format_sellers}，归类为有空品牌信息(5)", level=LogLevel.NORMAL)
                                # 保存原始分类以便后续使用
                                search_result["has_id_format_sellers"] = True
                                search_result["id_format_sellers"] = id_format_sellers
                                search_result["original_category"] = original_category  # 保存原始分类
                                search_result["empty_brand"] = True  # 标记为空品牌
                                return 5  # 有ID格式卖家，归类为有空品牌信息
                            else:
                                search_result["has_id_format_sellers"] = False
                        else:
                            # 类别为4的情况，不检查卖家ID格式
                            search_result["has_id_format_sellers"] = False
                        
                        return original_category
                
        except Exception as e:
            self.log_message(f"分类品牌结果时出错: {str(e)}", level=LogLevel.NORMAL)
            return 6  # 出错时归类为超时类别

    def analyze_brand_on_amazon(self, brand, brand_type=None):
        """在Amazon上分析品牌，获取该品牌下的产品和卖家信息"""
        try:
            # 搜索品牌
            search_result = self.search_brand_on_amazon(brand)
            
            # 如果search_brand_on_amazon成功返回结果，就直接使用它
            if search_result.get("status") == "success":
                # 添加默认的品牌类型和符合条件标志
                # 如果传入了brand_type参数，使用传入的值，否则使用默认值
                if brand_type and brand_type in ["Visit the", "Brand:"]:
                    # 转换品牌类型格式
                    brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                    search_result["brand_type"] = brand_type_key
                else:
                    search_result["brand_type"] = "no_brand"  # 默认为无品牌类型
                
                search_result["is_compliant"] = True      # 默认为符合条件
                
                # 调用分类方法
                category = self.classify_brand_results(search_result)
                search_result["category"] = category
                
                # 记录分类信息和是否有空品牌
                has_empty_brands = search_result.get("has_empty_brands", False)
                if category == 5:
                    self.log_message(f"品牌 '{brand}' 含有空品牌信息，分类结果: {category}", always_show=True)
                elif has_empty_brands:
                    self.log_message(f"品牌 '{brand}' 含有空品牌信息，但因为品牌相同，分类结果为: {category}", always_show=True)
                else:
                    self.log_message(f"品牌 '{brand}' 分类结果: {category}", always_show=True)
                    
                return search_result
            
            # 如果search_brand_on_amazon未成功，根据状态分配默认分类
            if search_result.get("status") == "not_found":
                search_result["category"] = 0  # 品牌找不到
                # 添加默认的品牌类型和符合条件标志
                if brand_type and brand_type in ["Visit the", "Brand:"]:
                    brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                    search_result["brand_type"] = brand_type_key
                else:
                    search_result["brand_type"] = "no_brand"
                search_result["is_compliant"] = True # 未找到品牌，仍然可以认为是"符合"的（只是没找到）
            else:
                search_result["category"] = 6  # 网络超时或加载失败
                # 添加默认的品牌类型和符合条件标志
                if brand_type and brand_type in ["Visit the", "Brand:"]:
                    brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                    search_result["brand_type"] = brand_type_key
                else:
                    search_result["brand_type"] = "no_brand"
                search_result["is_compliant"] = False # 网络超时或加载失败，不符合
            
            return search_result
            
        except Exception as e:
            self.log_message(f"分析品牌时出错: {str(e)}", always_show=True)
            result = {
                "status": "error", 
                "brand": brand, 
                "error": str(e), 
                "category": 6, 
                "is_compliant": False # 错误时，不符合
            }
            
            # 添加品牌类型
            if brand_type and brand_type in ["Visit the", "Brand:"]:
                brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                result["brand_type"] = brand_type_key
            else:
                result["brand_type"] = "no_brand"
                
            return result
    
    def worker(self):
        """工作线程，不断从队列中获取ASIN进行处理"""
        try:
            # 启动浏览器，增加重试机制
            browser_started = False
            for attempt in range(self.MAX_BROWSER_SETUP_ATTEMPTS):
                self.log_message(f"尝试启动浏览器 ({attempt+1}/{self.MAX_BROWSER_SETUP_ATTEMPTS})...", always_show=True)
                if self.setup_selenium_browser():
                    browser_started = True
                    self.log_message("浏览器启动成功", always_show=True)
                    break
                else:
                    self.log_message(f"浏览器启动失败，等待 {self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS} 秒后重试...", always_show=True)
                    self.close_selenium_browser() # 尝试关闭可能未完全启动的浏览器
                    time.sleep(self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS)
            
            if not browser_started:
                self.log_message("多次尝试启动浏览器失败，线程退出", always_show=True)
                return
                
            # 首先访问亚马逊主页，然后设置位置
            max_location_attempts = 3
            location_set = False
            
            for attempt in range(max_location_attempts):
                try:
                    self.log_message(f"访问亚马逊{self.country}主页，尝试 {attempt+1}/{max_location_attempts}", always_show=True)
                    self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                    time.sleep(3)
                    
                    # 处理可能出现的验证码
                    if self.is_captcha_present():
                        if not self.handle_captcha():
                            self.log_message("无法解决主页验证码，继续尝试", always_show=True)
                    
                    # 检查并设置位置
                    location_set = self.check_and_fix_location()
                    if location_set:
                        if self.country == "美国":
                            self.log_message("成功设置美国位置为Kent 98032", always_show=True)
                        elif self.country == "日本":
                            self.log_message("成功设置日本位置为060-8588", always_show=True)
                        elif self.country == "加拿大":
                            self.log_message("成功设置加拿大位置为B3H 0A9", always_show=True)
                        else:
                            self.log_message(f"成功设置{self.country}位置", always_show=True)
                        break
                    else:
                        self.log_message(f"设置位置失败，尝试 {attempt+1}/{max_location_attempts}", always_show=True)
                        time.sleep(2)  # 等待2秒后重试
                except Exception as e:
                    self.log_message(f"设置初始环境时出错: {str(e)}", always_show=True)
                    time.sleep(2)  # 等待2秒后重试
            
            if not location_set:
                self.log_message(f"多次尝试设置{self.country}位置均失败，将使用默认位置继续", always_show=True)
                
            # 线程内ASIN处理计数
            local_asin_counter = 0
                
            # 处理队列中的ASIN
            # 修改循环条件，使其在收到停止信号时退出
            while not self._should_stop:
                try:
                    # 定期重启浏览器以更换指纹和清除缓存
                    current_time_in_worker = time.time()
                    if (current_time_in_worker - self.last_browser_restart_time) >= (self.BROWSER_RESTART_TIME_INTERVAL_MINUTES * 60):
                        self.log_message(f"已达到 {self.BROWSER_RESTART_TIME_INTERVAL_MINUTES} 分钟重启间隔。正在重启浏览器以更换指纹...", always_show=True)
                        self.close_selenium_browser()
                        time.sleep(2) # 确保浏览器完全关闭
                        
                        # 重新启动浏览器并设置初始环境，增加重试机制
                        browser_restarted = False
                        for attempt in range(self.MAX_BROWSER_SETUP_ATTEMPTS):
                            self.log_message(f"尝试重启浏览器 ({attempt+1}/{self.MAX_BROWSER_SETUP_ATTEMPTS})...", always_show=True)
                            if self.setup_selenium_browser():
                                browser_restarted = True
                                self.log_message("浏览器重启成功", always_show=True)
                                break
                            else:
                                self.log_message(f"浏览器重启失败，等待 {self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS} 秒后重试...", always_show=True)
                                self.close_selenium_browser() # 尝试关闭可能未完全启动的浏览器
                                time.sleep(self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS)
                        
                        if not browser_restarted:
                            self.log_message("多次尝试重启浏览器失败，工作线程退出", always_show=True)
                            break # 无法重启浏览器，退出线程
                        
                        self.log_message(f"在新浏览器中重新设置{self.country}位置...", always_show=True)
                        self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                        time.sleep(3)
                        if self.is_captcha_present():
                            if not self.handle_captcha():
                                self.log_message("无法解决主页验证码，继续尝试", always_show=True)
                        self.check_and_fix_location()
                        time.sleep(5) # 等待新浏览器和位置设置稳定
                        
                        self.last_browser_restart_time = time.time() # 更新上次重启时间

                    # 从队列中获取ASIN，如果1秒内没有获取到，且_should_stop为True，则退出
                    asin = self.asin_queue.get(timeout=1)
                    
                    if asin == "STOP":
                        self.log_message("收到停止信号，线程退出", always_show=True)
                        self._should_stop = True # 确保停止标志被设置
                        break
                    
                    # 再次检查停止标志，以防在get()之后但在处理之前收到停止信号
                    if self._should_stop:
                        self.log_message("在处理ASIN前收到停止信号，线程退出", always_show=True)
                        self.asin_queue.task_done()
                        break
                        
                    self.log_message(f"开始处理ASIN: {asin}", always_show=True)
                    
                    # 使用锁确保线程安全
                    with self.lock:
                        # 检查ASIN是否已经处理过
                        if asin in self.processed_asins:
                            self.log_message(f"ASIN {asin} 已经处理过，跳过", always_show=True)
                            self.asin_queue.task_done()
                            continue
                    
                    # 重试处理ASIN，最多尝试3次
                    max_retry = 3
                    processed_success = False  # 标记该品牌是否已成功处理
                    
                    for retry in range(max_retry):
                        try:
                            # 调用 process_asin。它会返回包含处理结果的字典。
                            process_result = self.process_asin(asin)
                            
                            # 检查是否为"商品不存在"错误
                            if process_result and process_result.get("reason") == "商品不存在":
                                self.consecutive_not_found_errors += 1
                                self.log_message(f"ASIN {asin}: 商品不存在。连续错误计数: {self.consecutive_not_found_errors}/{self.MAX_CONSECUTIVE_NOT_FOUND_ERRORS}", always_show=True)
                                
                                if self.consecutive_not_found_errors >= self.MAX_CONSECUTIVE_NOT_FOUND_ERRORS:
                                    self.log_message(f"连续{self.consecutive_not_found_errors}次遇到商品不存在页面，正在重启浏览器...", always_show=True)
                                    self.close_selenium_browser()
                                    time.sleep(2) # 等待浏览器完全关闭
                                    
                                    browser_restarted = False
                                    for setup_attempt in range(self.MAX_BROWSER_SETUP_ATTEMPTS):
                                        self.log_message(f"尝试重启浏览器 ({setup_attempt+1}/{self.MAX_BROWSER_SETUP_ATTEMPTS})...", always_show=True)
                                        if self.setup_selenium_browser():
                                            browser_restarted = True
                                            self.log_message("浏览器重启成功", always_show=True)
                                            break
                                        else:
                                            self.log_message(f"浏览器重启失败，等待 {self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS} 秒后重试...", always_show=True)
                                            self.close_selenium_browser() # 尝试关闭可能未完全启动的浏览器
                                            time.sleep(self.BROWSER_SETUP_RETRY_INTERVAL_SECONDS)
                                    
                                    if not browser_restarted:
                                        self.log_message("多次尝试重启浏览器失败，工作线程退出", always_show=True)
                                        processed_success = False # 浏览器无法重启，此ASIN视为最终失败
                                        break # 退出当前ASIN的重试循环
                                    else:
                                        self.log_message(f"在新浏览器中重新设置{self.country}位置...", always_show=True)
                                        self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                                        time.sleep(3)
                                        if self.is_captcha_present():
                                            if not self.handle_captcha():
                                                self.log_message("无法解决主页验证码，继续尝试", always_show=True)
                                        self.check_and_fix_location()
                                        time.sleep(5) # 等待新浏览器和位置设置稳定
                                        self.last_browser_restart_time = time.time() # 更新上次重启时间
                                        
                                        self.consecutive_not_found_errors = 0 # 浏览器重启成功，重置计数器
                                        continue # 立即跳到 for retry 循环的下一次迭代，重新尝试处理当前ASIN
                                else:
                                    # 未达到连续错误阈值，但ASIN仍是"商品不存在"
                                    processed_success = False # 仍然标记为未成功处理
                                    if retry < max_retry - 1:
                                        self.log_message(f"处理ASIN {asin}失败，错误: 商品不存在，尝试 {retry+1}/{max_retry}", always_show=True)
                                        time.sleep(2)
                                        # 移除：不再在商品不存在页面尝试检查和修复位置
                                        # self.check_and_fix_location() # {{ delete_line }}
                                        continue # 继续 for retry 循环，尝试处理当前ASIN
                                    else:
                                        self.log_message(f"多次尝试处理ASIN {asin}均失败 (商品不存在)。", always_show=True)
                                        break # 达到最大重试次数，退出当前ASIN的重试循环
                            else:
                                # ASIN成功处理，或者因其他原因失败（非"商品不存在"）
                                self.consecutive_not_found_errors = 0 # 重置连续错误计数器
                                processed_success = True
                                break # 退出当前ASIN的重试循环，表示已处理完成
                                
                        except Exception as e:
                            self.consecutive_not_found_errors = 0 # 遇到其他异常时也重置计数器
                            if retry < max_retry - 1:
                                self.log_message(f"处理ASIN {asin}失败，错误: {str(e)}，尝试 {retry+1}/{max_retry}", always_show=True)
                                time.sleep(2)  # 等待2秒后重试
                                
                                # 重新检查位置设置
                                self.check_and_fix_location()
                            else:
                                self.log_message(f"多次尝试处理ASIN {asin}均失败，错误: {str(e)}", always_show=True)
                                processed_success = False # 标记为失败
                                break  # 退出重试循环
                    
                    # 更新全局处理计数
                    with self.lock:
                        if hasattr(self, 'processed_count'):
                            self.processed_count += 1
                        
                        # 再次检查ASIN是否已添加到processed_asins
                        # 注：process_asin内部的save_result_realtime已经负责添加 asin 到 self.processed_asins。
                        # 因此，这里不需要额外添加。
                        
                        # 调用进度回调
                        if hasattr(self, 'progress_callback') and self.progress_callback and hasattr(self, 'total_asins') and self.total_asins > 0:
                            self.progress_callback(len(self.processed_asins), self.total_asins)
                    
                    # 增加线程内计数        
                    local_asin_counter += 1
                    
                    # 每处理10个ASIN就保存一次断点和结果
                    if local_asin_counter % 10 == 0:
                        with self.lock:
                            try:
                                self.log_message(f"线程已处理{local_asin_counter}个ASIN，保存断点和结果", always_show=True)
                                # 保存不符合条件的结果
                                self.save_brand_results_by_category()
                                # 保存断点
                                self.save_checkpoint(force=True)
                            except Exception as save_err:
                                self.log_message(f"保存断点或结果时出错: {str(save_err)}", always_show=True)
                    
                    # 标记队列任务完成
                    self.asin_queue.task_done()
                    
                except queue.Empty:
                    # 队列为空，检查停止标志，如果为True则退出循环
                    if self._should_stop:
                        self.log_message("队列为空且收到停止信号，线程退出", always_show=True)
                        break
                    # 如果队列为空但未收到停止信号，可以等待或短暂休眠后继续检查
                    # 为了不立即退出，这里可以添加一个短暂的sleep或者依赖外层循环的超时
                    time.sleep(0.1) 
                    continue # 继续循环，等待新任务或停止信号
                except Exception as e:
                    self.log_message(f"处理ASIN时出错: {str(e)}", always_show=True)
                    # 确保在异常发生时任务也被标记为完成，防止队列卡死
                    try:
                        self.asin_queue.task_done()
                    except ValueError:
                        # 如果在异常处理之前任务已被标记为完成，此处会引发ValueError，忽略即可
                        pass
                    
        except Exception as e:
            self.log_message(f"工作线程出错: {str(e)}", always_show=True)
        finally:
            # 关闭浏览器
            self.close_selenium_browser()
    
    def process_asins_with_threads(self, asins, num_threads=5):
        """使用多线程处理ASIN列表"""
        try:
            # 创建线程池
            threads = []
            
            self.log_message(f"开始使用{num_threads}个线程处理{len(asins)}个ASIN", level=LogLevel.SIMPLE)
            
            # 先将所有ASIN放入队列
            for asin in asins:
                self.asin_queue.put(asin)
                
            # 创建并启动工作线程
            for _ in range(num_threads):
                t = threading.Thread(target=self.worker)
                t.daemon = True
                t.start()
                threads.append(t)
                
            # 等待所有ASIN处理完成
            self.asin_queue.join()
            
            # 向所有线程发送停止信号
            for _ in range(num_threads):
                self.asin_queue.put("STOP")
                
            # 等待所有线程结束
            for t in threads:
                t.join()
                
            self.log_message(f"所有ASIN处理完成，共处理了{len(asins)}个ASIN", level=LogLevel.SIMPLE)
            
            # 保存结果
            self.save_results_to_excel()
            
        except Exception as e:
            self.log_message(f"多线程处理ASIN时出错: {str(e)}", always_show=True)
    
    def run(self, asins_file=None, num_threads=1, skip_summary=False, resume=True, log_level=LogLevel.SIMPLE, headless_mode=True, country=None, total_asins=0, progress_callback=None, disable_search_logs=True):
        """运行爬虫"""
        try:
            # 设置日志级别
            self.set_log_level(log_level)
            
            # 设置禁用搜索日志选项
            self.disable_search_logs = disable_search_logs
            
            # 设置无头模式
            self.headless_mode = headless_mode
            self.log_message(f"无头模式状态: {'开启' if headless_mode else '关闭'}", always_show=True)
            
            # 设置国家
            if country:
                if country in self.countries:
                    self.country = country
                    self.country_domain = self.countries[country]["domain"]
                    self.country_zipcode = self.countries[country]["zipcode"]
                else:
                    self.log_message(f"不支持的国家: {country}，使用默认美国站点", always_show=True)
                    self.country = "美国"
                    self.country_domain = self.countries["美国"]["domain"]
                    self.country_zipcode = self.countries["美国"]["zipcode"]
            else:
                self.country = "美国"
                self.country_domain = self.countries["美国"]["domain"]
                self.country_zipcode = self.countries["美国"]["zipcode"]
                
            self.log_message(f"使用国家站点: {self.country}，域名: amazon.{self.country_domain}，邮编: {self.country_zipcode}", always_show=True)
            
            # 创建结果文件夹
            self.setup_results_folder(self.country)
            
            # 移除针对Win7 32位系统限制线程数的旧逻辑，因为它已被强制单线程覆盖
            # if num_threads > 1 and self.system_info.get("is_win7", False) and not self.system_info.get("is_64bit", False):
            #     self.log_message(f"检测到32位Win7系统，将线程数从{num_threads}降为1以提高稳定性", always_show=True)
            #     num_threads = 1
            
            # 设置ASIN队列
            self.asin_queue = queue.Queue()
            
            # 标记是否已经运行
            self.is_running = True
            
            # 创建互斥锁，用于线程同步
            self.lock = threading.Lock()
            
            # 初始化计数器
            self.asin_counter = 0
            self.processed_count = 0
            
            # 初始化品牌搜索结果
            self.brand_results = {}  # 品牌类型 -> {"compliant": {分类1: [], 分类2: []}, "non_compliant": []}
            
            # 初始化不同品牌类型的字典
            for brand_type in ["visit_the", "brand", "no_brand"]:
                self.brand_results[brand_type] = {
                    "compliant": {
                        0: [],  # 品牌找不到
                        1: [],  # 品牌独占
                        2: [],  # 两个不重复卖家
                        3: [],  # 3-5个不重复卖家
                        4: [],  # 其他情况
                        5: []   # 品牌为空
                    },
                    "non_compliant": []
                }
            
            # 记录已处理的ASIN
            self.processed_asins = set()
            
            # 记录超时的ASIN
            self.timeout_asins = []
            
            # 记录非唯一品牌的ASIN
            self.non_unique_brand_asins = []
            
            # 记录visit_the类型的ASIN
            self.visit_the_brand_asins = []
            
            # 设置总ASIN数量和进度回调
            self.total_asins = total_asins
            self.progress_callback = progress_callback
            
            # 清理可能存在的残留数据
            if not resume:
                self.log_message("不恢复上次运行，丢弃已有断点", always_show=True)
            elif os.path.exists(self.checkpoint_file):
                self.log_message(f"尝试从断点恢复: {self.checkpoint_file}", always_show=True)
                if self.load_checkpoint():
                    self.log_message(f"成功从断点恢复，已处理{len(self.processed_asins)}个ASIN", always_show=True)
                else:
                    self.log_message("无法从断点恢复，将重新开始", always_show=True)
            
            # 加载ASIN列表
            if asins_file:
                asins = self.load_asins_from_excel(asins_file)
            else:
                self.log_message("未指定ASIN文件，尝试使用默认文件名", always_show=True)
                default_filename = f"amazon_results_{self.country}.xlsx"
                if os.path.exists(default_filename):
                    asins = self.load_asins_from_excel(default_filename)
                else:
                    self.log_message(f"默认文件 {default_filename} 不存在", always_show=True)
                    return False
            
            if not asins:
                self.log_message("ASIN列表为空，无法继续", always_show=True)
                return False
            
            # 仅处理尚未处理的ASIN
            pending_asins = [asin for asin in asins if asin not in self.processed_asins]
            self.log_message(f"加载了{len(asins)}个ASIN，其中{len(pending_asins)}个尚未处理", always_show=True)
            
            # 更新总ASIN数量以用于进度条
            with self.lock:
                self.total_asins = len(pending_asins) + len(self.processed_asins)
                if self.progress_callback and self.total_asins > 0:
                    self.progress_callback(len(self.processed_asins), self.total_asins)
            
            if not pending_asins:
                self.log_message("所有ASIN已处理完成", always_show=True)
                if not skip_summary:
                    self.save_brand_results_by_category()
                return True
                
            # 使用多线程处理ASIN
            self.process_asins_with_threads(pending_asins, num_threads)
            
            # 保存最终结果
            if not skip_summary:
                self.save_brand_results_by_category()
            
            # 标记运行完成
            self.is_running = False
            
            self.log_message("处理完成！", always_show=True)
            return True
            
        except Exception as e:
            self.log_message(f"运行出错: {str(e)}", always_show=True)
            traceback.print_exc()
            return False
    
    def save_results_to_excel(self):
        """将所有结果保存到Excel文件"""
        try:
            # 保存符合条件的结果
            if self.results:
                results_df = pd.DataFrame(self.results)
                
                # 处理销售排名列（含有列表的嵌套结构）
                if 'sales_rank' in results_df.columns:
                    results_df['sales_rank'] = results_df['sales_rank'].apply(lambda x: str(x) if x else "")
                
                # 保存到Excel
                output_file = os.path.join(self.results_folder, "Amazon产品信息.xlsx")
                results_df.to_excel(output_file, index=False)
                self.log_message(f"已保存{len(self.results)}个符合条件的产品信息到: {output_file}", level=LogLevel.SIMPLE)
            
            # 按类别保存结果
            self.save_brand_results_by_category()
                
        except Exception as e:
            self.log_message(f"保存结果到Excel时出错: {str(e)}", always_show=True)

    def set_us_location(self, stay_on_page=False):
        """设置美国位置
        
        Args:
            stay_on_page: 如果为True，则尝试在当前页面设置位置，而不是跳转到主页
        """
        us_zipcode_parts = ["98032"]
        us_domain = self.countries["美国"]["domain"]
        
        def check_us_location(location_text):
            return "98032" in location_text or "Kent 98032" in location_text
            
        return self._set_location(us_zipcode_parts, us_domain, "美国", check_us_location, stay_on_page)

    def set_japan_location(self, stay_on_page=False):
        """设置日本位置
        
        Args:
            stay_on_page: 如果为True，则尝试在当前页面设置位置，而不是跳转到主页
        """
        japan_zipcode_parts = ["060", "8588"]
        japan_domain = self.countries["日本"]["domain"]
        
        def check_japan_location(location_text):
            return "060-8588" in location_text
            
        return self._set_location(japan_zipcode_parts, japan_domain, "日本", check_japan_location, stay_on_page)

    def set_canada_location(self, stay_on_page=False):
        """设置加拿大位置
        
        Args:
            stay_on_page: 如果为True，则尝试在当前页面设置位置，而不是跳转到主页
        """
        canada_zipcode_parts = self.countries["加拿大"]["zipcode_parts"] # ["B3H", "0A9"]
        canada_domain = self.countries["加拿大"]["domain"]
        
        def check_canada_location(location_text):
            return "B3H" in location_text and "0A" in location_text
            
        return self._set_location(canada_zipcode_parts, canada_domain, "加拿大", check_canada_location, stay_on_page)

    def save_checkpoint(self, force=False):
        """保存断点续传数据到JSON文件"""
        try:
            # 如果没有新的处理，且不是强制保存，则跳过
            if self.asin_counter == 0 and not force:
                return
                
            # 保存完整的断点数据
            checkpoint_data = {
                "processed_count": self.processed_count,
                "brand_compliant_count": self.brand_compliant_count,
                "brand_noncompliant_count": self.brand_noncompliant_count,
                "compliant_brands": self.compliant_brands,
                "noncompliant_brands": self.noncompliant_brands,
                "processed_asins": list(self.processed_asins)  # 添加已处理的ASIN列表
            }
            
            # 保存到JSON文件
            with open(self.checkpoint_file, "w", encoding="utf-8") as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            
            self.log_message(f"断点续传数据已保存到: {self.checkpoint_file}", level=LogLevel.NORMAL)
        except Exception as e:
            self.log_message(f"保存断点续传数据时出错: {str(e)}", always_show=True)

    def load_checkpoint(self):
        """加载断点续传数据"""
        try:
            if not os.path.exists(self.checkpoint_file):
                self.log_message(f"没有找到断点续传文件: {self.checkpoint_file}，将从头开始处理", level=LogLevel.NORMAL)
                return False
                
            with open(self.checkpoint_file, "r", encoding="utf-8") as f:
                checkpoint_data = json.load(f)
                
            # 恢复各项数据
            self.processed_count = checkpoint_data.get("processed_count", 0)
            self.brand_compliant_count = checkpoint_data.get("brand_compliant_count", 0)
            self.brand_noncompliant_count = checkpoint_data.get("brand_noncompliant_count", 0)
            self.compliant_brands = checkpoint_data.get("compliant_brands", [])
            self.noncompliant_brands = checkpoint_data.get("noncompliant_brands", [])
            
            # 恢复已处理的ASIN列表
            processed_asins = checkpoint_data.get("processed_asins", [])
            self.processed_asins = set(processed_asins)
            
            self.log_message(f"已加载断点续传数据，上次处理到第 {self.processed_count} 个ASIN，共 {len(self.processed_asins)} 个已处理ASIN", level=LogLevel.NORMAL)
            return True
        except Exception as e:
            self.log_message(f"加载断点续传数据时出错: {str(e)}", always_show=True)
            # 出错时重置处理计数
            self.processed_count = 0
            return False
    
    def save_timeout_asin(self, asin, brand):
        """将超时的ASIN保存到专门的表格中"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.timeout_file):
                # 创建新表格
                df = pd.DataFrame(columns=["ASIN", "品牌", "时间"])
                df.to_excel(self.timeout_file, index=False)
            
            # 读取现有数据
            df = pd.read_excel(self.timeout_file)
            
            # 添加新数据
            new_row = {"ASIN": asin, "品牌": brand, "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            
            # 保存回文件
            df.to_excel(self.timeout_file, index=False)
            
            self.log_message(f"已将超时ASIN {asin} 保存到 {self.timeout_file}", level=LogLevel.DETAILED)
        except Exception as e:
            self.log_message(f"保存超时ASIN时出错: {str(e)}", level=LogLevel.NORMAL)
    
    def save_result_realtime(self, asin_data, is_qualified=False):
        """实时保存处理结果"""
        try:
            # 如果ASIN已经处理过，则跳过
            if asin_data["asin"] in self.processed_asins:
                self.log_message(f"跳过重复ASIN: {asin_data['asin']}", level=LogLevel.DETAILED)
                return
            
            # 添加到已处理ASIN集合中
            self.processed_asins.add(asin_data["asin"])
            
            # 品牌类型映射
            brand_type_map = {
                "Visit the": "visit_the",
                "Brand:": "brand",
                "": "no_brand"  # 无品牌
            }
            
            # 获取品牌类型和其他信息
            is_out_of_stock = asin_data.get("is_out_of_stock", False)
            brand_type = asin_data.get("brand_type", "")
            brand_type_key = brand_type_map.get(brand_type, "no_brand")
            
            # 使用传入的is_qualified参数，优先于asin_data中的is_compliant
            is_compliant = is_qualified if is_qualified else asin_data.get("is_compliant", False)
            
            # 获取品牌分类（如果有）
            brand_category = asin_data.get("brand_category", 4)  # 默认为类别4（其他情况）
            
            # 检查search_result中是否包含original_category并将其传递给asin_data
            if "search_result" in asin_data and isinstance(asin_data["search_result"], dict):
                if "original_category" in asin_data["search_result"]:
                    asin_data["original_category"] = asin_data["search_result"]["original_category"]
                    self.log_message(f"为ASIN {asin_data['asin']} 设置原始分类: {asin_data['original_category']}", level=LogLevel.DETAILED)
            
            # 初始化品牌类型对应的结果字典（如果不存在）
            if brand_type_key not in self.brand_results:
                self.brand_results[brand_type_key] = {
                    "compliant": {},
                    "non_compliant": []
                }
            
            # 检查是否有ID格式卖家标记
            has_id_format_sellers = False
            if "search_result" in asin_data and isinstance(asin_data["search_result"], dict):
                has_id_format_sellers = asin_data["search_result"].get("has_id_format_sellers", False)
            
            # 根据是否符合条件分别处理
            if is_compliant:
                # 符合条件的ASIN，按分类保存
                if "compliant" not in self.brand_results[brand_type_key]:
                    self.brand_results[brand_type_key]["compliant"] = {}
                
                # 如果有ID格式卖家，只保存到品牌为空分类(5)
                if has_id_format_sellers:
                    # 获取原始分类（没有则使用当前分类）
                    original_category = asin_data.get("original_category", brand_category)
                    
                    # 保存到品牌为空分类(5)，但记录原始分类
                    if 5 not in self.brand_results[brand_type_key]["compliant"]:
                        self.brand_results[brand_type_key]["compliant"][5] = []
                    
                    # 复制一份数据，避免引用相同对象
                    empty_brand_data = asin_data.copy()
                    # 标记为空品牌(5)，但保存原始分类
                    empty_brand_data["original_category"] = original_category
                    # 添加到品牌为空(5)分类
                    self.brand_results[brand_type_key]["compliant"][5].append(empty_brand_data)
                    self.log_message(f"ASIN {asin_data['asin']} 有ID格式卖家，添加到品牌为空分类，原始分类为 {original_category}", level=LogLevel.NORMAL)
                else:
                    # 没有ID格式卖家，正常保存到对应分类
                    # 确保品牌分类键存在
                    if brand_category not in self.brand_results[brand_type_key]["compliant"]:
                        self.brand_results[brand_type_key]["compliant"][brand_category] = []
                    
                    # 正常添加到对应分类中
                    self.brand_results[brand_type_key]["compliant"][brand_category].append(asin_data)
                    self.log_message(f"ASIN {asin_data['asin']} 已添加到符合条件分类 {brand_category}", level=LogLevel.NORMAL)
            else:
                # 不符合条件的ASIN
                if "non_compliant" not in self.brand_results[brand_type_key]:
                    self.brand_results[brand_type_key]["non_compliant"] = []
                
                # 添加到不符合条件列表
                self.brand_results[brand_type_key]["non_compliant"].append(asin_data)
                self.log_message(f"ASIN {asin_data['asin']} 已添加到不符合条件列表", level=LogLevel.NORMAL)
            
            # 每处理10个ASIN后，保存一次分类结果
            if len(self.processed_asins) % 10 == 0:
                self.save_brand_results_by_category()
                self.log_message(f"已处理 {len(self.processed_asins)} 个ASIN，保存分类结果", level=LogLevel.NORMAL)
                
        except Exception as e:
            self.log_message(f"保存结果时发生错误: {str(e)}", always_show=True)
            self.log_message(traceback.format_exc(), level=LogLevel.DETAILED)
    
    # 添加已处理ASIN集合的初始化（在__init__方法中）

    def add_result(self, brand, category, search_result=None):
        """添加品牌结果到相应类别"""
        result_data = {"品牌": brand}
        
        # 添加分类信息
        category_names = {
            0: "品牌未找到",
            1: "品牌独占",
            2: "两个不重复卖家",
            3: "3-5个不重复卖家",
            4: "其他情况",
            5: "品牌信息为空",
            6: "网络超时或加载失败"
        }
        result_data["分类"] = category_names.get(category, "未知")
        
        if search_result:
            # 添加更多搜索结果数据
            brands_found = search_result.get("brands_found", [])
            sellers = search_result.get("sellers", [])
            
            result_data.update({
                "匹配结果数": len(brands_found),
                "匹配品牌": ", ".join(brands_found[:5]),  # 最多显示前5个
                "卖家": ", ".join(sellers[:5]),  # 最多显示前5个
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        else:
            result_data["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 确定品牌类型
        brand_type = "no_brand"  # 默认为无品牌类型
        if search_result and "brand_type" in search_result:
            if search_result["brand_type"] == "visit_the":
                brand_type = "visit_the"
            elif search_result["brand_type"] == "brand":
                brand_type = "brand"
            
        # 判断是否符合条件的结果
        is_compliant = True  # 默认为符合条件
        if search_result and "is_compliant" in search_result:
            is_compliant = search_result["is_compliant"]
            
        # 存储到正确的位置
        if is_compliant:
            # 符合条件的结果按类别存储
            if category in self.brand_results[brand_type]["compliant"]:
                self.brand_results[brand_type]["compliant"][category].append(result_data)
            else:
                # 如果类别不存在，创建一个新类别列表
                self.brand_results[brand_type]["compliant"][category] = [result_data]
        else:
            # 不符合条件的结果直接添加到non_compliant列表
            self.brand_results[brand_type]["non_compliant"].append(result_data)
            
        self.log_message(f"品牌 '{brand}' 已分类为: {category}，品牌类型: {brand_type}，是否符合条件: {is_compliant}")
    
    def process_brands(self, brands, max_retries=5):  # 增加最大重试次数
        """处理品牌列表"""
        total_brands = len(brands)
        self.log_message(f"开始处理{total_brands}个品牌...", always_show=True)
        
        # 用于记录已处理的品牌，防止重复处理
        already_processed = set()
        
        for i, brand in enumerate(brands):
            # 检查品牌是否已经处理过
            if brand in already_processed:
                self.log_message(f"品牌 '{brand}' 已经处理过，跳过", always_show=True)
                continue
                
            self.log_message(f"处理品牌 {i+1}/{total_brands}: {brand}", always_show=True)
            
            retries = 0
            processed = False  # 标记该品牌是否已成功处理
            
            # 每三个品牌重启一次浏览器，降低出错率
            if i > 0 and i % 3 == 0:
                try:
                    self.log_message(f"定期重启浏览器以增加稳定性 (品牌索引: {i})", always_show=True)
                    self.close_selenium_browser()
                    time.sleep(3)  # 等待浏览器完全关闭
                    
                    # 重新启动浏览器
                    if not self.setup_selenium_browser():
                        self.log_message("浏览器重启失败，尝试再次启动", always_show=True)
                        time.sleep(5)
                        if not self.setup_selenium_browser():
                            self.log_message("浏览器二次重启失败，可能会影响后续处理", always_show=True)
                    else:
                        self.log_message("浏览器重启成功，访问主页并设置位置", always_show=True)
                        self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                        time.sleep(5)
                        
                        # 处理验证码
                        if self.is_captcha_present():
                            if not self.handle_captcha():
                                self.log_message("无法处理主页验证码", always_show=True)
                                
                        # 设置位置
                        self.check_and_fix_location()
                        time.sleep(3)
                except Exception as e:
                    self.log_message(f"重启浏览器过程中出错: {str(e)}", always_show=True)
            
            while retries < max_retries and not processed:
                try:
                    # 如果是重试，在重试前先回到主页并刷新
                    if retries > 0:
                        self.log_message(f"重试前刷新浏览器环境 (重试 {retries}/{max_retries})", always_show=True)
                        try:
                            self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                            time.sleep(3)
                            
                            # 处理验证码
                            if self.is_captcha_present():
                                if not self.handle_captcha():
                                    self.log_message("无法处理主页验证码", always_show=True)
                            
                            # 重新设置位置
                            self.check_and_fix_location()
                            time.sleep(3)
                        except Exception as refresh_error:
                            self.log_message(f"重试前刷新环境出错: {str(refresh_error)}", always_show=True)
                    
                    # 搜索品牌
                    search_result = self.analyze_brand_on_amazon(brand)
                    
                    # 检查搜索结果状态
                    if search_result:
                        status = search_result.get("status", "unknown")
                        category = search_result.get("category", 5)  # 默认为5（超时或加载失败）
                        
                        if status == "success":
                            # 成功获取结果，进行分类
                            self.log_message(f"品牌 '{brand}' 分类结果: {category}", always_show=True)
                            self.add_result(brand, category, search_result)
                            
                            # 实时保存结果
                            self.save_brand_results_by_category()
                            self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                            
                            processed = True  # 标记为已处理成功
                            
                            # 随机等待以避免被检测为机器人
                            wait_time = random.uniform(2, 5)
                            self.log_message(f"等待 {wait_time:.2f} 秒后继续...")
                            time.sleep(wait_time)
                        elif status == "not_found":
                            # 品牌未找到，直接分类为0
                            self.log_message(f"品牌 '{brand}' 未找到，直接分类为0", always_show=True)
                            self.add_result(brand, 0, {"状态": "not_found"})
                            
                            # 实时保存结果
                            self.save_brand_results_by_category()
                            self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                            
                            processed = True  # 标记为已处理成功
                            
                            # 随机等待以避免被检测为机器人
                            wait_time = random.uniform(2, 5)
                            time.sleep(wait_time)
                        elif status in ["no_elements", "extraction_failed"]:
                            # 其他提取失败情况，重试
                            if retries >= max_retries - 2:  # 只在倒数第二次重试时改为分类0
                                self.log_message(f"品牌 '{brand}' 处理失败(状态: {status})，分类为未找到", always_show=True)
                                self.add_result(brand, 0, {"状态": status})
                                
                                # 实时保存结果
                                self.save_brand_results_by_category()
                                self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                                
                                processed = True  # 标记为已处理完成
                            else:
                                self.log_message(f"处理品牌 '{brand}' 失败(状态: {status})，重试 ({retries+1}/{max_retries})")
                                retries += 1
                                # 增加等待时间，让网络更稳定
                                time.sleep(random.uniform(10, 15))
                        elif status in ["timeout", "no_sellers", "captcha_failed"]:
                            # 网络超时、无卖家信息或验证码问题，尝试重启浏览器并重试
                            if retries >= max_retries - 1:
                                self.log_message(f"品牌 '{brand}' 处理超时(状态: {status})，放入超时分类", always_show=True)
                                self.add_result(brand, 6, {"状态": status})
                                
                                # 实时保存结果
                                self.save_brand_results_by_category()
                                self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                                
                                processed = True  # 标记为已处理完成
                            else:
                                self.log_message(f"处理品牌 '{brand}' 超时(状态: {status})，重试 ({retries+1}/{max_retries})")
                                
                                # 如果是验证码失败或超时，尝试重启浏览器
                                if status == "captcha_failed" or status == "timeout":
                                    try:
                                        self.log_message("重启浏览器以处理验证码/超时问题", always_show=True)
                                        self.close_selenium_browser()
                                        time.sleep(3)
                                        if self.setup_selenium_browser():
                                            self.log_message("浏览器重启成功", always_show=True)
                                            self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                                            time.sleep(5)
                                            self.check_and_fix_location()
                                    except Exception as browser_error:
                                        self.log_message(f"重启浏览器出错: {str(browser_error)}", always_show=True)
                                
                                retries += 1
                                # 增加更长的等待时间
                                time.sleep(random.uniform(15, 20))
                        else:
                            # 其他未知状态，多次重试
                            if retries >= max_retries - 1:
                                self.log_message(f"品牌 '{brand}' 处理异常(状态: {status})，放入超时分类", always_show=True)
                                self.add_result(brand, 6, {"状态": status})
                                
                                # 实时保存结果
                                self.save_brand_results_by_category()
                                self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                                
                                processed = True  # 标记为已处理完成
                            else:
                                self.log_message(f"处理品牌 '{brand}' 异常(状态: {status})，重试 ({retries+1}/{max_retries})")
                                
                                # 如果是未知状态，也尝试刷新页面
                                try:
                                    self.driver.refresh()
                                    time.sleep(5)
                                    if self.is_captcha_present():
                                        self.handle_captcha()
                                except Exception:
                                    pass
                                    
                                retries += 1
                                time.sleep(random.uniform(10, 15))
                    else:
                        # 搜索结果为空，重试或放入超时分类
                        if retries >= max_retries - 1:
                            self.log_message(f"品牌 '{brand}' 处理返回空结果，放入超时分类", always_show=True)
                            self.add_result(brand, 6, {"状态": "empty_result"})
                            
                            # 实时保存结果
                            self.save_brand_results_by_category()
                            self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                            
                            processed = True  # 标记为已处理完成
                        else:
                            self.log_message(f"处理品牌 '{brand}' 返回空结果，重试 ({retries+1}/{max_retries})")
                            retries += 1
                            time.sleep(random.uniform(10, 15))
                
                except Exception as e:
                    self.log_message(f"处理品牌时出错: {str(e)}", always_show=True)
                    retries += 1
                    if retries >= max_retries:
                        # 达到最大重试次数，将品牌添加到超时类别
                        self.log_message(f"品牌 '{brand}' 处理出错，放入超时分类", always_show=True)
                        self.add_result(brand, 6, {"状态": "error", "错误": str(e)})
                        
                        # 实时保存结果
                        self.save_brand_results_by_category()
                        self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                        
                        processed = True  # 标记为已处理完成
                    else:
                        # 在重试前尝试重启浏览器
                        try:
                            self.log_message("因错误重启浏览器", always_show=True)
                            self.close_selenium_browser()
                            time.sleep(3)
                            if self.setup_selenium_browser():
                                self.log_message("浏览器重启成功", always_show=True)
                                self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                                time.sleep(5)
                                if self.is_captcha_present():
                                    self.handle_captcha()
                                self.check_and_fix_location()
                        except Exception:
                            self.log_message("重启浏览器失败，继续使用当前状态", always_show=True)
                            
                    time.sleep(random.uniform(15, 20))
            
            # 无论处理成功与否，都将品牌标记为已处理，避免重复处理
            already_processed.add(brand)
            
        # 最终日志信息
        self.log_message("所有品牌处理完成", always_show=True)

    def load_brands_from_excel(self, filename="brands.xlsx"):
        """从Excel文件加载品牌列表"""
        try:
            df = pd.read_excel(filename)
            brands = []
            
            # 检查列名 - 不区分大小写
            columns = [col.lower() for col in df.columns]
            
            if 'brand' in columns or '品牌' in columns:
                # 获取原始列名（保留大小写）
                if 'brand' in columns:
                    original_col = df.columns[columns.index('brand')]
                else:
                    original_col = df.columns[columns.index('品牌')]
                
                brands = df[original_col].dropna().tolist()
                # 清理品牌名称，去掉空格和其他不必要的字符
                brands = [str(brand).strip() for brand in brands if str(brand).strip()]
                self.log_message(f"成功从Excel的'{original_col}'列加载了{len(brands)}个品牌", always_show=True)
            else:
                # 查找任何包含"brand"或"品牌"的列（不区分大小写）
                brand_cols = [col for col in df.columns if 'brand' in col.lower() or '品牌' in col.lower()]
                if brand_cols:
                    # 使用第一个匹配的列
                    brands = df[brand_cols[0]].dropna().tolist()
                    # 清理品牌名称
                    brands = [str(brand).strip() for brand in brands if str(brand).strip()]
                    self.log_message(f"成功从Excel的'{brand_cols[0]}'列加载了{len(brands)}个品牌", always_show=True)
                else:
                    # 如果找不到合适的列名，尝试使用第一列
                    if len(df.columns) > 0:
                        first_col = df.columns[0]
                        brands = df[first_col].dropna().tolist()
                        # 清理品牌名称
                        brands = [str(brand).strip() for brand in brands if str(brand).strip()]
                        self.log_message(f"未找到品牌列，使用第一列'{first_col}'加载了{len(brands)}个项目", always_show=True)
                    else:
                        self.log_message(f"Excel文件为空或没有列", always_show=True)
                        return []
            
            return brands
        except Exception as e:
            self.log_message(f"从Excel文件加载品牌时出错: {str(e)}", always_show=True)
            return []

    def execute_cdp_commands(self):
        """执行CDP命令绕过自动化检测"""
        try:
            # 设置用户代理
            user_agent = self.get_random_user_agent()
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": user_agent,
                "platform": "Windows" if "Windows" in user_agent else "macOS"
            })
            self.log_message("已通过CDP设置用户代理", level=LogLevel.DETAILED)
            
            # 隐藏webdriver属性
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    })
                '''
            })
            self.log_message("已隐藏webdriver属性", level=LogLevel.DETAILED)
            
            # 隐藏自动化相关特征
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                            Promise.resolve({ state: Notification.permission }) :
                            originalQuery(parameters)
                    );
                    
                    // 修改navigator.plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => {
                            return {
                                length: 5,
                                item: () => { return {}; },
                                namedItem: () => { return {}; },
                                refresh: () => {}
                            };
                        }
                    });
                    
                    // 修改navigator.languages
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en-US', 'en']
                    });
                    
                    // 创建一个假的WebGL
                    const getParameter = WebGLRenderingContext.getParameter;
                    WebGLRenderingContext.prototype.getParameter = function(parameter) {
                        if (parameter === 37445) {
                            return 'Intel Open Source Technology Center';
                        }
                        if (parameter === 37446) {
                            return 'Mesa DRI Intel(R) HD Graphics (Skylake GT2)';
                        }
                        return getParameter.apply(this, arguments);
                    };
                '''
            })
            self.log_message("已隐藏多个自动化特征", level=LogLevel.DETAILED)
            
            # 屏蔽关于"chrome.automation"的可能检测
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {},
                        csi: function() {},
                        app: {}
                    };
                '''
            })
            self.log_message("已模拟chrome对象", level=LogLevel.DETAILED)
            
            return True
        except Exception as e:
            self.log_message(f"执行CDP命令时出错: {str(e)}", level=LogLevel.DETAILED)
            return False

    def _set_location(self, zipcode_parts, country_domain, country_name_for_log, expected_location_check_func, stay_on_page=False):
        """
        通用设置国家位置的方法
        Args:
            zipcode_parts (list): 邮编的各个部分，例如 ["98032"] 或 ["060", "8588"]
            country_domain (str): 国家域名，例如 "com", "co.jp"
            country_name_for_log (str): 用于日志输出的国家名称
            expected_location_check_func (function): 一个函数，用于检查当前位置文本是否符合预期
            stay_on_page (bool): 如果为True，则尝试在当前页面设置位置，而不是跳转到主页
        Returns:
            bool: 是否成功设置位置
        """
        try:
            self.log_message(f"开始设置{country_name_for_log}位置...", always_show=True)
            driver = self.driver

            # 如果不是stay_on_page模式，访问Amazon主页，否则保持在当前页面
            if not stay_on_page:
                driver.get(f"https://www.amazon.{country_domain}/?language=en_US")
                time.sleep(3)

            # 检查当前位置是否已经是正确的
            current_location = ""
            try:
                # 使用 WebDriverWait 确保元素存在后再获取其文本
                location_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.ID, "glow-ingress-line2"))
                )
                current_location = location_element.text.strip()
                self.log_message(f"当前位置: {current_location}", always_show=True)

                if expected_location_check_func(current_location):
                    self.log_message(f"当前位置已经是正确的{country_name_for_log}位置: {current_location}", always_show=True)
                    # 新增：位置设置成功后，检查并处理登录弹窗
                    self._handle_login_popup()
                    return True
                else:
                    self.log_message(f"当前{country_name_for_log}位置不正确，需要重新设置", always_show=True)
            except TimeoutException:
                self.log_message(f"获取{country_name_for_log}位置信息超时，将尝试设置位置", always_show=True)
            except Exception as e:
                self.log_message(f"获取{country_name_for_log}位置信息出错: {str(e)}，将尝试设置位置", always_show=True)

            # 检查是否需要处理验证码
            if self.is_captcha_present():
                self.log_message("检测到验证码，尝试处理", always_show=True)
                if not self.handle_captcha():
                    self.log_message("验证码处理失败，继续尝试设置位置", always_show=True)
                    return False # 验证码处理失败，直接返回False

            # 添加内部重试循环，用于位置弹窗的查找和操作
            max_popover_retries = 3
            for popover_retry in range(max_popover_retries):
                try:
                    self.log_message(f"尝试找到并设置位置弹窗 ({popover_retry+1}/{max_popover_retries})", always_show=True)
                    
                    location_clicked = False
                    location_element = None

                    # 方法1: 通过ID找位置选择器
                    try:
                        location_element = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.ID, "nav-global-location-popover-link"))
                        )
                        location_element.click()
                        location_clicked = True
                        self.log_message("通过ID找到位置选择器并点击", always_show=True)
                    except Exception as e:
                        self.log_message(f"通过ID查找位置选择器失败: {str(e)}", always_show=True)

                    # 方法2: 如果方法1失败，尝试通过通配符XPath找位置选择器
                    if not location_clicked:
                        try:
                            location_xpath = "//a[contains(@id, 'location') or contains(@class, 'location')]"
                            location_element = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, location_xpath))
                            )
                            location_element.click()
                            location_clicked = True
                            self.log_message("通过XPath找到位置选择器并点击", always_show=True)
                        except Exception as e:
                            self.log_message(f"通过XPath查找位置选择器失败: {str(e)}", always_show=True)

                    # 方法3: 如果上述方法失败，尝试通过文本内容找位置选择器
                    if not location_clicked:
                        try:
                            location_text_xpath = "//*[contains(text(), 'Deliver to') or contains(text(), 'Delivering to')]"
                            location_element = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, location_text_xpath))
                            )
                            location_element.click()
                            location_clicked = True
                            self.log_message("通过文本内容找到位置选择器并点击", always_show=True)
                        except Exception as e:
                            self.log_message(f"通过文本内容查找位置选择器失败: {str(e)}", always_show=True)

                    if not location_clicked:
                        raise Exception("无法找到位置选择器") # Raise an exception to trigger the retry

                    # 等待位置弹窗加载
                    time.sleep(2)

                    zipcode_input_found = False
                    zipcode_inputs = []

                    # 根据zipcode_parts的长度选择输入框ID
                    input_ids = ["GLUXZipUpdateInput"] if len(zipcode_parts) == 1 else ["GLUXZipUpdateInput_0", "GLUXZipUpdateInput_1"]
                    
                    try:
                        for i, input_id in enumerate(input_ids):
                            zip_input = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.ID, input_id))
                            )
                            zip_input.clear()
                            zip_input.send_keys(zipcode_parts[i])
                            zipcode_inputs.append(zip_input)
                        zipcode_input_found = True
                        self.log_message(f"通过ID找到并输入邮政编码: {zipcode_parts}", always_show=True)
                    except Exception as e:
                        self.log_message(f"通过ID查找邮政编码输入框失败: {str(e)}", always_show=True)
                        
                        # 如果位置按钮已点击但找不到邮编输入框，可能是被亚马逊暂时封禁
                        if location_clicked:
                            self.log_message("位置按钮已点击但无法找到邮编输入框，可能被亚马逊暂时封禁，等待5分钟后重启浏览器", always_show=True)
                            # 等待5分钟
                            time.sleep(300)
                            # 关闭当前浏览器
                            self.close_selenium_browser()
                            # 重启浏览器
                            self.setup_selenium_browser()
                            # 递归调用本方法重试设置位置
                            return self._set_location(zipcode_parts, country_domain, country_name_for_log, expected_location_check_func, stay_on_page)

                    # 如果方法1失败，尝试通过XPath找邮政编码输入框 (只处理单个输入框的情况)
                    if not zipcode_input_found and len(zipcode_parts) == 1:
                        try:
                            # 尝试多种XPath查找邮政编码输入框
                            zipcode_xpaths = [
                                "//input[contains(@id, 'GLUXZip') or contains(@placeholder, 'zip') or contains(@placeholder, 'code')]",
                                "//input[contains(@class, 'GLUX') and @type='text']",
                                "//div[contains(@class, 'a-popover-wrapper')]//input[@type='text']"
                            ]
                            
                            for zipcode_xpath in zipcode_xpaths:
                                try:
                                    zip_input = WebDriverWait(driver, 5).until(
                                        EC.element_to_be_clickable((By.XPATH, zipcode_xpath))
                                    )
                                    zip_input.clear()
                                    time.sleep(0.5)  # 短暂等待以模拟人工操作
                                    zip_input.send_keys(zipcode_parts[0])
                                    zipcode_inputs.append(zip_input)
                                    zipcode_input_found = True
                                    self.log_message(f"通过XPath '{zipcode_xpath}' 找到邮政编码输入框", always_show=True)
                                    break
                                except Exception as e:
                                    self.log_message(f"通过XPath '{zipcode_xpath}' 查找邮政编码输入框失败: {str(e)}", level=LogLevel.DETAILED)
                                    continue
                        except Exception as e:
                            self.log_message(f"尝试所有XPath查找邮政编码输入框失败: {str(e)}", always_show=True)

                    if not zipcode_input_found:
                        self.log_message("未找到邮政编码输入框，暂停5分钟后重试...", always_show=True)
                        time.sleep(300)  # 等待5分钟
                        # 重新尝试加载地址选择页面
                        try:
                            self.log_message("重新加载地址选择页面后再次尝试...", always_show=True)
                            driver.refresh()
                            time.sleep(5)  # 等待页面刷新
                            
                            # 处理可能出现的验证码
                            if self.is_captcha_present():
                                if not self.handle_captcha():
                                    self.log_message("刷新后出现验证码且无法处理，继续尝试", always_show=True)
                            
                            # 继续执行后续代码
                        except Exception as e:
                            self.log_message(f"刷新页面失败: {str(e)}", always_show=True)
                            # 仍然继续执行后续代码

                    apply_clicked = False
                    
                    # 方法1: 通过ID找应用按钮
                    try:
                        apply_button = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.ID, "GLUXZipUpdate"))
                        )
                        apply_button.click()
                        apply_clicked = True
                        self.log_message("通过ID找到应用按钮并点击", always_show=True)
                    except Exception as e:
                        self.log_message(f"通过ID查找应用按钮失败: {str(e)}", always_show=True)
                    
                    # 方法2: 如果方法1失败，尝试通过XPath找应用按钮
                    if not apply_clicked:
                        try:
                            apply_xpath = "//input[@type='submit'] | //span[contains(text(), 'Apply')] | //button[contains(text(), 'Apply')]"
                            apply_button = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, apply_xpath))
                            )
                            apply_button.click()
                            apply_clicked = True
                            self.log_message("通过XPath找到应用按钮并点击", always_show=True)
                        except Exception as e:
                            self.log_message(f"通过XPath查找应用按钮失败: {str(e)}", always_show=True)
                    
                    # 方法3: 尝试直接按Enter键 (如果找到了至少一个输入框)
                    if not apply_clicked and zipcode_inputs:
                        try:
                            # 找回邮政编码输入框，并按Enter键
                            zipcode_inputs[-1].send_keys(Keys.ENTER) # 对最后一个输入框发送Enter
                            apply_clicked = True
                            self.log_message("通过Enter键提交", always_show=True)
                        except Exception as e:
                            self.log_message(f"通过Enter键提交失败: {str(e)}", always_show=True)
                    
                    if not apply_clicked:
                        raise Exception("无法找到应用按钮或提交方式") # Raise an exception to trigger the retry
                    
                    break # 如果所有操作都成功，跳出内部重试循环

                except Exception as e:
                    self.log_message(f"设置位置弹窗失败，错误: {str(e)}", always_show=True)
                    if popover_retry < max_popover_retries - 1:
                        self.log_message(f"正在刷新页面并重试 ({popover_retry+2}/{max_popover_retries})...", always_show=True)
                        driver.refresh()
                        time.sleep(5) # 给页面重新加载的时间
                    else:
                        self.log_message("多次尝试后仍无法设置位置弹窗，可能需要手动设置位置", always_show=True)
                        return False # 所有重试次数耗尽，返回False
            
            # 等待位置更新
            time.sleep(3)
            
            # 确认位置或处理额外的弹窗
            try:
                # 查找可能存在的"Done"按钮
                done_buttons = driver.find_elements(By.XPATH, 
                    "//button[contains(text(), 'Done')] | //span[contains(text(), 'Done')]")
                if done_buttons:
                    done_buttons[0].click()
                    self.log_message("点击Done按钮确认位置", always_show=True)
                    time.sleep(2)
            except Exception as e:
                self.log_message(f"处理确认按钮时出错: {str(e)}", always_show=True)
            
            # 刷新页面以确保位置更新
            driver.refresh()
            time.sleep(3)
            
            # 检查位置是否已设置为正确的值
            try:
                location_text_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.ID, "glow-ingress-line2"))
                )
                location_text = location_text_element.text.strip()
                
                # 清理位置文本，移除不可见的Unicode字符
                location_text = re.sub(r'[\u200b-\u200f\u2028-\u202f\u205f-\u206f]', '', location_text)
                
                self.log_message(f"当前位置显示为: {location_text}", always_show=True)
                
                if expected_location_check_func(location_text):
                    self.log_message(f"位置已成功设置为{country_name_for_log}", always_show=True)
                    # 新增：位置设置成功后，检查并处理登录弹窗
                    self._handle_login_popup()
                    return True
                else:
                    self.log_message(f"位置设置不匹配，当前显示: {location_text}，位置设置失败", always_show=True)
                    return False # 验证不匹配，返回False
            except TimeoutException:
                self.log_message(f"验证{country_name_for_log}位置时超时，位置设置失败", always_show=True)
                return False # 验证超时，返回False
            except Exception as e:
                self.log_message(f"验证{country_name_for_log}位置时出错: {str(e)}，位置设置失败", always_show=True)
                return False # 验证出错，返回False
            
        except Exception as e:
            self.log_message(f"设置{country_name_for_log}位置时出错: {str(e)}", always_show=True)
            return False # 顶级错误，返回False

    def _handle_login_popup(self):
        """处理可能出现的登录弹窗"""
        if not self.login_username or not self.login_password:
            self.log_message("未配置登录用户名或密码，跳过登录弹窗处理", level=LogLevel.DETAILED)
            return False

        try:
            self.log_message("检查是否存在登录弹窗...", level=LogLevel.DETAILED)
            # 等待 div[class="zying-content-float"] 元素出现，最多等待5秒
            login_popup_trigger = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'div.zying-content-float'))
            )
            self.log_message("检测到登录弹窗触发元素，尝试点击...", always_show=True)
            login_popup_trigger.click()
            time.sleep(2) # 等待弹窗完全展开

            # 等待用户名输入框出现
            username_input = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.ID, 'name'))
            )
            password_input = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.ID, 'password'))
            )
            login_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
            )

            self.log_message("找到登录输入框和按钮，正在输入凭据...", always_show=True)
            username_input.clear()
            username_input.send_keys(self.login_username)
            password_input.clear()
            password_input.send_keys(self.login_password)
            
            time.sleep(1) # 短暂等待
            login_button.click()
            self.log_message("已点击登录按钮", always_show=True)
            
            time.sleep(5) # 等待登录完成和页面跳转
            self.log_message("登录弹窗处理完成", always_show=True)
            return True

        except TimeoutException:
            self.log_message("未检测到登录弹窗或登录元素超时", level=LogLevel.DETAILED)
            return False
        except Exception as e:
            self.log_message(f"处理登录弹窗时出错: {str(e)}", always_show=True)
            return False

    def stop(self):
        """设置停止标志并向队列发送停止信号，以优雅地停止工作线程"""
        self.log_message("收到停止信号，正在请求工作线程停止...", always_show=True)
        self._should_stop = True
        # 为了确保即使队列为空也能唤醒线程并处理停止标志，放入STOP信号
        try:
            self.asin_queue.put("STOP")
        except Exception as e:
            self.log_message(f"向ASIN队列发送STOP信号时出错: {str(e)}", always_show=True)

    def find_browser_pid(self):
        """查找并返回当前Edge浏览器的进程ID"""
        try:
            # 在Windows系统上，使用命令行获取Edge浏览器进程
            if platform.system() == "Windows":
                import subprocess
                import re
                
                # 方法1: 使用WebDriver内部属性获取（如果可用）
                if hasattr(self.driver, 'service') and hasattr(self.driver.service, 'process'):
                    if self.driver.service.process:
                        process = self.driver.service.process
                        pid = process.pid
                        self.log_message(f"从WebDriver服务获取到进程ID: {pid}", level=LogLevel.DETAILED)
                        
                        # 尝试查找此进程的子进程，通常浏览器是WebDriver的子进程
                        try:
                            import psutil
                            parent = psutil.Process(pid)
                            children = parent.children(recursive=True)
                            
                            # 查找msedge.exe进程
                            for child in children:
                                if 'msedge' in child.name().lower():
                                    browser_pid = child.pid
                                    self.log_message(f"找到Edge浏览器子进程ID: {browser_pid}", level=LogLevel.DETAILED)
                                    return browser_pid
                        except ImportError:
                            self.log_message("psutil库未安装，无法查找子进程", level=LogLevel.DETAILED)
                        except Exception as e:
                            self.log_message(f"查找子进程时出错: {str(e)}", level=LogLevel.DETAILED)
                
                # 方法2: 直接查找所有Edge进程
                try:
                    result = subprocess.check_output("wmic process where name='msedge.exe' get processid", shell=True)
                    result_str = result.decode('utf-8', errors='ignore')
                    # 提取所有进程ID
                    pids = re.findall(r'\d+', result_str)
                    if pids and len(pids) > 0:
                        # 记录所有找到的进程ID
                        self.log_message(f"找到的Edge进程ID列表: {', '.join(pids)}", level=LogLevel.DETAILED)
                        # 取最新的进程ID（通常是最后一个）
                        browser_pid = int(pids[-1])
                        return browser_pid
                except Exception as e:
                    self.log_message(f"查找Edge进程ID时出错: {str(e)}", level=LogLevel.DETAILED)
            
            # 非Windows系统
            else:
                try:
                    import psutil
                    # 查找名称包含edge或msedge的进程
                    for proc in psutil.process_iter(['pid', 'name']):
                        if 'edge' in proc.info['name'].lower():
                            return proc.info['pid']
                except ImportError:
                    self.log_message("psutil库未安装，无法在非Windows系统查找进程", level=LogLevel.DETAILED)
                except Exception as e:
                    self.log_message(f"在非Windows系统上查找Edge进程ID时出错: {str(e)}", level=LogLevel.DETAILED)
            
            return None
        except Exception as e:
            self.log_message(f"查找浏览器进程ID时出错: {str(e)}", level=LogLevel.DETAILED)
            return None
            
    def is_seller_id_format(self, seller_text):
        """判断卖家信息是否是ID格式（如A338C5IIRQHL0Y）
        
        Args:
            seller_text (str): 卖家文本信息
            
        Returns:
            bool: 如果是ID格式则返回True，否则返回False
        """
        if not seller_text or seller_text == 'no':
            return False
            
        # ID格式特征: 纯大写字母+数字组合，通常长度在10-20之间
        if re.match(r'^[A-Z0-9]{10,20}$', seller_text):
            self.log_message(f"检测到卖家ID格式: {seller_text}", level=LogLevel.DETAILED)
            return True
            
        # 也可能是全字母数字，但没有空格和符号的较长字符串
        if re.match(r'^[A-Za-z0-9]{10,}$', seller_text) and ' ' not in seller_text:
            self.log_message(f"检测到可能的卖家ID格式: {seller_text}", level=LogLevel.DETAILED)
            return True
            
        return False

def main():
    """主函数"""
    try:
        # 创建爬虫实例
        scraper = AmazonAsinScraper()
        
        # 设置日志级别
        scraper.set_log_level(LogLevel.NORMAL)
        
        # 运行爬虫
        scraper.run()
        
        return True
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        logger.error("程序运行出错", exc_info=True)
        return False


def create_gui():
    """创建GUI界面，使用精简版主题适合客户使用"""
    root = tk.Tk()
    
    # 设置应用程序图标和任务栏图标
    try:
        # 先设置应用程序ID - 这会帮助Windows正确识别应用并更新任务栏图标
        if platform.system() == "Windows":
            try:
                import ctypes
                app_id = "AmazonAsinScraper.App.1.0"  # 唯一应用ID
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
            except Exception:
                pass
        
        # 设置窗口图标
        icon_path = "icon.ico"
        if os.path.exists(icon_path):
            root.iconbitmap(icon_path)
            
            # 在Windows上使用多种方法设置任务栏图标
            if platform.system() == "Windows":
                try:
                    import ctypes
                    # 获取窗口根句柄 - 更可靠的方法
                    hwnd = ctypes.windll.user32.GetForegroundWindow()
                    
                    # 常量定义
                    ICON_SMALL = 0
                    ICON_BIG = 1
                    WM_SETICON = 0x0080
                    LR_LOADFROMFILE = 0x0010
                    IMAGE_ICON = 1
                    
                    # 使用绝对路径
                    abs_icon_path = os.path.abspath(icon_path)
                    
                    # 加载小图标
                    h_icon_small = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 16, 16, LR_LOADFROMFILE
                    )
                    if h_icon_small:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                    
                    # 加载大图标
                    h_icon_big = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 32, 32, LR_LOADFROMFILE
                    )
                    if h_icon_big:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                    
                    # 刷新窗口以应用更改
                    ctypes.windll.user32.UpdateWindow(hwnd)
                    
                    # 在窗口显示后再次设置图标 - 使用事件回调
                    def set_icon_after_visible():
                        try:
                            hwnd = ctypes.windll.user32.GetForegroundWindow()
                            if h_icon_small:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                            if h_icon_big:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                        except Exception:
                            pass
                    
                    # 在100ms后执行以确保窗口已完全加载
                    root.after(100, set_icon_after_visible)
                except Exception:
                    pass
    except:
        pass  # 忽略图标设置错误
    
    # 创建主题
    class AmazonUITheme:
        # 颜色方案
        COLORS = {
            "primary": "#356cac",     # 深蓝色 - 主色
            "secondary": "#4a90e2",   # 亮蓝色 - 次要色
            "accent": "#f89406",      # 橙色 - 强调色
            "background": "#f5f5f5",  # 浅灰色 - 背景色
            "text": "#333333",        # 深灰色 - 文本色
            "light_text": "#666666",  # 中灰色 - 次要文本
            "white": "#ffffff",       # 白色
            "light_gray": "#f0f0f0"   # 更浅的灰色
        }
    
    # 使用主题设置窗口
    root.title("亚马逊产品筛选工具")
    root.geometry("900x650")
    root.configure(bg=AmazonUITheme.COLORS["background"])
    
    # 应用样式
    style = ttk.Style()
    try:
        style.theme_use("clam")  # 使用clam主题作为基础
    except:
        pass  # 如果主题不可用，使用默认主题
    
    # 背景配置
    style.configure("TFrame", background=AmazonUITheme.COLORS["background"])
    style.configure("TLabelframe", background=AmazonUITheme.COLORS["background"])
    style.configure("TLabelframe.Label", background=AmazonUITheme.COLORS["background"], 
                foreground=AmazonUITheme.COLORS["primary"], font=("微软雅黑", 11, "bold"))
    
    # 按钮风格
    style.configure("TButton", 
                background=AmazonUITheme.COLORS["primary"],
                foreground=AmazonUITheme.COLORS["white"],
                font=("微软雅黑", 10),
                padding=8)
    
    style.map("TButton",
            background=[('active', AmazonUITheme.COLORS["secondary"]), 
                        ('disabled', AmazonUITheme.COLORS["light_gray"])],
            foreground=[('disabled', AmazonUITheme.COLORS["light_text"])])
    
    # 标签风格
    style.configure("TLabel", 
                background=AmazonUITheme.COLORS["background"],
                foreground=AmazonUITheme.COLORS["text"],
                font=("微软雅黑", 10))
    
    # Entry风格
    style.configure("TEntry", 
                background=AmazonUITheme.COLORS["white"],
                foreground=AmazonUITheme.COLORS["text"],
                fieldbackground=AmazonUITheme.COLORS["white"],
                padding=3)
    
    # Combobox风格
    style.configure("TCombobox", 
                background=AmazonUITheme.COLORS["white"],
                foreground=AmazonUITheme.COLORS["text"],
                fieldbackground=AmazonUITheme.COLORS["white"],
                padding=3)
    
    # 创建标题栏
    title_frame = ttk.Frame(root)
    title_frame.pack(fill=tk.X, padx=15, pady=(15, 5))
    
    title_label = ttk.Label(
        title_frame, 
        text="亚马逊产品筛选工具",
        font=("微软雅黑", 16, "bold"),
        foreground=AmazonUITheme.COLORS["primary"]
    )
    title_label.pack(side=tk.LEFT)
    
    # 添加横向分隔线
    separator = ttk.Separator(root, orient=tk.HORIZONTAL)
    separator.pack(fill=tk.X, padx=15, pady=5)
    
    # 主框架
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
    
    # 创建左右分栏
    left_frame = ttk.Frame(main_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 10))
    
    right_frame = ttk.Frame(main_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    # ===== 左侧配置区域 =====
    
    # 国家选择
    country_frame = ttk.LabelFrame(left_frame, text="国家设置", padding=10)
    country_frame.pack(fill=tk.X, pady=(0, 10))
    
    # 国家变量
    country_var = tk.StringVar(value="美国")
    
    # 国家选择下拉框
    ttk.Label(country_frame, text="选择国家:").pack(anchor=tk.W, pady=(0, 5))
    country_combo = ttk.Combobox(
        country_frame, 
        textvariable=country_var, 
        values=["美国", "日本", "加拿大"], 
        state="readonly",
        width=15
    )
    country_combo.pack(fill=tk.X, pady=5)
    
    # 文件选择框架
    file_frame = ttk.LabelFrame(left_frame, text="ASIN列表文件", padding=10)
    file_frame.pack(fill=tk.X, pady=10)
    
    # 文件路径变量
    file_path_var = tk.StringVar()
    
    # 文件选择说明标签
    ttk.Label(
        file_frame, 
        text="导入包含ASIN列表的Excel文件:",
        foreground=AmazonUITheme.COLORS["primary"],
        font=("微软雅黑", 9, "bold")
    ).pack(anchor=tk.W, pady=(0, 5))
    
    # 文件路径显示
    file_entry_frame = ttk.Frame(file_frame)
    file_entry_frame.pack(fill=tk.X, pady=5)
    
    file_path_entry = ttk.Entry(file_entry_frame, textvariable=file_path_var, width=30)
    file_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
    
    # 初始化默认文件名
    default_filename = f"amazon_results_{country_var.get()}.xlsx"
    file_path_var.set(default_filename)
    
    # 添加国家选择变化时的回调函数，更新默认文件名
    def on_country_change(*args):
        selected_country = country_var.get()
        default_filename = f"amazon_results_{selected_country}.xlsx"
        file_path_var.set(default_filename)
        
    # 绑定国家选择变化事件
    country_var.trace_add("write", on_country_change)
    
    # 隐藏的配置项（设置默认值）
    threads_var = tk.IntVar(value=1)           # 默认1个线程
    skip_summary_var = tk.BooleanVar(value=True)  # 默认跳过汇总
    resume_var = tk.BooleanVar(value=True)     # 默认启用断点续传
    log_level_var = tk.IntVar(value=0)         # 默认简易日志
    headless_var = tk.BooleanVar(value=True)   # 默认无头模式
    progress_var = tk.DoubleVar()              # 进度值
    
    # 登录凭据（默认为空）
    login_username_var = tk.StringVar(value="")
    login_password_var = tk.StringVar(value="")
    
    # 从配置文件加载登录凭据
    try:
        temp_scraper = AmazonAsinScraper()  # 创建临时实例加载配置
        login_username_var.set(temp_scraper.login_username)
        login_password_var.set(temp_scraper.login_password)
        # 同时也更新其他配置
        country_var.set(temp_scraper.country)
        headless_var.set(temp_scraper.headless_mode)
        log_level_var.set(temp_scraper.log_level)
    except Exception as e:
        print(f"加载配置失败: {str(e)}")

    def browse_file():
        """浏览文件按钮回调函数"""
        selected_country = country_var.get()
        default_filename = f"amazon_results_{selected_country}.xlsx"
        
        file_path = filedialog.askopenfilename(
            initialdir=".", 
            title=f"选择包含{selected_country}的ASINs的Excel文件",
            filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))
        )
        if file_path:
            file_path_var.set(file_path)
            status_label.config(text=f"已选择文件: {os.path.basename(file_path)}")
    
    # 导入按钮
    import_btn = ttk.Button(
        file_entry_frame, 
        text="导入文件", 
        command=browse_file,
        width=15
    )
    import_btn.pack(side=tk.RIGHT)
    
    # 文件格式提示
    file_info_frame = ttk.Frame(file_frame)
    file_info_frame.pack(fill=tk.X, pady=(5, 0))
    
    ttk.Label(
        file_info_frame, 
        text="支持的格式: Excel文件(.xlsx)，包含ASIN列或Amazon Links列",
        font=("微软雅黑", 8),
        foreground=AmazonUITheme.COLORS["light_text"]
    ).pack(anchor=tk.W)
    
    # 登录凭据框架
    login_frame = ttk.LabelFrame(left_frame, text="智赢账号", padding=10)
    login_frame.pack(fill=tk.X, pady=10)
    
    # 用户名输入框
    username_frame = ttk.Frame(login_frame)
    username_frame.pack(fill=tk.X, pady=5)
    
    ttk.Label(username_frame, text="用户:").pack(side=tk.LEFT, padx=(0, 5))
    username_entry = ttk.Entry(username_frame, textvariable=login_username_var, width=30)
    username_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True)
    
    # 密码输入框
    password_frame = ttk.Frame(login_frame)
    password_frame.pack(fill=tk.X, pady=5)
    
    ttk.Label(password_frame, text="密码:").pack(side=tk.LEFT, padx=(0, 5))
    password_entry = ttk.Entry(password_frame, textvariable=login_password_var, width=30, show="*")
    password_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True)
    
    # 按钮框架 - 将所有按钮整合到一个框架中以保持对齐
    button_frame = ttk.Frame(left_frame)
    button_frame.pack(fill=tk.X, pady=15)
    
    def save_gui_config():
        """保存GUI界面的配置到文件"""
        try:
            temp_scraper = AmazonAsinScraper() # 创建一个临时实例来访问保存配置的方法
            temp_scraper.log_level = log_level_var.get()
            temp_scraper.headless_mode = headless_var.get()
            temp_scraper.country = country_var.get()
            temp_scraper.login_username = login_username_var.get()
            temp_scraper.login_password = login_password_var.get()
            temp_scraper._save_config() # 调用保存配置方法
            messagebox.showinfo("配置", "配置已成功保存！")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置时出错: {str(e)}")
    
    # 启动按钮
    start_button = ttk.Button(
        button_frame, 
        text="开始处理", 
        command=lambda: start_processing(),
        width=15
    )
    start_button.pack(side=tk.RIGHT, padx=5)
    
    # 保存配置按钮
    save_config_btn = ttk.Button(
        button_frame,
        text="保存配置",
        command=save_gui_config,
        width=15
    )
    save_config_btn.pack(side=tk.RIGHT, padx=5)
    
    # 退出按钮
    exit_button = ttk.Button(
        button_frame, 
        text="退出", 
        command=root.destroy,
        width=15
    )
    exit_button.pack(side=tk.LEFT, padx=5)
    
    # ===== 右侧日志区域 =====
    log_frame = ttk.LabelFrame(right_frame, text="运行日志", padding=10)
    log_frame.pack(fill=tk.BOTH, expand=True)
    
    log_text = tk.Text(log_frame, wrap=tk.WORD, height=20)
    log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    scrollbar = ttk.Scrollbar(log_text, orient="vertical", command=log_text.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    log_text.configure(yscrollcommand=scrollbar.set)
    
    # 状态栏
    status_frame = ttk.Frame(root)
    status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=15, pady=5)
    
    status_label = ttk.Label(
        status_frame, 
        text="准备就绪",
        font=("微软雅黑", 9),
        foreground=AmazonUITheme.COLORS["light_text"]
    )
    status_label.pack(side=tk.LEFT)
    
    # 进度条
    progress_bar = ttk.Progressbar(status_frame, variable=progress_var, maximum=100, length=300)
    progress_bar.pack(side=tk.RIGHT, padx=10, fill=tk.X, expand=True)
    
    def start_processing():
        """开始处理按钮回调函数"""
        excel_file = file_path_var.get()
        if not excel_file:
            messagebox.showerror("错误", "请先选择Excel文件")
            return
            
        if not os.path.exists(excel_file):
            messagebox.showerror("错误", f"文件不存在: {excel_file}")
            return
        
        # 使用默认设置
        num_threads = threads_var.get()          # 固定为1
        skip_summary = skip_summary_var.get()    # 固定为True
        resume = resume_var.get()                # 固定为True
        log_level = log_level_var.get()          # 固定为简易(0)
        headless_mode = headless_var.get()       # 固定为True
        selected_country = country_var.get()
        
        status_label.config(text="处理中...")
        progress_var.set(0)
        
        # 禁用按钮，避免重复点击
        start_button.configure(state="disabled")
        import_btn.configure(state="disabled")
        
        # 使用线程执行处理，避免UI卡死
        def processing_thread():
            try:
                global running_scraper_instance
                # 创建爬虫实例
                scraper = AmazonAsinScraper()
                # 保存到全局变量中，以便关闭时访问
                running_scraper_instance = scraper
                
                # 设置选定的国家
                scraper.country = selected_country
                scraper.country_domain = scraper.countries[selected_country]["domain"]
                scraper.country_zipcode = scraper.countries[selected_country]["zipcode"]
                
                scraper.log_message(f"使用国家: {selected_country}, 域名: {scraper.country_domain}", always_show=True)
                
                # 配置日志输出到GUI
                # 创建自定义日志处理器，将日志输出到Text控件
                class TextHandler(logging.Handler):
                    def __init__(self, text_widget):
                        logging.Handler.__init__(self)
                        self.text_widget = text_widget
                        
                    def emit(self, record):
                        msg = self.format(record)
                        
                        def append():
                            self.text_widget.configure(state='normal')
                            self.text_widget.insert(tk.END, msg + '\n')
                            self.text_widget.see(tk.END)  # 自动滚动到最新日志
                            self.text_widget.configure(state='disabled')
                            
                        # 使用afterQueue避免线程问题
                        if not root.winfo_exists():
                            return  # 如果窗口已关闭，不执行操作
                        self.text_widget.after(0, append)
                
                # 添加Text控件处理器到日志
                text_handler = TextHandler(log_text)
                text_handler.setFormatter(logging.Formatter(LOG_FORMAT))
                logger.addHandler(text_handler)
                
                # 在运行前再次强制设置国家
                scraper.country = selected_country
                
                # 获取ASIN总数用于进度条
                all_asins = scraper.load_asins_from_excel(excel_file)
                total_asins_count = len(all_asins)
                
                def update_progress_ui(processed_count, total_count):
                    if not root.winfo_exists():
                        return
                    percentage = (processed_count / total_count) * 100 if total_count > 0 else 0
                    progress_var.set(percentage)
                    status_label.config(text=f"处理中: {processed_count}/{total_count} ({percentage:.2f}%)")
                
                # 运行爬虫
                scraper.run(
                    asins_file=excel_file, 
                    num_threads=num_threads, 
                    skip_summary=skip_summary,
                    resume=resume,
                    log_level=log_level,
                    headless_mode=headless_mode,
                    country=selected_country,
                    total_asins=total_asins_count,
                    progress_callback=update_progress_ui,
                    disable_search_logs=True  # 默认禁用搜索日志输出
                )
                
                # 更新UI
                def update_ui():
                    if not root.winfo_exists():
                        return  # 如果窗口已关闭，不执行操作
                    status_label.config(text="处理完成")
                    progress_var.set(100)
                    messagebox.showinfo("完成", "所有ASIN处理完成")
                    # 恢复按钮状态
                    start_button.configure(state="normal")
                    import_btn.configure(state="normal")
                
                root.after(0, update_ui)
                
            except Exception as e:
                def show_error():
                    if not root.winfo_exists():
                        return  # 如果窗口已关闭，不执行操作
                    error_msg = str(e)
                    messagebox.showerror("错误", f"处理过程中出错: {error_msg}")
                    status_label.config(text="处理出错")
                    progress_var.set(0)
                    # 记录详细错误信息到日志
                    logger.error(f"处理出错: {error_msg}", exc_info=True)
                    # 恢复按钮状态
                    start_button.configure(state="normal")
                    import_btn.configure(state="normal")
                
                root.after(0, show_error)
        
        # 启动线程
        global processing_thread_ref
        processing_thread_ref = threading.Thread(target=processing_thread, daemon=True)
        processing_thread_ref.start()
    
    # 设置窗口协议，确保窗口关闭时正确退出
    def on_closing():
        if messagebox.askokcancel("退出", "确定要退出程序吗?"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    return root


# 全局变量，用于跟踪爬虫实例和处理线程
running_scraper_instance = None
processing_thread_ref = None

if __name__ == "__main__":
    try:
        # 如果有命令行参数，则使用命令行模式
        if len(sys.argv) > 1 and sys.argv[1] == "--console":
            print("以命令行模式运行...")
            main()
        else:
            # 否则使用GUI模式
            try:
                root = create_gui()
                # 设置应用程序图标
                try:
                    icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
                    if os.path.exists(icon_path):
                        root.iconbitmap(icon_path)
                except:
                    pass  # 忽略图标设置错误
                
                # 设置窗口协议，确保窗口关闭时正确退出
                def on_closing():
                    global running_scraper_instance, processing_thread_ref # 声明使用全局变量
                    if messagebox.askokcancel("退出", "确定要退出程序吗?"):
                        # 如果有正在运行的爬虫线程，尝试优雅地停止它
                        if processing_thread_ref and processing_thread_ref.is_alive():
                            print("正在请求后台处理线程停止，请稍候...")
                            
                            # 创建进度窗口
                            close_progress = tk.Toplevel(root)
                            close_progress.title("正在关闭...")
                            close_progress.geometry("300x150")
                            close_progress.transient(root)  # 设置为root的临时子窗口
                            close_progress.grab_set()  # 模态窗口
                            
                            # 居中显示
                            close_progress.geometry("+%d+%d" % (
                                root.winfo_rootx() + (root.winfo_width() // 2) - 150,
                                root.winfo_rooty() + (root.winfo_height() // 2) - 75
                            ))
                            
                            # 添加提示和进度条
                            tk.Label(close_progress, text="正在停止处理并关闭浏览器...", padx=20, pady=10).pack()
                            progress = ttk.Progressbar(close_progress, mode="indeterminate")
                            progress.pack(fill=tk.X, padx=20, pady=10)
                            progress.start()
                            status_label = tk.Label(close_progress, text="请稍候...")
                            status_label.pack(pady=10)
                            
                            # 更新UI
                            close_progress.update()
                            
                            if running_scraper_instance:
                                # 先确保停止所有线程
                                status_label.config(text="正在停止处理线程...")
                                close_progress.update()
                                running_scraper_instance.stop() # 发送停止信号
                                
                                # 等待线程结束，设置超时
                                start_time = time.time()
                                max_wait_time = 10  # 最多等待10秒
                                
                                while processing_thread_ref.is_alive() and (time.time() - start_time) < max_wait_time:
                                    status_label.config(text=f"等待线程停止... {int(time.time() - start_time)}/{max_wait_time}秒")
                                    close_progress.update()
                                    time.sleep(0.5)
                                
                                # 主动关闭浏览器
                                status_label.config(text="正在关闭浏览器...")
                                close_progress.update()
                                try:
                                    running_scraper_instance.close_selenium_browser()
                                except Exception as e:
                                    print(f"关闭浏览器时出错: {str(e)}")
                                
                                # 终止所有残留的Edge进程
                                status_label.config(text="正在清理残留进程...")
                                close_progress.update()
                                try:
                                    if platform.system() == "Windows":
                                        os.system("taskkill /F /IM msedge.exe /T 2>nul")
                                        os.system("taskkill /F /IM msedgedriver.exe /T 2>nul")
                                except Exception as e:
                                    print(f"终止残留进程时出错: {str(e)}")
                            
                            # 关闭进度窗口
                            progress.stop()
                            close_progress.destroy()
                            
                            if processing_thread_ref.is_alive():
                                print("警告: 后台处理线程未能及时停止。已强制终止浏览器进程。")
                            else:
                                print("后台处理线程已停止。")
                        
                        root.destroy()
                
                root.protocol("WM_DELETE_WINDOW", on_closing)
                
                # 启动GUI主循环
                root.mainloop()
            except Exception as gui_error:
                print(f"GUI启动失败: {str(gui_error)}")
                logger.error("GUI启动失败", exc_info=True)
                
                # 如果GUI模式失败，尝试回退到控制台模式
                print("尝试以命令行模式运行...")
                main()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        logger.error("程序启动失败", exc_info=True)
        
        # 显示错误对话框（如果可能）
        try:
            from tkinter import messagebox
            messagebox.showerror("严重错误", f"程序启动失败: {str(e)}")
        except:
            pass
            
        # 保持控制台窗口打开，以便用户看到错误信息
        input("按回车键退出...")