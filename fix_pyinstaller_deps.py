#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复PyInstaller依赖问题的脚本
"""

import os
import sys
import subprocess
import logging
import importlib.util

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_module_location(module_name):
    """检查模块的安装位置和版本"""
    try:
        spec = importlib.util.find_spec(module_name)
        if spec is None:
            return None, None
        
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'unknown')
        location = spec.origin if spec.origin else 'built-in'
        
        return version, location
    except Exception as e:
        return None, str(e)

def diagnose_dependencies():
    """诊断依赖问题"""
    
    logger.info("🔍 诊断依赖问题...")
    
    critical_deps = {
        'fake_useragent': 'fake-useragent',
        'openpyxl': 'openpyxl',
        'pandas': 'pandas',
        'requests': 'requests',
        'selenium': 'selenium',
        'bs4': 'beautifulsoup4',  # beautifulsoup4的导入名是bs4
        'lxml': 'lxml',
        'cryptography': 'cryptography',
        'PyInstaller': 'pyinstaller'
    }
    
    issues = []
    
    for module_name, pip_name in critical_deps.items():
        version, location = check_module_location(module_name)
        
        if version is None:
            logger.error(f"❌ {module_name} - 未安装")
            issues.append(f"pip install {pip_name}")
        else:
            logger.info(f"✅ {module_name} - v{version}")
            if location != 'built-in':
                logger.info(f"   位置: {location}")
    
    return issues

def test_fake_useragent():
    """专门测试fake_useragent的问题"""
    
    logger.info("🧪 测试fake_useragent...")
    
    try:
        from fake_useragent import UserAgent
        ua = UserAgent()
        user_agent = ua.random
        logger.info(f"✅ fake_useragent工作正常: {user_agent[:50]}...")
        return True
    except Exception as e:
        logger.error(f"❌ fake_useragent测试失败: {e}")
        
        # 尝试修复
        logger.info("尝试修复fake_useragent...")
        try:
            # 清除缓存
            import tempfile
            import shutil
            cache_dir = os.path.join(tempfile.gettempdir(), 'fake_useragent')
            if os.path.exists(cache_dir):
                shutil.rmtree(cache_dir)
                logger.info("清除fake_useragent缓存")
            
            # 重新测试
            from fake_useragent import UserAgent
            ua = UserAgent()
            user_agent = ua.random
            logger.info(f"✅ fake_useragent修复成功: {user_agent[:50]}...")
            return True
        except Exception as e2:
            logger.error(f"❌ fake_useragent修复失败: {e2}")
            return False

def test_openpyxl():
    """专门测试openpyxl的问题"""
    
    logger.info("🧪 测试openpyxl...")
    
    try:
        import openpyxl
        from openpyxl import Workbook
        
        # 创建测试工作簿
        wb = Workbook()
        ws = wb.active
        ws['A1'] = "测试"
        
        # 测试保存
        import tempfile
        test_file = os.path.join(tempfile.gettempdir(), "test_openpyxl.xlsx")
        wb.save(test_file)
        
        # 测试读取
        wb2 = openpyxl.load_workbook(test_file)
        value = wb2.active['A1'].value
        
        # 清理
        os.remove(test_file)
        
        if value == "测试":
            logger.info("✅ openpyxl工作正常")
            return True
        else:
            logger.error("❌ openpyxl数据读写异常")
            return False
            
    except Exception as e:
        logger.error(f"❌ openpyxl测试失败: {e}")
        return False

def create_hook_files():
    """创建PyInstaller hook文件来解决依赖问题"""
    
    logger.info("📝 创建PyInstaller hook文件...")
    
    # 创建hooks目录
    hooks_dir = "hooks"
    os.makedirs(hooks_dir, exist_ok=True)
    
    # fake_useragent hook
    fake_useragent_hook = '''
# Hook for fake_useragent
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集所有数据文件
datas = collect_data_files('fake_useragent')

# 收集所有子模块
hiddenimports = collect_submodules('fake_useragent')

# 添加额外的隐藏导入
hiddenimports += [
    'fake_useragent.fake',
    'fake_useragent.utils',
    'fake_useragent.settings',
    'fake_useragent.errors',
]
'''
    
    with open(os.path.join(hooks_dir, 'hook-fake_useragent.py'), 'w', encoding='utf-8') as f:
        f.write(fake_useragent_hook)
    
    # openpyxl hook
    openpyxl_hook = '''
# Hook for openpyxl
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集所有数据文件
datas = collect_data_files('openpyxl')

# 收集所有子模块
hiddenimports = collect_submodules('openpyxl')

# 添加额外的隐藏导入
hiddenimports += [
    'openpyxl.workbook.workbook',
    'openpyxl.worksheet.worksheet',
    'openpyxl.cell.cell',
    'openpyxl.styles',
    'openpyxl.utils',
    'openpyxl.reader.excel',
    'openpyxl.writer.excel',
    'openpyxl.xml.functions',
    'openpyxl.xml.constants',
]
'''
    
    with open(os.path.join(hooks_dir, 'hook-openpyxl.py'), 'w', encoding='utf-8') as f:
        f.write(openpyxl_hook)
    
    logger.info(f"✅ Hook文件创建完成: {hooks_dir}/")
    return hooks_dir

def reinstall_problematic_packages():
    """重新安装可能有问题的包"""
    
    logger.info("🔄 重新安装可能有问题的包...")
    
    packages = [
        'fake-useragent',
        'openpyxl',
        'pyinstaller'
    ]
    
    for package in packages:
        try:
            logger.info(f"重新安装 {package}...")
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', 
                '--upgrade', '--force-reinstall', package
            ], check=True, capture_output=True, text=True)
            logger.info(f"✅ {package} 重新安装成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {package} 重新安装失败: {e}")

def main():
    """主函数"""
    
    logger.info("=" * 60)
    logger.info("🔧 PyInstaller依赖问题修复工具")
    logger.info("=" * 60)
    
    # 1. 诊断依赖
    issues = diagnose_dependencies()
    
    if issues:
        logger.warning("发现依赖问题，建议执行以下命令:")
        for issue in issues:
            logger.warning(f"  {issue}")
        
        # 询问是否自动修复
        try:
            response = input("\n是否自动重新安装有问题的包? (y/n): ")
            if response.lower() == 'y':
                reinstall_problematic_packages()
        except KeyboardInterrupt:
            logger.info("用户取消操作")
            return
    
    # 2. 测试关键模块
    logger.info("\n" + "=" * 40)
    logger.info("测试关键模块")
    logger.info("=" * 40)
    
    fake_useragent_ok = test_fake_useragent()
    openpyxl_ok = test_openpyxl()
    
    # 3. 创建hook文件
    if not fake_useragent_ok or not openpyxl_ok:
        hooks_dir = create_hook_files()
        logger.info(f"\n💡 建议在构建时使用hook文件:")
        logger.info(f"   pyinstaller --additional-hooks-dir={hooks_dir} ...")
    
    # 4. 给出建议
    logger.info("\n" + "=" * 40)
    logger.info("修复建议")
    logger.info("=" * 40)
    
    if fake_useragent_ok and openpyxl_ok:
        logger.info("✅ 所有关键模块测试通过")
        logger.info("建议使用 build_with_spec.py 进行构建")
    else:
        logger.warning("⚠️  仍有模块存在问题")
        logger.info("建议:")
        logger.info("1. 使用虚拟环境重新安装依赖")
        logger.info("2. 使用 build_with_spec.py 进行构建")
        logger.info("3. 如果问题持续，考虑降级到兼容版本")

if __name__ == "__main__":
    main()
