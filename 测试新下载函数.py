#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新下载函数 - 验证修复效果
"""

import sys
import os
import time
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext

class DownloadTester:
    """下载测试器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧪 下载函数测试器")
        self.root.geometry("600x500")
        
        self.create_gui()
        
    def create_gui(self):
        """创建GUI"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="🧪 下载函数测试器",
            font=("微软雅黑", 16, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=10)
        
        # 说明
        info_label = tk.Label(
            self.root,
            text="测试新的下载函数是否能解决99.8%问题",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        info_label.pack()
        
        # 进度区域
        progress_frame = tk.LabelFrame(self.root, text="下载进度", font=("微软雅黑", 10, "bold"))
        progress_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=500
        )
        self.progress_bar.pack(pady=10)
        
        # 进度文本
        self.progress_text = tk.Label(
            progress_frame,
            text="0.0%",
            font=("微软雅黑", 14, "bold"),
            fg="#27ae60"
        )
        self.progress_text.pack(pady=5)
        
        # 状态标签
        self.status_label = tk.Label(
            progress_frame,
            text="准备测试...",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        self.status_label.pack(pady=5)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        self.test_button = tk.Button(
            button_frame,
            text="🚀 开始测试下载",
            command=self.start_test,
            font=("微软雅黑", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        self.test_button.pack(side=tk.LEFT, padx=10)
        
        self.stop_button = tk.Button(
            button_frame,
            text="⏹️ 停止测试",
            command=self.stop_test,
            font=("微软雅黑", 12),
            bg="#e74c3c",
            fg="white",
            padx=20,
            pady=10,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=10)
        
        # 日志区域
        log_frame = tk.LabelFrame(self.root, text="测试日志", font=("微软雅黑", 10, "bold"))
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 测试状态
        self.is_testing = False
        self.test_thread = None
    
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "ERROR":
            prefix = "❌"
            color = "red"
        elif level == "SUCCESS":
            prefix = "✅"
            color = "green"
        elif level == "WARNING":
            prefix = "⚠️"
            color = "orange"
        else:
            prefix = "ℹ️"
            color = "black"
        
        log_message = f"[{timestamp}] {prefix} {message}"
        
        # 在主线程中更新UI
        def update_log():
            self.log_text.insert(tk.END, log_message + "\n")
            self.log_text.see(tk.END)
            self.root.update()
        
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)
        
        print(log_message)
    
    def update_progress(self, progress):
        """更新进度"""
        def update_ui():
            self.progress_var.set(progress)
            self.progress_text.config(text=f"{progress:.1f}%")
            
            # 根据进度改变颜色
            if progress >= 100.0:
                self.progress_text.config(fg="#27ae60")  # 绿色
                self.status_label.config(text="下载完成!", fg="#27ae60")
            elif progress >= 99.0:
                self.progress_text.config(fg="#f39c12")  # 橙色
                self.status_label.config(text="即将完成...", fg="#f39c12")
            else:
                self.progress_text.config(fg="#3498db")  # 蓝色
                self.status_label.config(text=f"正在下载... {progress:.1f}%", fg="#3498db")
        
        if threading.current_thread() == threading.main_thread():
            update_ui()
        else:
            self.root.after(0, update_ui)
    
    def start_test(self):
        """开始测试"""
        if self.is_testing:
            return
        
        self.is_testing = True
        self.test_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 清空日志
        self.log_text.delete(1.0, tk.END)
        
        # 重置进度
        self.update_progress(0)
        
        def test_thread():
            try:
                self.log("开始测试新的下载函数...", "INFO")
                
                # 导入auto_updater模块
                try:
                    import auto_updater
                    self.log("成功导入auto_updater模块", "SUCCESS")
                except Exception as e:
                    self.log(f"导入auto_updater失败: {e}", "ERROR")
                    return
                
                # 创建AutoUpdater实例
                try:
                    updater = auto_updater.AutoUpdater(
                        current_version="2.1.0",
                        server_url="http://198.23.135.176:5000",
                        license_key="ADMIN_BYPASS",
                        device_id="ADMIN-DEVICE-001"
                    )
                    self.log("创建AutoUpdater实例成功", "SUCCESS")
                except Exception as e:
                    self.log(f"创建AutoUpdater实例失败: {e}", "ERROR")
                    return
                
                # 检查更新
                try:
                    self.log("检查更新信息...", "INFO")
                    update_info = updater.check_for_updates()
                    if not update_info:
                        self.log("当前已是最新版本", "WARNING")
                        return
                    
                    self.log(f"发现更新: {update_info.get('version', 'Unknown')}", "SUCCESS")
                    file_size = update_info.get('file_size', 0)
                    self.log(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)", "INFO")
                    
                except Exception as e:
                    self.log(f"检查更新失败: {e}", "ERROR")
                    return
                
                # 开始下载测试
                try:
                    self.log("开始下载测试...", "INFO")
                    self.log("注意观察进度是否能达到100%", "INFO")
                    
                    # 下载文件
                    downloaded_file = updater.download_update(update_info, self.update_progress)
                    
                    if downloaded_file and os.path.exists(downloaded_file):
                        actual_size = os.path.getsize(downloaded_file)
                        self.log(f"下载成功! 文件路径: {downloaded_file}", "SUCCESS")
                        self.log(f"实际文件大小: {actual_size:,} 字节", "SUCCESS")
                        
                        # 验证文件大小
                        expected_size = update_info.get('file_size', 0)
                        if expected_size > 0:
                            size_diff = abs(actual_size - expected_size)
                            size_percent = (size_diff / expected_size) * 100
                            self.log(f"大小差异: {size_diff:,} 字节 ({size_percent:.2f}%)", "INFO")
                            
                            if size_diff <= max(1024*1024, expected_size*0.05):
                                self.log("文件大小验证通过!", "SUCCESS")
                            else:
                                self.log("文件大小验证失败!", "WARNING")
                        
                        # 确保进度显示100%
                        self.update_progress(100.0)
                        self.log("🎉 测试完成! 下载成功达到100%", "SUCCESS")
                        
                        # 清理测试文件
                        try:
                            os.remove(downloaded_file)
                            self.log("已清理测试文件", "INFO")
                        except:
                            pass
                    else:
                        self.log("下载失败! 未获得有效文件", "ERROR")
                        self.status_label.config(text="下载失败!", fg="#e74c3c")
                
                except Exception as e:
                    self.log(f"下载测试失败: {e}", "ERROR")
                    self.status_label.config(text="测试失败!", fg="#e74c3c")
                
            except Exception as e:
                self.log(f"测试过程出错: {e}", "ERROR")
            
            finally:
                # 恢复按钮状态
                def restore_buttons():
                    self.is_testing = False
                    self.test_button.config(state=tk.NORMAL)
                    self.stop_button.config(state=tk.DISABLED)
                
                self.root.after(0, restore_buttons)
        
        self.test_thread = threading.Thread(target=test_thread, daemon=True)
        self.test_thread.start()
    
    def stop_test(self):
        """停止测试"""
        self.is_testing = False
        self.log("用户停止测试", "WARNING")
        self.status_label.config(text="测试已停止", fg="#f39c12")

def main():
    """主函数"""
    app = DownloadTester()
    app.root.mainloop()

if __name__ == "__main__":
    main()
