#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细诊断服务状态 - 等待服务启动并进行全面检查
"""

import paramiko
import time
import requests

def diagnose_service():
    """详细诊断服务状态"""
    print("🔍 详细诊断服务状态")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 等待服务启动（最多60秒）
        print("⏳ 步骤1: 等待服务完全启动...")
        max_wait = 60
        wait_time = 0
        
        while wait_time < max_wait:
            stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
            status = stdout.read().decode('utf-8').strip()
            
            print(f"   [{wait_time:02d}s] 服务状态: {status}")
            
            if status == "active":
                print("   ✅ 服务已完全启动")
                break
            elif status == "failed":
                print("   ❌ 服务启动失败")
                break
            
            time.sleep(5)
            wait_time += 5
        
        # 步骤2: 检查详细服务状态
        print("\n🔄 步骤2: 检查详细服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl status license-manager --no-pager -l")
        status_output = stdout.read().decode('utf-8')
        print(f"   详细状态:\n{status_output}")
        
        # 步骤3: 检查最近的日志
        print("\n📋 步骤3: 检查最近的服务日志...")
        stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '2 minutes ago' --no-pager")
        log_output = stdout.read().decode('utf-8')
        print(f"   最近日志:\n{log_output}")
        
        # 步骤4: 检查Python进程
        print("\n🐍 步骤4: 检查Python进程...")
        stdin, stdout, stderr = client.exec_command("ps aux | grep license_server.py | grep -v grep")
        process_output = stdout.read().decode('utf-8')
        
        if process_output.strip():
            print(f"   ✅ Python进程运行中:\n{process_output}")
        else:
            print("   ❌ 未找到Python进程")
        
        # 步骤5: 检查端口占用
        print("\n🌐 步骤5: 检查端口占用...")
        stdin, stdout, stderr = client.exec_command("netstat -tlnp | grep :5000")
        port_output = stdout.read().decode('utf-8')
        
        if port_output.strip():
            print(f"   ✅ 端口5000已监听:\n{port_output}")
        else:
            print("   ❌ 端口5000未监听")
            
            # 检查其他可能的端口
            stdin, stdout, stderr = client.exec_command("netstat -tlnp | grep python")
            python_ports = stdout.read().decode('utf-8')
            if python_ports.strip():
                print(f"   Python进程监听的端口:\n{python_ports}")
        
        # 步骤6: 检查文件完整性
        print("\n📁 步骤6: 检查文件完整性...")
        stdin, stdout, stderr = client.exec_command(f"ls -la {config['deploy_path']}/license_server.py")
        file_info = stdout.read().decode('utf-8')
        print(f"   文件信息: {file_info.strip()}")
        
        stdin, stdout, stderr = client.exec_command(f"tail -5 {config['deploy_path']}/license_server.py")
        file_tail = stdout.read().decode('utf-8')
        print(f"   文件末尾:\n{file_tail}")
        
        # 步骤7: 测试API可用性
        print("\n🧪 步骤7: 测试API可用性...")
        
        # 先检查基本连接
        server_urls = [
            "http://**************:5000",
            "http://**************:44285"  # 之前发现的端口
        ]
        
        working_url = None
        for url in server_urls:
            try:
                print(f"   测试连接: {url}")
                response = requests.get(url, timeout=10)
                print(f"   ✅ {url}: HTTP {response.status_code}")
                working_url = url
                break
            except Exception as e:
                print(f"   ❌ {url}: 连接失败 - {e}")
        
        if working_url:
            # 测试新的API路径
            test_apis = [
                ("检查更新", f"{working_url}/update/check?current_version=1.0.0"),
                ("版本列表", f"{working_url}/update/versions"),
                ("原有API", f"{working_url}/check_license")
            ]
            
            success_count = 0
            for name, test_url in test_apis:
                try:
                    response = requests.get(test_url, timeout=10)
                    if response.status_code == 200:
                        print(f"   ✅ {name}: HTTP {response.status_code}")
                        success_count += 1
                    else:
                        print(f"   ❌ {name}: HTTP {response.status_code}")
                except Exception as e:
                    print(f"   ❌ {name}: 连接失败 - {e}")
            
            print(f"\n📊 API测试结果: {success_count}/{len(test_apis)} 个接口正常")
        
        # 步骤8: 提供修复建议
        print("\n💡 步骤8: 修复建议...")
        
        # 获取最终服务状态
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        final_status = stdout.read().decode('utf-8').strip()
        
        if final_status == "active":
            print("   ✅ 服务运行正常")
            if working_url:
                print(f"   🌐 服务地址: {working_url}")
                print("   💡 建议: 更新客户端配置使用新的API路径")
            else:
                print("   ⚠️ 服务运行但API不可访问，可能需要检查防火墙")
        elif final_status == "activating":
            print("   ⏳ 服务仍在启动中，建议等待更长时间")
        elif final_status == "failed":
            print("   ❌ 服务启动失败，建议检查日志并修复代码问题")
        else:
            print(f"   ❓ 服务状态未知: {final_status}")
        
        # 关闭连接
        client.close()
        
        return final_status == "active"
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 详细诊断服务状态")
        print("🔧 方案: 等待启动 + 全面检查")
        print()
        
        if diagnose_service():
            print("\n🎉 服务诊断完成 - 服务正常运行！")
        else:
            print("\n⚠️ 服务诊断完成 - 发现问题需要修复")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
