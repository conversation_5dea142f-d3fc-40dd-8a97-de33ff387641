#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试下载进度问题 - 详细分析99.8%停止的原因
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import requests
import os
import tempfile
import time
import threading

class DownloadProgressDebugger:
    """下载进度调试器"""
    
    def __init__(self):
        self.server_url = "http://198.23.135.176:5000"
        self.license_key = "ADMIN_BYPASS"
        self.device_id = "ADMIN-DEVICE-001"
        
    def create_gui(self):
        """创建GUI界面"""
        self.root = tk.Tk()
        self.root.title("下载进度调试器")
        self.root.geometry("900x700")
        
        # 设置图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 标题
        title_label = tk.Label(
            self.root,
            text="🔍 下载进度调试器",
            font=("微软雅黑", 16, "bold"),
            pady=10
        )
        title_label.pack()
        
        # 说明
        info_label = tk.Label(
            self.root,
            text="专门调试99.8%或99.99%停止的下载问题",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        info_label.pack()
        
        # 日志区域
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=30, width=100)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        # 调试下载按钮
        debug_button = tk.Button(
            button_frame,
            text="🔍 调试下载",
            command=self.start_debug_download,
            font=("微软雅黑", 12),
            bg="#e74c3c",
            fg="white",
            padx=20,
            pady=10
        )
        debug_button.pack(side=tk.LEFT, padx=10)
        
        # 清空日志按钮
        clear_button = tk.Button(
            button_frame,
            text="🗑️ 清空日志",
            command=self.clear_log,
            font=("微软雅黑", 12),
            bg="#95a5a6",
            fg="white",
            padx=20,
            pady=10
        )
        clear_button.pack(side=tk.LEFT, padx=10)
        
        self.log("🔍 下载进度调试器已启动")
        self.log("💡 点击'调试下载'开始详细分析")
        
        return self.root
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.insert(tk.END, log_message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
        print(log_message)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def start_debug_download(self):
        """开始调试下载"""
        self.clear_log()
        self.log("🔍 开始调试下载进度问题...")
        
        def debug_thread():
            try:
                # 1. 获取更新信息
                self.log("\n1️⃣ 获取更新信息...")
                update_info = self.get_update_info()
                if not update_info:
                    self.log("❌ 获取更新信息失败")
                    return
                
                version = update_info.get('version')
                expected_size = update_info.get('file_size', 0)
                
                self.log(f"📊 版本: {version}")
                self.log(f"📦 预期大小: {expected_size:,} 字节 ({expected_size/1024/1024:.2f}MB)")
                
                # 2. 准备下载
                self.log("\n2️⃣ 准备下载...")
                temp_file = os.path.join(tempfile.gettempdir(), f"debug_download_{version}.exe")
                
                # 删除旧文件
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self.log(f"🗑️ 删除旧文件: {temp_file}")
                
                # 3. 开始下载
                self.log("\n3️⃣ 开始详细下载...")
                self.debug_download(version, temp_file, expected_size)
                
            except Exception as e:
                self.log(f"❌ 调试过程异常: {e}")
                import traceback
                self.log(f"📄 详细错误: {traceback.format_exc()}")
        
        # 在后台线程运行调试
        thread = threading.Thread(target=debug_thread, daemon=True)
        thread.start()
    
    def get_update_info(self):
        """获取更新信息"""
        try:
            url = f"{self.server_url}/update/check"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'current_version': '2.1.0'
            }
            
            response = requests.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('has_update'):
                    return data.get('update_info')
            
            return None
            
        except Exception as e:
            self.log(f"❌ 获取更新信息异常: {e}")
            return None
    
    def debug_download(self, version, temp_file, expected_size):
        """调试下载过程"""
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': version
            }
            
            self.log("🚀 发送下载请求...")
            
            response = requests.get(
                url, 
                params=params,
                stream=True,
                timeout=(60, 3600)
            )
            
            if response.status_code != 200:
                self.log(f"❌ HTTP错误: {response.status_code}")
                return
            
            # 检查响应头
            content_length = response.headers.get('Content-Length')
            if content_length:
                actual_size = int(content_length)
                self.log(f"📡 服务器返回大小: {actual_size:,} 字节 ({actual_size/1024/1024:.2f}MB)")
                
                if actual_size != expected_size:
                    self.log(f"⚠️ 大小不匹配! 预期: {expected_size:,}, 实际: {actual_size:,}")
                else:
                    self.log("✅ 文件大小匹配")
            else:
                self.log("⚠️ 服务器未返回Content-Length")
                actual_size = expected_size
            
            # 开始下载
            self.log("\n📥 开始逐块下载...")
            downloaded_size = 0
            chunk_count = 0
            last_progress = 0
            
            with open(temp_file, 'wb') as f:
                chunk_size = 32 * 1024  # 32KB
                
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        chunk_len = len(chunk)
                        f.write(chunk)
                        f.flush()  # 强制刷新缓冲区
                        
                        downloaded_size += chunk_len
                        chunk_count += 1
                        
                        # 计算进度
                        if actual_size > 0:
                            progress = (downloaded_size / actual_size) * 100
                        else:
                            progress = 0
                        
                        # 详细日志（每1%或每1000个chunk）
                        if int(progress) > last_progress or chunk_count % 1000 == 0:
                            self.log(f"📊 块#{chunk_count}: +{chunk_len:,}字节, 总计:{downloaded_size:,}字节, 进度:{progress:.3f}%")
                            last_progress = int(progress)
                        
                        # 特别关注99%以后的情况
                        if progress >= 99.0:
                            self.log(f"🔍 高进度块#{chunk_count}: +{chunk_len:,}字节, 总计:{downloaded_size:,}字节, 进度:{progress:.6f}%")
            
            # 下载完成后的检查
            self.log(f"\n✅ 下载循环结束")
            self.log(f"📊 最终统计:")
            self.log(f"  - 总块数: {chunk_count:,}")
            self.log(f"  - 下载字节: {downloaded_size:,}")
            self.log(f"  - 预期字节: {expected_size:,}")
            self.log(f"  - 服务器字节: {actual_size:,}")
            
            # 检查文件
            if os.path.exists(temp_file):
                file_size = os.path.getsize(temp_file)
                self.log(f"📁 文件系统大小: {file_size:,} 字节")
                
                if file_size == downloaded_size:
                    self.log("✅ 内存计数与文件大小一致")
                else:
                    self.log(f"❌ 大小不一致! 内存:{downloaded_size:,}, 文件:{file_size:,}")
                
                if file_size == expected_size:
                    self.log("🎉 文件下载完整!")
                    final_progress = 100.0
                else:
                    final_progress = (file_size / expected_size) * 100
                    self.log(f"⚠️ 文件不完整: {final_progress:.6f}%")
                
                self.log(f"🎯 最终进度: {final_progress:.6f}%")
                
                # 分析问题
                if final_progress < 100.0:
                    missing_bytes = expected_size - file_size
                    self.log(f"❌ 缺少字节: {missing_bytes:,}")
                    self.log(f"💡 可能原因:")
                    self.log(f"  1. 网络连接在最后时刻中断")
                    self.log(f"  2. 服务器提前关闭连接")
                    self.log(f"  3. 文件写入缓冲区问题")
                    self.log(f"  4. 进度计算错误")
                
            else:
                self.log("❌ 文件不存在!")
            
        except Exception as e:
            self.log(f"❌ 下载异常: {e}")
            import traceback
            self.log(f"📄 详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    debugger = DownloadProgressDebugger()
    root = debugger.create_gui()
    root.mainloop()

if __name__ == "__main__":
    main()
