#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试修复效果
"""

import requests

def test_final_fix():
    """最终测试所有修复效果"""
    print("🎯 最终测试所有修复效果")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 测试1: 管理员权限（应该成功）
    print("🔧 测试1: 管理员权限")
    admin_params = {
        "current_version": "2.0.0",
        "key": "ADMIN_BYPASS",
        "device_id": "ADMIN-DEVICE-001"
    }
    
    try:
        response = requests.get(
            f"{server_url}/update/check",
            params=admin_params,
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 管理员权限测试成功")
            print(f"📄 成功: {data.get('success', False)}")
            print(f"📄 有更新: {data.get('has_update', False)}")
            print(f"📄 当前版本: {data.get('current_version', 'Unknown')}")
            print(f"📄 最新版本: {data.get('latest_version', 'Unknown')}")
        else:
            print(f"❌ 管理员权限测试失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 管理员测试失败: {e}")
    
    # 测试2: 用户权限（可能认证失败，但不应该是500错误）
    print("\n👤 测试2: 用户权限")
    user_params = {
        "current_version": "2.0.0",
        "key": "83R2AXQK-20250725-67c80d8d",
        "device_id": "20cc47fd9ca63e67"
    }
    
    try:
        response = requests.get(
            f"{server_url}/update/check",
            params=user_params,
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 用户权限测试成功")
            print(f"📄 响应: {data}")
        elif response.status_code == 401:
            data = response.json()
            print("✅ datetime修复成功（认证失败是正常的）")
            print(f"📄 认证错误: {data.get('message', 'Unknown')}")
        elif response.status_code == 500:
            print("❌ 仍然有500错误")
            print(f"📄 错误: {response.text}")
        else:
            print(f"⚠️ 其他状态码: {response.status_code}")
            print(f"📄 响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 用户测试失败: {e}")
    
    # 测试3: 其他API端点
    print("\n🔍 测试3: 其他API端点")
    
    endpoints = [
        {
            "name": "根路径",
            "url": "/",
            "method": "GET"
        },
        {
            "name": "更新统计",
            "url": "/update/stats",
            "method": "GET"
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n📡 测试: {endpoint['name']}")
        
        try:
            response = requests.get(f"{server_url}{endpoint['url']}", timeout=5)
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 端点正常")
            else:
                print("⚠️ 端点异常")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def test_license_client_simulation():
    """模拟license_client.py的自动更新检查"""
    print("\n🎯 模拟license_client.py自动更新检查")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 模拟用户启动license_client.py时的自动更新检查
    user_params = {
        "current_version": "2.1.0",  # 当前版本
        "key": "83R2AXQK-20250725-67c80d8d",
        "device_id": "20cc47fd9ca63e67"
    }
    
    print(f"📋 模拟参数: {user_params}")
    
    try:
        response = requests.get(
            f"{server_url}/update/check",
            params=user_params,
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("🎉 自动更新检查成功！")
            print(f"📄 成功: {data.get('success', False)}")
            print(f"📄 有更新: {data.get('has_update', False)}")
            print(f"📄 当前版本: {data.get('current_version', 'Unknown')}")
            print(f"📄 最新版本: {data.get('latest_version', 'Unknown')}")
            
            if data.get('has_update'):
                print("🔄 有可用更新")
            else:
                print("✅ 已是最新版本")
                
        elif response.status_code == 401:
            data = response.json()
            print("🚫 认证失败（需要有效的激活码）")
            print(f"📄 错误信息: {data.get('message', 'Unknown')}")
            
            # 分析认证失败的原因
            message = data.get('message', '')
            if "激活码不存在" in message:
                print("💡 原因: 激活码不在服务器数据库中")
            elif "激活码已过期" in message:
                print("💡 原因: 激活码已过期")
            elif "设备不匹配" in message:
                print("💡 原因: 设备ID不匹配")
            else:
                print("💡 原因: 其他认证问题")
                
        elif response.status_code == 500:
            print("❌ 服务器内部错误（修复失败）")
            print(f"📄 错误: {response.text}")
            
        else:
            print(f"⚠️ 未知状态码: {response.status_code}")
            print(f"📄 响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")

def main():
    """主函数"""
    print("🎯 最终测试修复效果")
    print("🔧 修复内容:")
    print("  1. license_client.py: 端口44285 → 5000")
    print("  2. auto_updater.py: API路径 /api/check_update → /update/check")
    print("  3. license_server.py: 添加Python 3.6兼容的日期解析")
    print("  4. 支持多种日期格式: ISO格式 + YYYYMMDD格式")
    print()
    
    test_final_fix()
    test_license_client_simulation()
    
    print("\n" + "=" * 50)
    print("📋 修复总结:")
    print("✅ 客户端端口配置正确")
    print("✅ 自动更新API路径正确")
    print("✅ 服务器Python 3.6兼容性修复")
    print("✅ 日期格式解析支持多种格式")
    
    print("\n🎯 结论:")
    print("1. 如果管理员权限测试成功 → 服务器修复完成")
    print("2. 如果用户权限返回401 → datetime修复成功，需要有效激活码")
    print("3. 如果用户权限返回200 → 完全成功，可以正常使用")
    print("4. 如果仍然返回500 → 需要进一步检查")
    
    print("\n🚀 现在可以启动license_client.py测试了！")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
