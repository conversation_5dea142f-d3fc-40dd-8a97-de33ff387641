#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复auto_updater.py - 移除复杂的断点续传逻辑
"""

import shutil
import os

def fix_auto_updater():
    """修复auto_updater.py"""
    print("🔧 修复auto_updater.py")
    print("=" * 50)
    
    try:
        # 备份原文件
        if os.path.exists("auto_updater.py"):
            shutil.copy2("auto_updater.py", "auto_updater_backup.py")
            print("✅ 已备份原文件为 auto_updater_backup.py")
        
        # 读取原文件
        with open("auto_updater.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到download_update方法并替换
        new_download_method = '''    def download_update(self, update_info, progress_callback=None):
        """
        下载更新文件 - 简化版，不使用断点续传
        
        Args:
            update_info: 更新信息
            progress_callback: 进度回调函数
            
        Returns:
            str: 下载的文件路径，失败返回None
        """
        try:
            # 获取下载信息
            version = update_info.get('version')
            file_size = update_info.get('file_size', 0)
            
            if not version:
                return None
            
            # 创建临时文件
            import tempfile
            temp_file = os.path.join(tempfile.gettempdir(), f"amazon_blueprint_update_{version}.exe")
            
            # 删除可能存在的旧文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            
            # 构建下载URL
            from urllib.parse import urljoin
            url = urljoin(self.server_url, "/update/download")
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': version
            }
            
            max_retries = 3
            retry_count = 0
            
            while retry_count < max_retries:
                try:
                    # 发送请求 - 不使用Range头，完整下载
                    response = requests.get(
                        url, 
                        params=params,
                        stream=True,
                        timeout=(60, 3600)  # 1小时超时
                    )
                    
                    if response.status_code != 200:
                        raise Exception(f"HTTP错误: {response.status_code}")
                    
                    # 获取文件大小
                    actual_file_size = int(response.headers.get('Content-Length', file_size))
                    downloaded_size = 0
                    
                    # 下载文件
                    with open(temp_file, 'wb') as f:
                        chunk_size = 32 * 1024  # 32KB chunks
                        
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)
                                
                                # 更新进度
                                if progress_callback and actual_file_size > 0:
                                    progress = (downloaded_size / actual_file_size) * 100
                                    progress_callback(progress)
                    
                    # 验证下载完整性
                    if downloaded_size >= actual_file_size:
                        return temp_file
                    else:
                        raise Exception(f"下载不完整: {downloaded_size}/{actual_file_size}")
                        
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        return None
                    
                    # 删除可能损坏的文件
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass
                    
                    # 等待后重试
                    import time
                    time.sleep(retry_count * 5)
            
            return None
            
        except Exception as e:
            return None'''
        
        # 使用正则表达式找到并替换download_update方法
        import re
        
        # 找到方法的开始
        pattern = r'(\s+def download_update\(self[^:]*\):.*?)(\n\s+def|\nclass|\nif __name__|$)'
        
        def replace_method(match):
            indent = match.group(1)[:match.group(1).find('def')]
            return new_download_method + match.group(2)
        
        new_content = re.sub(pattern, replace_method, content, flags=re.DOTALL)
        
        # 如果没有找到方法，说明可能格式不同，尝试其他方式
        if new_content == content:
            print("⚠️ 未找到download_update方法，尝试其他方式...")
            
            # 查找方法位置
            lines = content.split('\n')
            start_line = -1
            end_line = -1
            indent_level = 0
            
            for i, line in enumerate(lines):
                if 'def download_update(' in line:
                    start_line = i
                    indent_level = len(line) - len(line.lstrip())
                    break
            
            if start_line >= 0:
                # 找到方法结束位置
                for i in range(start_line + 1, len(lines)):
                    line = lines[i]
                    if line.strip() == '':
                        continue
                    current_indent = len(line) - len(line.lstrip())
                    if current_indent <= indent_level and (line.strip().startswith('def ') or line.strip().startswith('class ') or line.strip().startswith('if __name__')):
                        end_line = i
                        break
                
                if end_line == -1:
                    end_line = len(lines)
                
                # 替换方法
                new_lines = lines[:start_line] + new_download_method.split('\n') + lines[end_line:]
                new_content = '\n'.join(new_lines)
                print(f"✅ 找到并替换了download_update方法 (行 {start_line+1}-{end_line})")
            else:
                print("❌ 未找到download_update方法")
                return False
        else:
            print("✅ 使用正则表达式成功替换download_update方法")
        
        # 写入新文件
        with open("auto_updater.py", 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ auto_updater.py修复完成")
        print("📋 修复内容:")
        print("  - 移除了复杂的断点续传逻辑")
        print("  - 使用简单的完整下载")
        print("  - 增加了更长的超时时间")
        print("  - 改进了错误处理")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_fixed_updater():
    """测试修复后的更新器"""
    print("\n🧪 测试修复后的更新器")
    print("=" * 50)
    
    try:
        from auto_updater import AutoUpdater
        
        # 创建更新器实例
        updater = AutoUpdater(
            current_version="2.1.0",
            license_server_url="http://198.23.135.176:5000/",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        print("✅ 更新器创建成功")
        
        # 测试检查更新
        print("📋 测试检查更新...")
        update_info = updater.check_for_updates()
        
        if update_info:
            print("✅ 检查更新成功")
            print(f"📊 版本: {update_info.get('version')}")
            print(f"📦 大小: {update_info.get('file_size', 0)/1024/1024:.1f}MB")
            
            # 测试下载
            print("📥 测试下载...")
            
            def progress_callback(progress):
                if progress % 10 == 0:  # 每10%显示一次
                    print(f"📊 下载进度: {progress:.1f}%")
            
            temp_file = updater.download_update(update_info, progress_callback)
            
            if temp_file:
                print(f"✅ 下载成功: {temp_file}")
                
                # 检查文件
                if os.path.exists(temp_file):
                    file_size = os.path.getsize(temp_file)
                    print(f"📦 文件大小: {file_size/1024/1024:.1f}MB")
                else:
                    print("❌ 文件不存在")
            else:
                print("❌ 下载失败")
        else:
            print("ℹ️ 没有可用更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 修复auto_updater.py")
    print("=" * 60)
    
    # 1. 修复auto_updater.py
    if fix_auto_updater():
        print("✅ 修复完成")
    else:
        print("❌ 修复失败")
        return
    
    # 2. 测试修复结果
    if test_fixed_updater():
        print("✅ 测试通过")
    else:
        print("❌ 测试失败")
    
    print("\n" + "=" * 60)
    print("📊 修复完成")
    print("💡 建议:")
    print("1. 运行 python 终极简单下载器.py 进行详细诊断")
    print("2. 运行 python license_client.py 测试更新功能")

if __name__ == "__main__":
    main()
