#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试exe文件是否包含所有必要的依赖
这个脚本应该在exe文件运行环境中执行
"""

import sys
import os
import traceback

def test_import(module_name, description=""):
    """测试模块导入"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - 导入成功 {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {module_name} - 导入异常: {e}")
        return False

def test_fake_useragent():
    """测试fake_useragent功能"""
    print("\n🧪 测试fake_useragent功能...")
    try:
        from fake_useragent import UserAgent
        ua = UserAgent()
        user_agent = ua.random
        print(f"✅ fake_useragent工作正常")
        print(f"   示例UA: {user_agent[:50]}...")
        return True
    except Exception as e:
        print(f"❌ fake_useragent功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_openpyxl():
    """测试openpyxl功能"""
    print("\n🧪 测试openpyxl功能...")
    try:
        import openpyxl
        from openpyxl import Workbook
        
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws['A1'] = "测试数据"
        
        # 测试保存到内存
        import io
        buffer = io.BytesIO()
        wb.save(buffer)
        buffer.seek(0)
        
        # 测试从内存读取
        wb2 = openpyxl.load_workbook(buffer)
        value = wb2.active['A1'].value
        
        if value == "测试数据":
            print("✅ openpyxl工作正常")
            return True
        else:
            print(f"❌ openpyxl数据不匹配: 期望'测试数据', 得到'{value}'")
            return False
            
    except Exception as e:
        print(f"❌ openpyxl功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_pandas_excel():
    """测试pandas Excel功能"""
    print("\n🧪 测试pandas Excel功能...")
    try:
        import pandas as pd
        import io
        
        # 创建测试数据
        df = pd.DataFrame({
            'A': [1, 2, 3],
            'B': ['a', 'b', 'c']
        })
        
        # 测试保存到内存
        buffer = io.BytesIO()
        df.to_excel(buffer, index=False, engine='openpyxl')
        buffer.seek(0)
        
        # 测试从内存读取
        df2 = pd.read_excel(buffer, engine='openpyxl')
        
        if df.equals(df2):
            print("✅ pandas Excel功能工作正常")
            return True
        else:
            print("❌ pandas Excel数据不匹配")
            return False
            
    except Exception as e:
        print(f"❌ pandas Excel功能测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 exe依赖测试")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"运行路径: {os.getcwd()}")
    print(f"可执行文件: {sys.executable}")
    print("=" * 60)
    
    # 基础模块导入测试
    print("\n📦 基础模块导入测试:")
    basic_modules = [
        ('os', '操作系统接口'),
        ('sys', '系统相关'),
        ('json', 'JSON处理'),
        ('requests', 'HTTP请求'),
        ('tkinter', 'GUI框架'),
        ('threading', '多线程'),
        ('subprocess', '子进程'),
        ('pathlib', '路径处理'),
    ]
    
    basic_ok = 0
    for module, desc in basic_modules:
        if test_import(module, desc):
            basic_ok += 1
    
    # 关键依赖测试
    print("\n🔑 关键依赖导入测试:")
    critical_modules = [
        ('fake_useragent', '用户代理生成'),
        ('openpyxl', 'Excel文件处理'),
        ('pandas', '数据分析'),
        ('selenium', 'Web自动化'),
        ('bs4', 'BeautifulSoup'),  # beautifulsoup4的导入名是bs4
        ('lxml', 'XML/HTML解析'),
        ('cryptography', '加密库'),
        ('PIL', '图像处理'),
    ]
    
    critical_ok = 0
    for module, desc in critical_modules:
        if test_import(module, desc):
            critical_ok += 1
    
    # 功能测试
    print("\n🔧 功能测试:")
    function_tests = [
        ("fake_useragent", test_fake_useragent),
        ("openpyxl", test_openpyxl),
        ("pandas Excel", test_pandas_excel),
    ]
    
    function_ok = 0
    for name, test_func in function_tests:
        if test_func():
            function_ok += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print("=" * 60)
    print(f"基础模块: {basic_ok}/{len(basic_modules)} 通过")
    print(f"关键依赖: {critical_ok}/{len(critical_modules)} 通过")
    print(f"功能测试: {function_ok}/{len(function_tests)} 通过")
    
    total_tests = len(basic_modules) + len(critical_modules) + len(function_tests)
    total_passed = basic_ok + critical_ok + function_ok
    
    print(f"\n总体通过率: {total_passed}/{total_tests} ({total_passed/total_tests*100:.1f}%)")
    
    if total_passed == total_tests:
        print("\n🎉 所有测试通过！exe文件依赖完整。")
        return True
    else:
        print(f"\n⚠️  有 {total_tests - total_passed} 个测试失败。")
        print("\n💡 建议:")
        if critical_ok < len(critical_modules):
            print("1. 重新构建exe，确保包含所有隐藏导入")
            print("2. 使用 build_with_spec.py 进行构建")
            print("3. 运行 fix_pyinstaller_deps.py 修复依赖问题")
        
        if function_ok < len(function_tests):
            print("4. 检查数据文件是否正确包含")
            print("5. 确保在干净的环境中测试")
        
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程出现异常: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
