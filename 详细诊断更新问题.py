#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细诊断更新问题
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import requests
import json
import traceback
import os
import tempfile
import time

class UpdateDiagnostic:
    """更新诊断工具"""
    
    def __init__(self, log_callback=None):
        self.log_callback = log_callback or print
        self.license_key = "ADMIN_BYPASS"
        self.device_id = "ADMIN-DEVICE-001"
        self.server_url = "http://198.23.135.176:5000"
    
    def log(self, message):
        """记录日志"""
        self.log_callback(message)
        print(message)  # 同时输出到控制台
    
    def test_step_by_step(self):
        """逐步测试更新流程"""
        self.log("🔍 开始逐步诊断更新流程")
        self.log("=" * 60)
        
        # 步骤1：测试网络连接
        self.log("\n1️⃣ 测试网络连接...")
        if not self.test_network():
            return False
        
        # 步骤2：测试更新检查API
        self.log("\n2️⃣ 测试更新检查API...")
        update_info = self.test_check_update()
        if not update_info:
            return False
        
        # 步骤3：测试下载API连接
        self.log("\n3️⃣ 测试下载API连接...")
        if not self.test_download_connection():
            return False
        
        # 步骤4：测试小文件下载
        self.log("\n4️⃣ 测试小文件下载...")
        if not self.test_small_download():
            return False
        
        # 步骤5：测试大文件下载
        self.log("\n5️⃣ 测试大文件下载...")
        if not self.test_large_download():
            return False
        
        # 步骤6：测试auto_updater模块
        self.log("\n6️⃣ 测试auto_updater模块...")
        if not self.test_auto_updater():
            return False
        
        self.log("\n✅ 所有测试通过！")
        return True
    
    def test_network(self):
        """测试网络连接"""
        try:
            response = requests.get(self.server_url, timeout=10)
            self.log(f"✅ 网络连接正常: {response.status_code}")
            return True
        except Exception as e:
            self.log(f"❌ 网络连接失败: {e}")
            return False
    
    def test_check_update(self):
        """测试更新检查"""
        try:
            url = f"{self.server_url}/update/check"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'current_version': '2.1.0'
            }
            
            response = requests.get(url, params=params, timeout=15)
            self.log(f"📡 更新检查响应: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"📊 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success') and data.get('has_update'):
                    self.log("✅ 发现可用更新")
                    return data.get('update_info')
                else:
                    self.log("ℹ️ 没有可用更新")
                    return None
            else:
                self.log(f"❌ 更新检查失败: {response.text}")
                return None
                
        except Exception as e:
            self.log(f"❌ 更新检查异常: {e}")
            self.log(f"📄 详细错误: {traceback.format_exc()}")
            return None
    
    def test_download_connection(self):
        """测试下载连接"""
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': '2.1.1'
            }
            
            # 只请求头部信息
            response = requests.head(url, params=params, timeout=15)
            self.log(f"📡 下载连接响应: {response.status_code}")
            self.log(f"📄 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                self.log("✅ 下载连接正常")
                return True
            else:
                self.log(f"❌ 下载连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ 下载连接异常: {e}")
            return False
    
    def test_small_download(self):
        """测试小文件下载（前1KB）"""
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': '2.1.1'
            }
            
            headers = {'Range': 'bytes=0-1023'}  # 前1KB
            
            response = requests.get(url, params=params, headers=headers, timeout=30)
            self.log(f"📡 小文件下载响应: {response.status_code}")
            
            if response.status_code in [200, 206]:
                data_size = len(response.content)
                self.log(f"✅ 小文件下载成功: {data_size} 字节")
                return True
            else:
                self.log(f"❌ 小文件下载失败: {response.status_code}")
                self.log(f"📄 错误内容: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ 小文件下载异常: {e}")
            return False
    
    def test_large_download(self):
        """测试大文件下载（前10MB）"""
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': '2.1.1'
            }
            
            headers = {'Range': 'bytes=0-10485759'}  # 前10MB
            
            self.log("📥 开始下载前10MB...")
            start_time = time.time()
            
            response = requests.get(url, params=params, headers=headers, timeout=120, stream=True)
            
            if response.status_code in [200, 206]:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=32768):
                    if chunk:
                        downloaded += len(chunk)
                        if downloaded % (1024*1024) == 0:  # 每1MB显示进度
                            self.log(f"📊 已下载: {downloaded/1024/1024:.1f}MB")
                
                end_time = time.time()
                speed = downloaded / (end_time - start_time) / 1024  # KB/s
                
                self.log(f"✅ 大文件下载测试成功")
                self.log(f"📦 下载大小: {downloaded/1024/1024:.1f}MB")
                self.log(f"⏱️ 下载时间: {end_time-start_time:.1f}秒")
                self.log(f"🚀 下载速度: {speed:.1f}KB/s")
                
                if speed < 50:
                    self.log("⚠️ 下载速度较慢，可能导致超时")
                
                return True
            else:
                self.log(f"❌ 大文件下载失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ 大文件下载异常: {e}")
            self.log(f"📄 详细错误: {traceback.format_exc()}")
            return False
    
    def test_auto_updater(self):
        """测试auto_updater模块"""
        try:
            self.log("📦 测试导入auto_updater...")
            from auto_updater import AutoUpdater
            
            self.log("✅ auto_updater导入成功")
            
            # 创建更新器实例
            updater = AutoUpdater(
                license_key=self.license_key,
                device_id=self.device_id
            )
            
            self.log("📋 测试检查更新...")
            update_info = updater.check_update("2.1.0")
            
            if update_info:
                self.log("✅ auto_updater检查更新成功")
                self.log(f"📊 更新信息: {update_info}")
                
                # 测试下载（只下载前1MB）
                self.log("📥 测试auto_updater下载...")
                
                # 模拟下载测试
                version = update_info.get('version')
                temp_file = os.path.join(tempfile.gettempdir(), f"test_download_{version}.exe")
                
                try:
                    # 这里我们不实际下载，只测试下载方法是否存在
                    if hasattr(updater, 'download_update'):
                        self.log("✅ auto_updater下载方法存在")
                        return True
                    else:
                        self.log("❌ auto_updater缺少下载方法")
                        return False
                        
                except Exception as download_error:
                    self.log(f"❌ auto_updater下载测试失败: {download_error}")
                    return False
            else:
                self.log("❌ auto_updater检查更新失败")
                return False
                
        except ImportError as e:
            self.log(f"❌ auto_updater导入失败: {e}")
            return False
        except Exception as e:
            self.log(f"❌ auto_updater测试异常: {e}")
            self.log(f"📄 详细错误: {traceback.format_exc()}")
            return False

def create_diagnostic_gui():
    """创建诊断界面"""
    root = tk.Tk()
    root.title("更新问题详细诊断")
    root.geometry("900x700")
    
    # 设置图标
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 标题
    title_label = tk.Label(
        root,
        text="🔧 更新问题详细诊断工具",
        font=("微软雅黑", 16, "bold"),
        pady=10
    )
    title_label.pack()
    
    # 日志显示区域
    log_frame = tk.Frame(root)
    log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    log_text = scrolledtext.ScrolledText(log_frame, height=30, width=100)
    log_text.pack(fill=tk.BOTH, expand=True)
    
    def log_message(msg):
        """记录日志消息"""
        log_text.insert(tk.END, msg + "\n")
        log_text.see(tk.END)
        root.update()
    
    # 创建诊断器
    diagnostic = UpdateDiagnostic(log_callback=log_message)
    
    # 按钮区域
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    def start_diagnosis():
        """开始诊断"""
        log_text.delete(1.0, tk.END)
        log_message("🚀 开始详细诊断...")
        
        try:
            success = diagnostic.test_step_by_step()
            
            if success:
                log_message("\n🎉 诊断完成：所有测试通过！")
                messagebox.showinfo("诊断完成", "所有测试通过，更新功能应该正常工作")
            else:
                log_message("\n❌ 诊断发现问题，请查看上面的详细日志")
                messagebox.showwarning("发现问题", "诊断发现问题，请查看详细日志")
                
        except Exception as e:
            error_msg = f"❌ 诊断过程异常: {e}"
            log_message(error_msg)
            log_message(f"📄 详细错误: {traceback.format_exc()}")
            messagebox.showerror("诊断异常", error_msg)
    
    # 创建按钮
    start_button = tk.Button(
        button_frame,
        text="🔍 开始详细诊断",
        command=start_diagnosis,
        font=("微软雅黑", 12),
        bg="#e74c3c",
        fg="white",
        padx=20,
        pady=10
    )
    start_button.pack(side=tk.LEFT, padx=10)
    
    clear_button = tk.Button(
        button_frame,
        text="🗑️ 清空日志",
        command=lambda: log_text.delete(1.0, tk.END),
        font=("微软雅黑", 12),
        bg="#95a5a6",
        fg="white",
        padx=20,
        pady=10
    )
    clear_button.pack(side=tk.LEFT, padx=10)
    
    # 初始化日志
    log_message("🔧 更新问题详细诊断工具已启动")
    log_message("💡 点击'开始详细诊断'按钮开始逐步检查更新功能")
    log_message("📋 将逐步测试：网络连接 → 更新检查 → 下载连接 → 文件下载 → 模块测试")
    
    root.mainloop()

if __name__ == "__main__":
    create_diagnostic_gui()
