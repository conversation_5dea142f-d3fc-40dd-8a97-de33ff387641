
import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from auto_updater import check_and_update
    
    def test_update():
        """测试更新功能"""
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        result = check_and_update(
            parent_window=None,
            current_version="2.1.0",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        if result:
            messagebox.showinfo("成功", "更新功能测试成功！")
        else:
            messagebox.showinfo("信息", "没有可用更新或用户取消")
        
        root.destroy()
    
    if __name__ == "__main__":
        test_update()
        
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
