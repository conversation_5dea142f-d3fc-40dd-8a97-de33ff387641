#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加自动更新API到license_server.py
"""

import paramiko
import sys
import os

def add_update_apis():
    """添加自动更新API到license_server.py"""
    print("🔄 添加自动更新API到license_server.py")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 自动更新API代码
    update_api_code = '''

# ==================== 自动更新API ====================

import hashlib
import mimetypes
from werkzeug.utils import secure_filename

# 更新文件存储目录
UPDATE_DIR = "/opt/license_manager/updates"
CURRENT_VERSION = "2.1.0"

# 确保更新目录存在
os.makedirs(UPDATE_DIR, exist_ok=True)

@app.route('/api/check_update', methods=['GET'])
def check_update():
    """检查更新API"""
    try:
        # 获取参数
        license_key = request.args.get('key')
        device_id = request.args.get('device_id')
        current_version = request.args.get('current_version', '1.0.0')
        
        # 验证授权（可选，根据需要启用）
        # if not license_key or not device_id:
        #     return jsonify({"success": False, "message": "缺少授权信息"})
        
        # 获取最新版本信息
        latest_version = get_latest_version()
        
        if not latest_version:
            return jsonify({
                "success": True,
                "has_update": False,
                "message": "当前已是最新版本"
            })
        
        # 比较版本
        if is_newer_version(latest_version['version'], current_version):
            return jsonify({
                "success": True,
                "has_update": True,
                "update_info": {
                    "version": latest_version['version'],
                    "file_size": latest_version['file_size'],
                    "file_hash": latest_version['file_hash'],
                    "changelog": latest_version['changelog'],
                    "download_url": f"/api/download_update?version={latest_version['version']}"
                }
            })
        else:
            return jsonify({
                "success": True,
                "has_update": False,
                "message": "当前已是最新版本"
            })
            
    except Exception as e:
        return jsonify({"success": False, "message": f"检查更新失败: {str(e)}"})

@app.route('/api/download_update', methods=['GET'])
def download_update():
    """下载更新文件API"""
    try:
        # 获取参数
        license_key = request.args.get('key')
        device_id = request.args.get('device_id')
        version = request.args.get('version')
        
        if not version:
            return jsonify({"success": False, "message": "缺少版本参数"})
        
        # 验证授权（可选）
        # if not license_key or not device_id:
        #     return jsonify({"success": False, "message": "缺少授权信息"})
        
        # 查找更新文件
        update_file = find_update_file(version)
        if not update_file:
            return jsonify({"success": False, "message": "更新文件不存在"})
        
        # 返回文件
        return send_file(
            update_file,
            as_attachment=True,
            download_name=f"亚马逊蓝图工具_v{version}.exe",
            mimetype='application/octet-stream'
        )
        
    except Exception as e:
        return jsonify({"success": False, "message": f"下载失败: {str(e)}"})

@app.route('/api/upload_update', methods=['POST'])
def upload_update():
    """上传更新文件API（管理员使用）"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({"success": False, "message": "没有文件"})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "message": "没有选择文件"})
        
        # 获取版本信息
        version = request.form.get('version')
        changelog = request.form.get('changelog', '')
        
        if not version:
            return jsonify({"success": False, "message": "缺少版本信息"})
        
        # 保存文件
        filename = secure_filename(f"亚马逊蓝图工具_v{version}.exe")
        file_path = os.path.join(UPDATE_DIR, filename)
        file.save(file_path)
        
        # 计算文件哈希
        file_hash = calculate_file_hash(file_path)
        file_size = os.path.getsize(file_path)
        
        # 保存版本信息
        save_version_info(version, file_size, file_hash, changelog, filename)
        
        return jsonify({
            "success": True,
            "message": "更新文件上传成功",
            "version": version,
            "file_size": file_size,
            "file_hash": file_hash
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"上传失败: {str(e)}"})

@app.route('/api/list_versions', methods=['GET'])
def list_versions():
    """列出所有版本API"""
    try:
        versions = get_all_versions()
        return jsonify({
            "success": True,
            "versions": versions,
            "current_version": CURRENT_VERSION
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"获取版本列表失败: {str(e)}"})

# ==================== 辅助函数 ====================

def get_latest_version():
    """获取最新版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return None
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        if not versions:
            return None
        
        # 返回最新版本
        latest = max(versions, key=lambda x: version_to_tuple(x['version']))
        return latest
        
    except Exception as e:
        print(f"获取最新版本失败: {e}")
        return None

def get_all_versions():
    """获取所有版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return []
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        # 按版本号排序
        versions.sort(key=lambda x: version_to_tuple(x['version']), reverse=True)
        return versions
        
    except Exception as e:
        print(f"获取版本列表失败: {e}")
        return []

def save_version_info(version, file_size, file_hash, changelog, filename):
    """保存版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        
        # 读取现有版本
        versions = []
        if os.path.exists(version_file):
            with open(version_file, 'r', encoding='utf-8') as f:
                versions = json.load(f)
        
        # 添加新版本
        new_version = {
            "version": version,
            "file_size": file_size,
            "file_hash": file_hash,
            "changelog": changelog,
            "filename": filename,
            "upload_time": datetime.now().isoformat()
        }
        
        # 移除同版本的旧记录
        versions = [v for v in versions if v['version'] != version]
        versions.append(new_version)
        
        # 保存
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(versions, f, ensure_ascii=False, indent=2)
        
    except Exception as e:
        print(f"保存版本信息失败: {e}")

def find_update_file(version):
    """查找更新文件"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return None
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        for v in versions:
            if v['version'] == version:
                file_path = os.path.join(UPDATE_DIR, v['filename'])
                if os.path.exists(file_path):
                    return file_path
        
        return None
        
    except Exception as e:
        print(f"查找更新文件失败: {e}")
        return None

def calculate_file_hash(file_path):
    """计算文件SHA256哈希"""
    try:
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    except Exception as e:
        print(f"计算文件哈希失败: {e}")
        return ""

def is_newer_version(latest, current):
    """比较版本号"""
    try:
        return version_to_tuple(latest) > version_to_tuple(current)
    except:
        return False

def version_to_tuple(version):
    """将版本号转换为元组用于比较"""
    try:
        return tuple(map(int, version.split('.')))
    except:
        return (0, 0, 0)

# ==================== 更新API结束 ====================
'''
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 备份当前文件
        print("📁 步骤1: 备份当前文件...")
        from datetime import datetime
        backup_name = f"license_server.py.backup_update_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        stdin, stdout, stderr = client.exec_command(
            f"cp {config['deploy_path']}/license_server.py {config['deploy_path']}/{backup_name}"
        )
        stdout.channel.recv_exit_status()
        print(f"   ✅ 已备份到: {backup_name}")
        
        # 步骤2: 检查是否已有更新API
        print("🔍 步骤2: 检查是否已有更新API...")
        stdin, stdout, stderr = client.exec_command(
            f"grep -c 'check_update' {config['deploy_path']}/license_server.py"
        )
        api_count = stdout.read().decode('utf-8').strip()
        
        if api_count and int(api_count) > 0:
            print("   ⚠️ 检测到已有更新API，将覆盖")
        else:
            print("   ✅ 未检测到更新API，将添加")
        
        # 步骤3: 添加更新API代码
        print("📝 步骤3: 添加更新API代码...")
        
        # 创建临时文件包含更新API代码
        temp_api_file = "/tmp/update_api.py"
        stdin, stdout, stderr = client.exec_command(f"cat > {temp_api_file} << 'EOF'\n{update_api_code}EOF")
        stdout.channel.recv_exit_status()
        
        # 将更新API添加到license_server.py末尾（在app.run之前）
        stdin, stdout, stderr = client.exec_command(f"""
        # 创建新的license_server.py
        head -n -1 {config['deploy_path']}/license_server.py > {config['deploy_path']}/license_server_new.py
        cat {temp_api_file} >> {config['deploy_path']}/license_server_new.py
        tail -n 1 {config['deploy_path']}/license_server.py >> {config['deploy_path']}/license_server_new.py
        mv {config['deploy_path']}/license_server_new.py {config['deploy_path']}/license_server.py
        rm {temp_api_file}
        """)
        stdout.channel.recv_exit_status()
        print("   ✅ 更新API代码已添加")
        
        # 步骤4: 创建更新目录
        print("📁 步骤4: 创建更新目录...")
        stdin, stdout, stderr = client.exec_command(f"mkdir -p {config['deploy_path']}/updates")
        stdout.channel.recv_exit_status()
        print("   ✅ 更新目录已创建")
        
        # 步骤5: 重启服务
        print("🔄 步骤5: 重启服务...")
        stdin, stdout, stderr = client.exec_command("systemctl restart license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务重启成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务重启失败: {error}")
            return False
        
        # 等待服务启动
        import time
        time.sleep(5)
        
        # 步骤6: 测试更新API
        print("🧪 步骤6: 测试更新API...")
        test_apis = [
            ("检查更新", "curl -s http://localhost:5000/api/check_update?current_version=1.0.0"),
            ("版本列表", "curl -s http://localhost:5000/api/list_versions")
        ]
        
        success_count = 0
        for name, cmd in test_apis:
            stdin, stdout, stderr = client.exec_command(cmd)
            output = stdout.read().decode('utf-8')
            
            if '"success"' in output:
                print(f"   ✅ {name}: 正常")
                success_count += 1
            else:
                print(f"   ❌ {name}: 异常")
        
        print(f"\n📊 更新API测试结果: {success_count}/{len(test_apis)} 个接口正常")
        
        # 关闭连接
        client.close()
        
        if success_count >= 1:
            print("\n🎉 自动更新API添加成功！")
            print("🌐 现在可以使用以下API:")
            print("• 📋 检查更新: http://**************:5000/api/check_update")
            print("• 📥 下载更新: http://**************:5000/api/download_update")
            print("• 📤 上传更新: http://**************:5000/api/upload_update")
            print("• 📊 版本列表: http://**************:5000/api/list_versions")
            return True
        else:
            print("\n⚠️ API添加完成，但测试部分失败")
            return True
        
    except Exception as e:
        print(f"❌ 添加过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 为license_server.py添加自动更新API")
        print("📋 功能: 检查更新、下载更新、上传更新、版本管理")
        print()
        
        print("📋 即将添加的API:")
        print("• 🔍 /api/check_update - 检查是否有新版本")
        print("• 📥 /api/download_update - 下载更新文件")
        print("• 📤 /api/upload_update - 上传新版本（管理员）")
        print("• 📊 /api/list_versions - 列出所有版本")
        print()
        
        confirm = input("确认添加自动更新API？(y/n): ").lower().strip()
        
        if confirm in ['y', 'yes', '是']:
            if add_update_apis():
                print("\n✅ 自动更新API添加成功！")
                print("💡 现在您的exe文件可以使用自动更新功能了")
            else:
                print("\n❌ 添加失败，请检查错误信息")
        else:
            print("❌ 用户取消添加")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
