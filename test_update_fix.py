#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动更新修复是否有效
"""

import os
import sys
import json
from datetime import datetime, timedelta

def test_version_comparison():
    """测试版本比较逻辑"""
    print("=== 测试版本比较逻辑 ===")
    
    try:
        from auto_updater import AutoUpdater
        
        updater = AutoUpdater()
        
        # 测试用例
        test_cases = [
            ("2.1.1", "2.1.0", True),   # 新版本更高
            ("2.1.0", "2.1.0", False),  # 版本相同
            ("2.1.0", "2.1.1", False),  # 新版本更低
            ("2.2.0", "2.1.9", True),   # 主版本号更高
            ("3.0.0", "2.9.9", True),   # 大版本号更高
        ]
        
        for latest, current, expected in test_cases:
            result = updater._is_newer_version(latest, current)
            status = "✅" if result == expected else "❌"
            print(f"{status} {latest} vs {current}: {result} (期望: {expected})")
            
    except Exception as e:
        print(f"❌ 版本比较测试失败: {e}")

def test_update_frequency_limit():
    """测试更新频率限制"""
    print("\n=== 测试更新频率限制 ===")
    
    try:
        update_info_file = os.path.join(os.path.expanduser("~"), ".amazon_last_update.json")
        
        # 创建测试数据
        test_data = {
            "last_check_time": datetime.now().isoformat(),
            "version": "2.1.0",
            "updated": False
        }
        
        with open(update_info_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建测试文件: {update_info_file}")
        
        # 读取并验证
        with open(update_info_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        last_check = datetime.fromisoformat(data['last_check_time'])
        time_diff = datetime.now() - last_check
        
        print(f"✅ 上次检查时间: {last_check}")
        print(f"✅ 时间差: {time_diff}")
        print(f"✅ 是否应该跳过检查: {time_diff < timedelta(hours=1)}")
        
    except Exception as e:
        print(f"❌ 频率限制测试失败: {e}")

def test_config_status():
    """测试配置状态"""
    print("\n=== 测试配置状态 ===")
    
    try:
        from update_config import get_config, CURRENT_VERSION
        
        config = get_config()
        print(f"✅ 当前版本: {CURRENT_VERSION}")
        print(f"✅ 自动更新启用: {config.get('auto_check_enabled', False)}")
        print(f"✅ 检查间隔: {config.get('check_interval', 0)} 秒")
        print(f"✅ 服务器URL: {config.get('license_server_url', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")

def check_duplicate_functions():
    """检查是否还有重复的函数定义"""
    print("\n=== 检查重复函数定义 ===")
    
    try:
        with open('auto_updater.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找check_and_update_silent函数定义
        import re
        pattern = r'def check_and_update_silent\('
        matches = re.findall(pattern, content)
        
        print(f"✅ 找到 check_and_update_silent 函数定义: {len(matches)} 个")
        
        if len(matches) == 1:
            print("✅ 重复函数定义已修复")
        else:
            print("❌ 仍有重复函数定义")
            
    except Exception as e:
        print(f"❌ 检查重复函数失败: {e}")

def main():
    """主测试函数"""
    print("🔧 自动更新修复验证测试")
    print("=" * 50)
    
    test_version_comparison()
    test_update_frequency_limit()
    test_config_status()
    check_duplicate_functions()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("\n📋 修复总结:")
    print("1. ✅ 启用自动更新但添加了版本比较逻辑")
    print("2. ✅ 修复了版本号更新逻辑中的硬编码问题")
    print("3. ✅ 删除了重复的函数定义")
    print("4. ✅ 添加了更新频率限制（1小时间隔）")
    print("5. ✅ 保留了更新完成后的跳过逻辑")
    
    print("\n🚀 现在重新构建exe文件应该不会再出现重复更新循环问题！")

if __name__ == "__main__":
    main()
