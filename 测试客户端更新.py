#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户端更新功能
"""

import tkinter as tk
from tkinter import messagebox
import traceback

def test_client_update():
    """测试客户端更新功能"""
    print("🧪 测试客户端更新功能")
    print("=" * 50)
    
    try:
        from auto_updater import check_and_update
        print("✅ 成功导入 check_and_update")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("客户端更新测试")
        root.geometry("500x400")
        
        # 设置图标
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        # 测试结果显示
        result_text = tk.Text(root, height=15, width=60)
        result_text.pack(pady=10, padx=10)
        
        def log_message(msg):
            """记录消息"""
            result_text.insert(tk.END, msg + "\n")
            result_text.see(tk.END)
            root.update()
        
        def test_update_with_details():
            """详细测试更新功能"""
            result_text.delete(1.0, tk.END)
            log_message("🔄 开始测试更新功能...")
            
            try:
                # 测试参数
                current_version = "2.1.0"
                license_key = "ADMIN_BYPASS"
                device_id = "ADMIN-DEVICE-001"
                
                log_message(f"📋 测试参数:")
                log_message(f"   版本: {current_version}")
                log_message(f"   密钥: {license_key}")
                log_message(f"   设备: {device_id}")
                log_message("")
                
                # 调用更新函数
                log_message("🚀 调用 check_and_update...")
                result = check_and_update(
                    parent_window=root,
                    current_version=current_version,
                    license_key=license_key,
                    device_id=device_id
                )
                
                log_message(f"📊 更新结果: {result}")
                
                if result:
                    log_message("✅ 更新函数返回成功")
                    messagebox.showinfo("成功", "更新功能测试成功！")
                else:
                    log_message("ℹ️ 更新函数返回False（可能没有更新或用户取消）")
                    messagebox.showinfo("信息", "没有可用更新或用户取消了更新")
                
            except Exception as e:
                error_msg = f"❌ 更新测试失败: {str(e)}"
                log_message(error_msg)
                log_message(f"📄 详细错误:")
                log_message(traceback.format_exc())
                messagebox.showerror("错误", error_msg)
        
        def test_update_check_only():
            """只测试更新检查"""
            result_text.delete(1.0, tk.END)
            log_message("🔍 测试更新检查...")
            
            try:
                import requests
                
                url = "http://198.23.135.176:5000/update/check"
                params = {
                    'key': 'ADMIN_BYPASS',
                    'device_id': 'ADMIN-DEVICE-001',
                    'current_version': '2.1.0'
                }
                
                log_message("📡 发送更新检查请求...")
                response = requests.get(url, params=params, timeout=15)
                
                log_message(f"📊 响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    log_message("✅ 更新检查成功")
                    log_message(f"📄 响应数据: {data}")
                    
                    if data.get('has_update'):
                        log_message("🎉 有可用更新")
                        log_message(f"📦 新版本: {data.get('latest_version')}")
                        log_message(f"📁 文件大小: {data.get('update_info', {}).get('file_size', 0)} 字节")
                    else:
                        log_message("ℹ️ 没有可用更新")
                else:
                    log_message(f"❌ 更新检查失败: {response.text}")
                    
            except Exception as e:
                log_message(f"❌ 更新检查异常: {e}")
                log_message(traceback.format_exc())
        
        # 创建界面
        title_label = tk.Label(
            root,
            text="客户端更新功能测试",
            font=("微软雅黑", 14, "bold"),
            pady=10
        )
        title_label.pack()
        
        button_frame = tk.Frame(root)
        button_frame.pack(pady=10)
        
        test_full_button = tk.Button(
            button_frame,
            text="🧪 测试完整更新流程",
            command=test_update_with_details,
            font=("微软雅黑", 10),
            bg="#3498db",
            fg="white",
            padx=15,
            pady=5
        )
        test_full_button.pack(side=tk.LEFT, padx=5)
        
        test_check_button = tk.Button(
            button_frame,
            text="🔍 只测试更新检查",
            command=test_update_check_only,
            font=("微软雅黑", 10),
            bg="#27ae60",
            fg="white",
            padx=15,
            pady=5
        )
        test_check_button.pack(side=tk.LEFT, padx=5)
        
        clear_button = tk.Button(
            button_frame,
            text="🗑️ 清空日志",
            command=lambda: result_text.delete(1.0, tk.END),
            font=("微软雅黑", 10),
            bg="#95a5a6",
            fg="white",
            padx=15,
            pady=5
        )
        clear_button.pack(side=tk.LEFT, padx=5)
        
        info_label = tk.Label(
            root,
            text="点击按钮测试更新功能，查看详细日志",
            font=("微软雅黑", 9),
            fg="#666666"
        )
        info_label.pack(pady=5)
        
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        messagebox.showerror("错误", f"导入auto_updater失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        messagebox.showerror("错误", f"测试失败: {e}")

if __name__ == "__main__":
    test_client_update()
