#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建管理员密钥 - 为exe文件管理工具创建管理员权限
"""

import requests
import json

def create_admin_key():
    """创建管理员密钥"""
    print("🔑 创建管理员密钥")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 管理员密钥信息
    admin_data = {
        'expire_days': 3650,  # 10年有效期
        'permission_level': 4,  # 最高权限级别
        'device_id': 'ADMIN-DEVICE-001',
        'device_name': 'exe文件管理工具',
        'notes': '管理员专用密钥，用于exe文件管理'
    }
    
    try:
        print("🌐 正在连接服务器...")
        
        # 生成管理员密钥
        response = requests.post(
            f"{server_url}/license/generate",
            json=admin_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                keys = result.get('keys', [])
                if keys:
                    admin_key = keys[0]  # 取第一个密钥
                    print(f"✅ 管理员密钥创建成功！")
                    print(f"🔑 密钥: {admin_key}")
                    print(f"📅 有效期: {admin_data['expire_days']} 天")
                    print(f"🔒 权限级别: {admin_data['permission_level']}")
                    print(f"📱 设备ID: {admin_data['device_id']}")
                else:
                    print("❌ 服务器未返回密钥")
                    return None
                
                # 保存到文件
                config = {
                    'admin_key': admin_key,
                    'device_id': admin_data['device_id'],
                    'server_url': server_url,
                    'created_time': '2024-12-01',
                    'permission_level': admin_data['permission_level']
                }
                
                with open('管理员配置.json', 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                print(f"💾 配置已保存到: 管理员配置.json")
                
                # 测试密钥
                print("\n🧪 测试管理员密钥...")
                test_response = requests.post(
                    f"{server_url}/license/check",
                    json={
                        'key': admin_key,
                        'device_id': admin_data['device_id']
                    },
                    timeout=10
                )
                
                if test_response.status_code == 200:
                    test_result = test_response.json()
                    if test_result.get('success'):
                        print("✅ 管理员密钥验证成功！")
                        print(f"📋 权限: {test_result.get('permissions', [])}")
                    else:
                        print(f"⚠️ 密钥验证失败: {test_result.get('message')}")
                else:
                    print(f"⚠️ 密钥验证请求失败: HTTP {test_response.status_code}")
                
                return admin_key
                
            else:
                print(f"❌ 创建失败: {result.get('message', '未知错误')}")
                return None
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data.get('message', '未知错误')}")
            except:
                print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请检查服务器状态")
        return None
    except Exception as e:
        print(f"❌ 创建异常: {e}")
        return None

def main():
    """主函数"""
    try:
        print("🎯 目标: 为exe文件管理工具创建管理员权限")
        print("🔧 功能: 生成具有最高权限的管理员密钥")
        print("💡 用途: 用于上传、下载、管理exe文件")
        print()
        
        admin_key = create_admin_key()
        
        if admin_key:
            print("\n" + "=" * 50)
            print("🎉 管理员密钥创建完成！")
            print("\n📋 下一步操作:")
            print("1. 使用生成的密钥更新exe文件管理工具")
            print("2. 重新启动exe文件管理工具")
            print("3. 测试上传和管理功能")
            print("\n💡 密钥信息已保存到 '管理员配置.json' 文件")
        else:
            print("\n❌ 管理员密钥创建失败")
            print("请检查服务器状态和网络连接")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
