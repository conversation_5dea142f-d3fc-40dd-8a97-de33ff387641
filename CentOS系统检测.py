#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CentOS系统检测和优化脚本
检测CentOS系统信息并提供优化建议
"""

import subprocess
import sys
import json

# 服务器配置
SERVER_CONFIG = {
    "host": "**************",
    "port": 22,
    "username": "root",
    "password": "l39XNqJG24JmXc2za0"
}

def run_ssh_command(command, timeout=60):
    """执行SSH命令"""
    ssh_cmd = [
        "ssh",
        "-o", "ConnectTimeout=30",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        f"{SERVER_CONFIG['username']}@{SERVER_CONFIG['host']}",
        command
    ]
    
    try:
        # 使用sshpass如果可用
        try:
            subprocess.run(["sshpass", "-V"], capture_output=True, check=True)
            ssh_cmd = ["sshpass", "-p", SERVER_CONFIG['password']] + ssh_cmd
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        result = subprocess.run(
            ssh_cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            encoding='utf-8'
        )
        
        return result.returncode == 0, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def check_system_info():
    """检查系统信息"""
    print("🔍 检查CentOS系统信息...")
    print("=" * 50)
    
    commands = {
        "系统版本": "cat /etc/redhat-release",
        "内核版本": "uname -r",
        "硬件架构": "uname -m",
        "主机名": "hostname",
        "系统运行时间": "uptime",
        "内存信息": "free -h",
        "磁盘空间": "df -h",
        "CPU信息": "lscpu | head -10",
        "网络接口": "ip addr show | grep -E '^[0-9]+:|inet '",
        "防火墙状态": "systemctl is-active firewalld 2>/dev/null || echo 'inactive'",
        "SELinux状态": "getenforce 2>/dev/null || echo 'unknown'"
    }
    
    system_info = {}
    
    for name, command in commands.items():
        print(f"📋 检查{name}...")
        success, output, error = run_ssh_command(command)
        
        if success:
            print(f"✅ {name}:")
            for line in output.strip().split('\n'):
                if line.strip():
                    print(f"   {line}")
            system_info[name] = output.strip()
        else:
            print(f"❌ {name}检查失败: {error}")
            system_info[name] = f"检查失败: {error}"
        
        print()
    
    return system_info

def check_installed_packages():
    """检查已安装的包"""
    print("📦 检查已安装的相关包...")
    print("=" * 50)
    
    packages_to_check = [
        "python3",
        "python3-pip",
        "python3-devel",
        "nginx",
        "gcc",
        "gcc-c++",
        "make",
        "git",
        "curl",
        "wget",
        "firewalld",
        "epel-release"
    ]
    
    installed_packages = {}
    
    for package in packages_to_check:
        print(f"🔍 检查 {package}...")
        success, output, error = run_ssh_command(f"rpm -q {package}")
        
        if success:
            version = output.strip()
            print(f"✅ {package}: {version}")
            installed_packages[package] = version
        else:
            print(f"❌ {package}: 未安装")
            installed_packages[package] = "未安装"
    
    print()
    return installed_packages

def check_services():
    """检查服务状态"""
    print("🔧 检查系统服务状态...")
    print("=" * 50)
    
    services_to_check = [
        "sshd",
        "firewalld",
        "nginx",
        "license-manager"
    ]
    
    service_status = {}
    
    for service in services_to_check:
        print(f"🔍 检查 {service} 服务...")
        
        # 检查服务是否存在
        success, output, error = run_ssh_command(f"systemctl list-unit-files | grep {service}")
        
        if success and service in output:
            # 检查服务状态
            success, output, error = run_ssh_command(f"systemctl is-active {service}")
            status = output.strip() if success else "inactive"
            
            # 检查是否启用
            success, output, error = run_ssh_command(f"systemctl is-enabled {service}")
            enabled = output.strip() if success else "disabled"
            
            print(f"✅ {service}: {status} ({enabled})")
            service_status[service] = {"status": status, "enabled": enabled}
        else:
            print(f"❌ {service}: 服务不存在")
            service_status[service] = {"status": "不存在", "enabled": "不存在"}
    
    print()
    return service_status

def check_network_ports():
    """检查网络端口"""
    print("🌐 检查网络端口...")
    print("=" * 50)
    
    ports_to_check = [22, 80, 443, 5000]
    port_status = {}
    
    for port in ports_to_check:
        print(f"🔍 检查端口 {port}...")
        success, output, error = run_ssh_command(f"netstat -tlnp | grep :{port}")
        
        if success and output.strip():
            print(f"✅ 端口 {port}: 正在监听")
            print(f"   {output.strip()}")
            port_status[port] = "监听中"
        else:
            print(f"❌ 端口 {port}: 未监听")
            port_status[port] = "未监听"
    
    print()
    return port_status

def check_python_environment():
    """检查Python环境"""
    print("🐍 检查Python环境...")
    print("=" * 50)
    
    python_info = {}
    
    # 检查Python版本
    print("🔍 检查Python3版本...")
    success, output, error = run_ssh_command("python3 --version")
    if success:
        version = output.strip()
        print(f"✅ Python3版本: {version}")
        python_info["python3_version"] = version
    else:
        print(f"❌ Python3未安装: {error}")
        python_info["python3_version"] = "未安装"
    
    # 检查pip版本
    print("🔍 检查pip3版本...")
    success, output, error = run_ssh_command("pip3 --version")
    if success:
        version = output.strip()
        print(f"✅ pip3版本: {version}")
        python_info["pip3_version"] = version
    else:
        print(f"❌ pip3未安装: {error}")
        python_info["pip3_version"] = "未安装"
    
    # 检查已安装的Python包
    print("🔍 检查已安装的Python包...")
    packages_to_check = ["flask", "requests", "cryptography"]
    
    for package in packages_to_check:
        success, output, error = run_ssh_command(f"pip3 show {package}")
        if success:
            # 提取版本信息
            for line in output.split('\n'):
                if line.startswith('Version:'):
                    version = line.split(':', 1)[1].strip()
                    print(f"✅ {package}: {version}")
                    python_info[f"{package}_version"] = version
                    break
        else:
            print(f"❌ {package}: 未安装")
            python_info[f"{package}_version"] = "未安装"
    
    print()
    return python_info

def generate_optimization_suggestions(system_info, installed_packages, service_status, port_status, python_info):
    """生成优化建议"""
    print("💡 系统优化建议...")
    print("=" * 50)
    
    suggestions = []
    
    # 检查EPEL源
    if installed_packages.get("epel-release") == "未安装":
        suggestions.append("建议安装EPEL源: yum install -y epel-release")
    
    # 检查Python3
    if installed_packages.get("python3") == "未安装":
        suggestions.append("建议安装Python3: yum install -y python3 python3-pip python3-devel")
    
    # 检查开发工具
    if installed_packages.get("gcc") == "未安装":
        suggestions.append("建议安装开发工具: yum install -y gcc gcc-c++ make")
    
    # 检查nginx
    if installed_packages.get("nginx") == "未安装":
        suggestions.append("建议安装nginx: yum install -y nginx")
    
    # 检查防火墙
    if service_status.get("firewalld", {}).get("status") != "active":
        suggestions.append("建议启动防火墙: systemctl start firewalld && systemctl enable firewalld")
    
    # 检查Python包
    for package in ["flask", "requests", "cryptography"]:
        if python_info.get(f"{package}_version") == "未安装":
            suggestions.append(f"建议安装Python包: pip3 install {package}")
    
    # 检查端口
    if port_status.get(80) == "未监听":
        suggestions.append("建议配置nginx监听80端口")
    
    if suggestions:
        for i, suggestion in enumerate(suggestions, 1):
            print(f"{i}. {suggestion}")
    else:
        print("✅ 系统配置良好，无需特殊优化")
    
    print()
    return suggestions

def save_report(system_info, installed_packages, service_status, port_status, python_info, suggestions):
    """保存检测报告"""
    report = {
        "timestamp": subprocess.run(["date"], capture_output=True, text=True).stdout.strip(),
        "server": SERVER_CONFIG["host"],
        "system_info": system_info,
        "installed_packages": installed_packages,
        "service_status": service_status,
        "port_status": port_status,
        "python_info": python_info,
        "optimization_suggestions": suggestions
    }
    
    filename = f"centos_system_report_{SERVER_CONFIG['host'].replace('.', '_')}.json"
    
    try:
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"📋 检测报告已保存到: {filename}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")

def main():
    """主函数"""
    print("🔍 CentOS 7.4 系统检测工具")
    print("=" * 60)
    print(f"目标服务器: {SERVER_CONFIG['host']}")
    print("=" * 60)
    print()
    
    try:
        # 测试连接
        print("🔗 测试服务器连接...")
        success, output, error = run_ssh_command("echo '连接测试成功'")
        
        if not success:
            print(f"❌ 无法连接到服务器: {error}")
            return False
        
        print("✅ 服务器连接成功")
        print()
        
        # 执行各项检查
        system_info = check_system_info()
        installed_packages = check_installed_packages()
        service_status = check_services()
        port_status = check_network_ports()
        python_info = check_python_environment()
        
        # 生成优化建议
        suggestions = generate_optimization_suggestions(
            system_info, installed_packages, service_status, port_status, python_info
        )
        
        # 保存报告
        save_report(system_info, installed_packages, service_status, port_status, python_info, suggestions)
        
        print("🎉 系统检测完成！")
        return True
        
    except KeyboardInterrupt:
        print("\n👋 检测被用户中断")
        return False
        
    except Exception as e:
        print(f"\n❌ 检测异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    input(f"\n{'✅ 检测完成' if success else '❌ 检测失败'}，按回车键退出...")
