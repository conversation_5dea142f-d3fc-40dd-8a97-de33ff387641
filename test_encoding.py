#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码测试脚本 - 验证中文字符处理
"""

import subprocess
import sys
import os

def test_subprocess_encoding():
    """测试subprocess编码处理"""
    print("测试subprocess编码处理...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
        
        # 测试简单的Python命令
        cmd = [sys.executable, "-c", "print('测试中文输出：构建成功！')"]
        
        process = subprocess.Popen(cmd,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.STDOUT,
                                 text=True,
                                 encoding='utf-8',
                                 errors='replace',
                                 env=env)
        
        output, _ = process.communicate()
        print(f"输出: {output.strip()}")
        
        if process.returncode == 0:
            print("✓ subprocess编码测试成功")
            return True
        else:
            print("✗ subprocess编码测试失败")
            return False
            
    except Exception as e:
        print(f"✗ subprocess编码测试出错: {str(e)}")
        return False

def test_file_encoding():
    """测试文件编码处理"""
    print("\n测试文件编码处理...")
    
    try:
        test_file = "encoding_test.txt"
        test_content = "测试中文内容：亚马逊授权系统构建工具"
        
        # 写入文件
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 读取文件
        with open(test_file, 'r', encoding='utf-8') as f:
            read_content = f.read()
        
        if read_content == test_content:
            print("✓ 文件编码测试成功")
            success = True
        else:
            print("✗ 文件内容不匹配")
            success = False
        
        # 清理
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return success
        
    except Exception as e:
        print(f"✗ 文件编码测试出错: {str(e)}")
        return False

def test_console_encoding():
    """测试控制台编码"""
    print("\n测试控制台编码...")
    
    try:
        # 测试各种中文字符
        test_strings = [
            "简体中文：构建成功",
            "特殊字符：①②③④⑤",
            "符号：★☆♠♣♥♦",
            "数字：１２３４５"
        ]
        
        for test_str in test_strings:
            print(f"测试: {test_str}")
        
        print("✓ 控制台编码测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 控制台编码测试出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始编码测试...")
    print(f"Python版本: {sys.version}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    print("=" * 50)
    
    # 运行测试
    tests = [
        test_console_encoding,
        test_file_encoding,
        test_subprocess_encoding
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有编码测试通过！")
        print("现在可以安全使用GUI构建工具了。")
    else:
        print("❌ 部分编码测试失败")
        print("建议检查系统编码设置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
