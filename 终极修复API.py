#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极修复API - 使用更可靠的方法修复所有路由问题
"""

import paramiko
import os

def ultimate_fix_api():
    """终极修复API问题"""
    print("🚀 终极修复API问题")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 建立SFTP连接
        sftp = client.open_sftp()
        
        # 步骤1: 下载服务器文件到本地
        print("\n📥 步骤1: 下载服务器文件...")
        server_file = f"{config['deploy_path']}/license_server.py"
        local_file = "license_server_download.py"
        
        sftp.get(server_file, local_file)
        print(f"   ✅ 已下载到: {local_file}")
        
        # 步骤2: 在本地修复文件
        print("\n🔧 步骤2: 在本地修复文件...")
        
        with open(local_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有根路由
        has_root_route = '@app.route(\'/\')' in content
        print(f"   根路由存在: {'✅' if has_root_route else '❌'}")
        
        # 如果没有根路由，添加一个
        if not has_root_route:
            root_route = '''
@app.route('/')
def index():
    """根路径 - 返回API信息"""
    return {
        "message": "亚马逊蓝图工具授权服务器",
        "version": "2.1.0",
        "status": "running",
        "apis": [
            "/license/check - 检查授权",
            "/license/generate - 生成授权",
            "/update/check - 检查更新",
            "/update/stats - 更新统计"
        ]
    }
'''
            
            # 在第一个路由前插入
            lines = content.split('\n')
            insert_index = -1
            
            for i, line in enumerate(lines):
                if '@app.route(' in line and 'def ' not in line:
                    insert_index = i
                    break
            
            if insert_index > 0:
                lines.insert(insert_index, root_route)
                content = '\n'.join(lines)
                print("   ✅ 已添加根路由")
        
        # 检查并修复update/stats路由
        if '/update/stats' not in content or 'def get_update_stats' not in content:
            print("   ❌ update/stats路由缺失，添加中...")
            
            # 添加完整的update路由
            update_routes = '''

# Update相关路由
@app.route('/update/stats', methods=['GET'])
def get_update_stats():
    """获取更新统计信息"""
    try:
        import os
        import json
        
        # 定义更新目录
        updates_dir = os.path.join(os.path.dirname(__file__), 'updates')
        version_file = os.path.join(updates_dir, 'version_info.json')
        files_dir = os.path.join(updates_dir, 'files')
        
        # 获取当前版本信息
        current_version = {}
        if os.path.exists(version_file):
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    current_version = json.load(f)
            except:
                pass
        
        # 获取可用文件列表
        available_files = []
        if os.path.exists(files_dir):
            try:
                for filename in os.listdir(files_dir):
                    if filename.endswith('.exe'):
                        file_path = os.path.join(files_dir, filename)
                        file_size = os.path.getsize(file_path)
                        available_files.append({
                            'filename': filename,
                            'size_mb': round(file_size / (1024 * 1024), 2)
                        })
            except:
                pass
        
        return {
            'success': True,
            'current_version': current_version,
            'available_files': available_files
        }
        
    except Exception as e:
        return {'success': False, 'message': f'获取统计信息失败: {str(e)}'}, 500

@app.route('/update/check', methods=['GET'])
def check_update_fixed():
    """检查是否有新版本可用"""
    try:
        from flask import request
        import os
        import json
        
        current_version = request.args.get('current_version')
        if not current_version:
            return {'success': False, 'message': '缺少current_version参数'}, 400
        
        # 定义更新目录
        updates_dir = os.path.join(os.path.dirname(__file__), 'updates')
        version_file = os.path.join(updates_dir, 'version_info.json')
        
        if not os.path.exists(version_file):
            return {
                'success': True,
                'has_update': False,
                'message': '暂无可用更新'
            }
        
        try:
            with open(version_file, 'r', encoding='utf-8') as f:
                version_info = json.load(f)
        except:
            return {
                'success': True,
                'has_update': False,
                'message': '版本信息读取失败'
            }
        
        latest_version = version_info.get('version', '1.0.0')
        has_update = latest_version != current_version
        
        result = {
            'success': True,
            'has_update': has_update,
            'current_version': current_version,
            'latest_version': latest_version
        }
        
        if has_update:
            result['update_info'] = {
                'version': latest_version,
                'changelog': version_info.get('changelog', ''),
                'file_size': version_info.get('file_size', 0),
                'upload_time': version_info.get('upload_time', '')
            }
        
        return result
        
    except Exception as e:
        return {'success': False, 'message': f'检查更新失败: {str(e)}'}, 500
'''
            
            # 在文件末尾添加（在if __name__ == '__main__'之前）
            if 'if __name__ == \'__main__\':' in content:
                content = content.replace('if __name__ == \'__main__\':', update_routes + '\nif __name__ == \'__main__\':')
            else:
                content += update_routes
            
            print("   ✅ 已添加update路由")
        
        # 步骤3: 保存修复后的文件
        print("\n💾 步骤3: 保存修复后的文件...")
        fixed_file = "license_server_fixed.py"
        
        with open(fixed_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   ✅ 已保存到: {fixed_file}")
        
        # 步骤4: 验证Python语法
        print("\n🔍 步骤4: 验证Python语法...")
        import subprocess
        
        try:
            result = subprocess.run(['python', '-m', 'py_compile', fixed_file], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("   ✅ Python语法检查通过")
            else:
                print(f"   ❌ Python语法错误: {result.stderr}")
                return False
        except Exception as e:
            print(f"   ❌ 语法检查异常: {e}")
            return False
        
        # 步骤5: 停止服务并上传修复后的文件
        print("\n🛑 步骤5: 停止服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        stdout.channel.recv_exit_status()
        print("   ✅ 服务已停止")
        
        # 备份原文件
        from datetime import datetime
        backup_name = f"license_server.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        stdin, stdout, stderr = client.exec_command(f"cp {server_file} {config['deploy_path']}/{backup_name}")
        stdout.channel.recv_exit_status()
        print(f"   ✅ 已备份原文件: {backup_name}")
        
        # 上传修复后的文件
        print("\n📤 步骤6: 上传修复后的文件...")
        sftp.put(fixed_file, server_file)
        print("   ✅ 已上传修复后的文件")
        
        # 设置权限
        stdin, stdout, stderr = client.exec_command(f"chmod +x {server_file}")
        stdout.channel.recv_exit_status()
        
        # 步骤7: 启动服务
        print("\n🚀 步骤7: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error}")
            return False
        
        # 等待服务启动
        print("\n⏳ 等待服务完全启动...")
        import time
        time.sleep(15)
        
        # 步骤8: 测试所有API
        print("\n🧪 步骤8: 测试所有API...")
        import requests
        
        test_cases = [
            ("根路径", "http://**************:5000/"),
            ("更新统计", "http://**************:5000/update/stats"),
            ("检查更新", "http://**************:5000/update/check?current_version=1.0.0"),
            ("授权列表", "http://**************:5000/license/list")
        ]
        
        success_count = 0
        for name, url in test_cases:
            try:
                response = requests.get(url, timeout=10)
                print(f"   📋 {name}: HTTP {response.status_code}")
                
                if response.status_code in [200, 400]:
                    success_count += 1
                    print(f"      ✅ API正常")
                    
                    try:
                        data = response.json()
                        if 'success' in data:
                            print(f"      📄 成功: {data['success']}")
                        if 'message' in data:
                            print(f"      📄 消息: {data['message'][:50]}...")
                    except:
                        pass
                        
                elif response.status_code == 404:
                    print(f"      ❌ 路由不存在")
                else:
                    print(f"      ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {name}: 测试失败 - {e}")
        
        # 关闭连接
        sftp.close()
        client.close()
        
        # 清理临时文件
        try:
            os.remove(local_file)
            os.remove(fixed_file)
        except:
            pass
        
        if success_count >= 3:
            print("\n🎉 终极修复成功！")
            print("🌐 所有API现在都应该正常工作了")
            return True
        else:
            print(f"\n⚠️ 修复完成，但仍有 {4-success_count} 个API有问题")
            return False
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 终极修复所有API路由问题")
        print("🔧 方法: 下载→本地修复→验证→上传→测试")
        print("💡 优势: 更可靠的文件操作，完整的路由添加")
        print()
        
        if ultimate_fix_api():
            print("\n✅ 终极修复成功！")
            print("\n📋 现在所有API都应该正常工作:")
            print("• http://**************:5000/ - 根路径")
            print("• http://**************:5000/update/stats - 更新统计")
            print("• http://**************:5000/update/check?current_version=1.0.0")
            print("• http://**************:5000/license/list - 授权列表")
        else:
            print("\n❌ 修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
