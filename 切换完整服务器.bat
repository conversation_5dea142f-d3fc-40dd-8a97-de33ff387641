@echo off
chcp 65001 >nul
title 🔄 切换到完整license_server.py

echo.
echo ==========================================
echo 🔄 切换到完整的license_server.py
echo ==========================================
echo.
echo 🔍 问题确认:
echo • ❌ 当前运行简化版本服务器
echo • ❌ 缺少许可证管理API接口
echo • ❌ license_manager.py无法连接
echo • ✅ 完整的license_server.py已部署
echo.
echo 🎯 解决方案:
echo • 🛑 停止简化版本服务
echo • ⚙️ 修改systemd配置指向license_server.py
echo • 🚀 启动完整的许可证服务器
echo • 🧪 测试所有许可证管理API
echo.
echo 📋 切换后license_manager.py将能够:
echo • ✅ 生成激活码 (/license/generate)
echo • ✅ 查询激活码列表 (/license/list)
echo • ✅ 获取激活码详情 (/license/info)
echo • ✅ 修改激活码 (/license/modify)
echo • ✅ 删除激活码 (/license/delete)
echo • ✅ 完整的许可证管理功能
echo.

echo 🔄 开始切换到完整服务器...
echo.

REM 运行切换脚本
python "切换到完整服务器.py"

echo.
echo 👋 切换完成
pause
