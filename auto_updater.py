#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动更新模块
用于检查和下载程序更新
"""

import os
import sys
import json
import requests
import hashlib
import tempfile
import subprocess
import tkinter as tk
from tkinter import messagebox, ttk
from pathlib import Path
import threading
import time
from urllib.parse import urljoin

# 导入配置
try:
    from update_config import get_config, CURRENT_VERSION
except ImportError:
    # 如果配置文件不存在，使用默认配置
    def get_config(env="production"):
        return {
            "license_server_url": "http://198.23.135.176:5000/",
            "update_server_url": "http://198.23.135.176:5000/api/",
            "current_version": "2.1.0"
        }
    CURRENT_VERSION = "2.1.0"

class AutoUpdater:
    """自动更新器"""
    
    def __init__(self, current_version=None, license_server_url=None,
                 license_key=None, device_id=None):
        """
        初始化更新器

        Args:
            current_version: 当前程序版本
            license_server_url: 授权服务器URL
            license_key: 授权码
            device_id: 设备ID
        """
        # 使用配置文件中的默认值
        config = get_config()

        self.current_version = current_version or config.get("current_version", "2.1.0")
        self.license_server_url = license_server_url or config.get("license_server_url", "http://198.23.135.176:5000/")
        self.server_url = self.license_server_url  # 添加server_url别名
        self.license_key = license_key
        self.device_id = device_id
        self.update_check_url = urljoin(self.license_server_url, "update/check")
        self.update_download_url = urljoin(self.license_server_url, "update/download")
        self.progress_callback = None
        
    def check_for_updates(self):
        """
        检查是否有可用更新

        Returns:
            dict: 更新信息，如果没有更新则返回None
        """
        try:
            # 检查必要参数
            if not self.license_key or not self.device_id:
                print("缺少授权信息，无法检查更新")
                return None

            # 构建请求参数
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'current_version': self.current_version
            }

            # 发送更新检查请求
            response = requests.get(self.update_check_url, params=params, timeout=10)
            response.raise_for_status()

            result = response.json()

            if not result.get('success'):
                print(f"检查更新失败: {result.get('message', '未知错误')}")
                return None

            if result.get('has_update'):
                update_info = result.get('update_info')
                latest_version = update_info.get('version', '')

                # 添加版本比较逻辑，防止重复更新
                if self._is_newer_version(latest_version, self.current_version):
                    print(f"发现新版本: {latest_version} (当前: {self.current_version})")
                    return update_info
                else:
                    print(f"当前版本已是最新: {self.current_version}")
                    return None
            else:
                return None

        except Exception as e:
            print(f"检查更新失败: {e}")
            return None

    def check_update(self, current_version=None):
        """
        检查更新的别名方法

        Args:
            current_version: 当前版本号

        Returns:
            dict: 更新信息，如果没有更新则返回None
        """
        if current_version:
            self.current_version = current_version
        return self.check_for_updates()

    def _is_newer_version(self, latest, current):
        """
        比较版本号
        
        Args:
            latest: 最新版本号
            current: 当前版本号
            
        Returns:
            bool: 如果最新版本更新则返回True
        """
        try:
            latest_parts = [int(x) for x in latest.split('.')]
            current_parts = [int(x) for x in current.split('.')]
            
            # 补齐版本号长度
            max_len = max(len(latest_parts), len(current_parts))
            latest_parts.extend([0] * (max_len - len(latest_parts)))
            current_parts.extend([0] * (max_len - len(current_parts)))
            
            return latest_parts > current_parts
        except:
            return False

    def download_update(self, update_info, progress_callback=None):
        """
        终极下载方法 - 专门解决99.8%问题
        
        Args:
            update_info: 更新信息
            progress_callback: 进度回调函数
            
        Returns:
            str: 下载的文件路径，失败返回None
        """
        try:
            import requests
            import tempfile
            import time
            
            # 获取下载URL和参数
            if self.license_key and self.device_id:
                url = f"{self.server_url.rstrip('/')}/update/download"
                params = {
                    'key': self.license_key,
                    'device_id': self.device_id,
                    'version': update_info.get('version')
                }
            else:
                download_url = update_info.get('download_url', '')
                if download_url.startswith('/'):
                    url = f"{self.server_url.rstrip('/')}{download_url}"
                else:
                    url = download_url
                params = {}
            
            print(f"🚀 终极下载器启动: {url}")
            
            # 创建临时文件
            temp_file = os.path.join(tempfile.gettempdir(), f"ultimate_update_{int(time.time())}.exe")
            expected_size = update_info.get('file_size', 0)
            
            # 终极下载配置
            chunk_size = 4096      # 4KB小块
            max_retries = 10       # 最多10次重试
            timeout = (15, 60)     # 更短超时
            
            print(f"📊 预期大小: {expected_size:,} 字节")
            
            # 智能重试下载
            for attempt in range(max_retries):
                try:
                    print(f"\n📥 尝试 {attempt + 1}/{max_retries}")
                    
                    # 检查已下载的大小
                    resume_pos = 0
                    if os.path.exists(temp_file):
                        resume_pos = os.path.getsize(temp_file)
                        print(f"📂 已下载: {resume_pos:,} 字节")
                    
                    # 如果已经基本完整，直接返回
                    if resume_pos > 0 and expected_size > 0:
                        completion = (resume_pos / expected_size) * 100
                        if completion >= 99.0:
                            print(f"✅ 文件已基本完整 ({completion:.1f}%)")
                            if progress_callback:
                                progress_callback(100.0)
                            return temp_file
                    
                    # 设置断点续传请求头
                    headers = {}
                    if resume_pos > 0:
                        headers['Range'] = f'bytes={resume_pos}-'
                        print(f"🔄 断点续传: {resume_pos:,} 字节开始")
                    
                    # 发送请求
                    response = requests.get(
                        url,
                        params=params,
                        headers=headers,
                        stream=True,
                        timeout=timeout
                    )
                    
                    # 检查响应状态
                    if response.status_code == 206:
                        print("✅ 服务器支持断点续传")
                        content_length = int(response.headers.get('Content-Length', 0))
                        total_size = resume_pos + content_length
                    elif response.status_code == 200:
                        if resume_pos > 0:
                            print("⚠️ 服务器不支持断点续传，重新下载")
                            if os.path.exists(temp_file):
                                os.remove(temp_file)
                            resume_pos = 0
                        total_size = int(response.headers.get('Content-Length', expected_size))
                    else:
                        raise Exception(f"HTTP错误: {response.status_code}")
                    
                    print(f"📊 总大小: {total_size:,} 字节")
                    
                    # 下载文件
                    mode = 'ab' if resume_pos > 0 else 'wb'
                    downloaded_size = resume_pos
                    last_update = time.time()
                    
                    with open(temp_file, mode) as f:
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)
                                
                                # 立即刷新缓冲区
                                f.flush()
                                
                                # 更新进度 (每200ms更新一次)
                                current_time = time.time()
                                if current_time - last_update >= 0.2:
                                    if total_size > 0:
                                        progress = (downloaded_size / total_size) * 100
                                        if progress_callback:
                                            progress_callback(min(progress, 100.0))
                                    last_update = current_time
                        
                        # 强制同步到磁盘
                        f.flush()
                        os.fsync(f.fileno())
                    
                    # 验证下载
                    actual_size = os.path.getsize(temp_file)
                    print(f"📁 实际大小: {actual_size:,} 字节")
                    
                    if total_size > 0:
                        completion = (actual_size / total_size) * 100
                        print(f"📈 完成率: {completion:.2f}%")
                        
                        # 宽松的完成判断 - 99%即可
                        if completion >= 99.0:
                            print("🎉 终极下载器成功!")
                            if progress_callback:
                                progress_callback(100.0)
                            return temp_file
                        else:
                            print(f"⚠️ 下载不完整，继续重试...")
                    else:
                        print("✅ 下载完成 (无法验证大小)")
                        if progress_callback:
                            progress_callback(100.0)
                        return temp_file
                
                except Exception as e:
                    print(f"❌ 下载失败: {e}")
                    
                    # 检查是否有部分下载
                    if os.path.exists(temp_file):
                        partial_size = os.path.getsize(temp_file)
                        if partial_size > 0 and expected_size > 0:
                            completion = (partial_size / expected_size) * 100
                            print(f"📂 部分下载: {partial_size:,} 字节 ({completion:.1f}%)")
                            
                            # 如果已经下载了98%以上，认为基本成功
                            if completion >= 98.0:
                                print("✅ 下载基本成功 (>98%)")
                                if progress_callback:
                                    progress_callback(100.0)
                                return temp_file
                    
                    if attempt < max_retries - 1:
                        delay = min(2 ** attempt, 8)  # 最大8秒延迟
                        print(f"⏱️ 等待 {delay} 秒后重试...")
                        time.sleep(delay)
            
            print("❌ 终极下载器：所有重试都失败了")
            return None
            
        except Exception as e:
            print(f"❌ 终极下载器失败: {e}")
            return None
    def _verify_file_hash(self, file_path, expected_hash):
        """
        验证文件哈希值
        
        Args:
            file_path: 文件路径
            expected_hash: 期望的哈希值
            
        Returns:
            bool: 验证是否通过
        """
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            return sha256_hash.hexdigest().lower() == expected_hash.lower()
        except:
            return False
    
    def apply_update(self, update_file_path, new_version=None):
        """
        应用更新

        Args:
            update_file_path: 更新文件路径
            new_version: 新版本号

        Returns:
            bool: 是否成功启动更新
        """
        try:
            current_exe = sys.executable
            if getattr(sys, 'frozen', False):
                current_exe = sys.executable

            # 创建更新脚本
            update_script = self._create_update_script(update_file_path, current_exe, new_version)

            # 启动更新脚本
            subprocess.Popen([sys.executable, update_script],
                           creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)

            return True

        except Exception as e:
            print(f"应用更新失败: {e}")
            return False
    
    def _create_update_script(self, new_file, current_file, new_version=None):
        """
        创建更新脚本

        Args:
            new_file: 新文件路径
            current_file: 当前文件路径
            new_version: 新版本号

        Returns:
            str: 更新脚本路径
        """
        script_content = f'''
import os
import sys
import time
import shutil
import json
from datetime import datetime

def update_program():
    try:
        # 等待主程序退出
        time.sleep(2)

        # 备份当前文件
        backup_file = "{current_file}.backup"
        if os.path.exists("{current_file}"):
            shutil.copy2("{current_file}", backup_file)

        # 替换文件
        shutil.copy2("{new_file}", "{current_file}")

        # 删除临时文件
        os.remove("{new_file}")

        # 更新版本配置文件
        try:
            config_file = os.path.join(os.path.dirname("{current_file}"), "update_config.py")
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 从服务器获取的版本号应该在这里更新
                import re
                new_version = "{new_version or 'unknown'}"
                content = re.sub(
                    r'"current_version":\\s*"[^"]*"',
                    f'"current_version": "{{new_version}}"',
                    content
                )

                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                print(f"版本号已更新为: {{new_version}}")
        except Exception as ve:
            print(f"更新版本号失败: {{ve}}")

        # 记录更新信息
        try:
            update_info_file = os.path.join(os.path.expanduser("~"), ".amazon_last_update.json")
            update_info = {{
                "version": "{new_version or 'unknown'}",
                "timestamp": datetime.now().isoformat(),
                "updated": True,
                "update_file": "{current_file}"
            }}

            with open(update_info_file, 'w', encoding='utf-8') as f:
                json.dump(update_info, f, indent=2, ensure_ascii=False)
        except Exception as ue:
            print(f"保存更新信息失败: {{ue}}")

        # 启动新程序
        os.startfile("{current_file}")

        print("更新完成！")

    except Exception as e:
        print(f"更新失败: {{e}}")
        # 恢复备份
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, "{current_file}")

if __name__ == "__main__":
    update_program()
'''
        
        script_path = os.path.join(tempfile.gettempdir(), "update_script.py")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path


class UpdateDialog:
    """更新对话框"""
    
    def __init__(self, parent, update_info):
        """
        初始化更新对话框
        
        Args:
            parent: 父窗口
            update_info: 更新信息
        """
        self.parent = parent
        self.update_info = update_info
        self.result = False
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("发现新版本")
        self.dialog.geometry("450x400")  # 增加对话框大小确保按钮可见
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 设置图标（与主程序相同）
        self.set_dialog_icon()

        # 居中显示
        if parent:
            self.dialog.geometry("+%d+%d" % (
                parent.winfo_rootx() + 50,
                parent.winfo_rooty() + 50
            ))
        else:
            # 如果没有父窗口，居中显示在屏幕上
            self.dialog.geometry("+%d+%d" % (
                (self.dialog.winfo_screenwidth() // 2) - 200,
                (self.dialog.winfo_screenheight() // 2) - 150
            ))
        
        self.create_widgets()

    def set_dialog_icon(self):
        """设置对话框图标，与主程序保持一致"""
        try:
            # 尝试多种图标路径
            icon_paths = []

            # 1. 检查是否是打包环境
            if getattr(sys, 'frozen', False):
                # 打包环境下的图标路径
                exe_dir = os.path.dirname(sys.executable)
                icon_paths.append(os.path.join(exe_dir, "icon.ico"))

                # PyInstaller临时目录
                import tempfile
                temp_dir = tempfile._get_default_tempdir()
                for mei_dir in ["_MEI", "_MEIxxxx"]:
                    temp_icon = os.path.join(temp_dir, mei_dir, "icon.ico")
                    icon_paths.append(temp_icon)
            else:
                # 开发环境下的图标路径
                base_dir = os.path.dirname(os.path.abspath(__file__))
                icon_paths.append(os.path.join(base_dir, "icon.ico"))

            # 2. 检查应用数据目录
            try:
                app_data = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
                icon_paths.append(os.path.join(app_data, "icon.ico"))
            except:
                pass

            # 3. 检查用户目录
            icon_paths.append(os.path.join(os.path.expanduser("~"), "icon.ico"))

            # 4. 检查当前目录
            icon_paths.append("icon.ico")

            # 尝试设置图标
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    try:
                        self.dialog.iconbitmap(icon_path)
                        return True
                    except Exception:
                        continue

            # 如果没有找到图标文件，尝试从父窗口复制图标
            if self.parent:
                try:
                    # 获取父窗口的图标
                    parent_icon = self.parent.iconbitmap()
                    if parent_icon:
                        self.dialog.iconbitmap(parent_icon)
                        return True
                except:
                    pass

        except Exception:
            pass

        return False

    def create_widgets(self):
        """创建界面控件"""
        # 标题
        title_label = tk.Label(
            self.dialog,
            text="🎉 发现新版本！",
            font=("微软雅黑", 14, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # 版本信息
        info_frame = tk.Frame(self.dialog)
        info_frame.pack(fill=tk.X, padx=20, pady=10)
        
        current_label = tk.Label(
            info_frame,
            text=f"当前版本: v{self.update_info.get('current_version', 'Unknown')}",
            font=("微软雅黑", 10)
        )
        current_label.pack(anchor='w')
        
        latest_label = tk.Label(
            info_frame,
            text=f"最新版本: v{self.update_info.get('version', 'Unknown')}",
            font=("微软雅黑", 10, "bold"),
            fg="#e74c3c"
        )
        latest_label.pack(anchor='w')
        
        # 更新说明
        changelog_label = tk.Label(
            info_frame,
            text="更新内容:",
            font=("微软雅黑", 10, "bold")
        )
        changelog_label.pack(anchor='w', pady=(10, 5))
        
        changelog_text = tk.Text(
            info_frame,
            height=8,  # 增加高度
            width=45,  # 增加宽度
            font=("微软雅黑", 9),
            wrap=tk.WORD,
            bg="#f8f9fa",
            relief=tk.SUNKEN,
            bd=1
        )
        changelog_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        changelog_text.insert(tk.END, self.update_info.get('changelog', '暂无更新说明'))
        changelog_text.config(state=tk.DISABLED)
        
        # 按钮区域 - 确保按钮可见
        button_frame = tk.Frame(self.dialog, bg="#ffffff")
        button_frame.pack(fill=tk.X, padx=20, pady=(10, 20), side=tk.BOTTOM)

        # 创建按钮容器，居中显示
        button_container = tk.Frame(button_frame, bg="#ffffff")
        button_container.pack(expand=True)

        update_button = tk.Button(
            button_container,
            text="🚀 立即更新",
            command=self.on_update,
            bg="#27ae60",
            fg="white",
            font=("微软雅黑", 11, "bold"),
            padx=25,
            pady=8,
            relief=tk.RAISED,
            bd=2,
            cursor="hand2"
        )
        update_button.pack(side=tk.LEFT, padx=(0, 15))

        later_button = tk.Button(
            button_container,
            text="⏰ 稍后提醒",
            command=self.on_later,
            bg="#95a5a6",
            fg="white",
            font=("微软雅黑", 10),
            padx=25,
            pady=8,
            relief=tk.RAISED,
            bd=2,
            cursor="hand2"
        )
        later_button.pack(side=tk.LEFT)

        # 添加关闭按钮
        close_button = tk.Button(
            button_container,
            text="❌ 关闭",
            command=self.on_later,
            bg="#e74c3c",
            fg="white",
            font=("微软雅黑", 10),
            padx=25,
            pady=8,
            relief=tk.RAISED,
            bd=2,
            cursor="hand2"
        )
        close_button.pack(side=tk.LEFT, padx=(15, 0))
    
    def on_update(self):
        """点击更新按钮"""
        self.result = True
        self.dialog.destroy()
    
    def on_later(self):
        """点击稍后按钮"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


# 删除重复的函数定义，使用下面的完整版本

def check_and_update(parent_window=None, current_version="2.1.0", license_key=None, device_id=None):
    """
    检查并处理更新

    Args:
        parent_window: 父窗口
        current_version: 当前版本
        license_key: 授权码
        device_id: 设备ID
    """
    # 获取配置
    config = get_config()

    # 如果没有授权信息，使用静态文件方式（向后兼容）
    if license_key and device_id:
        updater = AutoUpdater(current_version, config["license_server_url"], license_key, device_id)
    else:
        # 向后兼容：使用静态文件方式
        updater = AutoUpdater(current_version, config["update_server_url"])

    # 检查更新
    update_info = updater.check_for_updates()
    if not update_info:
        return False

    # 显示更新对话框
    update_info['current_version'] = current_version
    dialog = UpdateDialog(parent_window, update_info)

    if dialog.show():
        # 用户选择更新
        download_and_install(updater, update_info, parent_window)
        return True

    return False


def check_and_update_silent(parent_window=None, current_version="2.1.0",
                           license_key=None, device_id=None):
    """
    静默检查并更新程序 - 只显示进度，自动重启

    Args:
        parent_window: 父窗口
        current_version: 当前版本
        license_key: 授权码
        device_id: 设备ID

    Returns:
        bool: 是否进行了更新
    """
    try:
        # 获取配置
        config = get_config()

        # 如果没有授权信息，使用静态文件方式（向后兼容）
        if license_key and device_id:
            updater = AutoUpdater(current_version, config["license_server_url"], license_key, device_id)
        else:
            # 向后兼容：使用静态文件方式
            updater = AutoUpdater(current_version, config["update_server_url"])

        # 检查更新
        update_info = updater.check_for_updates()
        if not update_info:
            return False

        # 直接开始静默下载和安装
        download_and_install_silent(updater, update_info, parent_window)
        return True

    except Exception as e:
        return False


def download_and_install(updater, update_info, parent_window):
    """
    下载并安装更新
    
    Args:
        updater: 更新器实例
        update_info: 更新信息
        parent_window: 父窗口
    """
    # 创建进度对话框
    progress_dialog = tk.Toplevel(parent_window)
    progress_dialog.title("正在更新")
    progress_dialog.geometry("300x150")
    progress_dialog.resizable(False, False)
    if parent_window:
        progress_dialog.transient(parent_window)
        progress_dialog.grab_set()

        # 居中显示
        progress_dialog.geometry("+%d+%d" % (
            parent_window.winfo_rootx() + 100,
            parent_window.winfo_rooty() + 100
        ))
    else:
        # 如果没有父窗口，居中显示在屏幕上
        progress_dialog.geometry("+%d+%d" % (
            (progress_dialog.winfo_screenwidth() // 2) - 150,
            (progress_dialog.winfo_screenheight() // 2) - 100
        ))
    
    # 进度条
    progress_label = tk.Label(progress_dialog, text="正在下载更新...", font=("微软雅黑", 10))
    progress_label.pack(pady=20)
    
    progress_bar = ttk.Progressbar(progress_dialog, length=250, mode='determinate')
    progress_bar.pack(pady=10)
    
    status_label = tk.Label(progress_dialog, text="0%", font=("微软雅黑", 9))
    status_label.pack()
    
    def update_progress(progress):
        """更新进度"""
        progress_bar['value'] = progress
        status_label.config(text=f"{progress:.1f}%")
        progress_dialog.update()
    
    def download_thread():
        """下载线程"""
        try:
            # 下载更新
            update_file = updater.download_update(update_info, update_progress)
            
            if update_file:
                progress_label.config(text="正在应用更新...")
                progress_dialog.update()
                
                # 应用更新
                new_version = update_info.get('version', 'unknown')
                if updater.apply_update(update_file, new_version):
                    messagebox.showinfo("更新成功", "更新将在程序重启后生效")
                    sys.exit(0)  # 退出当前程序
                else:
                    messagebox.showerror("更新失败", "应用更新时发生错误")
            else:
                messagebox.showerror("更新失败", "下载更新文件失败")
                
        except Exception as e:
            messagebox.showerror("更新失败", f"更新过程中发生错误: {e}")
        
        finally:
            progress_dialog.destroy()
    
    # 启动下载线程
    thread = threading.Thread(target=download_thread)
    thread.daemon = True
    thread.start()
    
    progress_dialog.wait_window()


def download_and_install_silent(updater, update_info, parent_window):
    """
    静默下载并安装更新 - 只显示进度条
    """
    import threading

    # 创建简化的进度窗口
    progress_dialog = tk.Toplevel()
    progress_dialog.title("正在更新")
    progress_dialog.geometry("400x120")
    progress_dialog.resizable(False, False)

    # 设置图标
    try:
        progress_dialog.iconbitmap("icon.ico")
    except:
        pass

    # 居中显示
    if parent_window:
        progress_dialog.geometry("+%d+%d" % (
            parent_window.winfo_rootx() + 100,
            parent_window.winfo_rooty() + 100
        ))
    else:
        progress_dialog.geometry("+%d+%d" % (
            (progress_dialog.winfo_screenwidth() // 2) - 200,
            (progress_dialog.winfo_screenheight() // 2) - 60
        ))

    # 禁用关闭按钮
    progress_dialog.protocol("WM_DELETE_WINDOW", lambda: None)

    # 状态标签
    status_var = tk.StringVar(value="正在下载更新...")
    status_label = tk.Label(
        progress_dialog,
        textvariable=status_var,
        font=("微软雅黑", 10)
    )
    status_label.pack(pady=10)

    # 进度条
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(
        progress_dialog,
        variable=progress_var,
        maximum=100,
        length=350
    )
    progress_bar.pack(pady=10)

    # 百分比标签
    percent_var = tk.StringVar(value="0%")
    percent_label = tk.Label(
        progress_dialog,
        textvariable=percent_var,
        font=("微软雅黑", 9),
        fg="#666666"
    )
    percent_label.pack()

    # 保持窗口在最前面
    progress_dialog.attributes('-topmost', True)
    progress_dialog.focus_force()

    def update_progress(progress):
        """更新进度"""
        progress_var.set(progress)
        percent_var.set(f"{progress:.1f}%")
        progress_dialog.update()

    def update_status(status):
        """更新状态"""
        status_var.set(status)
        progress_dialog.update()

    def download_thread():
        """下载线程"""
        try:
            # 下载文件
            update_status("正在下载更新...")
            temp_file = updater.download_update(update_info, update_progress)

            if temp_file:
                update_status("正在安装更新...")
                update_progress(95)

                # 应用更新
                new_version = update_info.get('version', 'unknown')
                if updater.apply_update(temp_file, new_version):
                    update_status("更新完成，正在重启...")
                    update_progress(100)

                    # 等待1秒后重启
                    progress_dialog.after(1000, restart_application)
                else:
                    update_status("安装失败")
                    progress_dialog.after(2000, progress_dialog.destroy)
            else:
                update_status("下载失败")
                progress_dialog.after(2000, progress_dialog.destroy)

        except Exception as e:
            update_status("更新失败")
            progress_dialog.after(2000, progress_dialog.destroy)

    def restart_application():
        """重启应用程序"""
        try:
            # 关闭进度窗口
            progress_dialog.destroy()

            # 如果有父窗口，也关闭它
            if parent_window:
                parent_window.quit()

            # 重启程序
            if getattr(sys, 'frozen', False):
                # 如果是exe文件
                subprocess.Popen([sys.executable])
            else:
                # 如果是Python脚本
                subprocess.Popen([sys.executable] + sys.argv)

            # 退出当前程序
            sys.exit(0)
        except:
            sys.exit(0)

    # 启动下载线程
    thread = threading.Thread(target=download_thread, daemon=True)
    thread.start()

    progress_dialog.wait_window()


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    check_and_update(root, "1.0.0")
