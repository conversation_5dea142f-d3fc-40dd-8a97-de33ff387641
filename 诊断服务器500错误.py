#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断服务器500错误
"""

import requests
import json

def test_update_api_detailed():
    """详细测试更新API"""
    print("🔍 诊断服务器500错误")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 测试用例
    test_cases = [
        {
            "name": "管理员权限测试",
            "params": {
                "current_version": "2.1.0",
                "key": "ADMIN_BYPASS",
                "device_id": "ADMIN-DEVICE-001"
            }
        },
        {
            "name": "普通用户测试",
            "params": {
                "current_version": "2.1.0", 
                "key": "83R2AXQK-20250725-67c80d8d",
                "device_id": "20cc47fd9ca63e67"
            }
        },
        {
            "name": "缺少参数测试",
            "params": {
                "current_version": "2.1.0"
            }
        }
    ]
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test['name']}")
        print(f"📋 参数: {test['params']}")
        
        try:
            response = requests.get(
                f"{server_url}/update/check",
                params=test['params'],
                timeout=10
            )
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ 响应成功")
                    print(f"📄 成功: {data.get('success', False)}")
                    print(f"📄 消息: {data.get('message', 'No message')}")
                except Exception as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"📄 原始响应: {response.text[:200]}...")
                    
            elif response.status_code == 400:
                try:
                    data = response.json()
                    print(f"⚠️ 参数错误: {data.get('message', 'Unknown')}")
                except:
                    print(f"⚠️ 参数错误: {response.text[:100]}...")
                    
            elif response.status_code == 401:
                try:
                    data = response.json()
                    print(f"🚫 认证失败: {data.get('message', 'Unknown')}")
                except:
                    print(f"🚫 认证失败: {response.text[:100]}...")
                    
            elif response.status_code == 500:
                print(f"❌ 服务器内部错误")
                print(f"📄 错误响应: {response.text[:200]}...")
                
                # 尝试获取更多错误信息
                try:
                    data = response.json()
                    print(f"📄 错误详情: {data}")
                except:
                    print("📄 无法解析错误响应为JSON")
                    
            else:
                print(f"❓ 未知状态码: {response.status_code}")
                print(f"📄 响应: {response.text[:100]}...")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败")
        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时")
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def test_other_apis():
    """测试其他API是否正常"""
    print("\n" + "=" * 50)
    print("🔍 测试其他API状态")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    apis = [
        {
            "name": "根路径",
            "method": "GET",
            "url": "/",
            "expected": [200]
        },
        {
            "name": "更新统计",
            "method": "GET", 
            "url": "/update/stats",
            "expected": [200]
        },
        {
            "name": "License检查",
            "method": "POST",
            "url": "/license/check",
            "data": {"key": "test", "device_id": "test"},
            "expected": [200, 400, 401]
        }
    ]
    
    for api in apis:
        print(f"\n📡 测试: {api['name']}")
        
        try:
            if api['method'] == 'GET':
                response = requests.get(f"{server_url}{api['url']}", timeout=5)
            else:
                response = requests.post(f"{server_url}{api['url']}", json=api.get('data', {}), timeout=5)
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code in api['expected']:
                print(f"✅ API正常")
            else:
                print(f"⚠️ API异常")
                
        except Exception as e:
            print(f"❌ API测试失败: {e}")

def main():
    """主函数"""
    print("🎯 诊断license_client.py的500错误")
    print("🔧 错误信息: 500 Server Error for /update/check")
    print("📋 用户激活码: 83R2AXQK-20250725-67c80d8d")
    print()
    
    test_update_api_detailed()
    test_other_apis()
    
    print("\n" + "=" * 50)
    print("💡 可能的问题原因:")
    print("1. 服务器端/update/check API代码有bug")
    print("2. 数据库文件损坏或不存在")
    print("3. 激活码格式或验证逻辑问题")
    print("4. Python依赖库问题")
    print("5. 服务器资源不足")
    
    print("\n🔧 建议解决方案:")
    print("1. 检查服务器日志文件")
    print("2. 重启license-manager服务")
    print("3. 检查/update/check API代码")
    print("4. 验证数据库文件完整性")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
