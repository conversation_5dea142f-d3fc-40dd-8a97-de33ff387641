#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新对话框显示
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from auto_updater import UpdateDialog
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def test_update_dialog():
    """测试更新对话框"""
    print("🧪 测试更新对话框")
    print("=" * 40)
    
    # 创建主窗口
    root = tk.Tk()
    root.title("测试主窗口")
    root.geometry("300x200")
    root.withdraw()  # 隐藏主窗口
    
    # 模拟更新信息
    update_info = {
        'current_version': '2.1.0',
        'version': '2.1.1',
        'changelog': '''🎉 新功能:
• 优化了界面显示效果
• 修复了已知bug
• 提升了程序稳定性
• 增加了新的功能模块

🔧 修复内容:
• 修复了登录问题
• 优化了网络连接
• 改进了错误处理

📈 性能提升:
• 启动速度提升30%
• 内存占用减少20%
• 响应速度更快'''
    }
    
    try:
        print("✅ 创建更新对话框...")
        
        # 创建更新对话框
        dialog = UpdateDialog(root, update_info)
        
        print("✅ 对话框创建成功")
        print("📋 对话框信息:")
        print(f"   - 当前版本: {update_info['current_version']}")
        print(f"   - 最新版本: {update_info['version']}")
        print(f"   - 更新内容长度: {len(update_info['changelog'])} 字符")
        
        print("\n🎯 显示对话框...")
        print("💡 请检查对话框是否正确显示:")
        print("   1. 标题: '发现新版本'")
        print("   2. 版本信息显示正确")
        print("   3. 更新内容显示完整")
        print("   4. 按钮显示正常:")
        print("      - 🚀 立即更新 (绿色)")
        print("      - ⏰ 稍后提醒 (灰色)")
        print("      - ❌ 关闭 (红色)")
        
        # 显示对话框
        result = dialog.show()
        
        print(f"\n📊 用户选择结果: {'立即更新' if result else '稍后提醒/关闭'}")
        
        if result:
            print("✅ 用户选择立即更新")
            messagebox.showinfo("测试结果", "用户选择了立即更新！\n在实际程序中，这里会开始下载更新。")
        else:
            print("⏰ 用户选择稍后提醒或关闭")
            messagebox.showinfo("测试结果", "用户选择了稍后提醒或关闭。")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        messagebox.showerror("错误", f"测试失败: {str(e)}")
        return False
    
    finally:
        try:
            root.destroy()
        except:
            pass

def test_dialog_layout():
    """测试对话框布局"""
    print("\n🎨 测试对话框布局")
    print("=" * 40)
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("布局测试")
    root.geometry("500x500")
    
    # 创建说明标签
    info_label = tk.Label(
        root,
        text="点击按钮测试更新对话框布局",
        font=("微软雅黑", 12),
        pady=20
    )
    info_label.pack()
    
    def show_dialog():
        update_info = {
            'current_version': '2.1.0',
            'version': '2.1.1',
            'changelog': '这是一个测试更新说明。\n包含多行内容。\n用于测试布局效果。'
        }
        
        dialog = UpdateDialog(root, update_info)
        result = dialog.show()
        
        result_text = "立即更新" if result else "稍后提醒"
        messagebox.showinfo("结果", f"用户选择: {result_text}")
    
    # 测试按钮
    test_button = tk.Button(
        root,
        text="🧪 显示更新对话框",
        command=show_dialog,
        font=("微软雅黑", 11),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10
    )
    test_button.pack(pady=20)
    
    # 说明文本
    help_text = tk.Text(root, height=15, width=60, font=("微软雅黑", 9))
    help_text.pack(padx=20, pady=20, fill=tk.BOTH, expand=True)
    
    help_content = """📋 测试检查项目:

✅ 对话框大小: 450x400 像素
✅ 标题显示: "发现新版本"
✅ 版本信息: 当前版本和最新版本
✅ 更新内容: 多行文本显示
✅ 按钮区域: 三个按钮水平排列
   - 🚀 立即更新 (绿色)
   - ⏰ 稍后提醒 (灰色)  
   - ❌ 关闭 (红色)

🎯 预期效果:
- 对话框居中显示
- 所有内容都在可视区域内
- 按钮在底部清晰可见
- 文本内容可以滚动查看

❌ 如果按钮不可见:
- 检查对话框大小设置
- 检查布局管理器使用
- 检查pack参数设置"""
    
    help_text.insert(tk.END, help_content)
    help_text.config(state=tk.DISABLED)
    
    print("✅ 布局测试窗口已创建")
    print("💡 请点击按钮测试对话框显示效果")
    
    root.mainloop()

def main():
    """主函数"""
    print("🔧 更新对话框测试工具")
    print("=" * 50)
    
    try:
        # 基础功能测试
        print("1️⃣ 基础功能测试")
        success = test_update_dialog()
        
        if success:
            print("\n✅ 基础测试通过")
            
            # 询问是否进行布局测试
            print("\n2️⃣ 布局测试")
            print("💡 即将打开布局测试窗口...")
            
            # 布局测试
            test_dialog_layout()
        else:
            print("\n❌ 基础测试失败")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")

if __name__ == "__main__":
    main()
