@echo off
chcp 65001 >nul
title 📊 改进版部署工具 - 带进度显示

echo.
echo ==========================================
echo 📊 改进版CentOS部署工具 - 带详细进度
echo ==========================================
echo.
echo 🎯 新增功能:
echo • 📊 实时进度条显示 (0-100%%)
echo • 🔧 详细步骤说明
echo • ⚡ 实时状态更新
echo • 🔍 精确定位卡住位置
echo • 📋 详细错误信息
echo.
echo 🚀 部署步骤:
echo   1. 准备配置 (5%%)
echo   2. 测试连接 (15%%)
echo   3. 创建目录 (25%%)
echo   4. 上传文件 (40%%)
echo   5. 安装依赖 (60%%)
echo   6. 配置服务 (75%%)
echo   7. 配置防火墙 (85%%)
echo   8. 启动服务 (95%%)
echo   9. 验证部署 (100%%)
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "一键部署界面工具.py" (
    echo ❌ 找不到一键部署界面工具.py
    echo 🔧 尝试创建测试文件...
    python "测试部署界面.py"
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.
echo 🖥️ 启动改进版部署工具...

REM 启动GUI
python "一键部署界面工具.py"

echo.
echo 👋 程序已退出
pause
