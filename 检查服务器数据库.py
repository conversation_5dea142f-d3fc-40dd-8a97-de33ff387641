#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查服务器数据库 - 通过SSH检查服务器上的授权数据库
"""

import paramiko
import json
import os

def check_server_database():
    """检查服务器数据库"""
    print("🔍 检查服务器数据库")
    print("=" * 50)
    
    # 服务器配置
    server_config = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0'
    }
    
    try:
        print("🌐 连接服务器...")
        
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # 连接服务器
        ssh.connect(**server_config, timeout=10)
        print("✅ SSH连接成功")
        
        # 检查授权数据库文件
        database_path = "/opt/license_manager/license_database.json"
        
        print(f"📁 检查数据库文件: {database_path}")
        
        # 检查文件是否存在
        stdin, stdout, stderr = ssh.exec_command(f"ls -la {database_path}")
        file_info = stdout.read().decode('utf-8').strip()
        error_info = stderr.read().decode('utf-8').strip()
        
        if error_info and "No such file" in error_info:
            print("❌ 授权数据库文件不存在")
            print("💡 这解释了为什么密钥不存在")
            
            # 尝试创建空数据库
            print("\n🔧 尝试创建空数据库...")
            create_cmd = f'echo "{{}}" > {database_path}'
            stdin, stdout, stderr = ssh.exec_command(create_cmd)
            
            create_error = stderr.read().decode('utf-8').strip()
            if create_error:
                print(f"❌ 创建数据库失败: {create_error}")
            else:
                print("✅ 空数据库创建成功")
                
        else:
            print(f"✅ 数据库文件存在: {file_info}")
            
            # 读取数据库内容
            print("\n📄 读取数据库内容...")
            stdin, stdout, stderr = ssh.exec_command(f"cat {database_path}")
            db_content = stdout.read().decode('utf-8').strip()
            db_error = stderr.read().decode('utf-8').strip()
            
            if db_error:
                print(f"❌ 读取数据库失败: {db_error}")
            else:
                try:
                    db_data = json.loads(db_content)
                    print(f"📊 数据库记录数: {len(db_data)}")
                    
                    if db_data:
                        print("\n🔑 现有密钥:")
                        for key, info in db_data.items():
                            status = info.get('status', 'unknown')
                            created = info.get('created_at', 'unknown')
                            level = info.get('permission_level', 'unknown')
                            print(f"   {key[:20]}... - 状态:{status} 级别:{level} 创建:{created}")
                    else:
                        print("📝 数据库为空")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ 数据库格式错误: {e}")
                    print(f"原始内容: {db_content[:200]}...")
        
        # 检查服务器日志
        print("\n📋 检查服务器日志...")
        log_path = "/opt/license_manager/license_server.log"
        
        stdin, stdout, stderr = ssh.exec_command(f"tail -20 {log_path}")
        log_content = stdout.read().decode('utf-8').strip()
        log_error = stderr.read().decode('utf-8').strip()
        
        if log_error and "No such file" in log_error:
            print("⚠️ 服务器日志文件不存在")
        else:
            print("📄 最近20行日志:")
            for line in log_content.split('\n')[-10:]:  # 只显示最后10行
                if line.strip():
                    print(f"   {line}")
        
        ssh.close()
        print("\n✅ 服务器检查完成")
        return True
        
    except paramiko.AuthenticationException:
        print("❌ SSH认证失败 - 请检查用户名和密码")
        return False
    except paramiko.SSHException as e:
        print(f"❌ SSH连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 检查服务器上的授权数据库状态")
        print("🔧 功能: 诊断密钥不存在的原因")
        print("💡 服务器: **************")
        print()
        
        if check_server_database():
            print("\n📋 检查结果分析:")
            print("1. 如果数据库文件不存在 - 需要初始化数据库")
            print("2. 如果数据库为空 - 需要重新生成密钥")
            print("3. 如果有权限问题 - 需要修复文件权限")
            print("4. 如果服务未运行 - 需要重启license-manager服务")
        else:
            print("\n❌ 服务器检查失败")
            print("请检查网络连接和服务器状态")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
