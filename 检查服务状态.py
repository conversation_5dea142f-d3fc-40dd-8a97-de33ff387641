#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查服务器实际状态
"""

import paramiko
import sys
import time

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器实际状态")
    print("=" * 40)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 检查1: 服务状态
    print("🔧 检查1: systemd服务状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl status license-manager --no-pager")
    print("服务状态:")
    for line in output.split('\n'):
        if line.strip():
            print(f"   {line}")
    print()
    
    # 检查2: 端口监听
    print("🔌 检查2: 端口监听状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "netstat -tlnp | grep -E ':(5000|44285)' || echo '端口未监听'")
    print("端口状态:")
    if output.strip():
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
    else:
        print("   ❌ 端口5000和44285都未监听")
    print()
    
    # 检查3: Python进程
    print("🐍 检查3: Python进程...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "ps aux | grep python3 | grep -v grep")
    print("Python进程:")
    if output.strip():
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
    else:
        print("   ❌ 没有Python进程运行")
    print()
    
    # 检查4: 服务日志
    print("📋 检查4: 服务日志...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "journalctl -u license-manager --no-pager -n 15")
    print("最近日志:")
    for line in output.split('\n')[-10:]:
        if line.strip():
            print(f"   {line}")
    print()
    
    # 检查5: 文件是否存在
    print("📄 检查5: 文件状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"ls -la {config['deploy_path']}/simple_server.py")
    if success:
        print(f"   ✅ simple_server.py存在: {output.strip()}")
    else:
        print(f"   ❌ simple_server.py不存在: {error}")
    print()
    
    # 检查6: 手动测试
    print("🧪 检查6: 手动运行测试...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"cd {config['deploy_path']} && timeout 5 python3 simple_server.py")
    print("手动运行结果:")
    if output:
        for line in output.split('\n'):
            if line.strip():
                print(f"   输出: {line}")
    if error:
        for line in error.split('\n'):
            if line.strip():
                print(f"   错误: {line}")
    print()
    
    # 检查7: 防火墙状态
    print("🔥 检查7: 防火墙状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "firewall-cmd --list-ports")
    print(f"开放端口: {output.strip()}")
    print()
    
    # 检查8: 网络连通性
    print("🌍 检查8: 网络连通性...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "curl -s --connect-timeout 5 http://localhost:5000/ || echo '连接失败'")
    print(f"本地连接测试: {output.strip()}")
    print()
    
    return True

def fix_service():
    """尝试修复服务"""
    print("🔧 尝试修复服务")
    print("=" * 30)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    # 步骤1: 重启服务
    print("🔄 步骤1: 重启服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl restart license-manager")
    if success:
        print("   ✅ 服务重启成功")
    else:
        print(f"   ❌ 服务重启失败: {error}")
    
    time.sleep(3)
    
    # 步骤2: 检查服务状态
    print("🔍 步骤2: 检查服务状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl is-active license-manager")
    if success and "active" in output:
        print("   ✅ 服务运行中")
    else:
        print(f"   ❌ 服务未运行: {output}")
        
        # 查看错误日志
        print("   📋 查看错误日志...")
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], 
                                               "journalctl -u license-manager --no-pager -n 5")
        for line in output2.split('\n')[-3:]:
            if line.strip():
                print(f"     {line}")
    
    # 步骤3: 检查端口
    print("🔌 步骤3: 检查端口...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "netstat -tlnp | grep 5000")
    if success and output.strip():
        print(f"   ✅ 端口5000监听中: {output.strip()}")
        return True
    else:
        print("   ❌ 端口5000未监听")
        return False

def main():
    """主函数"""
    try:
        print("🔍 问题: 修复脚本显示成功，但网页仍然404")
        print("🎯 检查服务器实际状态")
        print()
        
        # 检查状态
        check_server_status()
        
        print("\n" + "="*50)
        choice = input("是否尝试修复服务？(y/n): ").lower().strip()
        
        if choice == 'y' or choice == 'yes':
            success = fix_service()
            
            if success:
                print("\n🎉 修复成功！")
                print("🌐 请访问: http://**************:5000/")
            else:
                print("\n❌ 修复失败")
                print("💡 建议手动检查服务配置")
        else:
            print("\n✅ 状态检查完成")
            
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
