#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的构建脚本 - 专门用于减小exe文件大小
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedBuilder:
    """优化的构建器"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.client_py = self.script_dir / "license_client.py"
        self.icon_file = self.script_dir / "icon.ico"
        
    def create_minimal_spec_file(self):
        """创建最小化的spec文件"""
        spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取脚本目录
script_dir = Path(SPECPATH)

# 分析主程序
a = Analysis(
    ['license_client.py'],
    pathex=[str(script_dir)],
    binaries=[],
    datas=[
        ('采集8.py.encrypted', '.'),
        ('筛品终极版1.py.encrypted', '.'),
        ('历史价格7.py.encrypted', '.'),
        ('专利1.py.encrypted', '.'),
        ('icon.ico', '.'),
        ('auto_updater.py', '.'),
    ],
    hiddenimports=[
        # 核心依赖
        'cryptography',
        'cryptography.hazmat.primitives.ciphers',
        'cryptography.hazmat.backends.openssl',
        'cryptography.fernet',
        # Excel处理
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'pandas',
        'pandas.io.excel',
        # Web相关
        'selenium',
        'selenium.webdriver',
        'requests',
        'beautifulsoup4',
        # GUI相关
        'tkinter',
        'tkinter.ttk',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不必要的模块
        'matplotlib',
        'numpy.distutils',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'tkinter.test',
        'test',
        'unittest',
        'doctest',
        'pydoc',
        'xml.etree.cElementTree',
        'sqlite3',
        'bz2',
        'lzma',
        '_hashlib',
        '_ssl',
        'pyexpat',
        '_elementtree',
        '_ctypes_test',
        '_testbuffer',
        '_testcapi',
        '_testimportmultiple',
        '_testmultiphase',
        'xxlimited',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 移除不必要的二进制文件
a.binaries = [x for x in a.binaries if not any(exclude in x[0].lower() for exclude in [
    'api-ms-win',
    'ucrtbase',
    'msvcp',
    'vcruntime',
    'concrt',
    'mfc',
    'atl',
])]

# 移除不必要的数据文件
a.datas = [x for x in a.datas if not any(exclude in x[0].lower() for exclude in [
    'tcl',
    'tk',
    'test',
    'tests',
    'doc',
    'docs',
    'example',
    'examples',
    'sample',
    'samples',
])]

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='亚马逊蓝图工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,  # 禁用UPX避免杀毒软件误报
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico',
    version_file=None,
)
'''
        
        spec_file = self.script_dir / "optimized_build.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        return spec_file
    
    def build_optimized_exe(self):
        """构建优化的exe文件"""
        logger.info("开始构建优化版本的exe文件")

        try:
            # 检查必要文件
            if not self.client_py.exists():
                raise FileNotFoundError(f"主程序文件不存在: {self.client_py}")

            # 清理旧的构建文件
            self.cleanup_build_files()

            # 创建spec文件
            spec_file = self.create_minimal_spec_file()
            logger.info(f"创建spec文件: {spec_file}")

            # 执行PyInstaller构建
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                str(spec_file)
            ]
            
            logger.info(f"执行构建命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
            
            logger.info("构建完成")
            
            # 检查输出文件
            dist_dir = self.script_dir / "dist"
            exe_files = list(dist_dir.glob("*.exe"))
            
            if exe_files:
                exe_file = exe_files[0]
                file_size = exe_file.stat().st_size
                size_mb = file_size / (1024 * 1024)
                
                logger.info(f"生成的exe文件: {exe_file}")
                logger.info(f"文件大小: {size_mb:.2f} MB")
                
                # 进一步优化
                self.post_process_optimization(exe_file)
                
                return True
            else:
                logger.error("未找到生成的exe文件")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"构建失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"构建过程出错: {e}")
            return False
    
    def post_process_optimization(self, exe_file):
        """后处理优化"""
        logger.info("执行后处理优化")
        
        try:
            # 使用strip命令去除调试信息（如果可用）
            if shutil.which("strip"):
                subprocess.run(["strip", str(exe_file)], check=False)
                logger.info("已去除调试信息")
            
            # 检查优化后的文件大小
            file_size = exe_file.stat().st_size
            size_mb = file_size / (1024 * 1024)
            logger.info(f"优化后文件大小: {size_mb:.2f} MB")
            
        except Exception as e:
            logger.warning(f"后处理优化失败: {e}")
    
    def cleanup_build_files(self):
        """清理构建文件"""
        logger.info("清理旧的构建文件")

        dirs_to_clean = ['build', 'dist', '__pycache__']
        for dir_name in dirs_to_clean:
            dir_path = self.script_dir / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
                logger.info(f"已删除目录: {dir_path}")

        # 删除旧的spec文件（但不删除当前要使用的）
        spec_files = list(self.script_dir.glob("*.spec"))
        for spec_file in spec_files:
            if spec_file.name != "optimized_build.spec":
                spec_file.unlink()
                logger.info(f"已删除旧spec文件: {spec_file}")
    
    def create_portable_version(self):
        """创建便携版本"""
        logger.info("创建便携版本")
        
        try:
            dist_dir = self.script_dir / "dist"
            exe_files = list(dist_dir.glob("*.exe"))
            
            if not exe_files:
                logger.error("未找到exe文件")
                return False
            
            exe_file = exe_files[0]
            portable_dir = self.script_dir / "portable"
            portable_dir.mkdir(exist_ok=True)
            
            # 复制exe文件
            portable_exe = portable_dir / exe_file.name
            shutil.copy2(exe_file, portable_exe)
            
            # 创建配置文件
            config_content = """
# 亚马逊蓝图工具 - 便携版配置
# 此文件存在表示程序运行在便携模式

[Settings]
portable_mode=true
data_dir=./data
cache_dir=./cache
log_dir=./logs

[Update]
auto_check=true
server_url=https://your-server.com/updates/
"""
            
            config_file = portable_dir / "portable.ini"
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            # 创建必要的目录
            for dir_name in ['data', 'cache', 'logs']:
                (portable_dir / dir_name).mkdir(exist_ok=True)
            
            logger.info(f"便携版本创建完成: {portable_dir}")
            return True
            
        except Exception as e:
            logger.error(f"创建便携版本失败: {e}")
            return False


def main():
    """主函数"""
    print("🚀 亚马逊蓝图工具 - 优化构建器")
    print("=" * 50)
    
    builder = OptimizedBuilder()
    
    # 构建优化版本
    if builder.build_optimized_exe():
        print("✅ 构建成功！")
        
        # 创建便携版本
        if builder.create_portable_version():
            print("✅ 便携版本创建成功！")
        
        print("\n📊 构建统计:")
        dist_dir = Path("dist")
        if dist_dir.exists():
            exe_files = list(dist_dir.glob("*.exe"))
            for exe_file in exe_files:
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"  📁 {exe_file.name}: {size_mb:.2f} MB")
        
        print("\n💡 优化建议:")
        print("  • 使用便携版本可以进一步减小分发大小")
        print("  • 定期清理临时文件和缓存")
        print("  • 考虑使用在线更新减少初始下载大小")
        
    else:
        print("❌ 构建失败！")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
