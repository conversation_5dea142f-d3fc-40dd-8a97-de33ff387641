#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复exe更新问题 - 解决PyInstaller exe更新失败问题
"""

import os
import sys
import shutil
import subprocess
import tempfile
import time
from pathlib import Path

def create_external_updater():
    """创建外部更新器"""
    updater_script = '''@echo off
chcp 65001 >nul
echo 🔄 外部更新器启动...
echo.

REM 等待主程序完全退出
echo ⏳ 等待主程序退出...
timeout /t 3 /nobreak >nul

REM 检查新文件是否存在
if not exist "%~1" (
    echo ❌ 错误: 新文件不存在 %~1
    pause
    exit /b 1
)

REM 检查目标文件是否存在
if not exist "%~2" (
    echo ❌ 错误: 目标文件不存在 %~2
    pause
    exit /b 1
)

echo 📁 备份原文件...
copy "%~2" "%~2.backup" >nul
if errorlevel 1 (
    echo ❌ 备份失败
    pause
    exit /b 1
)

echo 🔄 替换文件...
copy "%~1" "%~2" >nul
if errorlevel 1 (
    echo ❌ 文件替换失败，恢复备份...
    copy "%~2.backup" "%~2" >nul
    del "%~2.backup" >nul
    pause
    exit /b 1
)

echo ✅ 更新成功！
echo 🚀 重新启动程序...

REM 清理临时文件
del "%~1" >nul 2>&1
del "%~2.backup" >nul 2>&1

REM 重新启动程序
start "" "%~2"

echo 🎉 更新完成！
timeout /t 2 /nobreak >nul
exit /b 0
'''
    
    with open('external_updater.bat', 'w', encoding='utf-8') as f:
        f.write(updater_script)
    
    print("✅ 已创建外部更新器: external_updater.bat")

def create_safe_updater():
    """创建安全的Python更新器"""
    safe_updater = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全更新器 - 独立的更新程序
"""

import os
import sys
import time
import shutil
import subprocess
from pathlib import Path

def safe_update(new_file, target_file):
    """安全更新文件"""
    try:
        print("🔄 安全更新器启动...")
        
        # 等待主程序退出
        print("⏳ 等待主程序完全退出...")
        time.sleep(3)
        
        # 验证文件
        if not os.path.exists(new_file):
            print(f"❌ 新文件不存在: {new_file}")
            return False
            
        if not os.path.exists(target_file):
            print(f"❌ 目标文件不存在: {target_file}")
            return False
        
        # 备份原文件
        backup_file = f"{target_file}.backup"
        print("📁 备份原文件...")
        shutil.copy2(target_file, backup_file)
        
        # 替换文件
        print("🔄 替换文件...")
        shutil.copy2(new_file, target_file)
        
        # 验证替换结果
        if os.path.exists(target_file):
            print("✅ 文件替换成功！")
            
            # 清理临时文件
            try:
                os.remove(new_file)
                os.remove(backup_file)
                print("🧹 清理临时文件完成")
            except:
                pass
            
            # 重新启动程序
            print("🚀 重新启动程序...")
            subprocess.Popen([target_file], shell=True)
            
            return True
        else:
            print("❌ 文件替换失败，恢复备份...")
            shutil.copy2(backup_file, target_file)
            os.remove(backup_file)
            return False
            
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python safe_updater.py <新文件> <目标文件>")
        sys.exit(1)
    
    new_file = sys.argv[1]
    target_file = sys.argv[2]
    
    if safe_update(new_file, target_file):
        print("🎉 更新完成！")
    else:
        print("❌ 更新失败！")
        input("按回车键退出...")
'''
    
    with open('safe_updater.py', 'w', encoding='utf-8') as f:
        f.write(safe_updater)
    
    print("✅ 已创建安全更新器: safe_updater.py")

def create_temp_cleaner():
    """创建临时目录清理工具"""
    cleaner_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时目录清理工具 - 清理PyInstaller临时文件
"""

import os
import shutil
import tempfile
import glob
import time

def clean_pyinstaller_temp():
    """清理PyInstaller临时目录"""
    try:
        temp_dir = tempfile.gettempdir()
        print(f"🧹 清理临时目录: {temp_dir}")
        
        # 查找MEI开头的目录（PyInstaller临时目录）
        mei_dirs = glob.glob(os.path.join(temp_dir, "_MEI*"))
        
        cleaned = 0
        for mei_dir in mei_dirs:
            try:
                if os.path.exists(mei_dir):
                    print(f"🗑️ 删除: {mei_dir}")
                    shutil.rmtree(mei_dir, ignore_errors=True)
                    cleaned += 1
            except Exception as e:
                print(f"⚠️ 无法删除 {mei_dir}: {e}")
        
        print(f"✅ 清理完成，删除了 {cleaned} 个临时目录")
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def clean_temp_files():
    """清理其他临时文件"""
    try:
        temp_dir = tempfile.gettempdir()
        
        # 清理其他可能的临时文件
        patterns = [
            "license_client_*.exe",
            "update_*.tmp",
            "download_*.tmp"
        ]
        
        cleaned = 0
        for pattern in patterns:
            files = glob.glob(os.path.join(temp_dir, pattern))
            for file in files:
                try:
                    os.remove(file)
                    print(f"🗑️ 删除文件: {file}")
                    cleaned += 1
                except:
                    pass
        
        if cleaned > 0:
            print(f"✅ 清理了 {cleaned} 个临时文件")
        else:
            print("ℹ️ 没有找到需要清理的临时文件")
            
        return True
        
    except Exception as e:
        print(f"❌ 清理临时文件失败: {e}")
        return False

if __name__ == "__main__":
    print("🧹 PyInstaller临时目录清理工具")
    print("=" * 50)
    
    clean_pyinstaller_temp()
    clean_temp_files()
    
    print("\\n🎉 清理完成！")
    input("按回车键退出...")
'''
    
    with open('temp_cleaner.py', 'w', encoding='utf-8') as f:
        f.write(cleaner_script)
    
    print("✅ 已创建临时目录清理工具: temp_cleaner.py")

def fix_auto_updater():
    """修复auto_updater.py中的更新机制"""
    try:
        print("🔧 修复auto_updater.py更新机制...")
        
        # 检查文件是否存在
        if not os.path.exists('auto_updater.py'):
            print("❌ auto_updater.py 文件不存在")
            return False
        
        # 备份原文件
        shutil.copy2('auto_updater.py', 'auto_updater.py.backup_fix')
        print("✅ 已备份auto_updater.py")
        
        # 读取文件内容
        with open('auto_updater.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加安全更新机制
        safe_update_code = '''
    def safe_restart_program(self, new_exe_path, current_exe_path):
        """安全重启程序 - 使用外部更新器"""
        try:
            import tempfile
            import subprocess
            
            # 检查是否是exe文件
            if getattr(sys, 'frozen', False):
                # 使用外部更新器
                if os.path.exists('external_updater.bat'):
                    print("🔄 使用外部更新器...")
                    subprocess.Popen([
                        'external_updater.bat',
                        new_exe_path,
                        current_exe_path
                    ], shell=True)
                elif os.path.exists('safe_updater.py'):
                    print("🔄 使用Python安全更新器...")
                    subprocess.Popen([
                        sys.executable,
                        'safe_updater.py',
                        new_exe_path,
                        current_exe_path
                    ])
                else:
                    # 创建临时批处理文件
                    temp_bat = os.path.join(tempfile.gettempdir(), 'temp_updater.bat')
                    with open(temp_bat, 'w', encoding='utf-8') as f:
                        bat_content = f'''@echo off
chcp 65001 >nul
timeout /t 3 /nobreak >nul
copy "{new_exe_path}" "{current_exe_path}" >nul
start "" "{current_exe_path}"
del "{temp_bat}" >nul
'''
                        f.write(bat_content)
                    subprocess.Popen([temp_bat], shell=True)
                
                # 退出当前程序
                sys.exit(0)
            else:
                # Python脚本模式，直接重启
                os.execv(sys.executable, [sys.executable] + sys.argv)
                
        except Exception as e:
            print(f"❌ 安全重启失败: {e}")
            return False
'''
        
        # 如果没有safe_restart_program方法，添加它
        if 'def safe_restart_program' not in content:
            # 找到UpdateDialog类的结尾，添加新方法
            class_end = content.rfind('class UpdateDialog')
            if class_end != -1:
                # 找到类的最后一个方法
                next_class = content.find('\nclass ', class_end + 1)
                if next_class == -1:
                    next_class = len(content)
                
                # 在类的结尾添加新方法
                insert_pos = content.rfind('\n', class_end, next_class)
                if insert_pos != -1:
                    content = content[:insert_pos] + safe_update_code + content[insert_pos:]
        
        # 修改restart_program调用为safe_restart_program
        content = content.replace(
            'os.execv(sys.executable, [sys.executable] + sys.argv)',
            'self.safe_restart_program(temp_file, current_exe)'
        )
        
        # 保存修复后的文件
        with open('auto_updater.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ auto_updater.py 更新机制已修复")
        return True
        
    except Exception as e:
        print(f"❌ 修复auto_updater.py失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ exe更新问题修复工具")
    print("=" * 60)
    print("问题: Failed to start embedded python interpreter!")
    print("原因: PyInstaller exe文件自我更新失败")
    print("=" * 60)
    
    print("\n🔧 创建修复工具...")
    
    # 创建各种修复工具
    create_external_updater()
    create_safe_updater()
    create_temp_cleaner()
    
    print("\n🔧 修复现有更新机制...")
    fix_auto_updater()
    
    print("\n🧹 清理临时目录...")
    # 立即清理一次临时目录
    try:
        import tempfile
        import glob
        
        temp_dir = tempfile.gettempdir()
        mei_dirs = glob.glob(os.path.join(temp_dir, "_MEI*"))
        
        for mei_dir in mei_dirs:
            try:
                shutil.rmtree(mei_dir, ignore_errors=True)
                print(f"🗑️ 清理: {mei_dir}")
            except:
                pass
        
        print("✅ 临时目录清理完成")
        
    except Exception as e:
        print(f"⚠️ 临时目录清理失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 修复完成！")
    print("=" * 60)
    
    print("\n📋 解决方案说明:")
    print("1. ✅ 创建了外部更新器 (external_updater.bat)")
    print("2. ✅ 创建了安全Python更新器 (safe_updater.py)")
    print("3. ✅ 创建了临时目录清理工具 (temp_cleaner.py)")
    print("4. ✅ 修复了auto_updater.py的更新机制")
    print("5. ✅ 清理了现有的临时目录")
    
    print("\n🚀 使用方法:")
    print("1. 重新构建exe文件")
    print("2. 测试更新功能")
    print("3. 如果仍有问题，运行 temp_cleaner.py")
    
    print("\n⚠️ 注意事项:")
    print("- 更新时程序会自动退出并重启")
    print("- 外部更新器会安全替换exe文件")
    print("- 临时文件会自动清理")

if __name__ == "__main__":
    main()
