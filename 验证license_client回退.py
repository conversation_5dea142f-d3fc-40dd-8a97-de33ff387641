#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证license_client.py回退状态
"""

import os
import sys

def check_license_client_status():
    """检查license_client.py的当前状态"""
    print("🔍 检查license_client.py回退状态")
    print("=" * 50)
    
    if not os.path.exists("license_client.py"):
        print("❌ 未找到license_client.py文件")
        return
    
    try:
        with open("license_client.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查导入语句
        print("📋 检查导入语句:")
        if "from auto_updater import check_and_update_silent as check_and_update" in content:
            print("❌ 仍在使用简化更新函数 (check_and_update_silent)")
            print("   需要回退到: from auto_updater import check_and_update")
        elif "from auto_updater import check_and_update" in content:
            print("✅ 已回退到标准更新函数 (check_and_update)")
        else:
            print("⚠️ 未找到更新函数导入")
        
        # 检查更新相关调用
        print("\n📋 检查更新函数调用:")
        update_calls = content.count("check_and_update(")
        if update_calls > 0:
            print(f"✅ 找到 {update_calls} 个更新函数调用")
        else:
            print("⚠️ 未找到更新函数调用")
        
        # 检查窗口标题
        print("\n📋 检查窗口标题:")
        if '"亚马逊蓝图工具"' in content:
            print("✅ 窗口标题为: 亚马逊蓝图工具")
        else:
            print("⚠️ 未找到预期的窗口标题")
        
        # 检查图标相关功能
        print("\n📋 检查图标功能:")
        icon_functions = [
            "check_and_download_icon",
            "download_icon", 
            "ensure_icon_in_temp_dir",
            "iconbitmap"
        ]
        
        for func in icon_functions:
            if func in content:
                print(f"✅ 包含图标功能: {func}")
            else:
                print(f"❌ 缺少图标功能: {func}")
        
        print("\n" + "=" * 50)
        print("📊 回退状态总结:")
        
        # 判断回退是否成功
        if "from auto_updater import check_and_update" in content and "check_and_update_silent" not in content:
            print("✅ license_client.py 已成功回退到标准更新版本")
            print("💡 现在使用标准的更新对话框（带确认按钮）")
        else:
            print("❌ license_client.py 回退不完整")
            print("💡 仍在使用简化更新功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查文件时出错: {e}")
        return False

def test_import():
    """测试导入是否正常"""
    print("\n🧪 测试导入功能:")
    
    try:
        # 测试auto_updater导入
        from auto_updater import check_and_update
        print("✅ 成功导入 check_and_update")
        
        # 检查函数类型
        if callable(check_and_update):
            print("✅ check_and_update 是可调用函数")
        else:
            print("❌ check_and_update 不是函数")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试导入时出错: {e}")

def main():
    """主函数"""
    print("🔄 license_client.py 回退验证")
    print("=" * 60)
    
    # 检查文件状态
    check_license_client_status()
    
    # 测试导入
    test_import()
    
    print("\n💡 说明:")
    print("- 标准更新: 显示确认对话框，用户可选择是否更新")
    print("- 简化更新: 自动开始更新，只显示进度条")
    print("- 如果需要简化更新，请告知我重新配置")
    
    print("\n🚀 可以运行 python license_client.py 测试程序")

if __name__ == "__main__":
    main()
