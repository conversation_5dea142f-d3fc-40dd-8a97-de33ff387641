#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import tempfile
import glob

def clean_temp():
    try:
        temp_dir = tempfile.gettempdir()
        print(f"🧹 清理临时目录: {temp_dir}")
        
        # 清理MEI目录
        mei_dirs = glob.glob(os.path.join(temp_dir, "_MEI*"))
        cleaned = 0
        
        for mei_dir in mei_dirs:
            try:
                if os.path.exists(mei_dir):
                    print(f"🗑️ 删除: {mei_dir}")
                    shutil.rmtree(mei_dir, ignore_errors=True)
                    cleaned += 1
            except:
                pass
        
        print(f"✅ 清理完成，删除了 {cleaned} 个临时目录")
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧹 临时目录清理工具")
    clean_temp()
    input("按回车键退出...")
