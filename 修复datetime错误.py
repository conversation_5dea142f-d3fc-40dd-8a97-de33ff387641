#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复datetime错误
"""

import paramiko
import json

def fix_datetime_error():
    """修复服务器端的datetime错误"""
    print("🔧 修复服务器端datetime错误")
    print("=" * 50)
    
    # 服务器连接信息
    server_info = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0'
    }
    
    try:
        print("🔗 连接到服务器...")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(**server_info)
        
        print("✅ SSH连接成功")
        
        # 检查Python版本
        print("🐍 检查Python版本...")
        stdin, stdout, stderr = ssh.exec_command('python3 --version')
        python_version = stdout.read().decode().strip()
        print(f"📋 Python版本: {python_version}")
        
        # 检查datetime模块
        print("📅 检查datetime模块...")
        test_datetime_cmd = '''python3 -c "
from datetime import datetime
print('datetime导入成功')
try:
    dt = datetime.fromisoformat('2025-01-01T00:00:00')
    print('fromisoformat方法可用')
except AttributeError as e:
    print(f'fromisoformat方法不可用: {e}')
except Exception as e:
    print(f'其他错误: {e}')
"'''
        
        stdin, stdout, stderr = ssh.exec_command(test_datetime_cmd)
        output = stdout.read().decode()
        error = stderr.read().decode()
        
        print(f"📋 测试输出: {output}")
        if error:
            print(f"⚠️ 错误信息: {error}")
        
        # 检查服务器日志
        print("📋 检查服务器日志...")
        log_cmd = "tail -20 /var/log/license_server.log"
        stdin, stdout, stderr = ssh.exec_command(log_cmd)
        log_output = stdout.read().decode()
        
        if log_output:
            print("📄 最近的日志:")
            print(log_output[-500:])  # 显示最后500字符
        
        # 创建修复版本的license_server.py
        print("🔧 创建修复版本...")
        
        # 读取当前的license_server.py
        get_file_cmd = "cat /opt/license_manager/license_server.py"
        stdin, stdout, stderr = ssh.exec_command(get_file_cmd)
        current_content = stdout.read().decode()
        
        if not current_content:
            print("❌ 无法读取当前的license_server.py")
            return False
        
        # 检查是否需要修复
        if 'fromisoformat' in current_content:
            print("🔍 检查fromisoformat使用...")
            
            # 创建兼容性修复
            fix_code = '''
# 兼容性修复：为旧版本Python添加fromisoformat支持
if not hasattr(datetime, 'fromisoformat'):
    def fromisoformat_fallback(date_string):
        """为Python 3.6及以下版本提供fromisoformat功能"""
        try:
            # 尝试解析ISO格式
            return datetime.strptime(date_string.replace('T', ' '), '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                return datetime.strptime(date_string, '%Y-%m-%d')
            except ValueError:
                return datetime.strptime(date_string, '%Y-%m-%dT%H:%M:%S.%f')
    
    datetime.fromisoformat = staticmethod(fromisoformat_fallback)
'''
            
            # 在导入后添加修复代码
            lines = current_content.split('\n')
            insert_index = -1
            
            for i, line in enumerate(lines):
                if 'import logging' in line:
                    insert_index = i + 1
                    break
            
            if insert_index > 0:
                lines.insert(insert_index, fix_code)
                fixed_content = '\n'.join(lines)
                
                # 创建临时文件
                temp_file = '/tmp/license_server_fixed.py'
                
                # 写入修复后的内容
                with open('license_server_temp.py', 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                # 上传修复后的文件
                sftp = ssh.open_sftp()
                sftp.put('license_server_temp.py', temp_file)
                sftp.close()
                
                # 备份原文件并替换
                backup_cmd = f"cp /opt/license_manager/license_server.py /opt/license_manager/license_server.py.backup"
                stdin, stdout, stderr = ssh.exec_command(backup_cmd)
                
                replace_cmd = f"cp {temp_file} /opt/license_manager/license_server.py"
                stdin, stdout, stderr = ssh.exec_command(replace_cmd)
                
                # 重启服务
                print("🔄 重启服务...")
                restart_cmd = "systemctl restart license-manager"
                stdin, stdout, stderr = ssh.exec_command(restart_cmd)
                
                print("✅ 修复完成")
                
                # 清理临时文件
                os.remove('license_server_temp.py')
                
            else:
                print("❌ 无法找到合适的插入位置")
                return False
        
        ssh.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_fix():
    """测试修复效果"""
    print("\n🧪 测试修复效果")
    print("=" * 50)
    
    import requests
    import time
    
    # 等待服务重启
    print("⏰ 等待服务重启...")
    time.sleep(5)
    
    server_url = "http://**************:5000"
    
    # 测试用户激活码
    params = {
        "current_version": "2.0.0",
        "key": "83R2AXQK-20250725-67c80d8d",
        "device_id": "20cc47fd9ca63e67"
    }
    
    try:
        response = requests.get(
            f"{server_url}/update/check",
            params=params,
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 修复成功！")
            print(f"📄 响应: {data}")
            
        elif response.status_code == 401:
            data = response.json()
            print("✅ 修复成功！（认证失败是正常的）")
            print(f"📄 认证错误: {data.get('message', 'Unknown')}")
            
        elif response.status_code == 500:
            print("❌ 仍然有500错误")
            print(f"📄 错误: {response.text}")
            
        else:
            print(f"⚠️ 其他状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🎯 修复datetime.fromisoformat错误")
    print("🔧 错误: type object 'datetime.datetime' has no attribute 'fromisormat'")
    print("💡 解决: 添加Python版本兼容性支持")
    print()
    
    if fix_datetime_error():
        print("\n🎉 修复完成！")
        test_fix()
        
        print("\n" + "=" * 50)
        print("📋 修复总结:")
        print("✅ 添加了datetime.fromisoformat兼容性支持")
        print("✅ 备份了原始文件")
        print("✅ 重启了license-manager服务")
        
        print("\n🚀 现在license_client.py应该可以正常工作了！")
        
    else:
        print("\n❌ 修复失败")
        print("💡 手动解决方案:")
        print("1. SSH连接到服务器")
        print("2. 检查Python版本是否支持fromisoformat")
        print("3. 升级Python或添加兼容性代码")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
