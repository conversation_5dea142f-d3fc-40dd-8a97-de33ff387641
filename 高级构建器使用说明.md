# 亚马逊蓝图工具 - 高级构建器使用说明

## 🎯 功能概述

高级构建器是一个功能强大的图形界面工具，专为 `build_with_spec.py` 设计，提供以下核心功能：

### ✨ 主要特性

1. **📦 智能依赖管理**
   - 自动扫描当前环境依赖
   - 检测缺失依赖并提供修复建议
   - 支持添加、删除、更新依赖
   - 智能版本选择和兼容性检查

2. **🔧 自动修复功能**
   - 一键修复缺失依赖
   - 自动解决版本冲突
   - 智能推荐最佳版本

3. **🧪 exe依赖测试**
   - 构建后自动测试exe文件
   - 检测运行时依赖问题
   - 提供详细的测试报告

4. **🎨 现代化界面**
   - 多标签页设计
   - 实时日志显示
   - 进度条和状态指示
   - 右键菜单和工具提示

## 🚀 快速开始

### 1. 启动程序

```bash
python 启动高级构建器.py
```

或者直接运行：

```bash
python build_gui_advanced.py
```

### 2. 界面布局

程序包含4个主要标签页：

- **📦 依赖管理** - 管理项目依赖
- **⚙️ 构建配置** - 配置PyInstaller选项
- **🚀 构建和测试** - 执行构建和测试
- **📋 日志查看** - 查看详细日志

## 📦 依赖管理功能

### 扫描依赖

1. 点击 **🔍 扫描依赖** 按钮
2. 程序会自动检测当前环境中的关键依赖
3. 显示每个依赖的状态（已安装/未安装）

### 添加依赖

1. 点击 **➕ 添加依赖** 按钮
2. 在对话框中输入包名
3. 点击 **查询可用版本** 获取版本列表
4. 选择合适的版本并点击 **添加**

### 智能版本选择

- 程序会自动查询PyPI获取最新版本信息
- 支持版本兼容性检查
- 推荐最稳定的版本

### 修复依赖

1. 点击 **🔧 修复依赖** 按钮
2. 程序会自动安装所有缺失的依赖
3. 实时显示安装进度和结果

### 导入/导出 requirements.txt

- **📥 导入requirements.txt** - 从文件导入依赖列表
- **📤 导出requirements.txt** - 导出当前依赖到文件

## ⚙️ 构建配置

### 基本配置

- **输出名称** - 设置生成的exe文件名
- **主程序文件** - 选择要打包的Python文件
- **图标文件** - 设置exe文件图标

### 构建选项

- **显示控制台窗口** - 是否显示命令行窗口
- **单文件模式** - 生成单个exe文件
- **代码优化** - 启用Python代码优化
- **去除调试信息** - 减小文件大小
- **UPX压缩** - 使用UPX压缩（需要安装UPX）

### 高级选项

在文本框中可以添加额外的PyInstaller参数，例如：

```
--collect-data=fake_useragent
--collect-submodules=openpyxl
--exclude-module=matplotlib
--exclude-module=scipy
```

## 🚀 构建和测试

### 开始构建

1. 确保配置正确
2. 点击 **🚀 开始构建** 按钮
3. 观察进度条和实时日志
4. 构建完成后会显示结果

### 测试exe依赖

1. 构建完成后点击 **🧪 测试exe依赖**
2. 程序会自动测试生成的exe文件
3. 检测所有关键依赖是否正确包含
4. 显示详细的测试报告

### 其他功能

- **📁 打开输出目录** - 快速打开dist文件夹
- **🗑️ 清理构建文件** - 清理build、dist等临时文件

## 📋 日志查看

### 日志功能

- **实时日志显示** - 所有操作都会记录日志
- **日志级别过滤** - 支持DEBUG、INFO、WARNING、ERROR
- **🗑️ 清空日志** - 清空当前日志
- **💾 保存日志** - 保存日志到文件

## 🔧 高级功能

### 右键菜单

在依赖列表中右键点击可以：

- **📋 查看详情** - 查看依赖详细信息
- **🔄 更新版本** - 更新到最新版本
- **🗑️ 删除依赖** - 从列表中删除
- **🔍 查询PyPI** - 查询PyPI版本信息

### 搜索过滤

- 在搜索框中输入关键词
- 实时过滤依赖列表
- 支持按包名和描述搜索

### 版本选择对话框

- 显示所有可用版本
- 高亮显示最新版本
- 支持安装指定版本

## 🛠️ 故障排除

### 常见问题

1. **依赖安装失败**
   - 检查网络连接
   - 尝试使用国内镜像源
   - 检查权限问题

2. **构建失败**
   - 检查主程序文件路径
   - 确保所有依赖已安装
   - 查看详细错误日志

3. **exe测试失败**
   - 检查隐藏导入配置
   - 添加缺失的数据文件
   - 检查模块路径

### 调试技巧

1. **查看详细日志**
   - 切换到"日志查看"标签页
   - 设置日志级别为DEBUG
   - 保存日志文件供分析

2. **手动测试**
   - 在dist目录中手动运行exe
   - 检查错误信息
   - 使用依赖测试脚本

## 📝 最佳实践

### 依赖管理

1. **定期扫描依赖** - 保持依赖列表最新
2. **使用固定版本** - 避免版本冲突
3. **测试兼容性** - 新增依赖后及时测试

### 构建优化

1. **排除不必要模块** - 减小文件大小
2. **使用单文件模式** - 便于分发
3. **启用优化选项** - 提高性能

### 测试验证

1. **构建后立即测试** - 确保功能正常
2. **在不同环境测试** - 验证兼容性
3. **保存测试日志** - 便于问题追踪

## 🔗 相关文件

- `build_gui_advanced.py` - 主程序文件
- `启动高级构建器.py` - 启动脚本
- `build_with_spec.py` - 底层构建脚本
- `requirements.txt` - 依赖列表文件

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查依赖是否正确安装
3. 确认配置参数是否正确
4. 参考故障排除部分

---

**版本**: 1.0.0  
**更新日期**: 2024年  
**兼容性**: Python 3.7+, Windows 10+
