#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复服务器端断点续传问题
"""

import paramiko
import os

def fix_server_resume_download():
    """修复服务器端断点续传功能"""
    print("🔧 修复服务器端断点续传功能")
    print("=" * 50)
    
    # 服务器连接信息
    hostname = "**************"
    username = "root"
    password = "l39XNqJG24JmXc2za0"
    
    try:
        # 连接服务器
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("✅ 已连接到服务器")
        
        # 检查当前license_server.py中的下载函数
        print("📋 检查当前下载函数...")
        
        stdin, stdout, stderr = ssh.exec_command("grep -n 'def.*download' /opt/license_manager/license_server.py")
        output = stdout.read().decode('utf-8')
        print(f"📄 下载函数: {output}")
        
        # 修复后的license_server.py下载函数
        fixed_download_function = '''
@app.route('/update/download')
def download_update():
    """下载更新文件 - 支持断点续传"""
    try:
        # 获取参数
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        version = request.args.get('version')
        
        # 验证参数
        if not key or not device_id or not version:
            return jsonify({"success": False, "message": "缺少必要参数"}), 400
        
        # 管理员绕过认证
        if key == "ADMIN_BYPASS" and device_id == "ADMIN-DEVICE-001":
            pass  # 管理员绕过认证
        else:
            # 验证激活码
            if key not in db:
                return jsonify({"success": False, "message": "激活码不存在"}), 401
            
            license_info = db[key]
            
            # 检查激活码状态
            if license_info.get('status') != 'active':
                return jsonify({"success": False, "message": "激活码未激活"}), 401
            
            # 检查设备绑定
            if license_info.get('device_id') and license_info['device_id'] != device_id:
                return jsonify({"success": False, "message": "设备已绑定其他用户"}), 401
            
            # 检查过期时间
            expire_date = license_info.get('expire_date')
            if expire_date and datetime.now() > datetime.fromisoformat(expire_date):
                return jsonify({"success": False, "message": "激活码已过期"}), 401
        
        # 查找更新文件
        update_file = None
        for filename in os.listdir(UPDATE_DIR):
            if filename.endswith('.exe') and version in filename:
                update_file = os.path.join(UPDATE_DIR, filename)
                break
        
        if not update_file or not os.path.exists(update_file):
            return jsonify({"success": False, "message": "更新文件不存在"}), 404
        
        # 获取文件大小
        file_size = os.path.getsize(update_file)
        
        # 处理Range请求（断点续传）
        range_header = request.headers.get('Range')
        if range_header:
            try:
                # 解析Range头: bytes=start-end
                range_match = re.match(r'bytes=(\d+)-(\d*)', range_header)
                if range_match:
                    start = int(range_match.group(1))
                    end = int(range_match.group(2)) if range_match.group(2) else file_size - 1
                    
                    # 验证范围
                    if start >= file_size:
                        return "Requested Range Not Satisfiable", 416
                    
                    if end >= file_size:
                        end = file_size - 1
                    
                    # 读取文件片段
                    def generate_chunk():
                        with open(update_file, 'rb') as f:
                            f.seek(start)
                            remaining = end - start + 1
                            while remaining > 0:
                                chunk_size = min(8192, remaining)  # 8KB chunks
                                chunk = f.read(chunk_size)
                                if not chunk:
                                    break
                                remaining -= len(chunk)
                                yield chunk
                    
                    # 返回部分内容
                    response = Response(
                        generate_chunk(),
                        206,  # Partial Content
                        headers={
                            'Content-Range': f'bytes {start}-{end}/{file_size}',
                            'Accept-Ranges': 'bytes',
                            'Content-Length': str(end - start + 1),
                            'Content-Type': 'application/octet-stream'
                        }
                    )
                    return response
                    
            except Exception as e:
                print(f"Range处理错误: {e}")
                # 如果Range处理失败，返回完整文件
        
        # 返回完整文件
        def generate_full_file():
            with open(update_file, 'rb') as f:
                while True:
                    chunk = f.read(8192)  # 8KB chunks
                    if not chunk:
                        break
                    yield chunk
        
        response = Response(
            generate_full_file(),
            200,
            headers={
                'Content-Length': str(file_size),
                'Content-Type': 'application/octet-stream',
                'Accept-Ranges': 'bytes'
            }
        )
        return response
        
    except Exception as e:
        print(f"下载错误: {e}")
        return jsonify({"success": False, "message": f"下载失败: {str(e)}"}), 500
'''
        
        # 创建修复脚本
        fix_script = f'''#!/bin/bash
cd /opt/license_manager

# 备份原文件
cp license_server.py license_server_backup_$(date +%Y%m%d_%H%M%S).py

# 创建临时修复文件
cat > fix_download.py << 'EOF'
import re
import sys

def fix_download_function():
    """修复下载函数"""
    with open('license_server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找下载函数的开始和结束
    start_pattern = r"@app\\.route\\('/update/download'\\)"
    
    # 找到函数开始位置
    import re
    match = re.search(start_pattern, content)
    if not match:
        print("未找到下载函数")
        return False
    
    start_pos = match.start()
    
    # 找到下一个@app.route或文件结束
    next_route_pattern = r"@app\\.route\\("
    next_match = re.search(next_route_pattern, content[start_pos + 10:])
    
    if next_match:
        end_pos = start_pos + 10 + next_match.start()
    else:
        # 如果没有找到下一个路由，查找文件结束或其他函数定义
        end_pattern = r"\\n\\ndef |\\nif __name__ == '__main__':"
        end_match = re.search(end_pattern, content[start_pos:])
        if end_match:
            end_pos = start_pos + end_match.start()
        else:
            end_pos = len(content)
    
    # 替换函数
    new_function = """{fixed_download_function}"""
    
    new_content = content[:start_pos] + new_function + content[end_pos:]
    
    # 写入文件
    with open('license_server.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("下载函数修复完成")
    return True

if __name__ == "__main__":
    fix_download_function()
EOF

# 运行修复脚本
python3 fix_download.py

# 重启服务
systemctl restart license-manager

# 检查服务状态
systemctl status license-manager

echo "修复完成"
'''
        
        # 上传并执行修复脚本
        print("📤 上传修复脚本...")
        
        # 创建临时脚本文件
        with open("temp_fix_script.sh", 'w', encoding='utf-8') as f:
            f.write(fix_script)
        
        # 上传脚本
        sftp = ssh.open_sftp()
        sftp.put("temp_fix_script.sh", "/tmp/fix_download.sh")
        sftp.close()
        
        # 执行修复脚本
        print("🚀 执行修复脚本...")
        stdin, stdout, stderr = ssh.exec_command("chmod +x /tmp/fix_download.sh && /tmp/fix_download.sh")
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        
        print("📄 执行输出:")
        print(output)
        
        if error:
            print("⚠️ 错误信息:")
            print(error)
        
        # 测试修复结果
        print("\n🧪 测试修复结果...")
        
        import requests
        test_url = "http://**************:5000/update/download"
        test_params = {
            'key': 'ADMIN_BYPASS',
            'device_id': 'ADMIN-DEVICE-001',
            'version': '2.1.1'
        }
        
        # 测试Range请求
        test_headers = {'Range': 'bytes=0-1023'}
        
        try:
            response = requests.get(test_url, params=test_params, headers=test_headers, timeout=10)
            print(f"✅ 测试结果: {response.status_code}")
            
            if response.status_code == 206:
                print("🎉 断点续传修复成功！")
            elif response.status_code == 200:
                print("✅ 下载功能正常，但可能不支持断点续传")
            else:
                print(f"❌ 测试失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        ssh.close()
        
        # 清理临时文件
        if os.path.exists("temp_fix_script.sh"):
            os.remove("temp_fix_script.sh")
        
        print("\n✅ 服务器修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_client_fallback():
    """创建客户端降级方案"""
    print("\n🔧 创建客户端降级方案")
    print("=" * 50)
    
    fallback_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新功能降级方案 - 不使用断点续传
"""

import requests
import os
import tempfile
import time

def download_without_resume(version="2.1.1", progress_callback=None):
    """不使用断点续传的下载"""
    print("📥 开始下载（不使用断点续传）")
    
    url = "http://**************:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': version
    }
    
    # 删除可能存在的未完成下载
    temp_file = os.path.join(tempfile.gettempdir(), f"amazon_blueprint_update_{version}.exe")
    if os.path.exists(temp_file):
        os.remove(temp_file)
        print("🗑️ 已删除未完成的下载文件")
    
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            print(f"🚀 开始下载 (尝试 {retry_count + 1}/{max_retries})")
            
            response = requests.get(
                url, 
                params=params,
                stream=True,
                timeout=(60, 1800)  # 30分钟超时
            )
            
            if response.status_code != 200:
                raise Exception(f"HTTP错误: {response.status_code}")
            
            file_size = int(response.headers.get('Content-Length', 0))
            downloaded_size = 0
            
            print(f"📦 文件大小: {file_size/1024/1024:.1f}MB")
            
            with open(temp_file, 'wb') as f:
                chunk_size = 32 * 1024  # 32KB chunks
                
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 更新进度
                        if progress_callback and file_size > 0:
                            progress = (downloaded_size / file_size) * 100
                            progress_callback(progress)
                        
                        # 每10MB显示进度
                        if downloaded_size % (10 * 1024 * 1024) == 0:
                            print(f"📊 已下载: {downloaded_size/1024/1024:.1f}MB")
            
            print(f"✅ 下载完成: {temp_file}")
            return temp_file
            
        except Exception as e:
            retry_count += 1
            print(f"❌ 下载失败 (尝试 {retry_count}/{max_retries}): {e}")
            
            if retry_count < max_retries:
                wait_time = retry_count * 10
                print(f"⏱️ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            
            # 删除可能损坏的文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
    
    print("❌ 下载失败，已重试所有次数")
    return None

if __name__ == "__main__":
    def progress_callback(progress):
        print(f"\\r📊 下载进度: {progress:.1f}%", end="", flush=True)
    
    result = download_without_resume("2.1.1", progress_callback)
    if result:
        print(f"\\n✅ 下载成功: {result}")
    else:
        print("\\n❌ 下载失败")
'''
    
    with open("降级下载方案.py", 'w', encoding='utf-8') as f:
        f.write(fallback_code)
    
    print("✅ 已创建 降级下载方案.py")

def main():
    """主函数"""
    print("🔧 修复服务器断点续传问题")
    print("=" * 60)
    
    # 1. 修复服务器端
    if fix_server_resume_download():
        print("✅ 服务器端修复完成")
    else:
        print("❌ 服务器端修复失败")
    
    # 2. 创建客户端降级方案
    create_client_fallback()
    
    print("\n" + "=" * 60)
    print("📊 修复完成")
    print("💡 建议:")
    print("1. 重新运行 python license_client.py 测试更新")
    print("2. 如果仍有问题，运行 python 降级下载方案.py 测试")

if __name__ == "__main__":
    main()
