#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新服务器管理脚本
用于管理程序更新文件和版本信息
"""

import os
import json
import hashlib
import shutil
from pathlib import Path
from datetime import datetime
import argparse

class UpdateServerManager:
    """更新服务器管理器"""
    
    def __init__(self, server_dir="update_server"):
        """
        初始化管理器
        
        Args:
            server_dir: 服务器文件目录
        """
        self.server_dir = Path(server_dir)
        self.server_dir.mkdir(exist_ok=True)
        
        # 创建必要的子目录
        (self.server_dir / "files").mkdir(exist_ok=True)
        (self.server_dir / "versions").mkdir(exist_ok=True)
        (self.server_dir / "logs").mkdir(exist_ok=True)
    
    def calculate_file_hash(self, file_path):
        """
        计算文件SHA256哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 哈希值
        """
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def upload_new_version(self, exe_file, version, changelog="", force_update=False):
        """
        上传新版本
        
        Args:
            exe_file: exe文件路径
            version: 版本号
            changelog: 更新日志
            force_update: 是否强制更新
            
        Returns:
            bool: 是否成功
        """
        try:
            exe_path = Path(exe_file)
            if not exe_path.exists():
                print(f"❌ 文件不存在: {exe_file}")
                return False
            
            # 计算文件信息
            file_size = exe_path.stat().st_size
            file_hash = self.calculate_file_hash(exe_path)
            
            # 复制文件到服务器目录
            server_file_name = f"amazon_blueprint_v{version}.exe"
            server_file_path = self.server_dir / "files" / server_file_name
            shutil.copy2(exe_path, server_file_path)
            
            print(f"✅ 文件已复制到: {server_file_path}")
            print(f"📊 文件大小: {file_size / (1024*1024):.2f} MB")
            print(f"🔐 文件哈希: {file_hash}")
            
            # 创建版本信息
            version_info = {
                "version": version,
                "download_url": f"https://your-server.com/updates/files/{server_file_name}",
                "file_size": file_size,
                "file_hash": file_hash,
                "changelog": changelog,
                "release_date": datetime.now().strftime("%Y-%m-%d"),
                "min_version": "1.0.0",
                "force_update": force_update,
                "update_notes": {
                    "critical": force_update,
                    "backup_recommended": True,
                    "restart_required": True
                }
            }
            
            # 保存版本信息
            version_file = self.server_dir / "version_info.json"
            with open(version_file, 'w', encoding='utf-8') as f:
                json.dump(version_info, f, ensure_ascii=False, indent=2)
            
            # 保存版本历史
            history_file = self.server_dir / "versions" / f"v{version}.json"
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(version_info, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 版本信息已保存: {version_file}")
            print(f"📚 版本历史已保存: {history_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 上传失败: {e}")
            return False
    
    def list_versions(self):
        """列出所有版本"""
        print("📋 版本列表:")
        print("-" * 50)
        
        versions_dir = self.server_dir / "versions"
        version_files = sorted(versions_dir.glob("v*.json"), reverse=True)
        
        if not version_files:
            print("暂无版本记录")
            return
        
        for version_file in version_files:
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_info = json.load(f)
                
                version = version_info.get("version", "Unknown")
                release_date = version_info.get("release_date", "Unknown")
                file_size = version_info.get("file_size", 0)
                size_mb = file_size / (1024 * 1024)
                force_update = version_info.get("force_update", False)
                
                print(f"🔖 版本 {version}")
                print(f"   📅 发布日期: {release_date}")
                print(f"   📊 文件大小: {size_mb:.2f} MB")
                print(f"   ⚠️  强制更新: {'是' if force_update else '否'}")
                print()
                
            except Exception as e:
                print(f"❌ 读取版本文件失败 {version_file}: {e}")
    
    def get_current_version(self):
        """获取当前版本信息"""
        version_file = self.server_dir / "version_info.json"
        
        if not version_file.exists():
            print("❌ 当前版本信息不存在")
            return None
        
        try:
            with open(version_file, 'r', encoding='utf-8') as f:
                version_info = json.load(f)
            
            print("📋 当前版本信息:")
            print("-" * 30)
            print(f"🔖 版本: {version_info.get('version', 'Unknown')}")
            print(f"📅 发布日期: {version_info.get('release_date', 'Unknown')}")
            print(f"📊 文件大小: {version_info.get('file_size', 0) / (1024*1024):.2f} MB")
            print(f"🔐 文件哈希: {version_info.get('file_hash', 'Unknown')}")
            print(f"⚠️  强制更新: {'是' if version_info.get('force_update', False) else '否'}")
            print(f"📝 更新说明:")
            print(version_info.get('changelog', '暂无说明'))
            
            return version_info
            
        except Exception as e:
            print(f"❌ 读取版本信息失败: {e}")
            return None
    
    def create_download_page(self):
        """创建下载页面"""
        version_info = self.get_current_version()
        if not version_info:
            return False
        
        html_content = f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亚马逊蓝图工具 - 下载页面</title>
    <style>
        body {{
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        .version-info {{
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        .download-btn {{
            display: inline-block;
            background: #007cba;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 18px;
            margin: 10px;
        }}
        .download-btn:hover {{
            background: #005a87;
        }}
        .changelog {{
            background: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #007cba;
            margin: 20px 0;
            white-space: pre-line;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 亚马逊蓝图工具</h1>
            <p>专业的亚马逊产品分析工具</p>
        </div>
        
        <div class="version-info">
            <h2>📋 版本信息</h2>
            <p><strong>当前版本:</strong> v{version_info.get('version', 'Unknown')}</p>
            <p><strong>发布日期:</strong> {version_info.get('release_date', 'Unknown')}</p>
            <p><strong>文件大小:</strong> {version_info.get('file_size', 0) / (1024*1024):.2f} MB</p>
        </div>
        
        <div style="text-align: center;">
            <a href="{version_info.get('download_url', '#')}" class="download-btn">
                📥 立即下载
            </a>
        </div>
        
        <div class="changelog">
            <h3>📝 更新说明</h3>
            {version_info.get('changelog', '暂无更新说明')}
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>© 2024 亚马逊蓝图工具 - 专业版</p>
        </div>
    </div>
</body>
</html>
'''
        
        html_file = self.server_dir / "download.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 下载页面已创建: {html_file}")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="更新服务器管理工具")
    parser.add_argument("--server-dir", default="update_server", help="服务器目录")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 上传命令
    upload_parser = subparsers.add_parser("upload", help="上传新版本")
    upload_parser.add_argument("exe_file", help="exe文件路径")
    upload_parser.add_argument("version", help="版本号")
    upload_parser.add_argument("--changelog", default="", help="更新日志")
    upload_parser.add_argument("--force", action="store_true", help="强制更新")
    
    # 列表命令
    subparsers.add_parser("list", help="列出所有版本")
    
    # 当前版本命令
    subparsers.add_parser("current", help="显示当前版本")
    
    # 创建下载页面命令
    subparsers.add_parser("page", help="创建下载页面")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    manager = UpdateServerManager(args.server_dir)
    
    if args.command == "upload":
        success = manager.upload_new_version(
            args.exe_file, 
            args.version, 
            args.changelog, 
            args.force
        )
        return 0 if success else 1
    
    elif args.command == "list":
        manager.list_versions()
        return 0
    
    elif args.command == "current":
        version_info = manager.get_current_version()
        return 0 if version_info else 1
    
    elif args.command == "page":
        success = manager.create_download_page()
        return 0 if success else 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
