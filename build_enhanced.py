#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版构建脚本 - 专门用于确保所有依赖正确安装和打包
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_enhanced.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("EnhancedBuild")

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    logger.info(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    
    return True

def install_requirements():
    """安装requirements.txt中的所有依赖"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        logger.error(f"未找到{requirements_file}文件")
        return False
    
    try:
        logger.info("升级pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        logger.info("安装requirements.txt中的依赖...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", requirements_file, "--upgrade"
        ])
        
        logger.info("依赖安装完成")
        return True
        
    except Exception as e:
        logger.error(f"安装依赖失败: {str(e)}")
        return False

def verify_critical_imports():
    """验证关键库是否能正常导入"""
    critical_packages = [
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('selenium', 'selenium'),
        ('requests', 'requests'),
        ('cryptography', 'cryptography'),
        ('fake_useragent', 'fake_useragent'),
        ('beautifulsoup4', 'bs4'),
        ('webdriver_manager', 'webdriver_manager'),
        ('amazoncaptcha', 'amazoncaptcha'),
        ('psutil', 'psutil'),
        ('xlsxwriter', 'xlsxwriter'),
        ('lxml', 'lxml'),
        ('pillow', 'PIL')
    ]
    
    failed_imports = []
    
    for package_name, import_name in critical_packages:
        try:
            __import__(import_name)
            logger.info(f"✓ {package_name} 导入成功")
        except ImportError as e:
            logger.error(f"✗ {package_name} 导入失败: {str(e)}")
            failed_imports.append(package_name)
    
    if failed_imports:
        logger.error(f"以下包导入失败: {', '.join(failed_imports)}")
        return False
    
    logger.info("所有关键包导入验证成功")
    return True

def test_excel_operations():
    """测试Excel操作功能"""
    try:
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Product': ['Product1', 'Product2', 'Product3'],
            'Price': [10.99, 20.99, 30.99],
            'Stock': [100, 200, 300]
        })
        
        # 测试Excel写入
        test_file = "test_excel_build.xlsx"
        test_data.to_excel(test_file, index=False, engine='openpyxl')
        
        # 测试Excel读取
        read_data = pd.read_excel(test_file, engine='openpyxl')
        
        # 验证数据
        if len(read_data) == len(test_data):
            logger.info("✓ Excel读写功能测试成功")
            success = True
        else:
            logger.error("✗ Excel数据不一致")
            success = False
        
        # 清理
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return success
        
    except Exception as e:
        logger.error(f"Excel功能测试失败: {str(e)}")
        return False

def run_main_build():
    """运行主构建脚本"""
    try:
        logger.info("运行主构建脚本...")
        result = subprocess.run([sys.executable, "build_license_system.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("主构建脚本执行成功")
            return True
        else:
            logger.error(f"主构建脚本执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"运行主构建脚本时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始增强版构建过程")
    
    # 1. 检查Python版本
    if not check_python_version():
        return False
    
    # 2. 安装依赖
    if not install_requirements():
        return False
    
    # 3. 验证导入
    if not verify_critical_imports():
        return False
    
    # 4. 测试Excel功能
    if not test_excel_operations():
        logger.warning("Excel功能测试失败，但继续构建")
    
    # 5. 运行主构建
    if not run_main_build():
        return False
    
    logger.info("增强版构建过程完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
