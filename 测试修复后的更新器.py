#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的更新器
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import threading
import time

def test_updater():
    """测试更新器"""
    
    # 创建GUI
    root = tk.Tk()
    root.title("测试修复后的更新器")
    root.geometry("700x500")
    
    # 设置图标
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 标题
    title_label = tk.Label(
        root,
        text="🔧 测试修复后的更新器",
        font=("微软雅黑", 16, "bold"),
        pady=10
    )
    title_label.pack()
    
    # 日志区域
    log_frame = tk.Frame(root)
    log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
    log_text.pack(fill=tk.BOTH, expand=True)
    
    def log(message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        log_text.insert(tk.END, log_message + "\n")
        log_text.see(tk.END)
        root.update()
        print(log_message)
    
    def test_download():
        """测试下载功能"""
        log("🚀 开始测试修复后的下载功能...")
        
        def test_thread():
            try:
                from auto_updater import AutoUpdater
                
                log("✅ 成功导入AutoUpdater")
                
                # 创建更新器
                updater = AutoUpdater(
                    current_version="2.1.0",
                    license_server_url="http://198.23.135.176:5000/",
                    license_key="ADMIN_BYPASS",
                    device_id="ADMIN-DEVICE-001"
                )
                
                log("✅ 更新器创建成功")
                
                # 检查更新
                log("📋 检查更新...")
                update_info = updater.check_for_updates()
                
                if update_info:
                    log("✅ 检查更新成功")
                    log(f"📊 版本: {update_info.get('version')}")
                    log(f"📦 大小: {update_info.get('file_size', 0)/1024/1024:.1f}MB")
                    
                    # 测试下载
                    log("📥 开始下载测试...")
                    
                    def progress_callback(progress):
                        if int(progress) % 5 == 0:  # 每5%显示一次
                            log(f"📊 下载进度: {progress:.1f}%")
                    
                    temp_file = updater.download_update(update_info, progress_callback)
                    
                    if temp_file:
                        log(f"✅ 下载成功: {temp_file}")
                        
                        # 检查文件
                        import os
                        if os.path.exists(temp_file):
                            file_size = os.path.getsize(temp_file)
                            log(f"📦 文件大小: {file_size/1024/1024:.1f}MB")
                            
                            expected_size = update_info.get('file_size', 0)
                            if file_size == expected_size:
                                log("🎉 文件下载完整！")
                                messagebox.showinfo("成功", "下载测试成功！文件完整。")
                            else:
                                log(f"⚠️ 文件大小不匹配: {file_size} vs {expected_size}")
                        else:
                            log("❌ 文件不存在")
                    else:
                        log("❌ 下载失败")
                        messagebox.showerror("失败", "下载测试失败")
                else:
                    log("ℹ️ 没有可用更新")
                    messagebox.showinfo("信息", "没有可用更新")
                
            except Exception as e:
                log(f"❌ 测试失败: {e}")
                import traceback
                log(f"📄 详细错误: {traceback.format_exc()}")
                messagebox.showerror("错误", f"测试失败: {e}")
        
        # 在后台线程运行测试
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
    
    def test_silent_update():
        """测试静默更新"""
        log("🔇 开始测试静默更新...")
        
        def test_thread():
            try:
                from auto_updater import check_and_update_silent
                
                log("✅ 成功导入check_and_update_silent")
                
                result = check_and_update_silent(
                    parent_window=root,
                    current_version="2.1.0",
                    license_key="ADMIN_BYPASS",
                    device_id="ADMIN-DEVICE-001"
                )
                
                if result:
                    log("✅ 静默更新已启动")
                    # 注意：如果有更新，程序会自动重启
                else:
                    log("ℹ️ 没有可用更新或更新失败")
                
            except Exception as e:
                log(f"❌ 静默更新测试失败: {e}")
                import traceback
                log(f"📄 详细错误: {traceback.format_exc()}")
        
        # 在后台线程运行测试
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
    
    def clear_log():
        """清空日志"""
        log_text.delete(1.0, tk.END)
    
    # 按钮区域
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    # 测试下载按钮
    download_button = tk.Button(
        button_frame,
        text="📥 测试下载",
        command=test_download,
        font=("微软雅黑", 12),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10
    )
    download_button.pack(side=tk.LEFT, padx=10)
    
    # 测试静默更新按钮
    silent_button = tk.Button(
        button_frame,
        text="🔇 测试静默更新",
        command=test_silent_update,
        font=("微软雅黑", 12),
        bg="#e74c3c",
        fg="white",
        padx=20,
        pady=10
    )
    silent_button.pack(side=tk.LEFT, padx=10)
    
    # 清空日志按钮
    clear_button = tk.Button(
        button_frame,
        text="🗑️ 清空日志",
        command=clear_log,
        font=("微软雅黑", 12),
        bg="#95a5a6",
        fg="white",
        padx=20,
        pady=10
    )
    clear_button.pack(side=tk.LEFT, padx=10)
    
    # 说明
    info_label = tk.Label(
        root,
        text="修复内容：移除复杂断点续传逻辑，使用简单完整下载，增加超时时间",
        font=("微软雅黑", 9),
        fg="#666666"
    )
    info_label.pack(pady=5)
    
    log("🔧 修复后的更新器测试工具已启动")
    log("💡 点击按钮开始测试")
    
    root.mainloop()

if __name__ == "__main__":
    test_updater()
