#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级构建器功能
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path

def test_file_exists():
    """测试必要文件是否存在"""
    print("🔍 检查必要文件...")
    
    required_files = [
        'build_gui_advanced.py',
        '启动高级构建器.py',
        'license_client.py',  # 主程序文件
        'build_with_spec.py'  # 底层构建脚本
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file} - 存在")
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def test_python_imports():
    """测试Python模块导入"""
    print("\n🐍 测试Python模块导入...")
    
    required_modules = [
        ('tkinter', 'GUI框架'),
        ('threading', '多线程支持'),
        ('subprocess', '子进程管理'),
        ('json', 'JSON处理'),
        ('pathlib', '路径处理'),
        ('datetime', '时间处理')
    ]
    
    failed_imports = []
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError as e:
            print(f"❌ {module} - 导入失败: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"❌ 导入失败的模块: {', '.join(failed_imports)}")
        return False
    
    print("✅ 所有必要模块导入成功")
    return True

def test_optional_imports():
    """测试可选模块导入"""
    print("\n📦 测试可选模块导入...")
    
    optional_modules = [
        ('requests', 'HTTP请求库'),
        ('pkg_resources', '包资源管理'),
        ('fake_useragent', '用户代理生成'),
        ('openpyxl', 'Excel文件处理'),
        ('pandas', '数据分析'),
        ('selenium', 'Web自动化'),
        ('bs4', 'HTML解析'),
        ('cryptography', '加密库'),
        ('PIL', '图像处理')
    ]
    
    available_modules = []
    missing_modules = []
    
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
            available_modules.append(module)
        except ImportError:
            print(f"⚠️ {module} - 未安装 ({description})")
            missing_modules.append(module)
    
    print(f"\n📊 模块统计:")
    print(f"   可用模块: {len(available_modules)}")
    print(f"   缺失模块: {len(missing_modules)}")
    
    if missing_modules:
        print(f"   缺失列表: {', '.join(missing_modules)}")
    
    return len(available_modules) >= len(missing_modules)

def test_gui_syntax():
    """测试GUI文件语法"""
    print("\n🎨 测试GUI文件语法...")
    
    try:
        # 检查语法
        with open('build_gui_advanced.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'build_gui_advanced.py', 'exec')
        print("✅ build_gui_advanced.py 语法正确")
        
        # 尝试导入（不运行）
        spec = importlib.util.spec_from_file_location("build_gui_advanced", "build_gui_advanced.py")
        module = importlib.util.module_from_spec(spec)
        
        print("✅ build_gui_advanced.py 可以导入")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 导入错误: {e}")
        return False

def test_pyinstaller():
    """测试PyInstaller是否可用"""
    print("\n🔧 测试PyInstaller...")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller', '--version'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ PyInstaller 可用 - 版本: {version}")
            return True
        else:
            print(f"❌ PyInstaller 不可用: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ PyInstaller 测试超时")
        return False
    except Exception as e:
        print(f"❌ PyInstaller 测试失败: {e}")
        return False

def test_pip_functionality():
    """测试pip功能"""
    print("\n📥 测试pip功能...")
    
    try:
        # 测试pip list
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'list'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ pip list 命令正常")
            
            # 统计已安装包数量
            lines = result.stdout.strip().split('\n')
            package_count = len([line for line in lines if line and not line.startswith('-')])
            print(f"   已安装包数量: {package_count}")
            
            return True
        else:
            print(f"❌ pip list 失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ pip 测试超时")
        return False
    except Exception as e:
        print(f"❌ pip 测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n📁 检查目录结构...")
    
    current_dir = Path(".")
    
    # 检查重要目录
    important_dirs = ['dist', 'build']
    for dir_name in important_dirs:
        dir_path = current_dir / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/ 目录存在")
        else:
            print(f"ℹ️ {dir_name}/ 目录不存在（正常，构建时会创建）")
    
    # 检查Python文件
    py_files = list(current_dir.glob("*.py"))
    print(f"📄 Python文件数量: {len(py_files)}")
    
    for py_file in py_files[:10]:  # 只显示前10个
        print(f"   - {py_file.name}")
    
    if len(py_files) > 10:
        print(f"   ... 还有 {len(py_files) - 10} 个文件")
    
    return True

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始高级构建器综合测试")
    print("=" * 60)
    
    tests = [
        ("文件存在性检查", test_file_exists),
        ("Python模块导入", test_python_imports),
        ("可选模块检查", test_optional_imports),
        ("GUI语法检查", test_gui_syntax),
        ("PyInstaller可用性", test_pyinstaller),
        ("pip功能测试", test_pip_functionality),
        ("目录结构检查", test_directory_structure)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！高级构建器已准备就绪")
        print("\n📝 下一步:")
        print("   1. 运行: python 启动高级构建器.py")
        print("   2. 或者直接运行: python build_gui_advanced.py")
    elif passed_tests >= total_tests * 0.8:
        print("\n⚠️ 大部分测试通过，可以尝试运行")
        print("   某些功能可能受限，请查看失败的测试项")
    else:
        print("\n❌ 多个测试失败，建议先解决问题")
        print("   请检查Python环境和依赖安装")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    try:
        success = run_comprehensive_test()
        
        print("\n" + "=" * 60)
        if success:
            print("🎯 测试完成 - 系统就绪")
        else:
            print("⚠️ 测试完成 - 发现问题")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
