@echo off
chcp 65001 >nul
title 🔧 修复license_server.py端口配置

echo.
echo ==========================================
echo 🔧 修复license_server.py端口配置
echo ==========================================
echo.
echo 🔍 问题确认:
echo • ❌ license_server.py配置端口44285
echo • ✅ 需要配置端口5000
echo • ❌ 端口冲突导致启动失败
echo • ❌ 404错误因为服务未正确启动
echo.
echo 🎯 解决方案:
echo • 🛑 停止当前服务
echo • 📁 备份原始文件
echo • 🔧 修改端口配置 (44285 → 5000)
echo • 🚀 重启服务
echo • 🧪 测试所有API接口
echo.
echo 📋 修复后效果:
echo • ✅ license_server.py正确运行在端口5000
echo • ✅ 所有API接口正常响应
echo • ✅ license_manager.py能够正常连接
echo • ✅ 完整的许可证管理功能可用
echo.

echo 🔧 开始修复端口配置...
echo.

REM 运行修复脚本
python "修复license_server端口.py"

echo.
echo 👋 修复完成
pause
