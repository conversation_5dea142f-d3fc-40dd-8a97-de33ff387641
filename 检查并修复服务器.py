#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查并修复服务器404问题
"""

import paramiko
import sys
import time

def check_and_fix_server():
    """检查并修复服务器404问题"""
    print("🔍 检查并修复服务器404问题")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 检查服务状态
        print("🔍 步骤1: 检查服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl status license-manager --no-pager")
        output = stdout.read().decode('utf-8')
        
        if "active (running)" in output:
            print("   ✅ 服务正在运行")
        else:
            print("   ❌ 服务未运行")
            print("   🔄 尝试启动服务...")
            stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
            stdout.channel.recv_exit_status()
            time.sleep(3)
        
        # 步骤2: 检查端口监听
        print("\n🔌 步骤2: 检查端口监听...")
        stdin, stdout, stderr = client.exec_command("netstat -tlnp | grep :5000")
        output = stdout.read().decode('utf-8')
        
        if "5000" in output:
            print("   ✅ 端口5000正在监听")
            print(f"   📊 详情: {output.strip()}")
        else:
            print("   ❌ 端口5000未监听")
            return False
        
        # 步骤3: 检查服务日志
        print("\n📋 步骤3: 检查服务日志...")
        stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --no-pager -n 10")
        log_output = stdout.read().decode('utf-8')
        
        print("   最近10条日志:")
        for line in log_output.split('\n')[-10:]:
            if line.strip() and any(keyword in line.lower() for keyword in ['error', 'exception', 'traceback', 'failed']):
                print(f"   ❌ {line}")
            elif line.strip():
                print(f"   📄 {line}")
        
        # 步骤4: 测试本地连接
        print("\n🧪 步骤4: 测试本地连接...")
        test_commands = [
            ("根路径", "curl -s -w '%{http_code}' http://localhost:5000/"),
            ("健康检查", "curl -s -w '%{http_code}' http://localhost:5000/health"),
            ("许可证列表", "curl -s -w '%{http_code}' http://localhost:5000/license/list")
        ]
        
        for name, cmd in test_commands:
            stdin, stdout, stderr = client.exec_command(cmd)
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            print(f"   🔗 {name}:")
            if error:
                print(f"     ❌ 错误: {error}")
            else:
                # 提取状态码（curl -w 参数会在最后输出状态码）
                if output:
                    print(f"     📊 响应: {output[-10:]}")  # 显示最后10个字符（包含状态码）
                else:
                    print("     ❌ 无响应")
        
        # 步骤5: 检查Python进程
        print("\n🐍 步骤5: 检查Python进程...")
        stdin, stdout, stderr = client.exec_command("ps aux | grep license_server.py | grep -v grep")
        output = stdout.read().decode('utf-8')
        
        if output.strip():
            print("   ✅ license_server.py进程运行中")
            print(f"   📊 进程信息: {output.strip()}")
        else:
            print("   ❌ license_server.py进程未找到")
            
            # 尝试手动启动
            print("   🔄 尝试手动启动...")
            stdin, stdout, stderr = client.exec_command(
                f"cd {config['deploy_path']} && python3 license_server.py &"
            )
            time.sleep(3)
        
        # 步骤6: 检查文件内容
        print("\n📄 步骤6: 检查license_server.py文件...")
        stdin, stdout, stderr = client.exec_command(f"head -20 {config['deploy_path']}/license_server.py")
        output = stdout.read().decode('utf-8')
        
        if "from flask import" in output or "app = Flask" in output:
            print("   ✅ license_server.py是Flask应用")
        else:
            print("   ❌ license_server.py文件可能有问题")
            print("   📄 文件开头:")
            for line in output.split('\n')[:5]:
                if line.strip():
                    print(f"     {line}")
        
        # 步骤7: 尝试重启服务
        print("\n🔄 步骤7: 重启服务...")
        stdin, stdout, stderr = client.exec_command("systemctl restart license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务重启成功")
            time.sleep(5)  # 等待服务启动
            
            # 再次测试
            print("   🧪 重启后测试...")
            stdin, stdout, stderr = client.exec_command("curl -s -w '%{http_code}' http://localhost:5000/")
            output = stdout.read().decode('utf-8')
            
            if "200" in output:
                print("   ✅ 重启后连接正常")
                return True
            else:
                print(f"   ❌ 重启后仍有问题: {output}")
        else:
            print("   ❌ 服务重启失败")
        
        # 关闭连接
        client.close()
        
        return False
        
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")
        return False

def provide_manual_fix():
    """提供手动修复建议"""
    print("\n🔧 手动修复建议")
    print("=" * 30)
    
    print("如果自动修复失败，请手动执行以下步骤:")
    print()
    print("1. 🔗 SSH连接到服务器:")
    print("   ssh root@**************")
    print()
    print("2. 🔍 检查服务状态:")
    print("   systemctl status license-manager")
    print("   journalctl -u license-manager -f")
    print()
    print("3. 🔄 重启服务:")
    print("   systemctl stop license-manager")
    print("   systemctl start license-manager")
    print()
    print("4. 🧪 手动测试:")
    print("   curl http://localhost:5000/")
    print("   curl http://localhost:5000/license/list")
    print()
    print("5. 🐍 手动运行Python脚本:")
    print("   cd /opt/license_manager")
    print("   python3 license_server.py")
    print("   (查看是否有错误信息)")

def main():
    """主函数"""
    try:
        print("🔍 问题: 所有API接口返回404错误")
        print("🎯 目标: 检查并修复服务器配置问题")
        print()
        
        if check_and_fix_server():
            print("\n🎉 服务器修复成功！")
            print("💡 现在请重新测试license_manager.py")
        else:
            print("\n❌ 自动修复失败")
            provide_manual_fix()
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
