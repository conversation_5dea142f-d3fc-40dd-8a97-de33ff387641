#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动更新功能
验证更新系统的各个组件是否正常工作
"""

import os
import json
import requests
import sys
from pathlib import Path
import hashlib

def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试服务器连接...")
    
    # 测试本地服务器
    test_urls = [
        "http://localhost:5000",
        "http://127.0.0.1:5000"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(f"{url}/update/check", timeout=5, params={
                'key': 'test',
                'device_id': 'test',
                'current_version': '1.0.0'
            })
            
            print(f"✅ 服务器 {url} 响应正常 (状态码: {response.status_code})")
            return url
            
        except requests.exceptions.ConnectionError:
            print(f"❌ 无法连接到 {url}")
        except Exception as e:
            print(f"⚠️ 连接 {url} 时出错: {e}")
    
    print("❌ 所有服务器连接失败")
    return None

def test_version_info():
    """测试版本信息文件"""
    print("\n📄 测试版本信息文件...")
    
    version_files = [
        "version_info.json",
        "update_server/version_info.json"
    ]
    
    for file_path in version_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    version_info = json.load(f)
                
                required_fields = ['version', 'download_url', 'file_size', 'file_hash', 'changelog']
                missing_fields = [field for field in required_fields if field not in version_info]
                
                if missing_fields:
                    print(f"⚠️ {file_path} 缺少字段: {missing_fields}")
                else:
                    print(f"✅ {file_path} 格式正确")
                    print(f"   版本: {version_info.get('version')}")
                    print(f"   文件大小: {version_info.get('file_size', 0) / (1024*1024):.1f} MB")
                
            except json.JSONDecodeError as e:
                print(f"❌ {file_path} JSON格式错误: {e}")
            except Exception as e:
                print(f"❌ 读取 {file_path} 失败: {e}")
        else:
            print(f"⚠️ {file_path} 不存在")

def test_update_files():
    """测试更新文件"""
    print("\n📦 测试更新文件...")
    
    update_dirs = [
        "update_server/files",
        "dist"
    ]
    
    for update_dir in update_dirs:
        if os.path.exists(update_dir):
            exe_files = list(Path(update_dir).glob("*.exe"))
            
            if exe_files:
                print(f"✅ {update_dir} 中找到 {len(exe_files)} 个exe文件:")
                for exe_file in exe_files:
                    file_size = exe_file.stat().st_size
                    print(f"   📁 {exe_file.name} ({file_size / (1024*1024):.1f} MB)")
            else:
                print(f"⚠️ {update_dir} 中没有找到exe文件")
        else:
            print(f"⚠️ {update_dir} 目录不存在")

def test_file_hash():
    """测试文件哈希计算"""
    print("\n🔐 测试文件哈希计算...")
    
    # 查找exe文件进行测试
    test_files = []
    
    for pattern in ["dist/*.exe", "update_server/files/*.exe"]:
        test_files.extend(Path(".").glob(pattern))
    
    if not test_files:
        print("⚠️ 没有找到可测试的exe文件")
        return
    
    for test_file in test_files[:2]:  # 只测试前两个文件
        try:
            print(f"📁 计算 {test_file.name} 的哈希值...")
            
            sha256_hash = hashlib.sha256()
            with open(test_file, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            file_hash = sha256_hash.hexdigest()
            print(f"✅ 哈希值: {file_hash[:32]}...")
            
        except Exception as e:
            print(f"❌ 计算哈希失败: {e}")

def test_auto_updater_import():
    """测试自动更新模块导入"""
    print("\n📥 测试自动更新模块...")
    
    try:
        from auto_updater import AutoUpdater, check_and_update
        print("✅ auto_updater 模块导入成功")
        
        # 测试创建更新器实例
        updater = AutoUpdater(
            current_version="1.0.0",
            license_server_url="http://localhost:5000/",
            license_key="test",
            device_id="test"
        )
        print("✅ AutoUpdater 实例创建成功")
        
    except ImportError as e:
        print(f"❌ 导入 auto_updater 失败: {e}")
    except Exception as e:
        print(f"❌ 测试 auto_updater 失败: {e}")

def test_license_client_integration():
    """测试license_client集成"""
    print("\n🔑 测试license_client集成...")
    
    if os.path.exists("license_client.py"):
        try:
            # 检查文件中是否包含更新相关代码
            with open("license_client.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            update_keywords = [
                "check_and_update",
                "auto_updater",
                "check_for_updates"
            ]
            
            found_keywords = [kw for kw in update_keywords if kw in content]
            
            if found_keywords:
                print(f"✅ license_client.py 包含更新功能: {found_keywords}")
            else:
                print("⚠️ license_client.py 可能未集成更新功能")
                
        except Exception as e:
            print(f"❌ 检查 license_client.py 失败: {e}")
    else:
        print("⚠️ license_client.py 不存在")

def test_directory_structure():
    """测试目录结构"""
    print("\n📁 测试目录结构...")
    
    required_dirs = [
        "update_server",
        "update_server/files",
        "update_server/versions",
        "dist"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path} 存在")
        else:
            print(f"⚠️ {dir_path} 不存在")
            # 尝试创建目录
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"   📁 已创建 {dir_path}")
            except Exception as e:
                print(f"   ❌ 创建 {dir_path} 失败: {e}")

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 自动更新功能综合测试")
    print("=" * 60)
    
    tests = [
        ("目录结构", test_directory_structure),
        ("版本信息文件", test_version_info),
        ("更新文件", test_update_files),
        ("文件哈希", test_file_hash),
        ("自动更新模块", test_auto_updater_import),
        ("license_client集成", test_license_client_integration),
        ("服务器连接", test_server_connection)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = "✅ 通过" if result is not False else "⚠️ 部分通过"
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results[test_name] = "❌ 失败"
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    for test_name, result in results.items():
        print(f"{result} {test_name}")
    
    passed_tests = sum(1 for result in results.values() if "✅" in result)
    total_tests = len(results)
    
    print(f"\n🎯 测试完成: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！自动更新系统准备就绪")
    elif passed_tests >= total_tests * 0.7:
        print("⚠️ 大部分测试通过，系统基本可用")
    else:
        print("❌ 多项测试失败，请检查配置")

def show_usage_tips():
    """显示使用提示"""
    print("\n" + "=" * 60)
    print("💡 使用提示")
    print("=" * 60)
    
    tips = [
        "1. 启动授权服务器: python license_server.py",
        "2. 启动更新管理工具: python 启动更新管理工具.py",
        "3. 发布新版本: 在更新管理工具中选择exe文件并发布",
        "4. 测试客户端更新: python license_client.py",
        "5. 查看详细文档: 自动更新功能使用指南.md"
    ]
    
    for tip in tips:
        print(f"   {tip}")
    
    print("\n📚 相关文件:")
    files = [
        "license_server.py - 授权服务器",
        "auto_updater.py - 自动更新器",
        "license_client.py - 客户端程序",
        "更新管理工具.py - 版本管理工具",
        "自动更新功能使用指南.md - 详细文档"
    ]
    
    for file_info in files:
        print(f"   📄 {file_info}")

def main():
    """主函数"""
    try:
        run_comprehensive_test()
        show_usage_tips()
        
        print(f"\n{'='*60}")
        input("测试完成，按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
