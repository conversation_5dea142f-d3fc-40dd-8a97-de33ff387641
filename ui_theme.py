import tkinter as tk
from tkinter import ttk
import os
import sys

class AmazonUITheme:
    """亚马逊产品分析工具统一界面主题"""
    
    # 颜色方案
    COLORS = {
        "primary": "#356cac",     # 深蓝色 - 主色
        "secondary": "#4a90e2",   # 亮蓝色 - 次要色
        "accent": "#f89406",      # 橙色 - 强调色
        "background": "#f5f5f5",  # 浅灰色 - 背景色
        "text": "#333333",        # 深灰色 - 文本色
        "light_text": "#666666",  # 中灰色 - 次要文本
        "border": "#dddddd",      # 边框色
        "success": "#28a745",     # 成功色
        "warning": "#ffc107",     # 警告色
        "error": "#dc3545",       # 错误色
        "white": "#ffffff",       # 白色
        "light_gray": "#f0f0f0"   # 更浅的灰色
    }
    
    # 字体设置
    FONTS = {
        "title": ("微软雅黑", 12, "bold"),
        "subtitle": ("微软雅黑", 11, "bold"),
        "body": ("微软雅黑", 10),
        "small": ("微软雅黑", 9),
        "menu": ("微软雅黑", 10),
        "button": ("微软雅黑", 10),
        "status": ("微软雅黑", 9)
    }
    
    # 尺寸和间距
    PADDING = {
        "frame": 15,      # 框架内边距
        "button": 8,      # 按钮内边距
        "widget": 5,      # 控件间距
        "section": 10,    # 区块间距
        "tiny": 2,        # 最小间距
    }
    
    @classmethod
    def get_image_path(cls):
        """获取图标路径"""
        # 确定工作目录
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 图标文件路径
        icon_path = os.path.join(base_dir, "icon.ico")
        if os.path.exists(icon_path):
            return icon_path
        return None
    
    @classmethod
    def setup_styles(cls):
        """设置通用ttk样式"""
        style = ttk.Style()
        
        # 设置全局主题
        try:
            style.theme_use("clam")  # 使用clam主题作为基础
        except:
            pass  # 如果主题不可用，使用默认主题
        
        # 背景配置
        style.configure("TFrame", background=cls.COLORS["background"])
        style.configure("TLabelframe", background=cls.COLORS["background"])
        style.configure("TLabelframe.Label", background=cls.COLORS["background"], 
                        foreground=cls.COLORS["primary"], font=cls.FONTS["subtitle"])
        
        # 按钮风格
        style.configure("TButton", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["button"],
                        padding=cls.PADDING["button"])
        
        style.map("TButton",
                  background=[('active', cls.COLORS["secondary"]), 
                              ('disabled', cls.COLORS["border"])],
                  foreground=[('disabled', cls.COLORS["light_text"])])
        
        # 次要按钮风格
        style.configure("Secondary.TButton", 
                        background=cls.COLORS["secondary"],
                        foreground=cls.COLORS["white"])
        
        # 强调按钮风格
        style.configure("Accent.TButton", 
                        background=cls.COLORS["accent"],
                        foreground=cls.COLORS["white"])
        
        # 标签风格
        style.configure("TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 标题标签风格
        style.configure("Title.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["primary"],
                        font=cls.FONTS["title"])
        
        # 子标题标签风格
        style.configure("Subtitle.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["secondary"],
                        font=cls.FONTS["subtitle"])
        
        # Entry风格
        style.configure("TEntry", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # Combobox风格
        style.configure("TCombobox", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # 复选框风格
        style.configure("TCheckbutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 单选按钮风格
        style.configure("TRadiobutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 进度条风格
        style.configure("TProgressbar", 
                        background=cls.COLORS["primary"],
                        troughcolor=cls.COLORS["light_gray"])
        
        # 分隔符风格
        style.configure("TSeparator", 
                        background=cls.COLORS["border"])
        
        # Treeview (表格) 风格
        style.configure("Treeview", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        font=cls.FONTS["body"])
        
        style.map("Treeview",
                  background=[('selected', cls.COLORS["secondary"])],
                  foreground=[('selected', cls.COLORS["white"])])
        
        style.configure("Treeview.Heading", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["subtitle"],
                        relief="flat")
        
        # Notebook (选项卡) 风格
        style.configure("TNotebook", 
                        background=cls.COLORS["background"],
                        tabmargins=[2, 5, 2, 0])
        
        style.configure("TNotebook.Tab", 
                        background=cls.COLORS["light_gray"],
                        foreground=cls.COLORS["text"],
                        padding=[10, 4],
                        font=cls.FONTS["body"])
        
        style.map("TNotebook.Tab",
                  background=[('selected', cls.COLORS["primary"])],
                  foreground=[('selected', cls.COLORS["white"])],
                  expand=[('selected', [1, 1, 1, 0])])
        
        return style
    
    @classmethod
    def setup_window(cls, root, title, size="800x600", resizable=(True, True), icon=True):
        """设置窗口基本属性"""
        root.title(title)
        root.geometry(size)
        root.resizable(resizable[0], resizable[1])
        root.configure(bg=cls.COLORS["background"])
        
        # 设置图标
        if icon:
            icon_path = cls.get_image_path()
            if icon_path and os.path.exists(icon_path):
                try:
                    root.iconbitmap(icon_path)
                except:
                    pass  # 图标设置失败时忽略
        
        # 设置样式
        cls.setup_styles()
        
        return root
    
    @classmethod
    def create_title_frame(cls, parent, title_text):
        """创建标题栏框架"""
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["frame"], 0))
        
        # 标题标签
        title_label = ttk.Label(title_frame, text=title_text, style="Title.TLabel")
        title_label.pack(side=tk.LEFT, pady=cls.PADDING["section"])
        
        # 添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(0, cls.PADDING["section"]))
        
        return title_frame
    
    @classmethod
    def create_footer_frame(cls, parent):
        """创建底部按钮框架"""
        # 先添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["section"], 0))
        
        footer_frame = ttk.Frame(parent)
        footer_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=cls.PADDING["section"])
        
        return footer_frame
    
    @classmethod
    def create_section_frame(cls, parent, title=None):
        """创建分区框架（可选带标题）"""
        if title:
            section_frame = ttk.LabelFrame(parent, text=title)
        else:
            section_frame = ttk.Frame(parent)
        
        section_frame.pack(fill=tk.BOTH, expand=True, padx=cls.PADDING["frame"], 
                          pady=cls.PADDING["section"])
        
        return section_frame 