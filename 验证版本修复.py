#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证版本修复 - 确认更新循环问题是否解决
"""

import requests

def check_current_version_fixed():
    """检查当前版本是否已修复"""
    try:
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找版本号
        import re
        pattern = r'current_version\s*=\s*["\']([^"\']+)["\']'
        matches = re.findall(pattern, content)
        
        return matches
        
    except Exception as e:
        return [f"错误: {e}"]

def test_update_check():
    """测试更新检查"""
    try:
        url = "http://198.23.135.176:5000/update/check"
        
        # 测试2.1.1版本
        params = {
            'key': 'ADMIN_BYPASS',
            'device_id': 'ADMIN-DEVICE-001',
            'current_version': '2.1.1'
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        return data
        
    except Exception as e:
        return {"error": str(e)}

def main():
    """主函数"""
    print("🔍 验证版本修复")
    print("=" * 50)
    
    # 1. 检查版本号是否已修复
    print("1️⃣ 检查版本号修复...")
    versions = check_current_version_fixed()
    print(f"   发现版本号: {versions}")
    
    if any('2.1.1' in v for v in versions):
        print("   ✅ 版本号已更新为2.1.1")
    else:
        print("   ❌ 版本号仍然是旧版本")
        return
    
    # 2. 测试服务器响应
    print("\n2️⃣ 测试服务器响应...")
    server_response = test_update_check()
    
    if "error" in server_response:
        print(f"   ❌ 服务器测试失败: {server_response['error']}")
    else:
        print(f"   ✅ 服务器响应: {server_response}")
        
        if server_response.get('has_update'):
            print("   ⚠️ 服务器仍然认为需要更新")
            print(f"   📦 服务器最新版本: {server_response.get('latest_version', 'Unknown')}")
        else:
            print("   ✅ 服务器认为当前已是最新版本")
    
    # 3. 总结
    print("\n" + "=" * 50)
    print("📋 修复结果")
    print("=" * 50)
    
    if any('2.1.1' in v for v in versions) and not server_response.get('has_update', True):
        print("🎉 修复成功!")
        print("✅ 版本号已更新")
        print("✅ 服务器确认无需更新")
        print("\n🚀 现在运行license_client.py应该不会再提示更新了")
        
    elif any('2.1.1' in v for v in versions) and server_response.get('has_update'):
        print("⚠️ 部分修复")
        print("✅ 版本号已更新")
        print("❌ 服务器仍认为需要更新")
        print("\n🔧 可能的原因:")
        print("- 服务器上有更新的版本")
        print("- 版本比较逻辑有问题")
        
    else:
        print("❌ 修复失败")
        print("❌ 版本号未正确更新")
    
    print("\n📞 如果还有问题，请运行 python license_client.py 测试")

if __name__ == "__main__":
    main()
