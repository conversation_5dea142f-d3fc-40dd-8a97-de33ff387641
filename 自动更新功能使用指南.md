# 🔄 自动更新功能使用指南

## 📋 目录
1. [功能概述](#功能概述)
2. [系统架构](#系统架构)
3. [服务器端配置](#服务器端配置)
4. [客户端配置](#客户端配置)
5. [发布新版本](#发布新版本)
6. [使用流程](#使用流程)
7. [故障排除](#故障排除)

## 🎯 功能概述

自动更新系统提供了完整的版本管理和自动更新功能：

### ✨ 主要特性
- **自动检查更新**: 程序启动时自动检查新版本
- **安全下载**: 基于授权验证的安全下载机制
- **文件验证**: SHA256哈希验证确保文件完整性
- **无缝更新**: 自动替换程序文件并重启
- **版本管理**: 支持多版本管理和回滚
- **用户友好**: 图形化更新界面和进度显示

### 🏗️ 系统架构

```
客户端 (license_client.py)
    ↓ 检查更新
服务器端 (license_server.py)
    ↓ 验证授权
更新文件存储 (update_server/)
    ↓ 下载文件
自动更新器 (auto_updater.py)
    ↓ 应用更新
程序重启
```

## 🖥️ 服务器端配置

### 1. 启动授权服务器

```bash
# 启动license_server.py
python license_server.py
```

服务器会在 `http://localhost:5000` 启动，提供以下API接口：

- `GET /update/check` - 检查更新
- `GET /update/download` - 下载更新文件

### 2. 配置更新目录结构

```
update_server/
├── version_info.json          # 最新版本信息
├── files/                     # 更新文件存储
│   ├── amazon_blueprint_v2.1.0.exe
│   ├── amazon_blueprint_v2.2.0.exe
│   └── ...
├── versions/                  # 版本历史信息
│   ├── v2.1.0.json
│   ├── v2.2.0.json
│   └── ...
└── logs/                      # 更新日志
```

### 3. 版本信息配置

编辑 `update_server/version_info.json`：

```json
{
  "version": "2.2.0",
  "download_url": "https://your-server.com/updates/files/amazon_blueprint_v2.2.0.exe",
  "file_size": 58000000,
  "file_hash": "sha256_hash_here",
  "changelog": "🎉 版本 2.2.0 更新内容:\n• 新增功能A\n• 修复问题B\n• 性能优化C",
  "release_date": "2024-02-01",
  "min_version": "1.0.0",
  "force_update": false,
  "update_notes": {
    "critical": false,
    "backup_recommended": true,
    "restart_required": true
  }
}
```

### 4. 服务器环境变量

在 `license_server.py` 中配置：

```python
# 更新相关配置
UPDATE_DIR = "update_server"
VERSION_INFO_PATH = os.path.join(UPDATE_DIR, "version_info.json")
```

## 💻 客户端配置

### 1. 自动更新配置

在 `license_client.py` 中已集成自动更新功能：

```python
# 程序启动时自动检查更新
def check_for_updates(self):
    current_version = "2.1.0"  # 当前版本号
    license_key = self.key_var.get().strip()
    device_id = self.client.get_device_id()
    
    check_and_update(self.root, current_version, license_key, device_id)
```

### 2. 更新器配置

在 `auto_updater.py` 中配置服务器地址：

```python
# 配置授权服务器地址
config = {
    "license_server_url": "https://your-server.com/",  # 替换为实际服务器地址
    "current_version": "2.1.0"
}
```

## 🚀 发布新版本

### 步骤1: 构建新版本

```bash
# 使用高级构建器构建新版本
python 启动高级构建器.py

# 或使用命令行构建
python build_with_spec.py
```

### 步骤2: 计算文件哈希

```python
import hashlib

def calculate_file_hash(file_path):
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    return sha256_hash.hexdigest()

# 计算新版本文件的哈希
hash_value = calculate_file_hash("dist/亚马逊蓝图工具.exe")
print(f"文件哈希: {hash_value}")
```

### 步骤3: 更新版本信息

1. **重命名exe文件**:
   ```bash
   # 将构建的exe文件重命名并复制到更新目录
   copy "dist\亚马逊蓝图工具.exe" "update_server\files\amazon_blueprint_v2.2.0.exe"
   ```

2. **更新版本信息文件**:
   ```json
   {
     "version": "2.2.0",
     "download_url": "https://your-server.com/updates/files/amazon_blueprint_v2.2.0.exe",
     "file_size": 58000000,
     "file_hash": "计算得到的SHA256哈希值",
     "changelog": "🎉 版本 2.2.0 更新内容:\n• 新增功能\n• 修复问题\n• 性能优化",
     "release_date": "2024-02-01",
     "min_version": "1.0.0",
     "force_update": false
   }
   ```

3. **创建版本历史文件**:
   ```bash
   copy "update_server\version_info.json" "update_server\versions\v2.2.0.json"
   ```

### 步骤4: 部署到服务器

```bash
# 上传文件到服务器
scp -r update_server/ <EMAIL>:/path/to/server/
```

## 🔄 使用流程

### 自动更新流程

1. **用户启动程序**
   - 程序自动检查更新（延迟1秒避免阻塞界面）

2. **检查更新**
   - 向服务器发送更新检查请求
   - 验证授权码和设备ID
   - 比较版本号

3. **发现新版本**
   - 显示更新对话框
   - 展示更新内容和版本信息
   - 用户选择是否更新

4. **下载更新**
   - 显示下载进度
   - 验证文件完整性
   - 保存到临时目录

5. **应用更新**
   - 创建更新脚本
   - 关闭当前程序
   - 替换程序文件
   - 重启新版本

### 手动检查更新

```python
# 在程序中添加手动检查更新按钮
def manual_check_update():
    current_version = "2.1.0"
    license_key = "your_license_key"
    device_id = "your_device_id"
    
    check_and_update(root, current_version, license_key, device_id)
```

## 🛠️ 故障排除

### 常见问题

#### 1. 更新检查失败
**症状**: 提示"检查更新失败"

**可能原因**:
- 网络连接问题
- 服务器地址错误
- 授权验证失败

**解决方案**:
```python
# 检查网络连接
import requests
try:
    response = requests.get("https://your-server.com/update/check", timeout=10)
    print(f"服务器响应: {response.status_code}")
except Exception as e:
    print(f"网络错误: {e}")
```

#### 2. 下载失败
**症状**: 下载进度中断或文件损坏

**可能原因**:
- 网络不稳定
- 服务器文件不存在
- 磁盘空间不足

**解决方案**:
- 检查网络连接稳定性
- 验证服务器文件存在
- 确保有足够磁盘空间

#### 3. 文件验证失败
**症状**: 提示"文件哈希验证失败"

**解决方案**:
```python
# 重新计算文件哈希
import hashlib

def verify_file_hash(file_path, expected_hash):
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    
    actual_hash = sha256_hash.hexdigest()
    return actual_hash == expected_hash
```

#### 4. 更新应用失败
**症状**: 程序无法重启或文件替换失败

**可能原因**:
- 权限不足
- 文件被占用
- 防病毒软件拦截

**解决方案**:
- 以管理员权限运行
- 关闭防病毒软件
- 手动替换文件

### 调试模式

启用详细日志：

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_debug.log'),
        logging.StreamHandler()
    ]
)
```

### 测试更新系统

```python
# 运行测试脚本
python test_update_system.py
```

## 📝 最佳实践

### 1. 版本管理
- 使用语义化版本号 (如: 2.1.0)
- 保留历史版本文件
- 记录详细的更新日志

### 2. 安全考虑
- 使用HTTPS传输
- 验证文件哈希
- 授权验证机制

### 3. 用户体验
- 非阻塞的更新检查
- 清晰的更新说明
- 可选的更新时机

### 4. 错误处理
- 完善的异常处理
- 详细的错误日志
- 用户友好的错误提示

---

**版本**: 1.0.0  
**更新日期**: 2024年  
**兼容性**: Python 3.7+, Windows 10+
