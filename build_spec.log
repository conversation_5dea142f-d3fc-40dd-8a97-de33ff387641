2025-08-01 00:16:14,873 - INFO - ============================================================
2025-08-01 00:16:14,873 - INFO - 🔧 亚马逊蓝图工具 - Spec文件构建器
2025-08-01 00:16:14,874 - INFO - ============================================================
2025-08-01 00:16:14,874 - INFO - 🧪 测试关键依赖...
2025-08-01 00:16:14,947 - INFO - ✅ fake_useragent - 导入成功
2025-08-01 00:16:15,304 - INFO - ✅ openpyxl - 导入成功
2025-08-01 00:16:15,803 - INFO - ✅ pandas - 导入成功
2025-08-01 00:16:15,950 - INFO - ✅ requests - 导入成功
2025-08-01 00:16:15,952 - INFO - ✅ selenium - 导入成功
2025-08-01 00:16:15,953 - ERROR - ❌ beautifulsoup4 - 导入失败: No module named 'beautifulsoup4'
2025-08-01 00:16:15,956 - INFO - ✅ cryptography - 导入成功
2025-08-01 00:16:15,957 - ERROR - 以下依赖导入失败: ['beautifulsoup4']
2025-08-01 00:16:15,957 - ERROR - 请安装缺失的依赖: pip install beautifulsoup4
2025-08-01 00:16:15,957 - ERROR - 依赖测试失败，请先安装缺失的依赖
2025-08-01 00:16:48,458 - INFO - ============================================================
2025-08-01 00:16:48,458 - INFO - 🔧 亚马逊蓝图工具 - Spec文件构建器
2025-08-01 00:16:48,459 - INFO - ============================================================
2025-08-01 00:16:48,459 - INFO - 🧪 测试关键依赖...
2025-08-01 00:16:48,535 - INFO - ✅ fake_useragent - 导入成功
2025-08-01 00:16:48,874 - INFO - ✅ openpyxl - 导入成功
2025-08-01 00:16:49,376 - INFO - ✅ pandas - 导入成功
2025-08-01 00:16:49,505 - INFO - ✅ requests - 导入成功
2025-08-01 00:16:49,507 - INFO - ✅ selenium - 导入成功
2025-08-01 00:16:49,613 - INFO - ✅ bs4 - 导入成功
2025-08-01 00:16:49,616 - INFO - ✅ cryptography - 导入成功
2025-08-01 00:16:49,616 - INFO - ✅ 所有关键依赖测试通过
2025-08-01 00:16:49,617 - INFO - 🚀 开始使用spec文件构建exe...
2025-08-01 00:16:49,664 - INFO - PyInstaller版本: 6.14.2
2025-08-01 00:16:49,665 - INFO - 创建spec文件: amazon_blueprint.spec
2025-08-01 00:16:49,672 - INFO - 清理旧的dist目录
2025-08-01 00:16:49,673 - INFO - 执行命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m PyInstaller --clean --noconfirm amazon_blueprint.spec
2025-08-01 00:17:52,426 - INFO - ✅ PyInstaller构建成功
2025-08-01 00:17:52,426 - INFO - 生成的exe文件: dist\亚马逊蓝图工具.exe
2025-08-01 00:17:52,427 - INFO - 文件大小: 55.6MB
2025-08-01 00:17:52,427 - INFO - 🎉 构建完成！
2025-08-01 00:17:52,427 - INFO - 请测试生成的exe文件是否能正常运行
2025-08-01 00:24:20,040 - INFO - ============================================================
2025-08-01 00:24:20,041 - INFO - 🔧 亚马逊蓝图工具 - Spec文件构建器
2025-08-01 00:24:20,041 - INFO - ============================================================
2025-08-01 00:24:20,042 - INFO - 🧪 测试关键依赖...
2025-08-01 00:24:20,062 - INFO - ✅ fake_useragent - 导入成功
2025-08-01 00:24:20,699 - INFO - ✅ openpyxl - 导入成功
2025-08-01 00:24:21,364 - INFO - ✅ pandas - 导入成功
2025-08-01 00:24:21,587 - INFO - ✅ requests - 导入成功
2025-08-01 00:24:21,589 - INFO - ✅ selenium - 导入成功
2025-08-01 00:24:21,896 - INFO - ✅ bs4 - 导入成功
2025-08-01 00:24:21,900 - INFO - ✅ cryptography - 导入成功
2025-08-01 00:24:21,901 - INFO - ✅ 所有关键依赖测试通过
2025-08-01 00:24:21,901 - INFO - 🚀 开始使用spec文件构建exe...
2025-08-01 00:24:21,946 - INFO - PyInstaller版本: 6.14.2
2025-08-01 00:24:21,947 - INFO - 创建spec文件: amazon_blueprint.spec
2025-08-01 00:24:21,955 - INFO - 清理旧的dist目录
2025-08-01 00:24:21,969 - INFO - 清理旧的build目录
2025-08-01 00:24:21,970 - INFO - 执行命令: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m PyInstaller --clean --noconfirm amazon_blueprint.spec
2025-08-01 00:28:06,124 - INFO - ============================================================
2025-08-01 00:28:06,124 - INFO - 🔧 亚马逊蓝图工具 - Spec文件构建器
2025-08-01 00:28:06,125 - INFO - ============================================================
2025-08-01 00:28:06,125 - INFO - 🧪 测试关键依赖...
2025-08-01 00:28:06,151 - INFO - ✅ fake_useragent - 导入成功
2025-08-01 00:28:06,880 - INFO - ✅ openpyxl - 导入成功
2025-08-01 00:28:07,448 - INFO - ✅ pandas - 导入成功
2025-08-01 00:28:07,656 - INFO - ✅ requests - 导入成功
2025-08-01 00:28:07,658 - INFO - ✅ selenium - 导入成功
2025-08-01 00:28:07,936 - INFO - ✅ bs4 - 导入成功
2025-08-01 00:28:07,940 - INFO - ✅ cryptography - 导入成功
2025-08-01 00:28:07,940 - INFO - ✅ 所有关键依赖测试通过
2025-08-01 00:28:07,941 - INFO - 🚀 开始使用spec文件构建exe...
2025-08-01 00:28:07,984 - INFO - PyInstaller版本: 6.14.2
2025-08-01 00:28:07,985 - INFO - 创建spec文件: amazon_blueprint.spec
2025-08-01 00:28:07,986 - INFO - 清理旧的dist目录
2025-08-01 00:28:07,986 - INFO - 清理旧的build目录
2025-08-01 00:28:07,987 - INFO - 执行命令: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m PyInstaller --clean --noconfirm amazon_blueprint.spec
2025-08-01 00:30:24,709 - INFO - ✅ PyInstaller构建成功
2025-08-01 00:30:24,710 - INFO - 生成的exe文件: dist\亚马逊蓝图工具.exe
2025-08-01 00:30:24,710 - INFO - 文件大小: 55.6MB
2025-08-01 00:30:24,710 - INFO - 🎉 构建完成！
2025-08-01 00:30:24,710 - INFO - 请测试生成的exe文件是否能正常运行
