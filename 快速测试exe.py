#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试生成的exe文件是否正常工作
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_exe_basic():
    """基础测试exe文件"""
    print("🧪 快速测试exe文件")
    print("=" * 50)
    
    # 查找exe文件
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    exe_files = list(dist_dir.glob("*.exe"))
    if not exe_files:
        print("❌ 未找到exe文件")
        return False
    
    exe_file = exe_files[0]
    print(f"📁 找到exe文件: {exe_file}")
    print(f"📊 文件大小: {exe_file.stat().st_size / 1024 / 1024:.1f}MB")
    
    # 测试1: 检查文件是否存在且可执行
    print("\n🔍 测试1: 文件检查")
    if exe_file.exists():
        print("✅ exe文件存在")
    else:
        print("❌ exe文件不存在")
        return False
    
    # 测试2: 尝试启动exe（短时间）
    print("\n🚀 测试2: 启动测试")
    try:
        # 启动exe进程
        process = subprocess.Popen(
            [str(exe_file)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=str(exe_file.parent)
        )
        
        # 等待2秒看是否能正常启动
        time.sleep(2)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ exe启动成功（进程正在运行）")
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            success = True
        else:
            # 进程已退出，检查退出码
            stdout, stderr = process.communicate()
            if process.returncode == 0:
                print("✅ exe正常退出")
                success = True
            else:
                print(f"❌ exe异常退出，退出码: {process.returncode}")
                if stderr:
                    print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
                success = False
                
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        success = False
    
    # 测试3: 检查依赖文件
    print("\n📦 测试3: 依赖检查")
    try:
        # 使用PyInstaller的分析工具检查依赖
        result = subprocess.run([
            "python", "-c", 
            "import sys; print('Python版本:', sys.version); "
            "try: import fake_useragent; print('✅ fake_useragent可用')\n"
            "except: print('❌ fake_useragent不可用'); "
            "try: import openpyxl; print('✅ openpyxl可用')\n"
            "except: print('❌ openpyxl不可用'); "
            "try: import requests; print('✅ requests可用')\n"
            "except: print('❌ requests不可用'); "
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 系统依赖检查通过")
            print(result.stdout)
        else:
            print("⚠️ 系统依赖检查有问题")
            print(result.stderr)
            
    except Exception as e:
        print(f"⚠️ 依赖检查失败: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    if success:
        print("🎉 exe文件测试通过！")
        print("✅ 文件可以正常启动")
        print("✅ 基础功能正常")
        print("\n💡 建议:")
        print("1. 可以将exe文件分发给其他用户")
        print("2. 确保目标机器有基本的Windows运行库")
        print("3. 如果遇到问题，检查杀毒软件设置")
        return True
    else:
        print("❌ exe文件测试失败")
        print("⚠️ 需要检查构建配置或依赖")
        return False

def test_exe_manual():
    """手动测试指导"""
    print("\n" + "=" * 50)
    print("🔧 手动测试指导")
    print("=" * 50)
    
    dist_dir = Path("dist")
    exe_files = list(dist_dir.glob("*.exe"))
    
    if exe_files:
        exe_file = exe_files[0]
        print(f"📁 exe文件位置: {exe_file.absolute()}")
        print("\n📋 手动测试步骤:")
        print("1. 双击exe文件")
        print("2. 检查是否出现程序界面")
        print("3. 尝试输入激活码测试")
        print("4. 检查程序功能是否正常")
        print("\n🚨 如果遇到问题:")
        print("- 检查杀毒软件是否拦截")
        print("- 确保有管理员权限")
        print("- 查看Windows事件日志")
        print("- 在命令行中运行查看错误信息")
        
        print(f"\n💻 命令行测试:")
        print(f'cd "{exe_file.parent}"')
        print(f'"{exe_file.name}"')
    else:
        print("❌ 未找到exe文件")

def main():
    """主函数"""
    try:
        # 基础测试
        success = test_exe_basic()
        
        # 手动测试指导
        test_exe_manual()
        
        if success:
            print("\n🎯 结论: exe构建成功！")
        else:
            print("\n⚠️ 结论: exe可能有问题，需要进一步检查")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")

if __name__ == "__main__":
    main()
