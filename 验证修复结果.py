#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复结果 - 确认所有问题都已解决
"""

import os
import re

def check_auto_updater():
    """检查auto_updater.py的修复情况"""
    print("🔍 检查 auto_updater.py 修复情况...")
    
    if not os.path.exists("auto_updater.py"):
        print("❌ auto_updater.py 文件不存在")
        return False
    
    with open("auto_updater.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    issues = []
    fixes = []
    
    # 1. 检查进度限制是否已修复
    if "min(progress, 99.9)" in content:
        issues.append("❌ 进度仍被限制在99.9%")
    else:
        fixes.append("✅ 进度限制已修复 (允许100%)")
    
    # 2. 检查URL配置
    if "/api/" in content:
        issues.append("❌ 仍包含错误的/api/路径")
    else:
        fixes.append("✅ URL配置已修复")
    
    # 3. 检查完整性验证阈值
    if "* 0.999" in content:
        issues.append("❌ 完整性验证仍过于严格")
    elif "* 0.95" in content:
        fixes.append("✅ 完整性验证阈值已调整为95%")
    
    # 4. 检查重复函数定义
    pattern = r'def check_and_update_silent\([^)]*\):'
    matches = list(re.finditer(pattern, content))
    if len(matches) > 1:
        issues.append(f"❌ 仍有{len(matches)}个重复的check_and_update_silent函数")
    else:
        fixes.append("✅ 重复函数定义已清理")
    
    # 5. 检查文件同步
    if "os.fsync" in content:
        fixes.append("✅ 文件同步已增强")
    
    # 显示结果
    for fix in fixes:
        print(f"  {fix}")
    for issue in issues:
        print(f"  {issue}")
    
    return len(issues) == 0

def check_license_client():
    """检查license_client.py的修复情况"""
    print("\n🔍 检查 license_client.py 修复情况...")
    
    if not os.path.exists("license_client.py"):
        print("❌ license_client.py 文件不存在")
        return False
    
    with open("license_client.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    issues = []
    fixes = []
    
    # 检查导入配置
    if "check_and_update_silent as check_and_update" in content:
        issues.append("❌ 仍使用静默更新导入")
    elif "from auto_updater import check_and_update" in content:
        fixes.append("✅ 更新导入配置已修复")
    
    # 显示结果
    for fix in fixes:
        print(f"  {fix}")
    for issue in issues:
        print(f"  {issue}")
    
    return len(issues) == 0

def check_backup_files():
    """检查备份文件"""
    print("\n📁 检查备份文件...")
    
    backup_files = []
    for file in os.listdir('.'):
        if file.endswith('.backup'):
            backup_files.append(file)
    
    if backup_files:
        print("✅ 找到以下备份文件:")
        for backup in backup_files:
            print(f"  - {backup}")
    else:
        print("⚠️ 未找到备份文件")
    
    return len(backup_files) > 0

def test_import():
    """测试导入是否正常"""
    print("\n🧪 测试模块导入...")
    
    try:
        # 测试auto_updater导入
        import auto_updater
        print("✅ auto_updater 模块导入成功")
        
        # 测试关键函数是否存在
        if hasattr(auto_updater, 'check_and_update'):
            print("✅ check_and_update 函数存在")
        else:
            print("❌ check_and_update 函数不存在")
            return False
        
        if hasattr(auto_updater, 'AutoUpdater'):
            print("✅ AutoUpdater 类存在")
        else:
            print("❌ AutoUpdater 类不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 验证更新下载问题修复结果")
    print("=" * 50)
    
    results = []
    
    # 检查各个文件的修复情况
    results.append(check_auto_updater())
    results.append(check_license_client())
    results.append(check_backup_files())
    results.append(test_import())
    
    print("\n" + "=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print("🎉 所有修复验证通过！")
        print("\n✅ 修复总结:")
        print("  - 进度显示: 现在可以显示100%")
        print("  - URL配置: 已修复路径错误")
        print("  - 完整性验证: 阈值调整为95%")
        print("  - 重复函数: 已清理")
        print("  - 导入配置: 使用标准更新")
        print("  - 文件同步: 已增强")
        
        print("\n🚀 建议下一步:")
        print("1. 重启 license_client.py 程序")
        print("2. 测试更新功能是否正常")
        print("3. 观察下载进度是否能达到100%")
        
    else:
        print(f"⚠️ 验证结果: {success_count}/{total_count} 项通过")
        print("请检查上述问题并重新运行修复脚本")
    
    print(f"\n📊 验证得分: {success_count}/{total_count}")

if __name__ == "__main__":
    main()
