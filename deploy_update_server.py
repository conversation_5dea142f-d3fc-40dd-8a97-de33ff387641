#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新服务器部署脚本
用于将更新文件部署到license_server.py服务器
"""

import os
import shutil
import json
import sys
from pathlib import Path

def deploy_update_files():
    """部署更新文件到服务器目录"""
    
    # 服务器目录配置
    SERVER_BASE_DIR = "/opt/license_manager"
    UPDATE_DIR = os.path.join(SERVER_BASE_DIR, "updates")
    
    # 本地文件路径
    LOCAL_UPDATE_DIR = "update_server"
    LOCAL_VERSION_INFO = os.path.join(LOCAL_UPDATE_DIR, "version_info.json")
    LOCAL_FILES_DIR = os.path.join(LOCAL_UPDATE_DIR, "files")
    
    print("🚀 开始部署更新服务器文件...")
    
    # 1. 检查本地文件
    if not os.path.exists(LOCAL_UPDATE_DIR):
        print("❌ 错误: 本地update_server目录不存在")
        print("请先运行 update_server_manager.py 生成更新文件")
        return False
    
    if not os.path.exists(LOCAL_VERSION_INFO):
        print("❌ 错误: version_info.json 文件不存在")
        return False
    
    # 2. 创建服务器目录
    try:
        os.makedirs(UPDATE_DIR, exist_ok=True)
        os.makedirs(os.path.join(UPDATE_DIR, "files"), exist_ok=True)
        os.makedirs(os.path.join(UPDATE_DIR, "versions"), exist_ok=True)
        print(f"✅ 创建服务器目录: {UPDATE_DIR}")
    except Exception as e:
        print(f"❌ 创建目录失败: {e}")
        return False
    
    # 3. 复制版本信息文件
    try:
        server_version_info = os.path.join(UPDATE_DIR, "version_info.json")
        shutil.copy2(LOCAL_VERSION_INFO, server_version_info)
        print(f"✅ 复制版本信息: {server_version_info}")
    except Exception as e:
        print(f"❌ 复制版本信息失败: {e}")
        return False
    
    # 4. 复制exe文件
    try:
        if os.path.exists(LOCAL_FILES_DIR):
            server_files_dir = os.path.join(UPDATE_DIR, "files")
            for file in os.listdir(LOCAL_FILES_DIR):
                if file.endswith('.exe'):
                    local_file = os.path.join(LOCAL_FILES_DIR, file)
                    server_file = os.path.join(server_files_dir, file)
                    shutil.copy2(local_file, server_file)
                    file_size = os.path.getsize(server_file)
                    print(f"✅ 复制exe文件: {file} ({file_size / (1024*1024):.1f}MB)")
    except Exception as e:
        print(f"❌ 复制exe文件失败: {e}")
        return False
    
    # 5. 复制版本历史
    try:
        local_versions_dir = os.path.join(LOCAL_UPDATE_DIR, "versions")
        if os.path.exists(local_versions_dir):
            server_versions_dir = os.path.join(UPDATE_DIR, "versions")
            for file in os.listdir(local_versions_dir):
                if file.endswith('.json'):
                    local_file = os.path.join(local_versions_dir, file)
                    server_file = os.path.join(server_versions_dir, file)
                    shutil.copy2(local_file, server_file)
                    print(f"✅ 复制版本历史: {file}")
    except Exception as e:
        print(f"❌ 复制版本历史失败: {e}")
        return False
    
    # 6. 设置文件权限
    try:
        # 设置目录权限
        os.chmod(UPDATE_DIR, 0o755)
        os.chmod(os.path.join(UPDATE_DIR, "files"), 0o755)
        os.chmod(os.path.join(UPDATE_DIR, "versions"), 0o755)
        
        # 设置文件权限
        for root, dirs, files in os.walk(UPDATE_DIR):
            for file in files:
                file_path = os.path.join(root, file)
                if file.endswith('.exe'):
                    os.chmod(file_path, 0o644)  # exe文件只读
                else:
                    os.chmod(file_path, 0o644)  # 其他文件只读
        
        print("✅ 设置文件权限完成")
    except Exception as e:
        print(f"⚠️  设置权限失败: {e}")
    
    # 7. 验证部署
    try:
        with open(os.path.join(UPDATE_DIR, "version_info.json"), 'r', encoding='utf-8') as f:
            version_info = json.load(f)
        
        print("\n📊 部署验证:")
        print(f"   版本: {version_info.get('version')}")
        print(f"   发布日期: {version_info.get('release_date')}")
        print(f"   文件大小: {version_info.get('file_size', 0) / (1024*1024):.1f}MB")
        print(f"   文件哈希: {version_info.get('file_hash', '')[:16]}...")
        
        # 检查exe文件是否存在
        files_dir = os.path.join(UPDATE_DIR, "files")
        exe_files = [f for f in os.listdir(files_dir) if f.endswith('.exe')]
        print(f"   可用文件: {len(exe_files)} 个")
        
    except Exception as e:
        print(f"❌ 验证部署失败: {e}")
        return False
    
    print("\n🎉 更新服务器部署完成!")
    print(f"📁 服务器目录: {UPDATE_DIR}")
    print("\n📋 下一步操作:")
    print("1. 确保license_server.py服务正在运行")
    print("2. 测试更新API端点:")
    print("   GET /update/check?key=YOUR_KEY&device_id=YOUR_DEVICE&current_version=2.0.0")
    print("   GET /update/download?key=YOUR_KEY&device_id=YOUR_DEVICE")
    print("3. 在客户端测试自动更新功能")
    
    return True

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    
    # 检查license_server.py是否在运行
    try:
        import requests
        response = requests.get("http://localhost:5000/", timeout=5)
        print("✅ license_server.py 服务正在运行")
        return True
    except:
        print("❌ license_server.py 服务未运行")
        print("请启动服务器: python license_server.py")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 亚马逊蓝图工具 - 更新服务器部署脚本")
    print("=" * 60)
    
    # 检查运行环境
    if os.name == 'nt':  # Windows
        print("⚠️  检测到Windows环境")
        print("请确保以管理员权限运行此脚本")
        print("或者手动将文件复制到服务器目录")
    
    # 部署文件
    if deploy_update_files():
        print("\n✅ 部署成功!")
        
        # 检查服务器状态
        check_server_status()
        
        print("\n🎯 测试建议:")
        print("1. 修改auto_updater.py中的服务器URL")
        print("2. 重新构建客户端程序")
        print("3. 测试自动更新功能")
        
    else:
        print("\n❌ 部署失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
