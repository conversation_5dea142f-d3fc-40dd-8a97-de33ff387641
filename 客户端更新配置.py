#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端更新配置 - 为exe程序提供更新功能的配置和代码
"""

import requests
import json
import os
import hashlib
import tempfile
import subprocess
from datetime import datetime

class AutoUpdater:
    """自动更新器"""
    
    def __init__(self, server_url="http://**************:5000", license_key=None, device_id=None):
        self.server_url = server_url.rstrip('/')
        self.license_key = license_key
        self.device_id = device_id
        self.current_version = "2.1.0"  # 当前版本，需要根据实际情况修改
    
    def check_for_updates(self, current_version=None):
        """检查是否有更新"""
        try:
            if current_version:
                self.current_version = current_version
            
            # 构建请求参数
            params = {
                'current_version': self.current_version
            }
            
            # 如果有授权信息，添加到参数中
            if self.license_key:
                params['key'] = self.license_key
            if self.device_id:
                params['device_id'] = self.device_id
            
            # 发送检查更新请求
            response = requests.get(
                f"{self.server_url}/update/check",
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return {
                        'success': True,
                        'has_update': data.get('has_update', False),
                        'current_version': data.get('current_version', self.current_version),
                        'latest_version': data.get('latest_version'),
                        'update_info': data.get('update_info')
                    }
                else:
                    return {
                        'success': False,
                        'message': data.get('message', '检查更新失败')
                    }
            else:
                return {
                    'success': False,
                    'message': f'服务器响应异常: HTTP {response.status_code}'
                }
                
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': '无法连接到更新服务器'
            }
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'message': '连接更新服务器超时'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'检查更新时发生错误: {str(e)}'
            }
    
    def download_update(self, version=None, progress_callback=None):
        """下载更新文件"""
        try:
            # 构建请求参数
            params = {}
            if version:
                params['version'] = version
            if self.license_key:
                params['key'] = self.license_key
            if self.device_id:
                params['device_id'] = self.device_id
            
            # 发送下载请求
            response = requests.get(
                f"{self.server_url}/update/download",
                params=params,
                stream=True,
                timeout=300  # 5分钟超时
            )
            
            if response.status_code == 200:
                # 获取文件大小
                total_size = int(response.headers.get('content-length', 0))
                
                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.exe')
                downloaded_size = 0
                
                try:
                    # 下载文件
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            temp_file.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # 调用进度回调
                            if progress_callback and total_size > 0:
                                progress = (downloaded_size / total_size) * 100
                                progress_callback(progress, downloaded_size, total_size)
                    
                    temp_file.close()
                    
                    return {
                        'success': True,
                        'file_path': temp_file.name,
                        'file_size': downloaded_size
                    }
                    
                except Exception as e:
                    temp_file.close()
                    if os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
                    raise e
                    
            else:
                return {
                    'success': False,
                    'message': f'下载失败: HTTP {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'下载更新时发生错误: {str(e)}'
            }
    
    def verify_file_integrity(self, file_path, expected_hash):
        """验证文件完整性"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            actual_hash = sha256_hash.hexdigest()
            return actual_hash.lower() == expected_hash.lower()
            
        except Exception:
            return False
    
    def install_update(self, update_file_path, current_exe_path=None):
        """安装更新"""
        try:
            if not current_exe_path:
                current_exe_path = os.path.abspath(sys.argv[0])
            
            # 创建备份
            backup_path = current_exe_path + '.backup'
            if os.path.exists(current_exe_path):
                os.rename(current_exe_path, backup_path)
            
            # 复制新文件
            import shutil
            shutil.copy2(update_file_path, current_exe_path)
            
            # 删除临时文件
            os.unlink(update_file_path)
            
            return {
                'success': True,
                'message': '更新安装成功',
                'backup_path': backup_path
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'安装更新时发生错误: {str(e)}'
            }

# 使用示例代码
def example_usage():
    """使用示例"""
    print("🔄 自动更新示例")
    print("=" * 30)
    
    # 创建更新器实例
    updater = AutoUpdater(
        server_url="http://**************:5000",
        license_key="your_license_key",  # 替换为实际的授权码
        device_id="your_device_id"       # 替换为实际的设备ID
    )
    
    # 检查更新
    print("📋 检查更新...")
    update_result = updater.check_for_updates("2.0.0")
    
    if update_result['success']:
        if update_result['has_update']:
            print(f"✅ 发现新版本: {update_result['latest_version']}")
            
            # 下载更新
            print("📥 下载更新...")
            def progress_callback(progress, downloaded, total):
                print(f"下载进度: {progress:.1f}% ({downloaded}/{total} 字节)")
            
            download_result = updater.download_update(
                version=update_result['latest_version'],
                progress_callback=progress_callback
            )
            
            if download_result['success']:
                print("✅ 下载完成")
                print(f"文件路径: {download_result['file_path']}")
                
                # 这里可以添加安装逻辑
                # install_result = updater.install_update(download_result['file_path'])
                
            else:
                print(f"❌ 下载失败: {download_result['message']}")
        else:
            print("✅ 当前已是最新版本")
    else:
        print(f"❌ 检查更新失败: {update_result['message']}")

if __name__ == "__main__":
    example_usage()
