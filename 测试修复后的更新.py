#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的更新功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_fixed_update():
    """测试修复后的更新功能"""
    print("🧪 测试修复后的更新功能")
    print("=" * 50)
    
    try:
        from auto_updater import check_and_update
        
        print("✅ 成功导入 check_and_update")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试修复后的更新")
        root.geometry("400x200")
        
        # 设置图标
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        # 创建界面
        title_label = tk.Label(
            root,
            text="测试修复后的更新功能",
            font=("微软雅黑", 14, "bold"),
            pady=20
        )
        title_label.pack()
        
        info_label = tk.Label(
            root,
            text="点击按钮测试更新功能\n现在支持断点续传和网络中断恢复",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        info_label.pack(pady=10)
        
        def start_update():
            """开始更新测试"""
            print("🚀 开始更新测试...")
            
            try:
                result = check_and_update(
                    parent_window=root,
                    current_version="2.1.0",
                    license_key="ADMIN_BYPASS",
                    device_id="ADMIN-DEVICE-001"
                )
                
                print(f"📊 更新结果: {result}")
                
                if result:
                    print("✅ 更新功能测试成功")
                    messagebox.showinfo("成功", "更新功能测试成功！")
                else:
                    print("ℹ️ 没有可用更新或用户取消")
                    messagebox.showinfo("信息", "没有可用更新或用户取消了更新")
                    
            except Exception as e:
                error_msg = f"❌ 更新测试失败: {str(e)}"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
        
        # 创建按钮
        test_button = tk.Button(
            root,
            text="🚀 开始更新测试",
            command=start_update,
            font=("微软雅黑", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        test_button.pack(pady=20)
        
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"❌ 导入失败: {e}"
        print(error_msg)
        messagebox.showerror("错误", error_msg)
    except Exception as e:
        error_msg = f"❌ 测试失败: {e}"
        print(error_msg)
        messagebox.showerror("错误", error_msg)

if __name__ == "__main__":
    test_fixed_update()
