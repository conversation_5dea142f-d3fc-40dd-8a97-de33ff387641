#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新功能降级方案 - 不使用断点续传
"""

import requests
import os
import tempfile
import time

def download_without_resume(version="2.1.1", progress_callback=None):
    """不使用断点续传的下载"""
    print("📥 开始下载（不使用断点续传）")
    
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': version
    }
    
    # 删除可能存在的未完成下载
    temp_file = os.path.join(tempfile.gettempdir(), f"amazon_blueprint_update_{version}.exe")
    if os.path.exists(temp_file):
        os.remove(temp_file)
        print("🗑️ 已删除未完成的下载文件")
    
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            print(f"🚀 开始下载 (尝试 {retry_count + 1}/{max_retries})")
            
            response = requests.get(
                url, 
                params=params,
                stream=True,
                timeout=(60, 1800)  # 30分钟超时
            )
            
            if response.status_code != 200:
                raise Exception(f"HTTP错误: {response.status_code}")
            
            file_size = int(response.headers.get('Content-Length', 0))
            downloaded_size = 0
            
            print(f"📦 文件大小: {file_size/1024/1024:.1f}MB")
            
            with open(temp_file, 'wb') as f:
                chunk_size = 32 * 1024  # 32KB chunks
                
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 更新进度
                        if progress_callback and file_size > 0:
                            progress = (downloaded_size / file_size) * 100
                            progress_callback(progress)
                        
                        # 每10MB显示进度
                        if downloaded_size % (10 * 1024 * 1024) == 0:
                            print(f"📊 已下载: {downloaded_size/1024/1024:.1f}MB")
            
            print(f"✅ 下载完成: {temp_file}")
            return temp_file
            
        except Exception as e:
            retry_count += 1
            print(f"❌ 下载失败 (尝试 {retry_count}/{max_retries}): {e}")
            
            if retry_count < max_retries:
                wait_time = retry_count * 10
                print(f"⏱️ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            
            # 删除可能损坏的文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
    
    print("❌ 下载失败，已重试所有次数")
    return None

if __name__ == "__main__":
    def progress_callback(progress):
        print(f"\r📊 下载进度: {progress:.1f}%", end="", flush=True)
    
    result = download_without_resume("2.1.1", progress_callback)
    if result:
        print(f"\n✅ 下载成功: {result}")
    else:
        print("\n❌ 下载失败")
