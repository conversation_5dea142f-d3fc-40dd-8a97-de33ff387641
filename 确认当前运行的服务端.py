#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
确认当前运行的是哪个服务端文件
"""

import paramiko
import sys

def check_running_server():
    """确认当前运行的服务端"""
    print("🔍 确认当前运行的服务端")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 检查1: systemd服务配置
        print("⚙️ 检查1: systemd服务配置")
        stdin, stdout, stderr = client.exec_command("cat /etc/systemd/system/license-manager.service")
        output = stdout.read().decode('utf-8')
        
        print("   📄 systemd服务配置:")
        for line in output.split('\n'):
            if 'ExecStart' in line:
                print(f"   🚀 启动命令: {line.strip()}")
            elif 'WorkingDirectory' in line:
                print(f"   📁 工作目录: {line.strip()}")
        print()
        
        # 检查2: 当前运行的Python进程
        print("🐍 检查2: 当前运行的Python进程")
        stdin, stdout, stderr = client.exec_command("ps aux | grep python | grep -v grep")
        output = stdout.read().decode('utf-8')
        
        if output.strip():
            print("   📊 Python进程列表:")
            for line in output.split('\n'):
                if line.strip() and 'license' in line:
                    print(f"   🔍 {line}")
        else:
            print("   ❌ 未找到Python进程")
        print()
        
        # 检查3: 端口5000监听进程
        print("🔌 检查3: 端口5000监听进程")
        stdin, stdout, stderr = client.exec_command("netstat -tlnp | grep :5000")
        output = stdout.read().decode('utf-8')
        
        if output.strip():
            print("   📊 端口5000监听详情:")
            for line in output.split('\n'):
                if line.strip():
                    print(f"   🔍 {line}")
                    
            # 提取PID并查看进程详情
            import re
            pid_match = re.search(r'(\d+)/python', output)
            if pid_match:
                pid = pid_match.group(1)
                print(f"\n   🔍 进程{pid}详情:")
                stdin, stdout, stderr = client.exec_command(f"ps -p {pid} -o pid,ppid,cmd --no-headers")
                process_info = stdout.read().decode('utf-8')
                if process_info.strip():
                    print(f"   📋 {process_info.strip()}")
        else:
            print("   ❌ 端口5000未被监听")
        print()
        
        # 检查4: 服务状态
        print("📊 检查4: 服务状态")
        stdin, stdout, stderr = client.exec_command("systemctl status license-manager --no-pager")
        output = stdout.read().decode('utf-8')
        
        print("   📄 服务状态:")
        for line in output.split('\n'):
            if any(keyword in line.lower() for keyword in ['active', 'loaded', 'main pid']):
                print(f"   📊 {line.strip()}")
        print()
        
        # 检查5: 文件对比
        print("📄 检查5: 服务器上的Python文件对比")
        files_to_check = ['license_server.py', 'simple_server.py']
        
        for filename in files_to_check:
            stdin, stdout, stderr = client.exec_command(f"wc -l {config['deploy_path']}/{filename} 2>/dev/null")
            output = stdout.read().decode('utf-8')
            
            if output.strip():
                lines = output.split()[0]
                print(f"   📊 {filename}: {lines} 行")
                
                # 检查是否包含许可证API
                stdin, stdout, stderr = client.exec_command(
                    f"grep -c '/license/' {config['deploy_path']}/{filename} 2>/dev/null"
                )
                api_count = stdout.read().decode('utf-8').strip()
                if api_count and api_count.isdigit():
                    print(f"     🔑 许可证API数量: {api_count}")
            else:
                print(f"   ❌ {filename}: 文件不存在")
        print()
        
        # 检查6: 测试API响应特征
        print("🧪 检查6: 测试API响应特征")
        stdin, stdout, stderr = client.exec_command("curl -s http://localhost:5000/license/list")
        output = stdout.read().decode('utf-8')
        
        if output:
            print("   📊 /license/list 响应:")
            print(f"   📄 {output[:200]}...")
            
            if '"success"' in output and '"licenses"' in output:
                print("   ✅ 这是完整的license_server.py响应")
            elif 'simple' in output.lower() or len(output) < 50:
                print("   ⚠️ 这可能是simple_server.py响应")
            else:
                print("   🔍 响应格式需要进一步分析")
        else:
            print("   ❌ API无响应")
        
        # 关闭连接
        client.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 确认当前运行的是哪个服务端文件")
        print("🔍 检查: systemd配置、进程信息、端口监听、API响应")
        print()
        
        if check_running_server():
            print("✅ 检查完成")
            print()
            print("📋 判断标准:")
            print("• ✅ 如果是license_server.py: 1000+行，8个许可证API，完整JSON响应")
            print("• ❌ 如果是simple_server.py: 100+行，0个许可证API，简单响应")
            print()
            print("💡 根据上面的检查结果，您可以确认当前运行的是哪个文件")
        else:
            print("❌ 检查失败")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
