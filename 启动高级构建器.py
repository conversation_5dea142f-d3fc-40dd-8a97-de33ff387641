#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动高级PyInstaller构建GUI工具
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查必要的依赖"""
    required_packages = ['requests', 'tkinter']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_missing_packages(packages):
    """安装缺失的包"""
    for package in packages:
        if package == 'tkinter':
            print("❌ tkinter 是Python标准库的一部分，如果缺失请重新安装Python")
            continue
        
        print(f"正在安装 {package}...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动亚马逊蓝图工具 - 高级构建器")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查依赖...")
    missing = check_dependencies()
    
    if missing:
        print(f"❌ 缺少依赖: {', '.join(missing)}")
        
        # 询问是否自动安装
        try:
            response = input("是否自动安装缺失的依赖？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                if not install_missing_packages(missing):
                    print("❌ 依赖安装失败，程序退出")
                    input("按回车键退出...")
                    return
            else:
                print("❌ 缺少必要依赖，程序退出")
                input("按回车键退出...")
                return
        except KeyboardInterrupt:
            print("\n❌ 用户取消，程序退出")
            return
    
    print("✅ 依赖检查完成")
    
    # 启动GUI
    try:
        print("🎨 启动图形界面...")
        
        # 检查build_gui_advanced.py是否存在
        if not os.path.exists('build_gui_advanced.py'):
            print("❌ 找不到 build_gui_advanced.py 文件")
            input("按回车键退出...")
            return
        
        # 导入并运行GUI
        from build_gui_advanced import AdvancedBuildGUI
        
        root = tk.Tk()
        app = AdvancedBuildGUI(root)
        
        print("✅ 图形界面启动成功")
        print("📝 使用说明:")
        print("   1. 在'依赖管理'标签页中扫描和管理依赖")
        print("   2. 在'构建配置'标签页中配置构建选项")
        print("   3. 在'构建和测试'标签页中执行构建和测试")
        print("   4. 在'日志查看'标签页中查看详细日志")
        print("=" * 50)
        
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保 build_gui_advanced.py 文件存在且格式正确")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        input("按回车键退出...")
