#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复更新API认证 - 为管理功能添加特殊认证或移除认证要求
"""

import paramiko
import os

def fix_update_api_auth():
    """修复更新API认证问题"""
    print("🔧 修复更新API认证")
    print("=" * 50)
    
    # 服务器配置
    server_config = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0'
    }
    
    try:
        print("🌐 连接服务器...")
        
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(**server_config, timeout=10)
        print("✅ SSH连接成功")
        
        # 备份原文件
        backup_cmd = "cp /opt/license_manager/license_server.py /opt/license_manager/license_server.py.backup"
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        print("💾 原文件已备份")
        
        # 读取当前文件
        print("📄 读取当前license_server.py...")
        stdin, stdout, stderr = ssh.exec_command("cat /opt/license_manager/license_server.py")
        current_content = stdout.read().decode('utf-8')
        
        # 修改认证逻辑 - 添加管理员绕过
        print("🔧 修改认证逻辑...")
        
        # 为/update/check添加管理员绕过
        old_check_auth = '''        # 验证激活码
        if not key or not device_id:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            }), 400

        # 验证激活码有效性
        db = load_database()
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            }), 401'''
        
        new_check_auth = '''        # 管理员绕过认证（用于管理工具）
        if key == "ADMIN_BYPASS" and device_id == "ADMIN-DEVICE-001":
            pass  # 管理员绕过认证
        else:
            # 验证激活码
            if not key or not device_id:
                return jsonify({
                    "success": False,
                    "message": "缺少必要参数"
                }), 400

            # 验证激活码有效性
            db = load_database()
            if key not in db:
                return jsonify({
                    "success": False,
                    "message": "激活码不存在"
                }), 401'''
        
        # 替换认证逻辑
        if old_check_auth in current_content:
            current_content = current_content.replace(old_check_auth, new_check_auth)
            print("✅ /update/check 认证逻辑已修改")
        else:
            print("⚠️ 未找到/update/check认证代码，尝试其他方法...")
        
        # 为/update/download添加相同的绕过
        old_download_auth = '''        # 验证参数
        if not key or not device_id:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            }), 400

        # 验证激活码
        db = load_database()
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            }), 401'''
        
        new_download_auth = '''        # 管理员绕过认证（用于管理工具）
        if key == "ADMIN_BYPASS" and device_id == "ADMIN-DEVICE-001":
            pass  # 管理员绕过认证
        else:
            # 验证参数
            if not key or not device_id:
                return jsonify({
                    "success": False,
                    "message": "缺少必要参数"
                }), 400

            # 验证激活码
            db = load_database()
            if key not in db:
                return jsonify({
                    "success": False,
                    "message": "激活码不存在"
                }), 401'''
        
        if old_download_auth in current_content:
            current_content = current_content.replace(old_download_auth, new_download_auth)
            print("✅ /update/download 认证逻辑已修改")
        else:
            print("⚠️ 未找到/update/download认证代码")
        
        # 上传修改后的文件
        print("📤 上传修改后的文件...")
        
        # 创建临时文件
        temp_file = "/tmp/license_server_fixed.py"
        with open("license_server_fixed.py", "w", encoding="utf-8") as f:
            f.write(current_content)
        
        # 使用SFTP上传
        sftp = ssh.open_sftp()
        sftp.put("license_server_fixed.py", temp_file)
        sftp.close()
        
        # 移动到目标位置
        move_cmd = f"mv {temp_file} /opt/license_manager/license_server.py"
        stdin, stdout, stderr = ssh.exec_command(move_cmd)
        
        # 重启服务
        print("🔄 重启license-manager服务...")
        restart_cmd = "systemctl restart license-manager"
        stdin, stdout, stderr = ssh.exec_command(restart_cmd)
        
        # 等待服务启动
        import time
        time.sleep(3)
        
        # 检查服务状态
        status_cmd = "systemctl is-active license-manager"
        stdin, stdout, stderr = ssh.exec_command(status_cmd)
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("✅ 服务重启成功")
        else:
            print(f"⚠️ 服务状态: {status}")
        
        ssh.close()
        
        # 清理本地临时文件
        if os.path.exists("license_server_fixed.py"):
            os.remove("license_server_fixed.py")
        
        print("\n🎉 更新API认证修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 修复更新API的认证问题")
        print("🔧 方案: 为管理工具添加认证绕过")
        print("🔑 管理员密钥: ADMIN_BYPASS")
        print("📱 管理设备: ADMIN-DEVICE-001")
        print()
        
        if fix_update_api_auth():
            print("\n" + "=" * 50)
            print("✅ 认证修复成功！")
            print("\n📋 下一步操作:")
            print("1. 更新exe文件管理工具使用新的管理员密钥")
            print("2. 重新测试所有更新API")
            print("3. 开始使用exe文件管理功能")
            print("\n💡 新的认证信息:")
            print("   密钥: ADMIN_BYPASS")
            print("   设备ID: ADMIN-DEVICE-001")
        else:
            print("\n❌ 认证修复失败")
            print("请检查服务器连接和权限")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
