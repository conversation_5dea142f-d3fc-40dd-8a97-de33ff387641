#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新对话框图标设置
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys

def create_test_icon():
    """创建一个测试图标文件（如果不存在）"""
    if not os.path.exists("icon.ico"):
        print("⚠️ 未找到icon.ico文件")
        print("💡 请确保icon.ico文件存在于当前目录")
        return False
    return True

def test_main_window_icon():
    """测试主窗口图标设置"""
    print("🧪 测试主窗口图标设置")
    
    root = tk.Tk()
    root.title("亚马逊蓝图工具 - 图标测试")
    root.geometry("400x300")
    
    # 尝试设置图标
    icon_set = False
    icon_paths = []
    
    # 检查各种可能的图标路径
    if getattr(sys, 'frozen', False):
        # 打包环境
        exe_dir = os.path.dirname(sys.executable)
        icon_paths.append(os.path.join(exe_dir, "icon.ico"))
    else:
        # 开发环境
        base_dir = os.path.dirname(os.path.abspath(__file__))
        icon_paths.append(os.path.join(base_dir, "icon.ico"))
    
    # 应用数据目录
    app_data = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
    icon_paths.append(os.path.join(app_data, "icon.ico"))
    
    # 用户目录
    icon_paths.append(os.path.join(os.path.expanduser("~"), "icon.ico"))
    
    # 当前目录
    icon_paths.append("icon.ico")
    
    # 尝试设置图标
    for icon_path in icon_paths:
        if os.path.exists(icon_path):
            try:
                root.iconbitmap(icon_path)
                print(f"✅ 成功设置主窗口图标: {icon_path}")
                icon_set = True
                break
            except Exception as e:
                print(f"❌ 设置图标失败 {icon_path}: {e}")
    
    if not icon_set:
        print("⚠️ 未能设置主窗口图标")
    
    return root, icon_set

def test_update_dialog_icon(parent):
    """测试更新对话框图标"""
    print("\n🧪 测试更新对话框图标")
    
    try:
        from 简化更新器 import SimpleUpdateDialog
        
        # 创建模拟更新信息
        update_info = {
            'version': '2.1.1',
            'has_update': True
        }
        
        # 创建对话框但不显示
        dialog = SimpleUpdateDialog(parent, update_info)
        
        # 手动创建对话框来测试图标
        test_dialog = tk.Toplevel(parent)
        test_dialog.title("更新对话框图标测试")
        test_dialog.geometry("300x150")
        test_dialog.transient(parent)
        
        # 使用相同的图标设置方法
        icon_set = dialog.set_dialog_icon.__func__(dialog)
        
        if icon_set:
            print("✅ 更新对话框图标设置成功")
        else:
            print("⚠️ 更新对话框图标设置失败")
        
        # 添加测试内容
        label = tk.Label(
            test_dialog,
            text="这是更新对话框图标测试\n检查窗口标题栏是否有图标",
            font=("微软雅黑", 10),
            pady=20
        )
        label.pack()
        
        close_button = tk.Button(
            test_dialog,
            text="关闭",
            command=test_dialog.destroy,
            font=("微软雅黑", 10)
        )
        close_button.pack(pady=10)
        
        return test_dialog
        
    except ImportError as e:
        print(f"❌ 导入简化更新器失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 测试更新对话框图标失败: {e}")
        return None

def test_standard_update_dialog(parent):
    """测试标准更新对话框图标"""
    print("\n🧪 测试标准更新对话框图标")
    
    try:
        from auto_updater import UpdateDialog
        
        # 创建模拟更新信息
        update_info = {
            'version': '2.1.1',
            'current_version': '2.1.0',
            'changelog': '测试更新内容',
            'has_update': True
        }
        
        # 创建标准更新对话框
        dialog = UpdateDialog(parent, update_info)
        
        print("✅ 标准更新对话框创建成功")
        return dialog
        
    except ImportError as e:
        print(f"❌ 导入auto_updater失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 测试标准更新对话框失败: {e}")
        return None

def main():
    """主函数"""
    print("🔧 更新对话框图标测试")
    print("=" * 50)
    
    # 检查图标文件
    if not create_test_icon():
        print("\n❌ 请先确保icon.ico文件存在")
        return
    
    # 创建主窗口并设置图标
    root, main_icon_set = test_main_window_icon()
    
    # 创建测试按钮
    title_label = tk.Label(
        root,
        text="更新对话框图标测试",
        font=("微软雅黑", 16, "bold"),
        pady=20
    )
    title_label.pack()
    
    status_label = tk.Label(
        root,
        text=f"主窗口图标: {'✅ 已设置' if main_icon_set else '❌ 未设置'}",
        font=("微软雅黑", 10),
        fg="#27ae60" if main_icon_set else "#e74c3c"
    )
    status_label.pack(pady=10)
    
    def test_simple_dialog():
        """测试简化对话框"""
        test_update_dialog_icon(root)
    
    def test_standard_dialog():
        """测试标准对话框"""
        test_standard_update_dialog(root)
    
    # 测试按钮
    simple_button = tk.Button(
        root,
        text="🧪 测试简化更新对话框图标",
        command=test_simple_dialog,
        font=("微软雅黑", 11),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=8
    )
    simple_button.pack(pady=5)
    
    standard_button = tk.Button(
        root,
        text="🧪 测试标准更新对话框图标",
        command=test_standard_dialog,
        font=("微软雅黑", 11),
        bg="#27ae60",
        fg="white",
        padx=20,
        pady=8
    )
    standard_button.pack(pady=5)
    
    info_label = tk.Label(
        root,
        text="点击按钮测试对话框图标\n检查对话框标题栏是否显示图标",
        font=("微软雅黑", 9),
        fg="#666666",
        justify=tk.CENTER
    )
    info_label.pack(pady=20)
    
    print("\n🚀 图标测试界面已启动")
    print("💡 请点击按钮测试各种对话框的图标显示")
    
    root.mainloop()

if __name__ == "__main__":
    main()
