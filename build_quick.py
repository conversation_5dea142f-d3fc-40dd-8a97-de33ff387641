#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速构建脚本 - 减小exe文件大小
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def cleanup():
    """清理旧文件"""
    print("🧹 清理旧文件...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  ✅ 删除: {dir_name}")

def build():
    """构建exe文件"""
    print("🚀 开始构建...")
    
    # 基础命令
    cmd = [
        sys.executable, "-m", "pyinstaller",
        "--onefile",
        "--windowed", 
        "--clean",
        "--optimize=2",
        "--noupx",
        "--icon=icon.ico",
        "--name=亚马逊蓝图工具"
    ]
    
    # 添加数据文件
    data_files = [
        "采集8.py.encrypted",
        "筛品终极版1.py.encrypted", 
        "历史价格7.py.encrypted",
        "专利1.py.encrypted",
        "icon.ico",
        "auto_updater.py"
    ]
    
    for file in data_files:
        if os.path.exists(file):
            cmd.append(f"--add-data={file};.")
    
    # 排除大型模块
    exclude_modules = [
        "matplotlib",
        "scipy", 
        "jupyter",
        "notebook",
        "test",
        "unittest",
        "doctest"
    ]
    
    for module in exclude_modules:
        cmd.append(f"--exclude-module={module}")
    
    # 核心依赖
    hidden_imports = [
        "cryptography",
        "openpyxl", 
        "pandas",
        "selenium",
        "requests",
        "tkinter",
        "tkinter.ttk"
    ]
    
    for module in hidden_imports:
        cmd.append(f"--hidden-import={module}")
    
    # 主程序
    cmd.append("license_client.py")
    
    print("  📝 执行PyInstaller...")
    print("  ⏳ 这可能需要几分钟，请耐心等待...")
    
    try:
        # 设置环境变量避免编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        result = subprocess.run(
            cmd, 
            check=True, 
            capture_output=True, 
            text=True,
            env=env,
            errors='replace'
        )
        
        print("  ✅ 构建完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"  ❌ 构建失败: {e}")
        if e.stdout:
            print("标准输出:")
            print(e.stdout[-1000:])  # 只显示最后1000字符
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-1000:])  # 只显示最后1000字符
        return False

def check_result():
    """检查构建结果"""
    print("📊 检查构建结果...")
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("  ❌ dist目录不存在")
        return False
    
    exe_files = list(dist_dir.glob("*.exe"))
    if not exe_files:
        print("  ❌ 未找到exe文件")
        return False
    
    exe_file = exe_files[0]
    file_size = exe_file.stat().st_size
    size_mb = file_size / (1024 * 1024)
    
    print(f"  ✅ 生成文件: {exe_file.name}")
    print(f"  📏 文件大小: {size_mb:.2f} MB")
    
    if size_mb < 40:
        print("  🎉 文件大小优化效果很好!")
    elif size_mb < 60:
        print("  👍 文件大小在合理范围内")
    else:
        print("  ⚠️  文件较大，可能需要进一步优化")
    
    return True

def create_portable():
    """创建便携版"""
    print("📦 创建便携版...")
    
    try:
        dist_dir = Path("dist")
        exe_files = list(dist_dir.glob("*.exe"))
        
        if not exe_files:
            print("  ❌ 未找到exe文件")
            return False
        
        exe_file = exe_files[0]
        portable_dir = Path("portable")
        portable_dir.mkdir(exist_ok=True)
        
        # 复制exe文件
        shutil.copy2(exe_file, portable_dir / exe_file.name)
        
        # 创建配置文件
        config = """[Settings]
portable_mode=true
data_dir=./data
cache_dir=./cache

[Update]
auto_check=true
"""
        
        with open(portable_dir / "portable.ini", 'w', encoding='utf-8') as f:
            f.write(config)
        
        # 创建目录
        for dir_name in ['data', 'cache', 'logs']:
            (portable_dir / dir_name).mkdir(exist_ok=True)
        
        print(f"  ✅ 便携版创建完成: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"  ❌ 创建便携版失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️  亚马逊蓝图工具 - 快速构建器")
    print("=" * 40)
    
    # 检查必要文件
    required_files = ["license_client.py", "icon.ico"]
    missing = [f for f in required_files if not os.path.exists(f)]
    
    if missing:
        print(f"❌ 缺少必要文件: {missing}")
        return 1
    
    # 清理旧文件
    cleanup()
    
    # 构建
    if not build():
        print("\n❌ 构建失败!")
        print("\n🔧 故障排除建议:")
        print("  1. 确保安装PyInstaller: pip install pyinstaller")
        print("  2. 检查Python环境和依赖包")
        print("  3. 尝试重新运行脚本")
        return 1
    
    # 检查结果
    if not check_result():
        print("❌ 构建结果检查失败!")
        return 1
    
    # 创建便携版
    create_portable()
    
    print("\n🎉 构建完成!")
    print("\n📁 输出目录:")
    print("  • dist/ - 标准版本")
    print("  • portable/ - 便携版本")
    
    print("\n🎯 下一步:")
    print("  1. 测试生成的exe文件")
    print("  2. 使用update_server_manager.py管理更新")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
