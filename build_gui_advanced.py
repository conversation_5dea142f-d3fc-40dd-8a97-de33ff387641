#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级PyInstaller构建GUI工具
功能：依赖管理、自动修复、版本选择、exe测试
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import subprocess
import sys
import os
import json
import requests
import importlib.util
try:
    from importlib.metadata import distributions
except ImportError:
    # Python < 3.8 fallback
    from importlib_metadata import distributions
from pathlib import Path
import time
from datetime import datetime

class AdvancedBuildGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("亚马逊蓝图工具 - 高级构建器")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置图标
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
        
        # 数据存储
        self.dependencies = {}
        self.build_config = {
            'output_name': '亚马逊蓝图工具',
            'console': False,
            'onefile': True,
            'optimize': True,
            'strip': False,  # 在Windows上禁用strip，避免找不到strip工具的错误
            'upx': False
        }
        
        # 创建界面
        self.create_widgets()
        self.load_dependencies()
        
    def create_widgets(self):
        """创建主界面"""
        # 创建样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔧 高级PyInstaller构建工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 依赖管理标签页
        self.create_dependencies_tab()
        
        # 构建配置标签页
        self.create_build_config_tab()
        
        # 构建和测试标签页
        self.create_build_test_tab()
        
        # 日志查看标签页
        self.create_log_tab()
        
        # 状态栏
        self.create_status_bar(main_frame)
        
    def create_dependencies_tab(self):
        """创建依赖管理标签页"""
        deps_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(deps_frame, text="📦 依赖管理")
        
        # 配置网格
        deps_frame.columnconfigure(1, weight=1)
        deps_frame.rowconfigure(2, weight=1)
        
        # 工具栏
        toolbar_frame = ttk.Frame(deps_frame)
        toolbar_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(toolbar_frame, text="🔍 扫描依赖", command=self.scan_dependencies).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="➕ 添加依赖", command=self.add_dependency).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🔧 修复依赖", command=self.fix_dependencies).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📥 导入requirements.txt", command=self.import_requirements).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📤 导出requirements.txt", command=self.export_requirements).pack(side=tk.LEFT)
        
        # 搜索框
        search_frame = ttk.Frame(deps_frame)
        search_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(search_frame, text="搜索依赖:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_dependencies)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(search_frame, text="🔍 查询PyPI版本", command=self.query_pypi_versions).pack(side=tk.LEFT)
        
        # 依赖列表
        list_frame = ttk.Frame(deps_frame)
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ('package', 'version', 'status', 'description')
        self.deps_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 配置列
        self.deps_tree.heading('package', text='包名')
        self.deps_tree.heading('version', text='版本')
        self.deps_tree.heading('status', text='状态')
        self.deps_tree.heading('description', text='描述')
        
        self.deps_tree.column('package', width=150)
        self.deps_tree.column('version', width=100)
        self.deps_tree.column('status', width=80)
        self.deps_tree.column('description', width=300)
        
        # 滚动条
        deps_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.deps_tree.yview)
        self.deps_tree.configure(yscrollcommand=deps_scrollbar.set)
        
        self.deps_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        deps_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 右键菜单
        self.create_context_menu()
        
        # 详情面板
        details_frame = ttk.LabelFrame(deps_frame, text="依赖详情", padding="5")
        details_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        details_frame.columnconfigure(1, weight=1)
        
        self.detail_labels = {}
        detail_fields = [
            ('package_name', '包名:'),
            ('current_version', '当前版本:'),
            ('latest_version', '最新版本:'),
            ('install_location', '安装位置:'),
            ('dependencies_count', '依赖数量:')
        ]
        
        for i, (key, label) in enumerate(detail_fields):
            ttk.Label(details_frame, text=label).grid(row=i, column=0, sticky=tk.W, padx=(0, 10))
            self.detail_labels[key] = ttk.Label(details_frame, text="未选择")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W)
        
        # 绑定选择事件
        self.deps_tree.bind('<<TreeviewSelect>>', self.on_dependency_select)
        
    def create_build_config_tab(self):
        """创建构建配置标签页"""
        config_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(config_frame, text="⚙️ 构建配置")
        
        # 基本配置
        basic_frame = ttk.LabelFrame(config_frame, text="基本配置", padding="10")
        basic_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        basic_frame.columnconfigure(1, weight=1)
        
        # 输出名称
        ttk.Label(basic_frame, text="输出名称:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.output_name_var = tk.StringVar(value=self.build_config['output_name'])
        ttk.Entry(basic_frame, textvariable=self.output_name_var, width=40).grid(row=0, column=1, sticky=tk.W)
        
        # 主程序文件
        ttk.Label(basic_frame, text="主程序文件:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        main_file_frame = ttk.Frame(basic_frame)
        main_file_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(5, 0))
        main_file_frame.columnconfigure(0, weight=1)
        
        self.main_file_var = tk.StringVar(value="license_client.py")
        ttk.Entry(main_file_frame, textvariable=self.main_file_var).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(main_file_frame, text="浏览", command=self.browse_main_file).grid(row=0, column=1)
        
        # 图标文件
        ttk.Label(basic_frame, text="图标文件:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        icon_file_frame = ttk.Frame(basic_frame)
        icon_file_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(5, 0))
        icon_file_frame.columnconfigure(0, weight=1)
        
        self.icon_file_var = tk.StringVar(value="icon.ico")
        ttk.Entry(icon_file_frame, textvariable=self.icon_file_var).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(icon_file_frame, text="浏览", command=self.browse_icon_file).grid(row=0, column=1)
        
        # 构建选项
        options_frame = ttk.LabelFrame(config_frame, text="构建选项", padding="10")
        options_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.console_var = tk.BooleanVar(value=self.build_config['console'])
        ttk.Checkbutton(options_frame, text="显示控制台窗口", variable=self.console_var).grid(row=0, column=0, sticky=tk.W)
        
        self.onefile_var = tk.BooleanVar(value=self.build_config['onefile'])
        ttk.Checkbutton(options_frame, text="单文件模式", variable=self.onefile_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        self.optimize_var = tk.BooleanVar(value=self.build_config['optimize'])
        ttk.Checkbutton(options_frame, text="代码优化", variable=self.optimize_var).grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        self.strip_var = tk.BooleanVar(value=self.build_config['strip'])
        ttk.Checkbutton(options_frame, text="去除调试信息", variable=self.strip_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0), pady=(5, 0))
        
        self.upx_var = tk.BooleanVar(value=self.build_config['upx'])
        ttk.Checkbutton(options_frame, text="UPX压缩", variable=self.upx_var).grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        
        # 高级选项
        advanced_frame = ttk.LabelFrame(config_frame, text="高级选项", padding="10")
        advanced_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        advanced_frame.columnconfigure(0, weight=1)
        advanced_frame.rowconfigure(1, weight=1)
        
        ttk.Label(advanced_frame, text="额外的PyInstaller参数:").grid(row=0, column=0, sticky=tk.W)
        
        self.extra_args_text = scrolledtext.ScrolledText(advanced_frame, height=8, width=60)
        self.extra_args_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        
        # 默认额外参数
        default_args = """# 示例额外参数（每行一个）
--collect-data=fake_useragent
--collect-submodules=openpyxl
--exclude-module=matplotlib
--exclude-module=scipy"""
        self.extra_args_text.insert('1.0', default_args)
        
    def create_build_test_tab(self):
        """创建构建和测试标签页"""
        build_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(build_frame, text="🚀 构建和测试")
        
        # 构建控制
        control_frame = ttk.Frame(build_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="🚀 开始构建", command=self.start_build, style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🧪 测试exe依赖", command=self.test_exe_dependencies).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="📁 打开输出目录", command=self.open_output_dir).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🗑️ 清理构建文件", command=self.clean_build_files).pack(side=tk.LEFT)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(build_frame, text="构建进度", padding="10")
        progress_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="准备就绪")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 构建结果
        result_frame = ttk.LabelFrame(build_frame, text="构建结果", padding="10")
        result_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 结果显示区域
        self.result_text = scrolledtext.ScrolledText(result_frame, height=15, state='normal')
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 设置为只读但可复制
        self.result_text.bind("<Key>", lambda e: "break" if e.keysym not in ['Control_L', 'Control_R', 'c', 'C', 'a', 'A'] else None)

        # 创建右键菜单
        self.create_text_context_menu(self.result_text)
        
        # 配置网格权重
        build_frame.columnconfigure(0, weight=1)
        build_frame.rowconfigure(2, weight=1)
        
    def create_log_tab(self):
        """创建日志查看标签页"""
        log_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(log_frame, text="📋 日志查看")
        
        # 日志控制
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(log_control_frame, text="🗑️ 清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_control_frame, text="💾 保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(0, 10))
        
        # 日志级别选择
        ttk.Label(log_control_frame, text="日志级别:").pack(side=tk.LEFT, padx=(20, 5))
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(log_control_frame, textvariable=self.log_level_var, 
                                      values=["DEBUG", "INFO", "WARNING", "ERROR"], width=10, state="readonly")
        log_level_combo.pack(side=tk.LEFT)
        
        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=25, state='normal')
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 设置为只读但可复制
        self.log_text.bind("<Key>", lambda e: "break" if e.keysym not in ['Control_L', 'Control_R', 'c', 'C', 'a', 'A'] else None)

        # 创建右键菜单
        self.create_text_context_menu(self.log_text)
        
        # 配置网格权重
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)
        
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=0, column=0, sticky=tk.W)
        
        # 时间显示
        self.time_var = tk.StringVar()
        ttk.Label(status_frame, textvariable=self.time_var).grid(row=0, column=2, sticky=tk.E)
        
        # 更新时间
        self.update_time()
        
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="📋 查看详情", command=self.show_dependency_details)
        self.context_menu.add_command(label="🔄 更新版本", command=self.update_dependency)
        self.context_menu.add_command(label="🗑️ 删除依赖", command=self.remove_dependency)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔍 查询PyPI", command=self.query_dependency_pypi)
        
        self.deps_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.deps_tree.selection()[0] if self.deps_tree.selection() else None
        if item:
            self.context_menu.post(event.x_root, event.y_root)

    def create_text_context_menu(self, text_widget):
        """为文本框创建右键菜单"""
        text_menu = tk.Menu(self.root, tearoff=0)
        text_menu.add_command(label="📋 复制", command=lambda: self.copy_text(text_widget))
        text_menu.add_command(label="📄 全选", command=lambda: self.select_all_text(text_widget))
        text_menu.add_separator()
        text_menu.add_command(label="💾 保存到文件", command=lambda: self.save_text_to_file(text_widget))
        text_menu.add_command(label="🗑️ 清空", command=lambda: self.clear_text(text_widget))

        def show_text_menu(event):
            text_menu.post(event.x_root, event.y_root)

        text_widget.bind("<Button-3>", show_text_menu)

    def copy_text(self, text_widget):
        """复制选中的文本"""
        try:
            selected_text = text_widget.selection_get()
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
            self.log_message("✅ 文本已复制到剪贴板")
        except tk.TclError:
            # 如果没有选中文本，复制全部
            all_text = text_widget.get('1.0', tk.END)
            self.root.clipboard_clear()
            self.root.clipboard_append(all_text)
            self.log_message("✅ 全部文本已复制到剪贴板")

    def select_all_text(self, text_widget):
        """全选文本"""
        text_widget.tag_add(tk.SEL, "1.0", tk.END)
        text_widget.mark_set(tk.INSERT, "1.0")
        text_widget.see(tk.INSERT)

    def save_text_to_file(self, text_widget):
        """保存文本到文件"""
        filename = filedialog.asksaveasfilename(
            title="保存文本",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("日志文件", "*.log"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(text_widget.get('1.0', tk.END))
                self.log_message(f"✅ 文本已保存到: {filename}")
            except Exception as e:
                self.log_message(f"❌ 保存失败: {e}", "ERROR")

    def clear_text(self, text_widget):
        """清空文本"""
        if text_widget == self.log_text:
            self.clear_log()
        else:
            text_widget.delete('1.0', tk.END)

    def _check_strip_available(self):
        """检查系统是否有strip工具"""
        try:
            # 尝试运行strip --version
            result = subprocess.run(['strip', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self.update_time)

    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 更新状态栏
        self.status_var.set(message)

    def load_dependencies(self):
        """加载当前环境的依赖"""
        self.log_message("正在扫描依赖...")
        threading.Thread(target=self._load_dependencies_thread, daemon=True).start()

    def _load_dependencies_thread(self):
        """在后台线程中加载依赖"""
        try:
            # 获取已安装的包 - 使用现代API替代pkg_resources
            installed_packages = {}
            try:
                for dist in distributions():
                    installed_packages[dist.metadata['Name']] = dist.version
            except Exception:
                # 如果新API失败，尝试传统方法
                try:
                    import pkg_resources
                    installed_packages = {pkg.project_name: pkg.version for pkg in pkg_resources.working_set}
                except ImportError:
                    installed_packages = {}

            # 关键依赖列表
            critical_deps = [
                'fake-useragent', 'openpyxl', 'pandas', 'requests', 'selenium',
                'beautifulsoup4', 'lxml', 'cryptography', 'pyinstaller', 'pillow'
            ]

            self.dependencies = {}

            for pkg_name in critical_deps:
                # 检查包是否安装
                installed_version = None
                for installed_name, version in installed_packages.items():
                    if installed_name.lower().replace('-', '_') == pkg_name.lower().replace('-', '_'):
                        installed_version = version
                        break

                status = "已安装" if installed_version else "未安装"

                self.dependencies[pkg_name] = {
                    'version': installed_version or "未知",
                    'status': status,
                    'description': self.get_package_description(pkg_name),
                    'latest_version': None
                }

            # 更新UI
            self.root.after(0, self.update_dependencies_display)
            self.root.after(0, lambda: self.log_message("依赖扫描完成"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"依赖扫描失败: {e}", "ERROR"))

    def get_package_description(self, package_name):
        """获取包的描述"""
        descriptions = {
            'fake-useragent': '随机用户代理生成器',
            'openpyxl': 'Excel文件读写库',
            'pandas': '数据分析和处理库',
            'requests': 'HTTP请求库',
            'selenium': 'Web自动化测试框架',
            'beautifulsoup4': 'HTML/XML解析库',
            'lxml': '高性能XML/HTML解析器',
            'cryptography': '加密和安全库',
            'pyinstaller': 'Python打包工具',
            'pillow': '图像处理库'
        }
        return descriptions.get(package_name, '未知描述')

    def update_dependencies_display(self):
        """更新依赖显示"""
        # 清空现有项目
        for item in self.deps_tree.get_children():
            self.deps_tree.delete(item)

        # 添加依赖项目
        for pkg_name, info in self.dependencies.items():
            status_color = "green" if info['status'] == "已安装" else "red"
            self.deps_tree.insert('', 'end', values=(
                pkg_name,
                info['version'],
                info['status'],
                info['description']
            ), tags=(status_color,))

        # 配置标签颜色
        self.deps_tree.tag_configure("green", foreground="green")
        self.deps_tree.tag_configure("red", foreground="red")

    def scan_dependencies(self):
        """扫描依赖"""
        self.load_dependencies()

    def add_dependency(self):
        """添加依赖对话框"""
        dialog = AddDependencyDialog(self.root, self)
        self.root.wait_window(dialog.dialog)

    def fix_dependencies(self):
        """修复依赖"""
        self.log_message("开始修复依赖...")
        threading.Thread(target=self._fix_dependencies_thread, daemon=True).start()

    def _fix_dependencies_thread(self):
        """在后台线程中修复依赖"""
        try:
            missing_deps = [name for name, info in self.dependencies.items()
                          if info['status'] == "未安装"]

            if not missing_deps:
                self.root.after(0, lambda: self.log_message("所有依赖都已安装"))
                return

            for dep in missing_deps:
                self.root.after(0, lambda d=dep: self.log_message(f"正在安装 {d}..."))

                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', dep
                ], capture_output=True, text=True, encoding='utf-8', errors='ignore')

                if result.returncode == 0:
                    self.root.after(0, lambda d=dep: self.log_message(f"✅ {d} 安装成功"))
                else:
                    self.root.after(0, lambda d=dep, e=result.stderr:
                                  self.log_message(f"❌ {d} 安装失败: {e}", "ERROR"))

            # 重新扫描依赖
            self.root.after(0, self.load_dependencies)

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"修复依赖失败: {e}", "ERROR"))

    def query_pypi_versions(self):
        """查询PyPI版本"""
        selected_item = self.deps_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个依赖包")
            return

        package_name = self.deps_tree.item(selected_item[0])['values'][0]
        self.log_message(f"正在查询 {package_name} 的版本信息...")

        threading.Thread(target=self._query_pypi_thread, args=(package_name,), daemon=True).start()

    def _query_pypi_thread(self, package_name):
        """在后台线程中查询PyPI"""
        try:
            response = requests.get(f"https://pypi.org/pypi/{package_name}/json", timeout=10)
            if response.status_code == 200:
                data = response.json()
                latest_version = data['info']['version']
                releases = list(data['releases'].keys())

                # 更新依赖信息
                if package_name in self.dependencies:
                    self.dependencies[package_name]['latest_version'] = latest_version

                self.root.after(0, lambda: self.show_version_dialog(package_name, releases, latest_version))
                self.root.after(0, lambda: self.log_message(f"✅ {package_name} 版本查询完成"))
            else:
                self.root.after(0, lambda: self.log_message(f"❌ 查询 {package_name} 失败", "ERROR"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"查询PyPI失败: {e}", "ERROR"))

    def show_version_dialog(self, package_name, versions, latest_version):
        """显示版本选择对话框"""
        dialog = VersionSelectDialog(self.root, package_name, versions, latest_version, self)
        self.root.wait_window(dialog.dialog)

    def on_dependency_select(self, event):
        """依赖选择事件"""
        selected_item = self.deps_tree.selection()
        if selected_item:
            values = self.deps_tree.item(selected_item[0])['values']
            package_name = values[0]

            if package_name in self.dependencies:
                info = self.dependencies[package_name]
                self.detail_labels['package_name'].config(text=package_name)
                self.detail_labels['current_version'].config(text=info['version'])
                self.detail_labels['latest_version'].config(text=info.get('latest_version', '未知'))

                # 获取安装位置
                try:
                    spec = importlib.util.find_spec(package_name.replace('-', '_'))
                    location = spec.origin if spec and spec.origin else '未知'
                    self.detail_labels['install_location'].config(text=location)
                except:
                    self.detail_labels['install_location'].config(text='未知')

                self.detail_labels['dependencies_count'].config(text='查询中...')

    def browse_main_file(self):
        """浏览主程序文件"""
        filename = filedialog.askopenfilename(
            title="选择主程序文件",
            filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")]
        )
        if filename:
            self.main_file_var.set(filename)

    def browse_icon_file(self):
        """浏览图标文件"""
        filename = filedialog.askopenfilename(
            title="选择图标文件",
            filetypes=[("图标文件", "*.ico"), ("所有文件", "*.*")]
        )
        if filename:
            self.icon_file_var.set(filename)

    def start_build(self):
        """开始构建"""
        if not os.path.exists(self.main_file_var.get()):
            messagebox.showerror("错误", f"主程序文件不存在: {self.main_file_var.get()}")
            return

        self.log_message("开始构建...")
        self.progress_bar.start()
        self.progress_var.set("正在构建...")

        threading.Thread(target=self._build_thread, daemon=True).start()

    def _build_thread(self):
        """在后台线程中构建"""
        try:
            # 构建PyInstaller命令
            cmd = [sys.executable, '-m', 'PyInstaller']

            # 基本选项
            if self.onefile_var.get():
                cmd.append('--onefile')
            else:
                cmd.append('--onedir')

            if not self.console_var.get():
                cmd.append('--windowed')

            if self.optimize_var.get():
                cmd.extend(['--optimize', '2'])

            if self.strip_var.get():
                # 检查系统是否有strip工具
                if self._check_strip_available():
                    cmd.append('--strip')
                else:
                    self.log_message("⚠️ 系统未找到strip工具，跳过去除调试信息", "WARNING")

            if not self.upx_var.get():
                cmd.append('--noupx')

            # 输出名称
            cmd.extend(['--name', self.output_name_var.get()])

            # 图标
            if os.path.exists(self.icon_file_var.get()):
                cmd.extend(['--icon', self.icon_file_var.get()])

            # 清理和确认
            cmd.extend(['--clean', '--noconfirm'])

            # 添加隐藏导入
            hidden_imports = [
                'fake_useragent', 'fake_useragent.fake', 'fake_useragent.utils',
                'openpyxl', 'openpyxl.workbook.workbook', 'openpyxl.worksheet.worksheet',
                'pandas', 'pandas.io.excel._openpyxl', 'requests', 'selenium',
                'bs4', 'lxml', 'cryptography', 'PIL'
            ]

            for imp in hidden_imports:
                cmd.extend(['--hidden-import', imp])

            # 数据收集
            cmd.extend(['--collect-data', 'fake_useragent'])
            cmd.extend(['--collect-data', 'openpyxl'])
            cmd.extend(['--collect-submodules', 'fake_useragent'])
            cmd.extend(['--collect-submodules', 'openpyxl'])

            # 排除模块
            exclude_modules = [
                'matplotlib', 'scipy', 'numpy.distutils', 'jupyter',
                'notebook', 'IPython', 'pytest', 'unittest'
            ]

            for mod in exclude_modules:
                cmd.extend(['--exclude-module', mod])

            # 额外参数
            extra_args = self.extra_args_text.get('1.0', tk.END).strip()
            if extra_args:
                for line in extra_args.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if line.startswith('--'):
                            if '=' in line:
                                key, value = line.split('=', 1)
                                cmd.extend([key, value])
                            else:
                                cmd.append(line)
                        else:
                            cmd.append(line)

            # 主程序文件
            cmd.append(self.main_file_var.get())

            self.root.after(0, lambda: self.log_message(f"执行命令: {' '.join(cmd)}"))

            # 执行构建
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                text=True, universal_newlines=True, encoding='utf-8', errors='ignore'
            )

            # 实时读取输出
            for line in process.stdout:
                line = line.strip()
                if line:
                    self.root.after(0, lambda l=line: self.update_build_output(l))

            process.wait()

            if process.returncode == 0:
                self.root.after(0, lambda: self.build_success())
            else:
                self.root.after(0, lambda: self.build_failed())

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"构建失败: {e}", "ERROR"))
            self.root.after(0, lambda: self.build_failed())

    def update_build_output(self, line):
        """更新构建输出"""
        self.result_text.insert(tk.END, line + '\n')
        self.result_text.see(tk.END)

        # 更新进度信息
        if "Analyzing" in line:
            self.progress_var.set("正在分析依赖...")
        elif "Building" in line:
            self.progress_var.set("正在构建...")
        elif "Collecting" in line:
            self.progress_var.set("正在收集文件...")

    def build_success(self):
        """构建成功"""
        self.progress_bar.stop()
        self.progress_var.set("构建完成")
        self.log_message("✅ 构建成功完成")

        # 检查输出文件
        dist_dir = Path("dist")
        exe_files = list(dist_dir.glob("*.exe"))

        if exe_files:
            exe_file = exe_files[0]
            file_size = exe_file.stat().st_size / (1024 * 1024)
            self.log_message(f"生成文件: {exe_file.name} ({file_size:.1f}MB)")

            # 询问是否测试
            if messagebox.askyesno("构建完成", "构建成功！是否立即测试exe依赖？"):
                self.test_exe_dependencies()
        else:
            self.log_message("警告: 未找到生成的exe文件", "WARNING")

    def build_failed(self):
        """构建失败"""
        self.progress_bar.stop()
        self.progress_var.set("构建失败")
        self.log_message("❌ 构建失败", "ERROR")

    def test_exe_dependencies(self):
        """测试exe依赖"""
        dist_dir = Path("dist")
        exe_files = list(dist_dir.glob("*.exe"))

        if not exe_files:
            messagebox.showwarning("警告", "未找到exe文件，请先构建")
            return

        self.log_message("开始测试exe依赖...")

        # 显示测试选项对话框
        self.show_test_options_dialog(exe_files[0])

    def show_test_options_dialog(self, exe_file):
        """显示测试选项对话框"""
        dialog = ExeTestDialog(self.root, exe_file, self)
        self.root.wait_window(dialog.dialog)

    def _test_exe_thread(self, exe_file, test_options):
        """在后台线程中测试exe"""
        try:
            self.root.after(0, lambda: self.log_message(f"正在测试 {exe_file.name}..."))

            test_results = []

            # 1. 基础启动测试
            if test_options.get('basic_test', True):
                result = self._test_exe_basic_startup(exe_file)
                test_results.append(("基础启动测试", result))

            # 2. 依赖导入测试
            if test_options.get('import_test', True):
                result = self._test_exe_imports(exe_file)
                test_results.append(("依赖导入测试", result))

            # 3. 功能性测试
            if test_options.get('functional_test', True):
                result = self._test_exe_functionality(exe_file)
                test_results.append(("功能性测试", result))

            # 4. 文件大小和性能测试
            if test_options.get('performance_test', True):
                result = self._test_exe_performance(exe_file)
                test_results.append(("性能测试", result))

            # 显示综合结果
            self.root.after(0, lambda: self.show_comprehensive_test_results(test_results))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"测试失败: {e}", "ERROR"))

    def _test_exe_basic_startup(self, exe_file):
        """测试exe基础启动"""
        try:
            # 尝试启动exe并快速关闭
            process = subprocess.Popen([str(exe_file)],
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     creationflags=subprocess.CREATE_NO_WINDOW,
                                     encoding='utf-8', errors='ignore')

            # 等待短时间看是否能正常启动
            try:
                stdout, stderr = process.communicate(timeout=5)
                return {
                    'success': process.returncode == 0 or process.returncode is None,
                    'message': '启动成功' if process.returncode == 0 else f'启动异常: {stderr}',
                    'details': f'返回码: {process.returncode}'
                }
            except subprocess.TimeoutExpired:
                process.terminate()
                return {
                    'success': True,
                    'message': '启动成功（程序正在运行）',
                    'details': '程序启动后持续运行，这是正常的GUI程序行为'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'启动失败: {str(e)}',
                'details': '可能缺少必要的依赖或系统库'
            }

    def _test_exe_imports(self, exe_file):
        """测试exe依赖导入"""
        try:
            # 创建测试脚本，通过exe运行
            test_script = self._create_import_test_script()
            # 确保使用正确的路径，避免重复的dist目录
            test_script_path = Path(exe_file).parent / "import_test.py"

            # 确保目录存在
            test_script_path.parent.mkdir(parents=True, exist_ok=True)

            with open(test_script_path, 'w', encoding='utf-8') as f:
                f.write(test_script)

            # 使用exe运行测试脚本（如果支持）
            # 注意：这种方法只适用于某些类型的exe
            try:
                result = subprocess.run([
                    str(exe_file), str(test_script_path)
                ], capture_output=True, text=True, timeout=30, cwd=str(test_script_path.parent), encoding='utf-8', errors='ignore')

                if result.returncode == 0:
                    return {
                        'success': True,
                        'message': '依赖导入测试通过',
                        'details': result.stdout
                    }
                else:
                    return {
                        'success': False,
                        'message': '依赖导入测试失败',
                        'details': result.stderr
                    }
            except subprocess.TimeoutExpired:
                return {
                    'success': False,
                    'message': '依赖测试超时',
                    'details': '测试脚本运行超过30秒'
                }
            finally:
                # 清理测试文件
                if test_script_path.exists():
                    test_script_path.unlink()

        except Exception as e:
            return {
                'success': False,
                'message': f'依赖测试异常: {str(e)}',
                'details': '无法创建或运行测试脚本'
            }

    def _create_import_test_script(self):
        """创建导入测试脚本"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""exe依赖导入测试脚本"""

import sys
import json

def test_imports():
    """测试关键模块导入"""
    test_modules = [
        'fake_useragent', 'openpyxl', 'pandas', 'requests',
        'selenium', 'bs4', 'lxml', 'cryptography', 'PIL'
    ]

    results = {}

    for module in test_modules:
        try:
            __import__(module)
            results[module] = {'success': True, 'error': None}
        except Exception as e:
            results[module] = {'success': False, 'error': str(e)}

    return results

def main():
    try:
        results = test_imports()
        print(json.dumps(results, indent=2, ensure_ascii=False))

        # 计算成功率
        total = len(results)
        passed = sum(1 for r in results.values() if r['success'])

        if passed == total:
            sys.exit(0)  # 全部成功
        else:
            sys.exit(1)  # 部分失败

    except Exception as e:
        print(f"测试脚本异常: {e}")
        sys.exit(2)  # 脚本异常

if __name__ == "__main__":
    main()
'''

    def _test_exe_functionality(self, exe_file):
        """测试exe功能性"""
        try:
            # 创建功能测试脚本
            test_script = self._create_functionality_test_script()
            # 确保使用正确的路径，避免重复的dist目录
            test_script_path = Path(exe_file).parent / "func_test.py"

            # 确保目录存在
            test_script_path.parent.mkdir(parents=True, exist_ok=True)

            with open(test_script_path, 'w', encoding='utf-8') as f:
                f.write(test_script)

            # 运行功能测试
            result = subprocess.run([
                sys.executable, str(test_script_path)
            ], capture_output=True, text=True, timeout=60, cwd=str(test_script_path.parent), encoding='utf-8', errors='ignore')

            try:
                # 解析JSON结果
                if result.stdout.strip():
                    test_data = json.loads(result.stdout)
                    passed = sum(1 for r in test_data.values() if r.get('success', False))
                    total = len(test_data)

                    return {
                        'success': passed == total,
                        'message': f'功能测试: {passed}/{total} 通过',
                        'details': json.dumps(test_data, indent=2, ensure_ascii=False)
                    }
                else:
                    return {
                        'success': False,
                        'message': '功能测试无输出',
                        'details': result.stderr
                    }
            except json.JSONDecodeError:
                return {
                    'success': False,
                    'message': '功能测试结果解析失败',
                    'details': f'stdout: {result.stdout}\nstderr: {result.stderr}'
                }
            finally:
                # 清理测试文件
                if test_script_path.exists():
                    test_script_path.unlink()

        except Exception as e:
            return {
                'success': False,
                'message': f'功能测试异常: {str(e)}',
                'details': '无法执行功能测试'
            }

    def _create_functionality_test_script(self):
        """创建功能测试脚本"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""exe功能性测试脚本"""

import sys
import json
import tempfile
import os

def test_fake_useragent():
    """测试fake_useragent功能"""
    try:
        from fake_useragent import UserAgent
        ua = UserAgent()
        user_agent = ua.random
        return {'success': True, 'details': f'生成用户代理: {user_agent[:50]}...'}
    except Exception as e:
        return {'success': False, 'details': str(e)}

def test_openpyxl():
    """测试openpyxl功能"""
    try:
        from openpyxl import Workbook
        wb = Workbook()
        ws = wb.active
        ws['A1'] = 'Test'

        # 保存到临时文件
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            wb.save(tmp.name)
            file_size = os.path.getsize(tmp.name)
            os.unlink(tmp.name)

        return {'success': True, 'details': f'Excel文件创建成功，大小: {file_size} bytes'}
    except Exception as e:
        return {'success': False, 'details': str(e)}

def test_requests():
    """测试requests功能"""
    try:
        import requests
        # 测试简单的HTTP请求
        response = requests.get('https://httpbin.org/get', timeout=10)
        return {'success': response.status_code == 200, 'details': f'HTTP状态码: {response.status_code}'}
    except Exception as e:
        return {'success': False, 'details': str(e)}

def test_cryptography():
    """测试cryptography功能"""
    try:
        from cryptography.fernet import Fernet
        key = Fernet.generate_key()
        f = Fernet(key)

        # 测试加密解密
        message = b"test message"
        encrypted = f.encrypt(message)
        decrypted = f.decrypt(encrypted)

        success = decrypted == message
        return {'success': success, 'details': f'加密解密测试: {"成功" if success else "失败"}'}
    except Exception as e:
        return {'success': False, 'details': str(e)}

def main():
    """主测试函数"""
    tests = {
        'fake_useragent': test_fake_useragent,
        'openpyxl': test_openpyxl,
        'requests': test_requests,
        'cryptography': test_cryptography
    }

    results = {}

    for test_name, test_func in tests.items():
        try:
            results[test_name] = test_func()
        except Exception as e:
            results[test_name] = {'success': False, 'details': f'测试异常: {str(e)}'}

    print(json.dumps(results, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
'''

    def _test_exe_performance(self, exe_file):
        """测试exe性能"""
        try:
            file_size = exe_file.stat().st_size
            file_size_mb = file_size / (1024 * 1024)

            # 性能评估
            performance_score = 100
            issues = []

            if file_size_mb > 100:
                performance_score -= 20
                issues.append(f"文件较大: {file_size_mb:.1f}MB")

            if file_size_mb > 200:
                performance_score -= 30
                issues.append("文件过大，可能影响启动速度")

            # 测试启动时间
            import time
            start_time = time.time()

            try:
                process = subprocess.Popen([str(exe_file)],
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE,
                                         creationflags=subprocess.CREATE_NO_WINDOW,
                                         encoding='utf-8', errors='ignore')

                # 等待进程启动
                time.sleep(2)
                startup_time = time.time() - start_time

                process.terminate()

                if startup_time > 5:
                    performance_score -= 15
                    issues.append(f"启动较慢: {startup_time:.1f}秒")

            except Exception as e:
                performance_score -= 25
                issues.append(f"启动测试失败: {str(e)}")

            return {
                'success': performance_score >= 70,
                'message': f'性能评分: {performance_score}/100',
                'details': f'文件大小: {file_size_mb:.1f}MB\n问题: {"; ".join(issues) if issues else "无"}'
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'性能测试异常: {str(e)}',
                'details': '无法获取文件信息或测试性能'
            }

    def show_comprehensive_test_results(self, test_results):
        """显示综合测试结果"""
        self.result_text.insert(tk.END, "\n" + "="*60 + "\n")
        self.result_text.insert(tk.END, "🧪 exe综合测试结果\n")
        self.result_text.insert(tk.END, "="*60 + "\n")

        total_tests = len(test_results)
        passed_tests = sum(1 for _, result in test_results if result['success'])

        for test_name, result in test_results:
            status = "✅" if result['success'] else "❌"
            self.result_text.insert(tk.END, f"\n{status} {test_name}\n")
            self.result_text.insert(tk.END, f"   结果: {result['message']}\n")
            if result.get('details'):
                self.result_text.insert(tk.END, f"   详情: {result['details']}\n")

        self.result_text.insert(tk.END, f"\n" + "="*60 + "\n")
        self.result_text.insert(tk.END, f"📊 总体结果: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)\n")

        if passed_tests == total_tests:
            self.result_text.insert(tk.END, "🎉 所有测试通过！exe文件运行正常\n")
            self.log_message("✅ exe综合测试全部通过")
        else:
            self.result_text.insert(tk.END, "⚠️ 部分测试失败，请检查相关依赖\n")
            self.log_message(f"⚠️ exe测试: {passed_tests}/{total_tests} 通过", "WARNING")

        self.result_text.insert(tk.END, "="*60 + "\n")
        self.result_text.see(tk.END)

    def get_test_script_content(self):
        """获取测试脚本内容"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""exe依赖测试脚本"""

import sys
import traceback

def test_import(module_name):
    try:
        __import__(module_name)
        return True, None
    except Exception as e:
        return False, str(e)

def main():
    print("=" * 50)
    print("🧪 exe依赖测试")
    print("=" * 50)

    test_modules = [
        'fake_useragent', 'openpyxl', 'pandas', 'requests',
        'selenium', 'bs4', 'lxml', 'cryptography', 'PIL'
    ]

    passed = 0
    total = len(test_modules)

    for module in test_modules:
        success, error = test_import(module)
        if success:
            print(f"✅ {module} - 导入成功")
            passed += 1
        else:
            print(f"❌ {module} - 导入失败: {error}")

    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 所有依赖测试通过！")
    else:
        print("⚠️ 部分依赖测试失败")

    return passed == total

if __name__ == "__main__":
    main()
'''

    def show_test_results(self, stdout, stderr):
        """显示测试结果"""
        self.result_text.insert(tk.END, "\n" + "="*50 + "\n")
        self.result_text.insert(tk.END, "exe依赖测试结果:\n")
        self.result_text.insert(tk.END, "="*50 + "\n")
        self.result_text.insert(tk.END, stdout)
        if stderr:
            self.result_text.insert(tk.END, "\n错误信息:\n")
            self.result_text.insert(tk.END, stderr)
        self.result_text.see(tk.END)

        if "🎉 所有依赖测试通过" in stdout:
            self.log_message("✅ exe依赖测试通过")
        else:
            self.log_message("⚠️ exe依赖测试部分失败", "WARNING")

    def open_output_dir(self):
        """打开输出目录"""
        dist_dir = Path("dist")
        if dist_dir.exists():
            os.startfile(str(dist_dir))
        else:
            messagebox.showwarning("警告", "输出目录不存在")

    def clean_build_files(self):
        """清理构建文件"""
        if messagebox.askyesno("确认", "确定要清理所有构建文件吗？"):
            try:
                import shutil
                for dir_name in ["build", "dist", "__pycache__"]:
                    if os.path.exists(dir_name):
                        shutil.rmtree(dir_name)
                        self.log_message(f"已删除 {dir_name} 目录")

                # 删除spec文件
                for spec_file in Path(".").glob("*.spec"):
                    spec_file.unlink()
                    self.log_message(f"已删除 {spec_file.name}")

                self.log_message("✅ 构建文件清理完成")

            except Exception as e:
                self.log_message(f"清理失败: {e}", "ERROR")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete('1.0', tk.END)

    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get('1.0', tk.END))
                self.log_message(f"日志已保存到: {filename}")
            except Exception as e:
                self.log_message(f"保存日志失败: {e}", "ERROR")

    def import_requirements(self):
        """导入requirements.txt"""
        filename = filedialog.askopenfilename(
            title="选择requirements.txt文件",
            filetypes=[("Requirements文件", "requirements*.txt"), ("文本文件", "*.txt")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 解析包名和版本
                        if '==' in line:
                            pkg_name, version = line.split('==', 1)
                        elif '>=' in line:
                            pkg_name = line.split('>=')[0]
                            version = "latest"
                        else:
                            pkg_name = line
                            version = "latest"

                        if pkg_name not in self.dependencies:
                            self.dependencies[pkg_name] = {
                                'version': version,
                                'status': '未安装',
                                'description': self.get_package_description(pkg_name),
                                'latest_version': None
                            }

                self.update_dependencies_display()
                self.log_message(f"已导入 {filename}")

            except Exception as e:
                self.log_message(f"导入失败: {e}", "ERROR")

    def export_requirements(self):
        """导出requirements.txt"""
        filename = filedialog.asksaveasfilename(
            title="保存requirements.txt",
            defaultextension=".txt",
            filetypes=[("Requirements文件", "requirements.txt"), ("文本文件", "*.txt")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    for pkg_name, info in self.dependencies.items():
                        if info['status'] == '已安装' and info['version'] != '未知':
                            f.write(f"{pkg_name}=={info['version']}\n")

                self.log_message(f"requirements.txt已保存到: {filename}")

            except Exception as e:
                self.log_message(f"导出失败: {e}", "ERROR")

    def filter_dependencies(self, *args):
        """过滤依赖显示"""
        search_text = self.search_var.get().lower()

        # 清空现有项目
        for item in self.deps_tree.get_children():
            self.deps_tree.delete(item)

        # 添加匹配的依赖项目
        for pkg_name, info in self.dependencies.items():
            if search_text in pkg_name.lower() or search_text in info['description'].lower():
                status_color = "green" if info['status'] == "已安装" else "red"
                self.deps_tree.insert('', 'end', values=(
                    pkg_name,
                    info['version'],
                    info['status'],
                    info['description']
                ), tags=(status_color,))

        # 配置标签颜色
        self.deps_tree.tag_configure("green", foreground="green")
        self.deps_tree.tag_configure("red", foreground="red")

    # 右键菜单功能
    def show_dependency_details(self):
        """显示依赖详情"""
        selected_item = self.deps_tree.selection()
        if selected_item:
            package_name = self.deps_tree.item(selected_item[0])['values'][0]
            messagebox.showinfo("依赖详情", f"包名: {package_name}\n详细信息请查看下方详情面板")

    def update_dependency(self):
        """更新依赖"""
        selected_item = self.deps_tree.selection()
        if selected_item:
            package_name = self.deps_tree.item(selected_item[0])['values'][0]
            if messagebox.askyesno("确认", f"确定要更新 {package_name} 吗？"):
                threading.Thread(target=self._update_dependency_thread, args=(package_name,), daemon=True).start()

    def _update_dependency_thread(self, package_name):
        """在后台线程中更新依赖"""
        try:
            self.root.after(0, lambda: self.log_message(f"正在更新 {package_name}..."))

            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', package_name
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                self.root.after(0, lambda: self.log_message(f"✅ {package_name} 更新成功"))
                self.root.after(0, self.load_dependencies)
            else:
                self.root.after(0, lambda: self.log_message(f"❌ {package_name} 更新失败: {result.stderr}", "ERROR"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"更新失败: {e}", "ERROR"))

    def remove_dependency(self):
        """删除依赖"""
        selected_item = self.deps_tree.selection()
        if selected_item:
            package_name = self.deps_tree.item(selected_item[0])['values'][0]
            if messagebox.askyesno("确认", f"确定要删除 {package_name} 吗？"):
                if package_name in self.dependencies:
                    del self.dependencies[package_name]
                    self.update_dependencies_display()
                    self.log_message(f"已从列表中删除 {package_name}")

    def query_dependency_pypi(self):
        """查询依赖的PyPI信息"""
        selected_item = self.deps_tree.selection()
        if selected_item:
            package_name = self.deps_tree.item(selected_item[0])['values'][0]
            self.query_pypi_versions()


class ExeTestDialog:
    """exe测试选项对话框"""
    def __init__(self, parent, exe_file, main_app):
        self.exe_file = exe_file
        self.main_app = main_app
        self.test_options = {}

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("exe依赖测试选项")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.create_widgets()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text=f"🧪 测试 {self.exe_file.name}", font=('Arial', 12, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))

        # 文件信息
        info_frame = ttk.LabelFrame(main_frame, text="文件信息", padding="10")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        info_frame.columnconfigure(1, weight=1)

        file_size = self.exe_file.stat().st_size / (1024 * 1024)

        ttk.Label(info_frame, text="文件路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=str(self.exe_file)).grid(row=0, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="文件大小:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=f"{file_size:.1f} MB").grid(row=1, column=1, sticky=tk.W)

        # 测试选项
        options_frame = ttk.LabelFrame(main_frame, text="测试选项", padding="10")
        options_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        self.basic_test_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="🚀 基础启动测试", variable=self.basic_test_var).grid(row=0, column=0, sticky=tk.W, pady=2)

        self.import_test_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="📦 依赖导入测试", variable=self.import_test_var).grid(row=1, column=0, sticky=tk.W, pady=2)

        self.functional_test_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="🔧 功能性测试", variable=self.functional_test_var).grid(row=2, column=0, sticky=tk.W, pady=2)

        self.performance_test_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="⚡ 性能测试", variable=self.performance_test_var).grid(row=3, column=0, sticky=tk.W, pady=2)

        # 测试说明
        desc_frame = ttk.LabelFrame(main_frame, text="测试说明", padding="10")
        desc_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        desc_frame.columnconfigure(0, weight=1)
        desc_frame.rowconfigure(0, weight=1)

        desc_text = tk.Text(desc_frame, height=8, width=50, wrap=tk.WORD, state='disabled')
        desc_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        desc_scrollbar = ttk.Scrollbar(desc_frame, orient=tk.VERTICAL, command=desc_text.yview)
        desc_text.configure(yscrollcommand=desc_scrollbar.set)
        desc_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 添加说明文本
        desc_content = """🚀 基础启动测试: 检查exe文件是否能正常启动，测试基本的运行环境。

📦 依赖导入测试: 验证所有关键Python模块是否正确打包到exe中。

🔧 功能性测试: 测试关键功能如Excel处理、网络请求、加密等是否正常工作。

⚡ 性能测试: 评估文件大小、启动速度等性能指标。

注意: 测试过程可能需要几分钟时间，请耐心等待。某些测试可能会短暂启动exe程序。"""

        desc_text.config(state='normal')
        desc_text.insert('1.0', desc_content)
        desc_text.config(state='disabled')

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="🧪 开始测试", command=self.start_test).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ 取消", command=self.dialog.destroy).pack(side=tk.LEFT)

        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)

    def start_test(self):
        """开始测试"""
        self.test_options = {
            'basic_test': self.basic_test_var.get(),
            'import_test': self.import_test_var.get(),
            'functional_test': self.functional_test_var.get(),
            'performance_test': self.performance_test_var.get()
        }

        # 检查是否至少选择了一个测试
        if not any(self.test_options.values()):
            messagebox.showwarning("警告", "请至少选择一个测试项目")
            return

        self.main_app.log_message("开始exe综合测试...")
        threading.Thread(target=self.main_app._test_exe_thread,
                        args=(self.exe_file, self.test_options), daemon=True).start()

        self.dialog.destroy()


class AddDependencyDialog:
    """添加依赖对话框"""
    def __init__(self, parent, main_app):
        self.main_app = main_app
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加依赖")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.create_widgets()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 包名输入
        ttk.Label(main_frame, text="包名:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.package_var = tk.StringVar()
        package_entry = ttk.Entry(main_frame, textvariable=self.package_var, width=30)
        package_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        package_entry.focus()

        # 版本选择
        ttk.Label(main_frame, text="版本:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.version_var = tk.StringVar(value="latest")
        version_combo = ttk.Combobox(main_frame, textvariable=self.version_var, width=27)
        version_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 查询版本按钮
        ttk.Button(main_frame, text="查询可用版本", command=self.query_versions).grid(row=2, column=1, sticky=tk.W, pady=(0, 10))

        # 版本列表
        ttk.Label(main_frame, text="可用版本:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=(0, 5))

        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        self.version_listbox = tk.Listbox(list_frame, height=6)
        self.version_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        version_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.version_listbox.yview)
        self.version_listbox.configure(yscrollcommand=version_scrollbar.set)
        version_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 绑定选择事件
        self.version_listbox.bind('<<ListboxSelect>>', self.on_version_select)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="添加", command=self.add_dependency).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)

        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

    def query_versions(self):
        """查询版本"""
        package_name = self.package_var.get().strip()
        if not package_name:
            messagebox.showwarning("警告", "请输入包名")
            return

        self.main_app.log_message(f"正在查询 {package_name} 的版本...")
        threading.Thread(target=self._query_versions_thread, args=(package_name,), daemon=True).start()

    def _query_versions_thread(self, package_name):
        """在后台线程中查询版本"""
        try:
            response = requests.get(f"https://pypi.org/pypi/{package_name}/json", timeout=10)
            if response.status_code == 200:
                data = response.json()
                versions = list(data['releases'].keys())
                versions.reverse()  # 最新版本在前

                self.dialog.after(0, lambda: self.update_version_list(versions))
                self.main_app.root.after(0, lambda: self.main_app.log_message(f"✅ {package_name} 版本查询完成"))
            else:
                self.main_app.root.after(0, lambda: self.main_app.log_message(f"❌ 查询 {package_name} 失败", "ERROR"))

        except Exception as e:
            self.main_app.root.after(0, lambda: self.main_app.log_message(f"查询失败: {e}", "ERROR"))

    def update_version_list(self, versions):
        """更新版本列表"""
        self.version_listbox.delete(0, tk.END)
        for version in versions[:20]:  # 只显示前20个版本
            self.version_listbox.insert(tk.END, version)

    def on_version_select(self, event):
        """版本选择事件"""
        selection = self.version_listbox.curselection()
        if selection:
            version = self.version_listbox.get(selection[0])
            self.version_var.set(version)

    def add_dependency(self):
        """添加依赖"""
        package_name = self.package_var.get().strip()
        version = self.version_var.get().strip()

        if not package_name:
            messagebox.showwarning("警告", "请输入包名")
            return

        # 添加到依赖列表
        self.main_app.dependencies[package_name] = {
            'version': version if version != 'latest' else '未知',
            'status': '未安装',
            'description': self.main_app.get_package_description(package_name),
            'latest_version': None
        }

        self.main_app.update_dependencies_display()
        self.main_app.log_message(f"已添加依赖: {package_name}")

        self.dialog.destroy()


class VersionSelectDialog:
    """版本选择对话框"""
    def __init__(self, parent, package_name, versions, latest_version, main_app):
        self.package_name = package_name
        self.versions = versions
        self.latest_version = latest_version
        self.main_app = main_app

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"{package_name} - 版本选择")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.create_widgets()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text=f"📦 {self.package_name}", font=('Arial', 12, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 最新版本信息
        info_frame = ttk.LabelFrame(main_frame, text="版本信息", padding="10")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)

        ttk.Label(info_frame, text="最新版本:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=self.latest_version, foreground="green").grid(row=0, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="可用版本数:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=str(len(self.versions))).grid(row=1, column=1, sticky=tk.W)

        # 版本列表
        ttk.Label(main_frame, text="选择版本:").grid(row=2, column=0, sticky=(tk.W, tk.N), pady=(0, 5))

        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        self.version_listbox = tk.Listbox(list_frame, height=12)
        self.version_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        version_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.version_listbox.yview)
        self.version_listbox.configure(yscrollcommand=version_scrollbar.set)
        version_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 填充版本列表
        sorted_versions = sorted(self.versions, reverse=True)
        for version in sorted_versions:
            self.version_listbox.insert(tk.END, version)
            if version == self.latest_version:
                self.version_listbox.selection_set(self.version_listbox.size() - 1)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="安装选中版本", command=self.install_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="安装最新版本", command=self.install_latest).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=self.dialog.destroy).pack(side=tk.LEFT)

        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

    def install_selected(self):
        """安装选中版本"""
        selection = self.version_listbox.curselection()
        if selection:
            version = self.version_listbox.get(selection[0])
            self.install_version(version)
        else:
            messagebox.showwarning("警告", "请选择一个版本")

    def install_latest(self):
        """安装最新版本"""
        self.install_version(self.latest_version)

    def install_version(self, version):
        """安装指定版本"""
        if messagebox.askyesno("确认", f"确定要安装 {self.package_name}=={version} 吗？"):
            self.main_app.log_message(f"正在安装 {self.package_name}=={version}...")
            threading.Thread(target=self._install_thread, args=(version,), daemon=True).start()
            self.dialog.destroy()

    def _install_thread(self, version):
        """在后台线程中安装"""
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', f"{self.package_name}=={version}"
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                self.main_app.root.after(0, lambda: self.main_app.log_message(f"✅ {self.package_name}=={version} 安装成功"))
                self.main_app.root.after(0, self.main_app.load_dependencies)
            else:
                self.main_app.root.after(0, lambda: self.main_app.log_message(f"❌ 安装失败: {result.stderr}", "ERROR"))

        except Exception as e:
            self.main_app.root.after(0, lambda: self.main_app.log_message(f"安装失败: {e}", "ERROR"))


def main():
    """主函数"""
    root = tk.Tk()
    app = AdvancedBuildGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
