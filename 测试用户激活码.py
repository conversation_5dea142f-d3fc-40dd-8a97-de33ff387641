#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户激活码
"""

import requests

def test_user_activation_code():
    """测试用户的激活码"""
    print("🔑 测试用户激活码")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 用户的激活码信息
    user_key = "83R2AXQK-20250725-67c80d8d"
    device_id = "20cc47fd9ca63e67"
    
    print(f"📋 激活码: {user_key}")
    print(f"📋 设备ID: {device_id}")
    
    # 测试更新检查
    print("\n🔍 测试更新检查...")
    try:
        response = requests.get(
            f"{server_url}/update/check",
            params={
                "current_version": "2.1.0",
                "key": user_key,
                "device_id": device_id
            },
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 更新检查成功")
            print(f"📄 成功: {data.get('success', False)}")
            print(f"📄 有更新: {data.get('has_update', False)}")
            print(f"📄 当前版本: {data.get('current_version', 'Unknown')}")
            print(f"📄 最新版本: {data.get('latest_version', 'Unknown')}")
            
        elif response.status_code == 401:
            data = response.json()
            print("🚫 认证失败")
            print(f"📄 错误信息: {data.get('message', 'Unknown')}")
            
            # 可能的原因
            print("\n💡 可能的原因:")
            if "激活码不存在" in data.get('message', ''):
                print("- 激活码不在服务器数据库中")
            elif "激活码已过期" in data.get('message', ''):
                print("- 激活码已过期")
            elif "设备不匹配" in data.get('message', ''):
                print("- 设备ID不匹配")
            else:
                print("- 其他认证问题")
                
        elif response.status_code == 400:
            data = response.json()
            print("⚠️ 参数错误")
            print(f"📄 错误信息: {data.get('message', 'Unknown')}")
            
        else:
            print(f"❓ 未知状态码: {response.status_code}")
            print(f"📄 响应: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_license_check():
    """测试授权检查"""
    print("\n🔍 测试授权检查...")
    
    server_url = "http://198.23.135.176:5000"
    user_key = "83R2AXQK-20250725-67c80d8d"
    device_id = "20cc47fd9ca63e67"
    
    try:
        # 构造授权检查请求（简化版）
        response = requests.post(
            f"{server_url}/license/check",
            json={
                "key": user_key,
                "device_id": device_id,
                "timestamp": "**********",
                "nonce": "test-nonce",
                "signature": "test-signature"
            },
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 授权检查成功")
            print(f"📄 响应: {data}")
            
        elif response.status_code == 401:
            data = response.json()
            print("🚫 授权失败")
            print(f"📄 错误信息: {data.get('message', 'Unknown')}")
            
        elif response.status_code == 400:
            data = response.json()
            print("⚠️ 请求格式错误")
            print(f"📄 错误信息: {data.get('message', 'Unknown')}")
            
        else:
            print(f"❓ 未知状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 授权检查失败: {e}")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 50)
    
    print("🔧 如果激活码认证失败:")
    print("1. 检查激活码是否正确")
    print("2. 检查激活码是否已过期")
    print("3. 检查设备ID是否匹配")
    print("4. 联系管理员重新生成激活码")
    
    print("\n🔧 如果需要新的激活码:")
    print("1. 使用exe文件管理工具.py")
    print("2. 在授权管理中生成新的激活码")
    print("3. 设置合适的过期时间和权限级别")

def main():
    """主函数"""
    print("🎯 测试用户激活码和自动更新功能")
    print("🔧 500错误已修复，现在测试用户权限")
    print()
    
    test_user_activation_code()
    test_license_check()
    suggest_solutions()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print("✅ 服务器500错误已修复")
    print("✅ 更新API正常工作")
    print("📋 用户激活码需要验证权限")
    
    print("\n🚀 下一步:")
    print("1. 如果激活码有效 - license_client.py可以正常使用")
    print("2. 如果激活码无效 - 需要重新生成激活码")
    print("3. 自动更新功能已经可以正常工作")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
