@echo off
chcp 65001 >nul
title 🔧 修复SSH问题并启动部署工具

echo.
echo ==========================================
echo 🔧 SSH连接问题自动修复工具
echo ==========================================
echo.
echo 🎯 解决问题:
echo • ❌ [WinError 2] 系统找不到指定的文件
echo • ❌ sshpass工具缺失
echo • ❌ SSH连接工具不可用
echo.
echo 🔧 修复方案:
echo • 📦 自动安装paramiko模块
echo • 🧪 测试SSH连接
echo • 📝 创建必要的测试文件
echo • 🚀 启动改进版部署工具
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装，请先安装Python
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 运行修复脚本
echo 🔧 开始修复SSH连接问题...
python "快速修复SSH问题.py"

echo.
echo 👋 修复完成
pause
