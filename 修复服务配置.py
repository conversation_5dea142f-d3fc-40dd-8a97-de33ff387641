#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复服务配置 - 确保systemd服务运行正确的license_server.py文件
"""

import paramiko
import os

def fix_service_config():
    """修复服务配置"""
    print("🔧 修复服务配置")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 建立SFTP连接
        sftp = client.open_sftp()
        
        # 步骤1: 检查当前systemd配置
        print("\n📋 步骤1: 检查当前systemd配置...")
        stdin, stdout, stderr = client.exec_command("cat /etc/systemd/system/license-manager.service")
        current_config = stdout.read().decode('utf-8')
        print(f"当前配置:\n{current_config}")
        
        # 步骤2: 停止服务
        print("\n🛑 步骤2: 停止服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        stdout.channel.recv_exit_status()
        print("   ✅ 服务已停止")
        
        # 步骤3: 上传正确的license_server.py
        print("\n📤 步骤3: 上传正确的license_server.py...")
        local_file = "license_server.py"
        if os.path.exists(local_file):
            remote_file = f"{config['deploy_path']}/license_server.py"
            sftp.put(local_file, remote_file)
            print(f"   ✅ 已上传 {local_file}")
            
            # 设置权限
            stdin, stdout, stderr = client.exec_command(f"chmod +x {remote_file}")
            stdout.channel.recv_exit_status()
            print("   ✅ 权限设置完成")
        else:
            print(f"   ❌ 本地文件不存在: {local_file}")
            return False
        
        # 步骤4: 验证Python语法
        print("\n🔍 步骤4: 验证Python语法...")
        stdin, stdout, stderr = client.exec_command(f"python3 -m py_compile {config['deploy_path']}/license_server.py")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ Python语法检查通过")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ Python语法错误: {error}")
            return False
        
        # 步骤5: 更新systemd服务配置
        print("\n⚙️ 步骤5: 更新systemd服务配置...")
        
        # 创建正确的systemd服务配置
        service_config = f"""[Unit]
Description=License Manager Service - Full Version
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={config['deploy_path']}
ExecStart=/usr/bin/python3 {config['deploy_path']}/license_server.py
Restart=always
RestartSec=10
Environment=PYTHONPATH={config['deploy_path']}
Environment=PYTHONIOENCODING=utf-8

[Install]
WantedBy=multi-user.target
"""
        
        # 写入服务配置文件
        stdin, stdout, stderr = client.exec_command(f"cat > /etc/systemd/system/license-manager.service << 'EOF'\n{service_config}EOF")
        stdout.channel.recv_exit_status()
        print("   ✅ systemd配置已更新")
        
        # 步骤6: 重新加载systemd配置
        print("\n🔄 步骤6: 重新加载systemd配置...")
        stdin, stdout, stderr = client.exec_command("systemctl daemon-reload")
        stdout.channel.recv_exit_status()
        print("   ✅ systemd配置已重新加载")
        
        # 步骤7: 创建必要的目录
        print("\n📁 步骤7: 创建必要的目录...")
        directories = [
            f"{config['deploy_path']}/updates",
            f"{config['deploy_path']}/updates/files"
        ]
        
        for directory in directories:
            stdin, stdout, stderr = client.exec_command(f"mkdir -p {directory}")
            stdout.channel.recv_exit_status()
            print(f"   ✅ 目录已创建: {directory}")
        
        # 步骤8: 启动服务
        print("\n🚀 步骤8: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error}")
            return False
        
        # 等待服务启动
        import time
        print("\n⏳ 等待服务完全启动...")
        time.sleep(15)
        
        # 步骤9: 检查服务状态
        print("\n🔄 步骤9: 检查服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("   ✅ 服务运行正常")
        else:
            print(f"   ❌ 服务状态异常: {status}")
            
            # 查看错误日志
            stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '2 minutes ago' --no-pager")
            log_output = stdout.read().decode('utf-8')
            print(f"   错误日志:\n{log_output}")
            return False
        
        # 步骤10: 验证进程
        print("\n🐍 步骤10: 验证Python进程...")
        stdin, stdout, stderr = client.exec_command("ps aux | grep license_server.py | grep -v grep")
        process_output = stdout.read().decode('utf-8')
        
        if process_output.strip():
            print(f"   ✅ 正确的Python进程运行中:\n{process_output}")
        else:
            print("   ❌ 未找到license_server.py进程")
            return False
        
        # 步骤11: 测试API
        print("\n🧪 步骤11: 测试API...")
        import requests
        
        test_apis = [
            ("基本连接", "http://**************:5000"),
            ("更新统计", "http://**************:5000/update/stats"),
            ("检查更新", "http://**************:5000/update/check?current_version=1.0.0")
        ]
        
        success_count = 0
        for name, url in test_apis:
            try:
                response = requests.get(url, timeout=10)
                print(f"   📋 {name}: HTTP {response.status_code}")
                
                if response.status_code in [200, 400]:  # 400也算API存在
                    success_count += 1
                    print(f"      ✅ API响应正常")
                elif response.status_code == 404:
                    print(f"      ❌ API不存在")
                else:
                    print(f"      ⚠️ 异常状态码")
                    
            except Exception as e:
                print(f"   ❌ {name}: 连接失败 - {e}")
        
        # 关闭连接
        sftp.close()
        client.close()
        
        if success_count >= 2:
            print("\n🎉 修复成功！")
            print("🌐 服务器地址: http://**************:5000")
            print("📋 可用API:")
            print("  • 检查更新: /update/check")
            print("  • 下载更新: /update/download")
            print("  • 上传更新: /update/upload")
            print("  • 获取统计: /update/stats")
            return True
        else:
            print("\n⚠️ 修复完成，但API测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 修复systemd服务配置")
        print("🔧 问题: 当前运行simple_server.py，需要运行license_server.py")
        print("💡 方案: 上传正确文件 + 更新systemd配置 + 重启服务")
        print()
        
        # 检查本地文件
        if not os.path.exists("license_server.py"):
            print("❌ 本地license_server.py文件不存在")
            print("💡 请确保当前目录有正确的license_server.py文件")
            input("\n按回车键退出...")
            return
        
        print(f"✅ 找到本地文件: license_server.py ({os.path.getsize('license_server.py')} 字节)")
        
        confirm = input("\n确认开始修复？(y/n): ").lower().strip()
        
        if confirm in ['y', 'yes', '是']:
            if fix_service_config():
                print("\n✅ 修复成功！现在可以使用自动更新功能了")
            else:
                print("\n❌ 修复失败，请检查错误信息")
        else:
            print("❌ 用户取消修复")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
