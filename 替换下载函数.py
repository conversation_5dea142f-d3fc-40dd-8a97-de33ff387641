#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
替换下载函数 - 直接替换auto_updater.py中有问题的下载函数
"""

import os
import shutil

def create_new_download_function():
    """创建新的下载函数代码"""
    return '''    def download_update(self, update_info, progress_callback=None):
        """
        下载更新文件 - 增强版本，解决99.8%问题
        
        Args:
            update_info: 更新信息
            progress_callback: 进度回调函数
            
        Returns:
            str: 下载的文件路径，失败返回None
        """
        try:
            import requests
            import tempfile
            import time
            
            # 获取下载URL和参数
            if self.license_key and self.device_id:
                # 使用授权服务器
                url = f"{self.server_url.rstrip('/')}/update/download"
                params = {
                    'key': self.license_key,
                    'device_id': self.device_id,
                    'version': update_info.get('version')
                }
            else:
                # 使用静态文件服务器
                download_url = update_info.get('download_url', '')
                if download_url.startswith('/'):
                    url = f"{self.server_url.rstrip('/')}{download_url}"
                else:
                    url = download_url
                params = {}
            
            print(f"开始下载: {url}")
            if params:
                print(f"参数: {params}")
            
            # 创建临时文件
            temp_file = os.path.join(tempfile.gettempdir(), f"update_{int(time.time())}.exe")
            
            # 多次尝试下载
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    print(f"下载尝试 {attempt + 1}/{max_attempts}")
                    
                    # 发送下载请求
                    response = requests.get(url, params=params, stream=True, timeout=(60, 1800))
                    response.raise_for_status()
                    
                    # 获取文件大小
                    total_size = int(response.headers.get('Content-Length', 0))
                    if total_size == 0:
                        total_size = update_info.get('file_size', 0)
                    
                    print(f"文件大小: {total_size:,} 字节")
                    
                    # 下载文件
                    downloaded_size = 0
                    chunk_size = 32 * 1024  # 32KB chunks
                    last_progress = 0
                    
                    with open(temp_file, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)
                                
                                # 更新进度
                                if progress_callback and total_size > 0:
                                    progress = (downloaded_size / total_size) * 100
                                    # 只在进度变化超过0.5%时回调，减少UI更新频率
                                    if progress - last_progress >= 0.5:
                                        progress_callback(min(progress, 100.0))  # 确保不超过100%
                                        last_progress = progress
                        
                        # 强制刷新缓冲区
                        f.flush()
                        os.fsync(f.fileno())
                    
                    # 验证文件大小
                    actual_size = os.path.getsize(temp_file)
                    print(f"实际下载: {actual_size:,} 字节")
                    
                    # 使用更宽松的验证条件
                    if total_size > 0:
                        size_diff = abs(actual_size - total_size)
                        # 允许最大1MB或5%的差异
                        max_diff = max(1024 * 1024, total_size * 0.05)
                        
                        if size_diff <= max_diff:
                            print("文件大小验证通过")
                            # 确保进度显示100%
                            if progress_callback:
                                progress_callback(100.0)
                            return temp_file
                        else:
                            print(f"文件大小验证失败: 差异 {size_diff:,} 字节 (允许 {max_diff:,})")
                            if attempt < max_attempts - 1:
                                print("重新尝试下载...")
                                if os.path.exists(temp_file):
                                    os.remove(temp_file)
                                continue
                    else:
                        # 没有大小信息，直接返回
                        print("无法验证文件大小，直接返回")
                        if progress_callback:
                            progress_callback(100.0)
                        return temp_file
                    
                except requests.exceptions.RequestException as e:
                    print(f"网络请求失败: {e}")
                    if attempt < max_attempts - 1:
                        print("等待2秒后重试...")
                        time.sleep(2)
                        continue
                    else:
                        raise
                
                except Exception as e:
                    print(f"下载过程出错: {e}")
                    if attempt < max_attempts - 1:
                        print("等待2秒后重试...")
                        time.sleep(2)
                        continue
                    else:
                        raise
            
            # 所有尝试都失败
            print("所有下载尝试都失败")
            return None
            
        except Exception as e:
            print(f"下载更新失败: {e}")
            return None'''

def replace_download_function():
    """替换auto_updater.py中的下载函数"""
    filename = "auto_updater.py"
    
    if not os.path.exists(filename):
        print(f"❌ 未找到 {filename}")
        return False
    
    # 备份原文件
    backup_name = f"{filename}.backup_new"
    shutil.copy2(filename, backup_name)
    print(f"✅ 已备份 {filename} -> {backup_name}")
    
    # 读取原文件
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找原始的download_update函数
    import re
    
    # 匹配函数定义到下一个函数或类定义
    pattern = r'(\s+def download_update\(self[^:]*\):.*?)(\n\s+def |\n\s+class |\nclass |\ndef |\Z)'
    
    match = re.search(pattern, content, re.DOTALL)
    if not match:
        print("❌ 未找到download_update函数")
        return False
    
    print("✅ 找到download_update函数")
    
    # 获取函数的缩进
    original_function = match.group(1)
    next_part = match.group(2)
    
    # 获取缩进级别
    lines = original_function.split('\n')
    first_line = lines[0]
    indent = len(first_line) - len(first_line.lstrip())
    
    # 创建新函数，保持相同缩进
    new_function_lines = create_new_download_function().split('\n')
    indented_new_function = '\n'.join(' ' * indent + line if line.strip() else line 
                                     for line in new_function_lines)
    
    # 替换函数
    new_content = content.replace(match.group(0), indented_new_function + next_part)
    
    # 保存新文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ download_update函数已替换")
    return True

def main():
    """主函数"""
    print("🔧 开始替换下载函数...")
    print("=" * 50)
    
    if replace_download_function():
        print("=" * 50)
        print("🎉 替换完成!")
        print("\n✅ 新的下载函数特点:")
        print("  - 支持多次重试 (最多3次)")
        print("  - 更宽松的文件大小验证 (允许1MB或5%差异)")
        print("  - 强制文件缓冲区刷新")
        print("  - 确保进度显示100%")
        print("  - 增强的错误处理")
        print("  - 详细的调试日志")
        
        print("\n🚀 建议:")
        print("1. 重启 license_client.py 程序")
        print("2. 测试更新功能")
        print("3. 观察控制台输出的详细日志")
        print("4. 如果仍有问题，可以恢复备份文件")
        
        print(f"\n📁 备份文件: auto_updater.py.backup_new")
    else:
        print("❌ 替换失败")

if __name__ == "__main__":
    main()
