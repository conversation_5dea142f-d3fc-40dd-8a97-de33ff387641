#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查服务器端问题 - 分析99%停止的服务器端原因
"""

import paramiko
import tkinter as tk
from tkinter import scrolledtext, messagebox
import threading
import time
import requests

class ServerChecker:
    """服务器检查器"""
    
    def __init__(self):
        self.server_ip = "**************"
        self.ssh_port = 22
        self.username = "root"
        self.password = "l39XNqJG24JmXc2za0"
        self.server_url = "http://**************:5000"
        
    def create_gui(self):
        """创建GUI界面"""
        self.root = tk.Tk()
        self.root.title("服务器端问题检查")
        self.root.geometry("1000x700")
        
        # 设置图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 标题
        title_label = tk.Label(
            self.root,
            text="🔍 服务器端问题检查",
            font=("微软雅黑", 16, "bold"),
            pady=10
        )
        title_label.pack()
        
        # 说明
        info_label = tk.Label(
            self.root,
            text="检查服务器端文件、配置和日志，找出99%停止的根本原因",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        info_label.pack()
        
        # 日志区域
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=35, width=120)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        # 检查服务器文件按钮
        check_files_button = tk.Button(
            button_frame,
            text="📁 检查服务器文件",
            command=self.check_server_files,
            font=("微软雅黑", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        check_files_button.pack(side=tk.LEFT, padx=5)
        
        # 检查服务器日志按钮
        check_logs_button = tk.Button(
            button_frame,
            text="📋 检查服务器日志",
            command=self.check_server_logs,
            font=("微软雅黑", 12),
            bg="#e74c3c",
            fg="white",
            padx=20,
            pady=10
        )
        check_logs_button.pack(side=tk.LEFT, padx=5)
        
        # 测试下载流按钮
        test_stream_button = tk.Button(
            button_frame,
            text="🌊 测试下载流",
            command=self.test_download_stream,
            font=("微软雅黑", 12),
            bg="#f39c12",
            fg="white",
            padx=20,
            pady=10
        )
        test_stream_button.pack(side=tk.LEFT, padx=5)
        
        # 修复服务器问题按钮
        fix_server_button = tk.Button(
            button_frame,
            text="🔧 修复服务器问题",
            command=self.fix_server_issues,
            font=("微软雅黑", 12),
            bg="#27ae60",
            fg="white",
            padx=20,
            pady=10
        )
        fix_server_button.pack(side=tk.LEFT, padx=5)
        
        # 清空日志按钮
        clear_button = tk.Button(
            button_frame,
            text="🗑️ 清空日志",
            command=self.clear_log,
            font=("微软雅黑", 12),
            bg="#95a5a6",
            fg="white",
            padx=20,
            pady=10
        )
        clear_button.pack(side=tk.LEFT, padx=5)
        
        self.log("🔍 服务器端问题检查工具已启动")
        self.log("💡 点击按钮开始检查服务器端问题")
        
        return self.root
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.insert(tk.END, log_message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
        print(log_message)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def ssh_execute(self, command):
        """执行SSH命令"""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(self.server_ip, self.ssh_port, self.username, self.password, timeout=30)
            
            stdin, stdout, stderr = ssh.exec_command(command)
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            ssh.close()
            
            return output, error
            
        except Exception as e:
            return None, str(e)
    
    def check_server_files(self):
        """检查服务器文件"""
        self.log("📁 检查服务器文件...")
        
        def check_thread():
            try:
                # 1. 检查更新文件目录
                self.log("\n1️⃣ 检查更新文件目录...")
                output, error = self.ssh_execute("ls -la /opt/license_manager/updates/")
                if output:
                    self.log("📂 更新文件目录内容:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                else:
                    self.log(f"❌ 无法访问更新目录: {error}")
                
                # 2. 检查具体的exe文件
                self.log("\n2️⃣ 检查exe文件详情...")
                output, error = self.ssh_execute("find /opt/license_manager -name '*.exe' -exec ls -lh {} \\;")
                if output:
                    self.log("📦 找到的exe文件:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                else:
                    self.log(f"❌ 未找到exe文件: {error}")
                
                # 3. 检查文件完整性
                self.log("\n3️⃣ 检查文件完整性...")
                output, error = self.ssh_execute("find /opt/license_manager -name '*.exe' -exec md5sum {} \\;")
                if output:
                    self.log("🔐 文件MD5校验:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                else:
                    self.log(f"❌ 无法计算MD5: {error}")
                
                # 4. 检查磁盘空间
                self.log("\n4️⃣ 检查磁盘空间...")
                output, error = self.ssh_execute("df -h /opt/license_manager")
                if output:
                    self.log("💾 磁盘空间:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                
                # 5. 检查文件权限
                self.log("\n5️⃣ 检查文件权限...")
                output, error = self.ssh_execute("ls -la /opt/license_manager/license_server.py")
                if output:
                    self.log("🔒 服务器文件权限:")
                    self.log(f"  {output.strip()}")
                
                self.log("\n✅ 服务器文件检查完成")
                
            except Exception as e:
                self.log(f"❌ 检查服务器文件失败: {e}")
        
        # 在后台线程运行检查
        thread = threading.Thread(target=check_thread, daemon=True)
        thread.start()
    
    def check_server_logs(self):
        """检查服务器日志"""
        self.log("📋 检查服务器日志...")
        
        def check_thread():
            try:
                # 1. 检查systemd服务日志
                self.log("\n1️⃣ 检查systemd服务日志...")
                output, error = self.ssh_execute("journalctl -u license-manager -n 50 --no-pager")
                if output:
                    self.log("📋 服务日志 (最近50行):")
                    for line in output.strip().split('\n')[-20:]:  # 只显示最后20行
                        if line.strip():
                            self.log(f"  {line}")
                
                # 2. 检查Flask应用日志
                self.log("\n2️⃣ 检查Flask应用日志...")
                output, error = self.ssh_execute("tail -n 30 /opt/license_manager/app.log 2>/dev/null || echo 'No app.log found'")
                if output and "No app.log found" not in output:
                    self.log("📋 应用日志:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                else:
                    self.log("ℹ️ 未找到应用日志文件")
                
                # 3. 检查nginx日志
                self.log("\n3️⃣ 检查nginx访问日志...")
                output, error = self.ssh_execute("tail -n 20 /var/log/nginx/access.log | grep update")
                if output:
                    self.log("📋 nginx更新相关访问:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                
                # 4. 检查nginx错误日志
                self.log("\n4️⃣ 检查nginx错误日志...")
                output, error = self.ssh_execute("tail -n 20 /var/log/nginx/error.log")
                if output:
                    self.log("📋 nginx错误日志:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                
                # 5. 检查系统资源
                self.log("\n5️⃣ 检查系统资源...")
                output, error = self.ssh_execute("free -h && echo '---' && ps aux | grep python | head -5")
                if output:
                    self.log("💻 系统资源:")
                    for line in output.strip().split('\n'):
                        if line.strip():
                            self.log(f"  {line}")
                
                self.log("\n✅ 服务器日志检查完成")
                
            except Exception as e:
                self.log(f"❌ 检查服务器日志失败: {e}")
        
        # 在后台线程运行检查
        thread = threading.Thread(target=check_thread, daemon=True)
        thread.start()
    
    def test_download_stream(self):
        """测试下载流"""
        self.log("🌊 测试下载流...")
        
        def test_thread():
            try:
                url = f"{self.server_url}/update/download"
                params = {
                    'key': 'ADMIN_BYPASS',
                    'device_id': 'ADMIN-DEVICE-001',
                    'version': '2.1.1'
                }
                
                self.log("🚀 开始流式下载测试...")
                
                response = requests.get(
                    url, 
                    params=params,
                    stream=True,
                    timeout=(60, 300)  # 5分钟超时
                )
                
                if response.status_code != 200:
                    self.log(f"❌ HTTP错误: {response.status_code}")
                    self.log(f"📄 响应内容: {response.text}")
                    return
                
                # 检查响应头
                content_length = response.headers.get('Content-Length')
                content_type = response.headers.get('Content-Type')
                
                self.log(f"📡 Content-Length: {content_length}")
                self.log(f"📡 Content-Type: {content_type}")
                
                if content_length:
                    expected_size = int(content_length)
                    self.log(f"📦 预期大小: {expected_size:,} 字节 ({expected_size/1024/1024:.2f}MB)")
                else:
                    self.log("⚠️ 服务器未返回Content-Length")
                    return
                
                # 测试流式下载
                downloaded_size = 0
                chunk_count = 0
                last_log_size = 0
                
                self.log("📥 开始流式下载测试...")
                
                for chunk in response.iter_content(chunk_size=32768):
                    if chunk:
                        downloaded_size += len(chunk)
                        chunk_count += 1
                        
                        # 每5MB记录一次
                        if downloaded_size - last_log_size >= 5 * 1024 * 1024:
                            progress = (downloaded_size / expected_size) * 100
                            self.log(f"📊 已下载: {downloaded_size:,} 字节 ({progress:.2f}%)")
                            last_log_size = downloaded_size
                        
                        # 特别关注接近完成的情况
                        if downloaded_size >= expected_size * 0.99:
                            progress = (downloaded_size / expected_size) * 100
                            self.log(f"🔍 高进度: {downloaded_size:,}/{expected_size:,} ({progress:.6f}%)")
                
                # 最终结果
                final_progress = (downloaded_size / expected_size) * 100
                self.log(f"\n📊 下载完成统计:")
                self.log(f"  - 总块数: {chunk_count:,}")
                self.log(f"  - 下载字节: {downloaded_size:,}")
                self.log(f"  - 预期字节: {expected_size:,}")
                self.log(f"  - 完成度: {final_progress:.6f}%")
                
                if downloaded_size == expected_size:
                    self.log("🎉 流式下载完整!")
                elif downloaded_size >= expected_size * 0.999:
                    self.log("✅ 流式下载基本完整 (99.9%+)")
                else:
                    missing = expected_size - downloaded_size
                    self.log(f"❌ 流式下载不完整，缺少 {missing:,} 字节")
                    self.log("💡 可能的原因:")
                    self.log("  1. 服务器提前关闭连接")
                    self.log("  2. 服务器文件本身不完整")
                    self.log("  3. 网络传输中断")
                
            except Exception as e:
                self.log(f"❌ 流式下载测试失败: {e}")
                import traceback
                self.log(f"📄 详细错误: {traceback.format_exc()}")
        
        # 在后台线程运行测试
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
    
    def fix_server_issues(self):
        """修复服务器问题"""
        self.log("🔧 修复服务器问题...")
        
        def fix_thread():
            try:
                # 1. 重启服务
                self.log("\n1️⃣ 重启license-manager服务...")
                output, error = self.ssh_execute("systemctl restart license-manager")
                if error:
                    self.log(f"⚠️ 重启警告: {error}")
                else:
                    self.log("✅ 服务重启成功")
                
                # 2. 检查服务状态
                self.log("\n2️⃣ 检查服务状态...")
                output, error = self.ssh_execute("systemctl status license-manager --no-pager")
                if output:
                    self.log("📋 服务状态:")
                    for line in output.strip().split('\n')[:10]:  # 只显示前10行
                        self.log(f"  {line}")
                
                # 3. 清理临时文件
                self.log("\n3️⃣ 清理临时文件...")
                output, error = self.ssh_execute("find /tmp -name '*amazon*' -type f -delete 2>/dev/null; echo 'Cleanup done'")
                self.log("✅ 临时文件清理完成")
                
                # 4. 检查端口监听
                self.log("\n4️⃣ 检查端口监听...")
                output, error = self.ssh_execute("netstat -tlnp | grep :5000")
                if output:
                    self.log("📡 端口5000监听状态:")
                    self.log(f"  {output.strip()}")
                else:
                    self.log("❌ 端口5000未监听")
                
                # 5. 测试API响应
                self.log("\n5️⃣ 测试API响应...")
                try:
                    response = requests.get(f"{self.server_url}/", timeout=10)
                    self.log(f"📡 根路径响应: {response.status_code}")
                    
                    response = requests.get(f"{self.server_url}/update/stats", timeout=10)
                    self.log(f"📡 更新统计响应: {response.status_code}")
                except Exception as e:
                    self.log(f"❌ API测试失败: {e}")
                
                self.log("\n✅ 服务器问题修复完成")
                
            except Exception as e:
                self.log(f"❌ 修复服务器问题失败: {e}")
        
        # 在后台线程运行修复
        thread = threading.Thread(target=fix_thread, daemon=True)
        thread.start()

def main():
    """主函数"""
    checker = ServerChecker()
    root = checker.create_gui()
    root.mainloop()

if __name__ == "__main__":
    main()
