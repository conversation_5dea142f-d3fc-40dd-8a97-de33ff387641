@echo off
chcp 65001 >nul
title 🚀 部署完整license_server.py

echo.
echo ==========================================
echo 🚀 部署完整的license_server.py
echo ==========================================
echo.
echo 🔍 问题确认:
echo • ❌ 服务器上的license_server.py是空文件 (0字节)
echo • ✅ 本地license_server.py功能完整 (30,460字符)
echo • ✅ 包含所有许可证管理API接口
echo • ❌ 当前运行的simple_server.py功能不完整
echo.
echo 🎯 解决方案:
echo • 🛑 停止当前simple_server.py服务
echo • 📤 上传完整的license_server.py
echo • ⚙️ 更新systemd配置
echo • 🚀 启动完整的许可证服务器
echo • 🧪 测试所有API接口
echo.
echo 📋 部署后license_manager.py将能够:
echo • ✅ 生成激活码
echo • ✅ 查询激活码列表
echo • ✅ 管理激活码权限
echo • ✅ 删除激活码
echo • ✅ 所有许可证管理功能
echo.

echo 🚀 开始部署完整服务器...
echo.

REM 运行部署脚本
python "部署完整license_server.py"

echo.
echo 👋 部署完成
pause
