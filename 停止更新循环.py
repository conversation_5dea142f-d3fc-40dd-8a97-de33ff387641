#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停止更新循环 - 立即解决一直更新的问题
"""

import re
import shutil
from datetime import datetime

def fix_version_mismatch():
    """修复版本不匹配问题"""
    try:
        print("🔧 修复版本不匹配问题...")
        
        # 服务器最新版本是 2.1.2，客户端是 2.1.1
        # 解决方案：将客户端版本更新为 2.1.2
        
        target_version = "2.1.2"  # 与服务器版本一致
        
        # 备份原文件
        backup_name = f"license_client.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2('license_client.py', backup_name)
        print(f"✅ 已备份原文件: {backup_name}")
        
        # 读取文件
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复所有版本号为 2.1.2
        print(f"🔄 将所有版本号更新为: {target_version}")
        
        # 修复程序版本号
        content = re.sub(
            r'current_version\s*=\s*["\'][^"\']+["\']',
            f'current_version = "{target_version}"',
            content
        )
        
        # 修复界面显示版本
        content = re.sub(
            r'蓝图工具\s*v[0-9.]+',
            f'蓝图工具 v{target_version}',
            content
        )
        
        # 修复客户端版本
        content = re.sub(
            r'"client_version":\s*"[^"]*"',
            f'"client_version": "{target_version}"',
            content
        )
        
        # 保存修复后的文件
        with open('license_client.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 版本号已更新为: {target_version}")
        
        # 验证修复结果
        with open('license_client.py', 'r', encoding='utf-8') as f:
            verify_content = f.read()
        
        current_versions = re.findall(r'current_version\s*=\s*["\']([^"\']+)["\']', verify_content)
        display_versions = re.findall(r'蓝图工具\s*v([0-9.]+)', verify_content)
        client_versions = re.findall(r'"client_version":\s*"([^"]*)"', verify_content)
        
        print("\n🔍 验证修复结果:")
        print(f"   程序版本: {current_versions}")
        print(f"   显示版本: {display_versions}")
        print(f"   客户端版本: {client_versions}")
        
        # 检查是否都是目标版本
        all_versions = current_versions + display_versions + client_versions
        all_correct = all(v == target_version for v in all_versions)
        
        if all_correct and all_versions:
            print("✅ 所有版本号已统一!")
            return True
        else:
            print("❌ 仍有版本号不一致")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def disable_auto_update():
    """临时禁用自动更新"""
    try:
        print("\n🛑 临时禁用自动更新...")
        
        # 备份auto_updater.py
        if os.path.exists('auto_updater.py'):
            shutil.copy2('auto_updater.py', 'auto_updater.py.backup_disable')
            
            with open('auto_updater.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 在check_and_update函数开头添加返回False
            if 'def check_and_update(' in content:
                content = content.replace(
                    'def check_and_update(',
                    'def check_and_update_disabled(',
                    1
                )
                
                # 添加新的禁用版本
                new_function = '''def check_and_update(parent_window, current_version, license_key=None, device_id=None):
    """
    检查更新 - 临时禁用版本
    """
    print("🛑 自动更新已临时禁用")
    return False

'''
                
                # 在原函数前插入新函数
                insert_pos = content.find('def check_and_update_disabled(')
                if insert_pos != -1:
                    content = content[:insert_pos] + new_function + content[insert_pos:]
                
                with open('auto_updater.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ 自动更新已临时禁用")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ 禁用自动更新失败: {e}")
        return False

def create_no_update_version():
    """创建无更新版本的license_client.py"""
    try:
        print("\n🚫 创建无更新版本...")
        
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 注释掉自动更新检查
        content = content.replace(
            'self.check_for_updates()',
            '# self.check_for_updates()  # 临时禁用自动更新'
        )
        
        # 注释掉手动更新检查
        content = content.replace(
            'check_and_update(self.root, current_version, license_key, device_id)',
            '# check_and_update(self.root, current_version, license_key, device_id)  # 临时禁用'
        )
        
        # 保存无更新版本
        with open('license_client_no_update.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已创建无更新版本: license_client_no_update.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建无更新版本失败: {e}")
        return False

def main():
    """主函数"""
    print("🛑 停止更新循环工具")
    print("=" * 50)
    print("问题: 程序一直在更新")
    print("原因: 客户端版本(2.1.1) < 服务器版本(2.1.2)")
    print("=" * 50)
    
    print("\n🎯 解决方案选择:")
    print("1. 修复版本号匹配 (推荐)")
    print("2. 临时禁用自动更新")
    print("3. 创建无更新版本")
    
    # 方案1: 修复版本号
    print("\n🔧 执行方案1: 修复版本号匹配...")
    if fix_version_mismatch():
        print("✅ 方案1执行成功!")
        print("💡 现在客户端版本与服务器版本一致，不会再更新")
    else:
        print("❌ 方案1执行失败")
        
        # 方案2: 禁用自动更新
        print("\n🛑 执行方案2: 临时禁用自动更新...")
        disable_auto_update()
    
    # 方案3: 创建无更新版本
    print("\n🚫 执行方案3: 创建无更新版本...")
    create_no_update_version()
    
    print("\n" + "=" * 50)
    print("🎉 处理完成!")
    print("=" * 50)
    
    print("\n📋 使用说明:")
    print("1. ✅ license_client.py - 版本已更新为2.1.2，不会再更新")
    print("2. 🚫 license_client_no_update.py - 完全禁用更新的版本")
    print("3. 🛑 auto_updater.py - 自动更新已临时禁用")
    
    print("\n🚀 测试方法:")
    print("1. 运行 python license_client.py")
    print("2. 检查是否还会弹出更新提示")
    print("3. 如果不再更新，问题解决!")
    
    print("\n⚠️ 注意:")
    print("- 如果需要恢复自动更新，使用备份文件")
    print("- 版本号现在与服务器一致，不会再循环更新")

if __name__ == "__main__":
    import os
    main()
