@echo off
chcp 65001 >nul
title 🔧 快速修复Flask路由问题

echo.
echo ==========================================
echo 🔧 快速修复Flask路由问题
echo ==========================================
echo.
echo 🔍 当前问题:
echo • ❌ 访问根路径显示 "Not Found"
echo • ❌ 原始Flask应用路由配置复杂
echo • ⚠️ 端口44285而不是标准5000
echo.
echo 🎯 解决方案:
echo • 📝 创建简单的测试服务器
echo • 🔧 配置基本路由 (/, /health, /test等)
echo • 🌐 使用标准端口5000
echo • ⚙️ 更新systemd服务配置
echo • 🧪 测试所有接口
echo.

echo 🔧 开始快速修复...
echo.

REM 运行修复脚本
python "快速修复路由.py"

echo.
echo 👋 修复完成
pause
