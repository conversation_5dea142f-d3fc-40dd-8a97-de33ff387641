# 亚马逊蓝图工具 - 自动更新系统使用说明

## 📋 概述

自动更新系统已经完成部署，包含以下组件：

### 🖥️ 服务器端 (已部署)
- **服务器地址**: `http://198.23.135.176:5000`
- **API路径**: `/update/*` (新路径，避免冲突)
- **文件存储**: `/opt/license_manager/updates/`

### 💻 客户端工具
- **exe文件管理工具.py**: 图形界面管理工具
- **测试更新API.py**: API测试工具
- **客户端更新配置.py**: 更新功能代码示例

## 🚀 快速开始

### 1. 测试服务器连接
```bash
python 测试更新API.py
```

### 2. 启动管理工具
```bash
python 启动exe管理工具.py
```

### 3. 上传更新文件
1. 打开管理工具
2. 选择要上传的exe文件
3. 输入版本号 (如: 2.1.1)
4. 填写更新说明
5. 点击上传

## 🔧 API接口说明

### 检查更新
```
GET /update/check
参数:
- current_version: 当前版本号
- key: 授权码 (可选)
- device_id: 设备ID (可选)
```

### 下载更新
```
GET /update/download
参数:
- version: 版本号 (可选，默认最新版本)
- key: 授权码 (可选)
- device_id: 设备ID (可选)
```

### 上传更新 (管理员)
```
POST /update/upload
文件: file (exe文件)
参数:
- version: 版本号
- changelog: 更新说明
```

### 获取统计
```
GET /update/stats
返回: 当前版本信息和可用文件列表
```

## 💡 在exe程序中集成自动更新

### 方法1: 使用提供的更新器类
```python
from 客户端更新配置 import AutoUpdater

# 创建更新器
updater = AutoUpdater(
    server_url="http://198.23.135.176:5000",
    license_key="your_license_key",
    device_id="your_device_id"
)

# 检查更新
result = updater.check_for_updates("2.1.0")
if result['success'] and result['has_update']:
    print(f"发现新版本: {result['latest_version']}")
    
    # 下载更新
    download_result = updater.download_update()
    if download_result['success']:
        print("下载完成，可以安装更新")
```

### 方法2: 直接使用requests
```python
import requests

# 检查更新
response = requests.get(
    "http://198.23.135.176:5000/update/check",
    params={"current_version": "2.1.0"}
)

if response.status_code == 200:
    data = response.json()
    if data.get('has_update'):
        print(f"有新版本: {data.get('latest_version')}")
```

## 📁 文件结构

```
服务器端:
/opt/license_manager/
├── license_server.py          # 主服务器程序
├── updates/                   # 更新文件目录
│   ├── version_info.json     # 版本信息
│   └── files/                # exe文件存储
│       └── amazon_blueprint_v2.1.1.exe
└── license_database.json     # 授权数据库

客户端:
├── exe文件管理工具.py         # 管理界面
├── 测试更新API.py            # API测试
├── 客户端更新配置.py          # 更新代码示例
└── 启动exe管理工具.py         # 启动脚本
```

## 🔍 故障排除

### 1. 连接失败
- 检查服务器是否运行: `systemctl status license-manager`
- 检查防火墙设置
- 确认端口5000是否开放

### 2. 上传失败
- 检查文件大小限制
- 确认版本号格式正确
- 查看服务器日志: `journalctl -u license-manager -f`

### 3. API返回错误
- 检查授权码是否有效
- 确认设备ID匹配
- 验证版本号格式

## 📊 监控和维护

### 查看服务状态
```bash
systemctl status license-manager
```

### 查看日志
```bash
journalctl -u license-manager -f
```

### 重启服务
```bash
systemctl restart license-manager
```

### 备份更新文件
```bash
cp -r /opt/license_manager/updates /backup/updates_$(date +%Y%m%d)
```

## 🎯 最佳实践

1. **版本号规范**: 使用语义化版本号 (如: 2.1.0, 2.1.1)
2. **更新说明**: 详细描述更新内容和修复的问题
3. **测试验证**: 上传前在测试环境验证exe文件
4. **备份策略**: 定期备份更新文件和版本信息
5. **监控日志**: 定期检查服务器日志确保正常运行

## 📞 技术支持

如果遇到问题，请：
1. 运行 `测试更新API.py` 检查连接
2. 查看服务器日志获取错误信息
3. 确认文件权限和目录结构正确

---

**注意**: 请确保服务器安全，定期更新系统和检查访问日志。
