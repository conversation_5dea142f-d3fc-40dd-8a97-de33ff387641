#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的更新功能测试
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time

def show_update_dialog():
    """显示更新对话框"""
    # 创建对话框
    dialog = tk.Toplevel()
    dialog.title("发现新版本")
    dialog.geometry("450x400")
    dialog.resizable(False, False)
    dialog.grab_set()
    
    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
    y = (dialog.winfo_screenheight() // 2) - (400 // 2)
    dialog.geometry(f"+{x}+{y}")
    
    # 标题
    title_label = tk.Label(
        dialog,
        text="🎉 发现新版本！",
        font=("微软雅黑", 14, "bold"),
        fg="#2c3e50"
    )
    title_label.pack(pady=20)
    
    # 版本信息
    info_frame = tk.Frame(dialog)
    info_frame.pack(fill=tk.X, padx=20, pady=10)
    
    current_label = tk.Label(
        info_frame,
        text="当前版本: v2.1.0",
        font=("微软雅黑", 10)
    )
    current_label.pack(anchor='w')
    
    latest_label = tk.Label(
        info_frame,
        text="最新版本: v2.1.1",
        font=("微软雅黑", 10, "bold"),
        fg="#e74c3c"
    )
    latest_label.pack(anchor='w')
    
    # 更新说明
    changelog_label = tk.Label(
        info_frame,
        text="更新内容:",
        font=("微软雅黑", 10, "bold")
    )
    changelog_label.pack(anchor='w', pady=(10, 5))
    
    changelog_text = tk.Text(
        info_frame,
        height=8,
        width=45,
        font=("微软雅黑", 9),
        wrap=tk.WORD,
        bg="#f8f9fa",
        relief=tk.SUNKEN,
        bd=1
    )
    changelog_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    changelog_text.insert(tk.END, "• 修复了已知问题\n• 优化了程序性能\n• 增加了新功能")
    changelog_text.config(state=tk.DISABLED)
    
    # 按钮区域
    button_frame = tk.Frame(dialog, bg="#ffffff")
    button_frame.pack(fill=tk.X, padx=20, pady=(10, 20), side=tk.BOTTOM)
    
    # 按钮容器
    button_container = tk.Frame(button_frame, bg="#ffffff")
    button_container.pack(expand=True)
    
    def on_update():
        messagebox.showinfo("更新", "开始下载更新...")
        dialog.destroy()
    
    def on_later():
        messagebox.showinfo("提醒", "稍后提醒更新")
        dialog.destroy()
    
    # 立即更新按钮
    update_button = tk.Button(
        button_container,
        text="🚀 立即更新",
        command=on_update,
        bg="#27ae60",
        fg="white",
        font=("微软雅黑", 11, "bold"),
        padx=25,
        pady=8,
        relief=tk.RAISED,
        bd=2,
        cursor="hand2"
    )
    update_button.pack(side=tk.LEFT, padx=(0, 15))
    
    # 稍后提醒按钮
    later_button = tk.Button(
        button_container,
        text="⏰ 稍后提醒",
        command=on_later,
        bg="#95a5a6",
        fg="white",
        font=("微软雅黑", 10),
        padx=25,
        pady=8,
        relief=tk.RAISED,
        bd=2,
        cursor="hand2"
    )
    later_button.pack(side=tk.LEFT)

def main():
    """主函数"""
    root = tk.Tk()
    root.title("更新功能测试")
    root.geometry("300x200")
    
    # 测试按钮
    test_button = tk.Button(
        root,
        text="🧪 测试更新对话框",
        command=show_update_dialog,
        font=("微软雅黑", 12),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10
    )
    test_button.pack(expand=True)
    
    root.mainloop()

if __name__ == "__main__":
    main()
