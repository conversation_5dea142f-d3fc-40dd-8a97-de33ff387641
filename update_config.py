#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新系统配置文件
"""

from datetime import datetime

# 服务器配置
SERVER_CONFIG = {
    # 授权服务器URL（用于新的API方式）
    "license_server_url": "http://**************:5000/",

    # 静态更新服务器URL（用于向后兼容）
    "update_server_url": "http://**************:5000/api/",

    # 当前版本
    "current_version": "2.1.0",

    # 更新检查间隔（秒）
    "check_interval": 3600,  # 1小时

    # 是否启用自动更新检查
    "auto_check_enabled": True,  # 启用自动更新

    # 是否启用强制更新
    "force_update_enabled": False,

    # 下载超时时间（秒）
    "download_timeout": 300,  # 5分钟

    # 重试次数
    "max_retries": 3,
}

# 开发环境配置
DEV_CONFIG = {
    "license_server_url": "http://localhost:5000/",
    "update_server_url": "http://localhost:8000/updates/",
    "current_version": "2.1.8",
    "check_interval": 60,  # 1分钟
    "auto_check_enabled": False,  # 已禁用自动更新
    "force_update_enabled": False,
    "download_timeout": 60,
    "max_retries": 1,
}

# 测试环境配置
TEST_CONFIG = {
    "license_server_url": "https://test-server.com/",
    "update_server_url": "https://test-server.com/updates/",
    "current_version": "2.1.8",
    "check_interval": 300,  # 5分钟
    "auto_check_enabled": False,  # 已禁用自动更新
    "force_update_enabled": False,
    "download_timeout": 120,
    "max_retries": 2,
}

def get_external_version():
    """
    从外部配置文件获取当前版本号（兼容exe环境）

    Returns:
        str: 当前版本号
    """
    try:
        import os
        import json

        config_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
        external_config_file = os.path.join(config_dir, "version_config.json")

        if os.path.exists(external_config_file):
            with open(external_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return config_data.get("current_version", SERVER_CONFIG["current_version"])
    except:
        pass

    # 如果外部配置文件不存在或读取失败，返回默认版本
    return SERVER_CONFIG["current_version"]

def get_config(environment="production"):
    """
    获取指定环境的配置

    Args:
        environment: 环境名称 ("production", "development", "test")

    Returns:
        dict: 配置字典
    """
    if environment == "development":
        config = DEV_CONFIG.copy()
    elif environment == "test":
        config = TEST_CONFIG.copy()
    else:
        config = SERVER_CONFIG.copy()

    # 使用外部配置文件的版本号（如果存在）
    config["current_version"] = get_external_version()
    return config

def update_config(environment="production", **kwargs):
    """
    更新配置

    Args:
        environment: 环境名称
        **kwargs: 要更新的配置项
    """
    config = get_config(environment)
    config.update(kwargs)
    return config

def update_version(new_version):
    """
    更新版本号到外部配置文件（兼容exe环境）

    Args:
        new_version: 新版本号
    """
    try:
        import os
        import json

        # 使用外部配置文件，兼容exe环境
        config_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
        os.makedirs(config_dir, exist_ok=True)

        external_config_file = os.path.join(config_dir, "version_config.json")
        print(f"外部配置文件路径: {external_config_file}")

        # 读取或创建外部配置文件
        try:
            if os.path.exists(external_config_file):
                with open(external_config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            else:
                config_data = {}
        except:
            config_data = {}

        # 更新版本号
        old_version = config_data.get("current_version", "unknown")
        config_data["current_version"] = new_version
        config_data["last_updated"] = datetime.now().isoformat()

        print(f"版本号更新: {old_version} -> {new_version}")

        # 保存外部配置文件
        with open(external_config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

        # 更新全局变量
        global CURRENT_VERSION
        CURRENT_VERSION = new_version

        print(f"版本号已更新为: {new_version}")
        return True

    except Exception as e:
        print(f"更新版本号失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_last_update_info():
    """获取最后更新信息"""
    try:
        import os
        import json
        update_info_file = os.path.join(os.path.expanduser("~"), ".amazon_last_update.json")

        if os.path.exists(update_info_file):
            with open(update_info_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except:
        pass
    return {}

def save_update_info(version, timestamp=None):
    """保存更新信息"""
    try:
        import os
        import json
        from datetime import datetime

        update_info_file = os.path.join(os.path.expanduser("~"), ".amazon_last_update.json")

        update_info = {
            "version": version,
            "timestamp": timestamp or datetime.now().isoformat(),
            "updated": True
        }

        with open(update_info_file, 'w', encoding='utf-8') as f:
            json.dump(update_info, f, indent=2, ensure_ascii=False)

        return True
    except Exception as e:
        print(f"保存更新信息失败: {e}")
        return False

# 导出常用配置
CURRENT_CONFIG = get_config()
LICENSE_SERVER_URL = CURRENT_CONFIG["license_server_url"]
UPDATE_SERVER_URL = CURRENT_CONFIG["update_server_url"]
CURRENT_VERSION = CURRENT_CONFIG["current_version"]
