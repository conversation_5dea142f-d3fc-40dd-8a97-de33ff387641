#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新系统配置文件
"""

# 服务器配置
SERVER_CONFIG = {
    # 授权服务器URL（用于新的API方式）
    "license_server_url": "http://198.23.135.176:5000/",

    # 静态更新服务器URL（用于向后兼容）
    "update_server_url": "http://198.23.135.176:5000/api/",

    # 当前版本
    "current_version": "2.1.0",

    # 更新检查间隔（秒）
    "check_interval": 3600,  # 1小时

    # 是否启用自动更新检查
    "auto_check_enabled": False,  # 禁用自动更新，避免重复更新循环

    # 是否启用强制更新
    "force_update_enabled": False,

    # 下载超时时间（秒）
    "download_timeout": 300,  # 5分钟

    # 重试次数
    "max_retries": 3,
}

# 开发环境配置
DEV_CONFIG = {
    "license_server_url": "http://localhost:5000/",
    "update_server_url": "http://localhost:8000/updates/",
    "current_version": "2.1.8",
    "check_interval": 60,  # 1分钟
    "auto_check_enabled": False,  # 已禁用自动更新
    "force_update_enabled": False,
    "download_timeout": 60,
    "max_retries": 1,
}

# 测试环境配置
TEST_CONFIG = {
    "license_server_url": "https://test-server.com/",
    "update_server_url": "https://test-server.com/updates/",
    "current_version": "2.1.8",
    "check_interval": 300,  # 5分钟
    "auto_check_enabled": False,  # 已禁用自动更新
    "force_update_enabled": False,
    "download_timeout": 120,
    "max_retries": 2,
}

def get_config(environment="production"):
    """
    获取指定环境的配置
    
    Args:
        environment: 环境名称 ("production", "development", "test")
    
    Returns:
        dict: 配置字典
    """
    if environment == "development":
        return DEV_CONFIG
    elif environment == "test":
        return TEST_CONFIG
    else:
        return SERVER_CONFIG

def update_config(environment="production", **kwargs):
    """
    更新配置

    Args:
        environment: 环境名称
        **kwargs: 要更新的配置项
    """
    config = get_config(environment)
    config.update(kwargs)
    return config

def update_version(new_version):
    """
    更新版本号到配置文件

    Args:
        new_version: 新版本号
    """
    try:
        import os
        import re

        # 获取配置文件的绝对路径
        config_file = os.path.abspath("update_config.py")
        print(f"配置文件路径: {config_file}")

        # 读取当前配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找原版本号
        version_pattern = r'"current_version":\s*"([^"]*)"'
        original_match = re.search(version_pattern, content)
        if original_match:
            print(f"原内容中的版本号: {original_match.group(1)}")

        # 替换版本号
        new_content = re.sub(
            r'"current_version":\s*"[^"]*"',
            f'"current_version": "{new_version}"',
            content
        )

        # 查找新版本号
        new_match = re.search(version_pattern, new_content)
        if new_match:
            print(f"替换后的版本号: {new_match.group(1)}")

        # 检查是否有变化
        if new_content != content:
            # 写回文件
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"版本号已更新为: {new_version}")
        else:
            print("内容没有变化")

        # 更新全局变量
        global CURRENT_VERSION
        CURRENT_VERSION = new_version

        return True
    except Exception as e:
        print(f"更新版本号失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_last_update_info():
    """获取最后更新信息"""
    try:
        import os
        import json
        update_info_file = os.path.join(os.path.expanduser("~"), ".amazon_last_update.json")

        if os.path.exists(update_info_file):
            with open(update_info_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except:
        pass
    return {}

def save_update_info(version, timestamp=None):
    """保存更新信息"""
    try:
        import os
        import json
        from datetime import datetime

        update_info_file = os.path.join(os.path.expanduser("~"), ".amazon_last_update.json")

        update_info = {
            "version": version,
            "timestamp": timestamp or datetime.now().isoformat(),
            "updated": True
        }

        with open(update_info_file, 'w', encoding='utf-8') as f:
            json.dump(update_info, f, indent=2, ensure_ascii=False)

        return True
    except Exception as e:
        print(f"保存更新信息失败: {e}")
        return False

# 导出常用配置
CURRENT_CONFIG = get_config()
LICENSE_SERVER_URL = CURRENT_CONFIG["license_server_url"]
UPDATE_SERVER_URL = CURRENT_CONFIG["update_server_url"]
CURRENT_VERSION = CURRENT_CONFIG["current_version"]
