#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

# 测试管理员权限
print("测试管理员权限...")
try:
    response = requests.get(
        "http://198.23.135.176:5000/update/check",
        params={
            "current_version": "2.0.0",
            "key": "ADMIN_BYPASS",
            "device_id": "ADMIN-DEVICE-001"
        },
        timeout=10
    )
    print(f"管理员测试 - 状态码: {response.status_code}")
    print(f"管理员测试 - 响应: {response.text}")
except Exception as e:
    print(f"管理员测试失败: {e}")

print("\n" + "="*50 + "\n")

# 测试用户权限
print("测试用户权限...")
try:
    response = requests.get(
        "http://198.23.135.176:5000/update/check",
        params={
            "current_version": "2.0.0",
            "key": "83R2AXQK-20250725-67c80d8d",
            "device_id": "20cc47fd9ca63e67"
        },
        timeout=10
    )
    print(f"用户测试 - 状态码: {response.status_code}")
    print(f"用户测试 - 响应: {response.text}")
except Exception as e:
    print(f"用户测试失败: {e}")

print("\n" + "="*50 + "\n")

# 测试根路径
print("测试根路径...")
try:
    response = requests.get("http://198.23.135.176:5000/", timeout=5)
    print(f"根路径测试 - 状态码: {response.status_code}")
    print(f"根路径测试 - 响应: {response.text[:200]}...")
except Exception as e:
    print(f"根路径测试失败: {e}")

input("\n按回车键退出...")
