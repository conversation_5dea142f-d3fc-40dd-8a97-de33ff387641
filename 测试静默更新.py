#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试静默更新功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_silent_update():
    """测试静默更新功能"""
    print("🧪 测试静默更新功能")
    print("=" * 50)
    
    try:
        from auto_updater import check_and_update_silent
        
        print("✅ 成功导入 check_and_update_silent")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试静默更新")
        root.geometry("400x250")
        
        # 设置图标
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        # 创建界面
        title_label = tk.Label(
            root,
            text="测试静默更新功能",
            font=("微软雅黑", 14, "bold"),
            pady=20
        )
        title_label.pack()
        
        info_label = tk.Label(
            root,
            text="静默更新特点：\n• 只显示进度条，无其他弹窗\n• 无需用户确认，自动开始\n• 更新完成后自动重启程序\n• 整个过程静默进行",
            font=("微软雅黑", 10),
            fg="#666666",
            justify="left"
        )
        info_label.pack(pady=10)
        
        def start_silent_update():
            """开始静默更新测试"""
            print("🚀 开始静默更新测试...")
            
            try:
                # 使用管理员权限进行测试
                result = check_and_update_silent(
                    parent_window=root,
                    current_version="2.1.0",
                    license_key="ADMIN_BYPASS",
                    device_id="ADMIN-DEVICE-001"
                )
                
                print(f"📊 静默更新结果: {result}")
                
                if result:
                    print("✅ 静默更新已启动")
                    # 注意：如果有更新，程序会自动重启，这里的代码可能不会执行
                else:
                    print("ℹ️ 没有可用更新")
                    messagebox.showinfo("信息", "没有可用更新")
                    
            except Exception as e:
                error_msg = f"❌ 静默更新测试失败: {str(e)}"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
        
        def start_normal_update():
            """开始普通更新测试（对比）"""
            print("🚀 开始普通更新测试...")
            
            try:
                from auto_updater import check_and_update
                
                result = check_and_update(
                    parent_window=root,
                    current_version="2.1.0",
                    license_key="ADMIN_BYPASS",
                    device_id="ADMIN-DEVICE-001"
                )
                
                print(f"📊 普通更新结果: {result}")
                
                if result:
                    print("✅ 普通更新已完成")
                else:
                    print("ℹ️ 没有更新或用户取消")
                    messagebox.showinfo("信息", "没有更新或用户取消了更新")
                    
            except Exception as e:
                error_msg = f"❌ 普通更新测试失败: {str(e)}"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
        
        # 创建按钮
        button_frame = tk.Frame(root)
        button_frame.pack(pady=20)
        
        silent_button = tk.Button(
            button_frame,
            text="🔇 静默更新测试",
            command=start_silent_update,
            font=("微软雅黑", 12),
            bg="#e74c3c",
            fg="white",
            padx=20,
            pady=10
        )
        silent_button.pack(side=tk.LEFT, padx=10)
        
        normal_button = tk.Button(
            button_frame,
            text="🔊 普通更新测试",
            command=start_normal_update,
            font=("微软雅黑", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        normal_button.pack(side=tk.LEFT, padx=10)
        
        # 说明标签
        note_label = tk.Label(
            root,
            text="注意：静默更新会在有更新时自动重启程序",
            font=("微软雅黑", 9),
            fg="#e74c3c"
        )
        note_label.pack(pady=10)
        
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"❌ 导入失败: {e}"
        print(error_msg)
        messagebox.showerror("错误", error_msg)
    except Exception as e:
        error_msg = f"❌ 测试失败: {e}"
        print(error_msg)
        messagebox.showerror("错误", error_msg)

def test_client_silent_update():
    """测试客户端静默更新"""
    print("\n🧪 测试客户端静默更新")
    print("=" * 50)
    
    try:
        # 模拟license_client.py的更新调用
        import license_client
        
        # 创建一个简单的窗口来测试
        root = tk.Tk()
        root.title("客户端静默更新测试")
        root.geometry("300x150")
        
        def test_client_update():
            """测试客户端更新"""
            try:
                # 这里模拟license_client.py中的更新调用
                from auto_updater import check_and_update_silent
                
                result = check_and_update_silent(
                    parent_window=root,
                    current_version="2.1.0",
                    license_key="QAFJZYZN-20250830-70c8b0cc",  # 使用真实的激活码
                    device_id="20cc47fd9ca63e67"  # 使用真实的设备ID
                )
                
                if result:
                    print("✅ 客户端静默更新已启动")
                else:
                    print("ℹ️ 没有可用更新")
                    messagebox.showinfo("信息", "没有可用更新")
                    
            except Exception as e:
                error_msg = f"❌ 客户端更新失败: {e}"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
        
        tk.Label(root, text="测试客户端静默更新", font=("微软雅黑", 12)).pack(pady=20)
        tk.Button(root, text="开始测试", command=test_client_update, font=("微软雅黑", 10)).pack(pady=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")

if __name__ == "__main__":
    print("🔧 静默更新功能测试")
    print("=" * 60)
    
    # 选择测试模式
    if len(sys.argv) > 1 and sys.argv[1] == "client":
        test_client_silent_update()
    else:
        test_silent_update()
