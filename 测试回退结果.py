#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试license_client.py回退结果
"""

import tkinter as tk
from tkinter import messagebox

def main():
    """测试回退结果"""
    print("🔄 license_client.py 回退验证")
    print("=" * 50)
    
    # 检查导入
    try:
        from auto_updater import check_and_update
        print("✅ 成功导入标准更新函数")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("回退测试")
        root.geometry("400x300")
        
        # 设置图标
        try:
            root.iconbitmap("icon.ico")
            print("✅ 图标设置成功")
        except:
            print("⚠️ 图标设置失败（但不影响功能）")
        
        def test_update():
            """测试更新功能"""
            print("🧪 测试更新功能...")
            result = check_and_update(
                parent_window=root,
                current_version="2.1.0",
                license_key="ADMIN_BYPASS",
                device_id="ADMIN-DEVICE-001"
            )
            
            if result:
                messagebox.showinfo("测试结果", "更新功能正常工作")
            else:
                messagebox.showinfo("测试结果", "没有可用更新或更新被取消")
        
        # 创建界面
        title_label = tk.Label(
            root,
            text="license_client.py 回退测试",
            font=("微软雅黑", 14, "bold"),
            pady=20
        )
        title_label.pack()
        
        status_label = tk.Label(
            root,
            text="✅ 已回退到标准更新版本\n现在使用带确认对话框的更新方式",
            font=("微软雅黑", 10),
            fg="#27ae60",
            justify=tk.CENTER
        )
        status_label.pack(pady=20)
        
        test_button = tk.Button(
            root,
            text="🧪 测试更新功能",
            command=test_update,
            font=("微软雅黑", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        test_button.pack(pady=10)
        
        launch_button = tk.Button(
            root,
            text="🚀 启动 license_client.py",
            command=lambda: [root.destroy(), __import__('os').system("python license_client.py")],
            font=("微软雅黑", 12),
            bg="#27ae60",
            fg="white",
            padx=20,
            pady=10
        )
        launch_button.pack(pady=10)
        
        info_label = tk.Label(
            root,
            text="回退说明:\n• 恢复了标准更新对话框\n• 用户可以选择是否更新\n• 图标功能保持不变",
            font=("微软雅黑", 9),
            fg="#666666",
            justify=tk.CENTER
        )
        info_label.pack(pady=20)
        
        print("🚀 测试界面已启动")
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        messagebox.showerror("错误", f"导入更新模块失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        messagebox.showerror("错误", f"测试失败: {e}")

if __name__ == "__main__":
    main()
