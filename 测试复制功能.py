#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试build_gui_advanced.py的复制功能
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox

def test_copy_functionality():
    """测试复制功能"""
    print("🧪 测试复制功能")
    print("=" * 50)
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("复制功能测试")
    root.geometry("600x400")
    
    # 创建文本框
    text_widget = scrolledtext.ScrolledText(root, height=15, state='normal')
    text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 添加测试文本
    test_text = """🔧 构建日志测试文本
[12:29:02] INFO: 正在扫描依赖...
[12:29:02] INFO: 依赖扫描完成
[12:29:06] INFO: 开始构建...
[12:29:06] INFO: 执行命令: python -m PyInstaller --onefile --windowed
[12:29:07] ERROR: 构建失败: 'gbk' codec can't decode byte 0xa5
[12:30:02] INFO: 开始构建...
[12:30:03] ERROR: 构建失败: 'gbk' codec can't decode byte 0xa5
[12:30:08] INFO: 开始测试exe依赖...

这是一个测试文本，用于验证复制功能是否正常工作。
您可以：
1. 选中部分文本，然后右键复制
2. 使用Ctrl+A全选，然后Ctrl+C复制
3. 右键菜单选择"复制"或"全选"
4. 保存文本到文件
5. 清空文本内容

测试完成后请关闭此窗口。"""
    
    text_widget.insert('1.0', test_text)
    
    # 设置为只读但可复制
    text_widget.bind("<Key>", lambda e: "break" if e.keysym not in ['Control_L', 'Control_R', 'c', 'C', 'a', 'A'] else None)
    
    # 创建右键菜单
    def create_context_menu():
        context_menu = tk.Menu(root, tearoff=0)
        context_menu.add_command(label="📋 复制", command=lambda: copy_text())
        context_menu.add_command(label="📄 全选", command=lambda: select_all())
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ 清空", command=lambda: clear_text())
        
        def show_menu(event):
            context_menu.post(event.x_root, event.y_root)
        
        text_widget.bind("<Button-3>", show_menu)
    
    def copy_text():
        """复制文本"""
        try:
            selected_text = text_widget.selection_get()
            root.clipboard_clear()
            root.clipboard_append(selected_text)
            messagebox.showinfo("成功", "✅ 选中文本已复制到剪贴板")
        except tk.TclError:
            # 如果没有选中文本，复制全部
            all_text = text_widget.get('1.0', tk.END)
            root.clipboard_clear()
            root.clipboard_append(all_text)
            messagebox.showinfo("成功", "✅ 全部文本已复制到剪贴板")
    
    def select_all():
        """全选文本"""
        text_widget.tag_add(tk.SEL, "1.0", tk.END)
        text_widget.mark_set(tk.INSERT, "1.0")
        text_widget.see(tk.INSERT)
        messagebox.showinfo("成功", "✅ 已全选文本")
    
    def clear_text():
        """清空文本"""
        text_widget.delete('1.0', tk.END)
        messagebox.showinfo("成功", "✅ 文本已清空")
    
    # 创建右键菜单
    create_context_menu()
    
    # 添加说明标签
    info_label = tk.Label(root, text="💡 右键点击文本区域测试复制功能，或使用Ctrl+A和Ctrl+C", 
                         fg='blue', font=('Arial', 10))
    info_label.pack(pady=5)
    
    print("✅ 测试窗口已创建")
    print("📋 功能说明:")
    print("1. 右键点击文本区域查看菜单")
    print("2. 选中文本后右键复制")
    print("3. 使用Ctrl+A全选，Ctrl+C复制")
    print("4. 测试完成后关闭窗口")
    
    root.mainloop()
    return True

def main():
    """主函数"""
    try:
        test_copy_functionality()
        print("\n✅ 复制功能测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
