#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断更新循环问题 - 为什么下载重启后还是提示更新
"""

import os
import sys
import requests

def check_current_version():
    """检查当前程序版本"""
    try:
        # 检查license_client.py中的版本定义
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找版本号定义
        import re
        
        # 常见的版本号定义模式
        patterns = [
            r'current_version\s*=\s*["\']([^"\']+)["\']',
            r'version\s*=\s*["\']([^"\']+)["\']',
            r'__version__\s*=\s*["\']([^"\']+)["\']',
            r'VERSION\s*=\s*["\']([^"\']+)["\']'
        ]
        
        found_versions = []
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                found_versions.append(match)
        
        return found_versions
        
    except Exception as e:
        return [f"错误: {e}"]

def check_server_version():
    """检查服务器最新版本"""
    try:
        url = "http://198.23.135.176:5000/update/check"
        params = {
            'key': 'ADMIN_BYPASS',
            'device_id': 'ADMIN-DEVICE-001',
            'current_version': '2.1.0'  # 用一个旧版本来查询
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        return data
        
    except Exception as e:
        return {"error": str(e)}

def check_version_comparison():
    """检查版本比较逻辑"""
    try:
        from auto_updater import AutoUpdater
        
        # 创建实例
        updater = AutoUpdater(
            current_version="2.1.0",
            license_server_url="http://198.23.135.176:5000",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        # 测试版本比较
        test_cases = [
            ("2.1.0", "2.1.1"),
            ("2.1.1", "2.1.1"),
            ("2.1.1", "2.1.0"),
            ("2.0.9", "2.1.0"),
        ]
        
        results = []
        for current, latest in test_cases:
            try:
                # 模拟版本比较
                updater.current_version = current
                is_newer = updater.is_newer_version(latest)
                results.append({
                    "current": current,
                    "latest": latest,
                    "is_newer": is_newer,
                    "should_update": is_newer
                })
            except Exception as e:
                results.append({
                    "current": current,
                    "latest": latest,
                    "error": str(e)
                })
        
        return results
        
    except Exception as e:
        return [{"error": str(e)}]

def check_file_info():
    """检查文件信息"""
    try:
        current_file = sys.argv[0] if sys.argv[0].endswith('.py') else 'license_client.py'
        
        info = {
            "current_script": current_file,
            "script_exists": os.path.exists(current_file),
            "script_size": os.path.getsize(current_file) if os.path.exists(current_file) else 0,
            "working_directory": os.getcwd(),
            "python_executable": sys.executable
        }
        
        # 检查是否有exe文件
        exe_files = [f for f in os.listdir('.') if f.endswith('.exe')]
        info["exe_files"] = exe_files
        
        return info
        
    except Exception as e:
        return {"error": str(e)}

def analyze_update_loop():
    """分析更新循环问题"""
    print("🔍 诊断更新循环问题")
    print("=" * 60)
    
    # 1. 检查当前版本
    print("1️⃣ 检查当前程序版本...")
    current_versions = check_current_version()
    print(f"   发现版本号: {current_versions}")
    
    # 2. 检查服务器版本
    print("\n2️⃣ 检查服务器最新版本...")
    server_info = check_server_version()
    if "error" in server_info:
        print(f"   ❌ 服务器检查失败: {server_info['error']}")
    else:
        print(f"   ✅ 服务器响应: {server_info}")
        if server_info.get('has_update'):
            print(f"   📦 最新版本: {server_info.get('version', 'Unknown')}")
        else:
            print("   ✅ 服务器认为当前已是最新版本")
    
    # 3. 检查版本比较逻辑
    print("\n3️⃣ 检查版本比较逻辑...")
    comparison_results = check_version_comparison()
    for result in comparison_results:
        if "error" in result:
            print(f"   ❌ 比较错误: {result['error']}")
        else:
            current = result['current']
            latest = result['latest']
            is_newer = result['is_newer']
            print(f"   {current} vs {latest}: {'需要更新' if is_newer else '无需更新'}")
    
    # 4. 检查文件信息
    print("\n4️⃣ 检查文件信息...")
    file_info = check_file_info()
    if "error" in file_info:
        print(f"   ❌ 文件检查失败: {file_info['error']}")
    else:
        print(f"   📁 当前脚本: {file_info['current_script']}")
        print(f"   📂 工作目录: {file_info['working_directory']}")
        print(f"   📄 exe文件: {file_info['exe_files']}")
    
    # 5. 分析问题
    print("\n" + "=" * 60)
    print("📋 问题分析")
    print("=" * 60)
    
    # 分析可能的原因
    possible_issues = []
    
    # 检查版本号是否更新
    if current_versions and any('2.1.0' in v for v in current_versions):
        possible_issues.append("🔴 版本号未更新 - 程序仍然认为自己是2.1.0")
    
    # 检查服务器响应
    if isinstance(server_info, dict) and server_info.get('has_update'):
        latest_version = server_info.get('version', '')
        if latest_version and any(latest_version not in v for v in current_versions):
            possible_issues.append(f"🔴 版本不匹配 - 服务器最新版本是{latest_version}")
    
    # 检查文件替换
    if isinstance(file_info, dict) and not file_info.get('exe_files'):
        possible_issues.append("🔴 没有exe文件 - 可能文件替换失败")
    
    if possible_issues:
        print("发现的问题:")
        for issue in possible_issues:
            print(f"  {issue}")
    else:
        print("✅ 未发现明显问题")
    
    return {
        "current_versions": current_versions,
        "server_info": server_info,
        "comparison_results": comparison_results,
        "file_info": file_info,
        "possible_issues": possible_issues
    }

def suggest_solutions(analysis_result):
    """建议解决方案"""
    print("\n" + "=" * 60)
    print("🛠️ 解决方案建议")
    print("=" * 60)
    
    issues = analysis_result.get('possible_issues', [])
    
    if any('版本号未更新' in issue for issue in issues):
        print("🔧 版本号问题解决方案:")
        print("1. 检查license_client.py中的current_version是否正确")
        print("2. 确认文件替换是否成功")
        print("3. 可能需要手动更新版本号")
    
    if any('版本不匹配' in issue for issue in issues):
        print("\n🔧 版本匹配问题解决方案:")
        print("1. 检查服务器上的版本信息是否正确")
        print("2. 确认下载的文件版本是否正确")
        print("3. 可能需要重新生成exe文件")
    
    if any('文件替换失败' in issue for issue in issues):
        print("\n🔧 文件替换问题解决方案:")
        print("1. 检查文件权限")
        print("2. 确认exe文件是否被正确下载")
        print("3. 手动检查临时文件夹")
    
    print("\n🚀 通用解决方案:")
    print("1. 手动修改license_client.py中的版本号为最新版本")
    print("2. 重新构建exe文件并上传到服务器")
    print("3. 清除可能的缓存文件")
    print("4. 检查auto_updater.py中的文件替换逻辑")

def main():
    """主函数"""
    print("🔍 更新循环问题诊断工具")
    print("=" * 60)
    
    # 执行分析
    analysis_result = analyze_update_loop()
    
    # 提供解决方案
    suggest_solutions(analysis_result)
    
    print("\n" + "=" * 60)
    print("📞 如需进一步帮助，请提供上述诊断信息")

if __name__ == "__main__":
    main()
