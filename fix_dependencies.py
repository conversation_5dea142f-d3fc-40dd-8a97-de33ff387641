#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖修复脚本 - 解决复杂的版本冲突
"""

import subprocess
import sys
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def run_pip_command(cmd, description=""):
    """运行pip命令"""
    try:
        logger.info(f"执行: {' '.join(cmd)} {description}")

        # 设置环境变量解决编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

        # 使用更安全的编码处理
        result = subprocess.run(cmd,
                              capture_output=True,
                              text=True,
                              encoding='utf-8',
                              errors='replace',
                              env=env,
                              check=True)
        logger.info(f"✓ 成功: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ 失败: {description}")
        # 安全地处理错误输出
        error_msg = e.stderr if e.stderr else str(e)
        logger.error(f"错误输出: {error_msg}")
        return False
    except UnicodeDecodeError as e:
        logger.warning(f"⚠ 编码警告: {description} (但可能已成功安装)")
        logger.warning(f"编码错误: {str(e)}")
        return True  # 假设安装成功，只是显示有问题

def uninstall_conflicting_packages():
    """卸载冲突的包"""
    logger.info("=== 第一步：卸载冲突的包 ===")
    
    packages_to_remove = [
        "amazoncaptcha",
        "pillow",
        "requests"
    ]
    
    for package in packages_to_remove:
        cmd = [sys.executable, "-m", "pip", "uninstall", package, "-y"]
        run_pip_command(cmd, f"卸载 {package}")

def install_core_dependencies():
    """安装核心依赖"""
    logger.info("\n=== 第二步：安装核心依赖 ===")
    
    core_deps = [
        "requests>=2.31.0",
        "urllib3>=2.0.0",
        "certifi>=2023.0.0",
        "charset-normalizer>=3.0.0",
        "idna>=3.4"
    ]
    
    for dep in core_deps:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        run_pip_command(cmd, f"安装 {dep}")

def install_data_processing():
    """安装数据处理库"""
    logger.info("\n=== 第三步：安装数据处理库 ===")
    
    data_deps = [
        "numpy>=1.24.0",
        "pandas>=2.0.0",
        "openpyxl>=3.1.0",
        "xlsxwriter>=3.1.0",
        "lxml>=4.9.0"
    ]
    
    for dep in data_deps:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        run_pip_command(cmd, f"安装 {dep}")

def install_web_scraping():
    """安装Web爬虫库"""
    logger.info("\n=== 第四步：安装Web爬虫库 ===")
    
    web_deps = [
        "beautifulsoup4>=4.12.0",
        "selenium>=4.15.0",
        "webdriver_manager>=4.0.0",
        "fake_useragent>=1.4.0"
    ]
    
    for dep in web_deps:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        run_pip_command(cmd, f"安装 {dep}")

def install_system_deps():
    """安装系统依赖"""
    logger.info("\n=== 第五步：安装系统依赖 ===")
    
    system_deps = [
        "psutil>=5.9.0",
        "cryptography>=41.0.0",
        "pillow>=10.0.0"
    ]
    
    for dep in system_deps:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        run_pip_command(cmd, f"安装 {dep}")

def install_build_tools():
    """安装构建工具"""
    logger.info("\n=== 第六步：安装构建工具 ===")
    
    build_deps = [
        "pyinstaller>=6.0.0"
    ]
    
    for dep in build_deps:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        run_pip_command(cmd, f"安装 {dep}")

def handle_amazoncaptcha():
    """处理amazoncaptcha的特殊安装"""
    logger.info("\n=== 第七步：尝试安装amazoncaptcha（可选）===")
    
    # 尝试安装兼容版本的amazoncaptcha
    logger.info("尝试安装amazoncaptcha的兼容版本...")
    
    # 首先降级requests到兼容版本
    cmd = [sys.executable, "-m", "pip", "install", "requests==2.30.0"]
    if run_pip_command(cmd, "降级requests到兼容版本"):
        # 然后尝试安装amazoncaptcha
        cmd = [sys.executable, "-m", "pip", "install", "amazoncaptcha>=0.5.0"]
        if run_pip_command(cmd, "安装amazoncaptcha"):
            logger.info("✓ amazoncaptcha安装成功")
        else:
            logger.warning("⚠ amazoncaptcha安装失败，但不影响主要功能")
            # 恢复requests版本
            cmd = [sys.executable, "-m", "pip", "install", "requests>=2.31.0"]
            run_pip_command(cmd, "恢复requests版本")
    else:
        logger.warning("⚠ 无法降级requests，跳过amazoncaptcha安装")

def verify_installation():
    """验证安装"""
    logger.info("\n=== 第八步：验证安装 ===")
    
    essential_packages = [
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('selenium', 'selenium'),
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('cryptography', 'cryptography'),
        ('pillow', 'PIL'),
        ('pyinstaller', 'PyInstaller')
    ]
    
    failed_imports = []
    
    for package_name, import_name in essential_packages:
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'Unknown')
            logger.info(f"✓ {package_name} ({version})")
        except ImportError:
            logger.error(f"✗ {package_name} 导入失败")
            failed_imports.append(package_name)
    
    # 检查可选包
    optional_packages = [
        ('amazoncaptcha', 'amazoncaptcha')
    ]
    
    for package_name, import_name in optional_packages:
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'Unknown')
            logger.info(f"✓ {package_name} ({version}) [可选]")
        except ImportError:
            logger.warning(f"⚠ {package_name} 未安装 [可选，不影响主要功能]")
    
    if failed_imports:
        logger.error(f"关键包导入失败: {', '.join(failed_imports)}")
        return False
    else:
        logger.info("所有关键包验证成功！")
        return True

def test_excel_functionality():
    """测试Excel功能"""
    logger.info("\n=== 第九步：测试Excel功能 ===")
    
    try:
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Product': ['Test1', 'Test2'],
            'Price': [10.99, 20.99]
        })
        
        # 测试Excel写入
        test_file = 'dependency_fix_test.xlsx'
        test_data.to_excel(test_file, index=False, engine='openpyxl')
        
        # 测试Excel读取
        read_data = pd.read_excel(test_file, engine='openpyxl')
        
        # 验证
        if len(read_data) == len(test_data):
            logger.info("✓ Excel读写功能正常")
            success = True
        else:
            logger.error("✗ Excel数据不一致")
            success = False
        
        # 清理
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return success
        
    except Exception as e:
        logger.error(f"✗ Excel功能测试失败: {str(e)}")
        return False

def create_fixed_requirements():
    """创建修复后的requirements文件"""
    logger.info("\n=== 第十步：创建修复后的requirements文件 ===")
    
    fixed_requirements = """# 亚马逊授权系统依赖包 - 修复版
# 核心依赖
selenium>=4.15.0
pandas>=2.0.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0
requests>=2.31.0
beautifulsoup4>=4.12.0
webdriver_manager>=4.0.0
psutil>=5.9.0
cryptography>=41.0.0
fake_useragent>=1.4.0

# Web爬虫和数据处理
lxml>=4.9.0
pillow>=10.0.0

# 开发和构建工具
pyinstaller>=6.0.0

# 基础依赖
numpy>=1.24.0
urllib3>=2.0.0
certifi>=2023.0.0
charset-normalizer>=3.0.0
idna>=3.4

# 注意：amazoncaptcha由于版本冲突已移除
# 如需使用验证码功能，请手动安装兼容版本
"""
    
    try:
        with open('requirements_fixed_final.txt', 'w', encoding='utf-8') as f:
            f.write(fixed_requirements)
        logger.info("✓ 创建修复后的requirements文件: requirements_fixed_final.txt")
        return True
    except Exception as e:
        logger.error(f"✗ 创建requirements文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始修复依赖冲突...")
    
    # 升级pip
    logger.info("升级pip...")
    run_pip_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], "升级pip")
    
    # 执行修复步骤
    steps = [
        uninstall_conflicting_packages,
        install_core_dependencies,
        install_data_processing,
        install_web_scraping,
        install_system_deps,
        install_build_tools,
        handle_amazoncaptcha,
        verify_installation,
        test_excel_functionality,
        create_fixed_requirements
    ]
    
    for step in steps:
        try:
            step()
        except Exception as e:
            logger.error(f"步骤执行失败: {str(e)}")
            continue
    
    logger.info("\n" + "=" * 60)
    logger.info("🎉 依赖修复完成！")
    logger.info("现在可以使用以下命令启动构建工具：")
    logger.info("python build_gui.py")
    logger.info("或者运行构建脚本：")
    logger.info("python build_license_system.py")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
