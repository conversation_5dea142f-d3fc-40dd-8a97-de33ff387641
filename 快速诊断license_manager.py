#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速诊断license_manager.py连接问题
"""

import requests
import json
import sys

def quick_diagnosis():
    """快速诊断license_manager.py连接"""
    print("🔍 快速诊断license_manager.py连接")
    print("=" * 50)
    
    server_url = "http://**************:5000"
    
    print(f"🌐 测试服务器: {server_url}")
    print()
    
    # 测试所有license_manager.py需要的接口
    test_cases = [
        {
            "name": "基本连接",
            "method": "GET",
            "url": f"{server_url}/",
            "description": "测试服务器是否响应"
        },
        {
            "name": "激活码列表",
            "method": "GET", 
            "url": f"{server_url}/license/list",
            "description": "license_manager.py启动时需要"
        },
        {
            "name": "生成激活码",
            "method": "POST",
            "url": f"{server_url}/license/generate",
            "data": {"expire_days": 1, "quantity": 1, "permission_level": 1},
            "description": "生成功能测试"
        },
        {
            "name": "激活码信息",
            "method": "GET",
            "url": f"{server_url}/license/info",
            "params": {"key": "test"},
            "description": "查询功能测试"
        }
    ]
    
    success_count = 0
    
    for i, test in enumerate(test_cases, 1):
        print(f"🧪 测试{i}: {test['name']}")
        print(f"   📋 {test['description']}")
        
        try:
            if test['method'] == 'GET':
                if 'params' in test:
                    response = requests.get(test['url'], params=test['params'], timeout=10)
                else:
                    response = requests.get(test['url'], timeout=10)
            else:  # POST
                response = requests.post(test['url'], json=test.get('data', {}), timeout=10)
            
            print(f"   📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 连接成功")
                try:
                    data = response.json()
                    if isinstance(data, dict):
                        if 'success' in data:
                            if data['success']:
                                print("   ✅ API响应正常")
                                success_count += 1
                            else:
                                print(f"   ⚠️ API返回失败: {data.get('message', '未知')}")
                        else:
                            print("   ✅ 返回数据正常")
                            success_count += 1
                    else:
                        print("   ✅ 返回数据正常")
                        success_count += 1
                except:
                    print(f"   📄 响应内容: {response.text[:100]}...")
                    if "html" not in response.text.lower():
                        success_count += 1
            else:
                print(f"   ❌ 连接失败 (状态码: {response.status_code})")
                print(f"   📄 错误内容: {response.text[:100]}...")
                
        except requests.exceptions.ConnectTimeout:
            print("   ❌ 连接超时")
        except requests.exceptions.ConnectionError:
            print("   ❌ 连接错误 - 服务器可能未启动")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        print()
    
    print(f"📊 总体测试结果: {success_count}/{len(test_cases)} 个接口正常")
    
    # 给出建议
    if success_count >= 3:
        print("🎉 大部分接口正常，license_manager.py应该可以工作！")
        print("💡 建议: 直接启动license_manager.py测试")
    elif success_count >= 1:
        print("⚠️ 部分接口正常，可能有些功能受限")
        print("💡 建议: 启动license_manager.py，可能基本功能可用")
    else:
        print("❌ 所有接口都异常，需要检查服务器状态")
        print("💡 建议: 检查服务器日志或重启服务")
    
    return success_count > 0

def provide_next_steps():
    """提供下一步操作建议"""
    print("\n📋 下一步操作建议")
    print("=" * 30)
    
    print("1. 🚀 直接测试license_manager.py:")
    print("   python license_manager.py")
    print()
    
    print("2. 🔍 如果license_manager.py报错:")
    print("   • 查看具体错误信息")
    print("   • 检查是否是网络连接问题")
    print("   • 确认端口5000是否开放")
    print()
    
    print("3. 🔧 如果仍有问题:")
    print("   • 重启服务器服务: systemctl restart license-manager")
    print("   • 查看服务日志: journalctl -u license-manager -f")
    print("   • 检查防火墙设置")
    print()
    
    print("4. 📞 联系支持:")
    print("   • 提供具体的错误信息")
    print("   • 说明测试结果")

def main():
    """主函数"""
    try:
        print("🎯 目标: 快速诊断license_manager.py连接问题")
        print("📋 方法: 测试所有必需的API接口")
        print()
        
        # 执行诊断
        if quick_diagnosis():
            print("\n✅ 诊断完成，服务器基本正常")
        else:
            print("\n❌ 诊断发现问题，需要进一步检查")
        
        # 提供建议
        provide_next_steps()
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
