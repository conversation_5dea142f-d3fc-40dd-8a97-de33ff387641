#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单下载测试 - 快速验证新下载函数效果
"""

import sys
import time
from auto_updater import AutoUpdater

def progress_callback(progress):
    """进度回调函数"""
    print(f"\r下载进度: {progress:.1f}%", end="", flush=True)

def main():
    """主函数"""
    print("🧪 简单下载测试")
    print("=" * 50)
    
    try:
        # 创建AutoUpdater实例
        print("📱 创建AutoUpdater实例...")
        updater = AutoUpdater(
            current_version="2.1.0",
            license_server_url="http://198.23.135.176:5000",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        print("✅ AutoUpdater实例创建成功")
        
        # 检查更新
        print("\n🔍 检查更新...")
        update_info = updater.check_for_updates()
        
        if not update_info:
            print("📦 当前已是最新版本")
            return True
        
        print(f"✅ 发现更新: {update_info.get('version', 'Unknown')}")
        file_size = update_info.get('file_size', 0)
        print(f"📁 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
        
        # 开始下载
        print("\n🚀 开始下载测试...")
        print("注意观察进度是否能达到100%")
        print("-" * 50)
        
        start_time = time.time()
        downloaded_file = updater.download_update(update_info, progress_callback)
        end_time = time.time()
        
        print()  # 换行
        
        if downloaded_file:
            import os
            actual_size = os.path.getsize(downloaded_file)
            download_time = end_time - start_time
            speed = actual_size / download_time / 1024 / 1024  # MB/s
            
            print("=" * 50)
            print("🎉 下载成功!")
            print(f"📁 文件路径: {downloaded_file}")
            print(f"📊 实际大小: {actual_size:,} 字节")
            print(f"⏱️ 下载时间: {download_time:.1f} 秒")
            print(f"🚀 下载速度: {speed:.2f} MB/s")
            
            # 验证文件大小
            expected_size = update_info.get('file_size', 0)
            if expected_size > 0:
                size_diff = abs(actual_size - expected_size)
                size_percent = (size_diff / expected_size) * 100
                print(f"🔍 大小差异: {size_diff:,} 字节 ({size_percent:.2f}%)")
                
                if size_diff <= max(1024*1024, expected_size*0.05):
                    print("✅ 文件大小验证通过!")
                else:
                    print("⚠️ 文件大小验证失败!")
            
            # 清理测试文件
            try:
                os.remove(downloaded_file)
                print("🗑️ 已清理测试文件")
            except:
                pass
            
            return True
        else:
            print("❌ 下载失败!")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试完成! 新的下载函数工作正常")
        print("\n✅ 关键改进:")
        print("  - 支持多次重试")
        print("  - 更宽松的文件验证")
        print("  - 强制缓冲区刷新")
        print("  - 确保进度显示100%")
        
        print("\n🚀 现在可以:")
        print("1. 重启 license_client.py")
        print("2. 正常使用更新功能")
        print("3. 享受稳定的下载体验")
    else:
        print("❌ 测试失败，可能需要进一步调试")
    
    sys.exit(0 if success else 1)
