# 亚马逊授权系统

这个系统包含一个客户端程序和服务器端程序，用于管理亚马逊产品分析工具的授权和分发。

## 系统组件

1. **授权客户端**：用于验证授权码并下载运行产品分析工具
2. **服务器程序**：用于验证授权码和提供加密的工具文件
3. **产品工具**：包括采集工具、筛品工具和历史价格工具

## 客户端使用说明

1. 运行`亚马逊授权客户端.exe`
2. 输入授权码并点击"保存"
3. 选择要运行的工具（采集/筛品/价格）
4. 点击"运行选中的程序"按钮

系统会自动下载并解密工具文件，然后启动所选工具。

## 服务器端设置

服务器文件位于`server_files`目录中，包括：

- `license_server.py`：服务器主程序
- 加密的工具文件（`*.py.encrypted`）
- 图标文件和其他资源

### 服务器安装步骤

1. 将`server_files`目录中的所有文件上传到服务器
2. 将文件放在`/opt/license_manager/`目录下
3. 安装必要的Python依赖：
   ```
   pip install flask flask-cors
   ```
4. 运行服务器程序：
   ```
   python license_server.py
   ```

## 开发者说明

### 构建流程

使用`build_license_system.py`脚本构建整个系统，它会：

1. 更新`license_client.py`和`license_server.py`
2. 将UI主题嵌入到各个工具中
3. 加密工具文件
4. 构建可执行文件
5. 准备服务器文件

运行构建：
```
python build_license_system.py
```

### 授权管理

授权码基于以下格式：`{随机ID}-{过期日期}-{验证码}`

授权级别：
- 级别1：只能使用采集工具
- 级别2：可以使用采集工具和筛品工具
- 级别3：可以使用所有工具

## 注意事项

1. 客户端与服务器通信需要网络连接
2. 授权码与设备绑定，不可在多台设备上使用
3. 工具运行需要安装Python和相关依赖，客户端会自动检查并安装

## 技术详情

- 使用XOR加密和Base64编码保护工具代码
- 授权验证基于设备ID和过期日期
- 运行时依赖自动安装
- UI主题直接嵌入工具代码中，不再依赖外部文件

## 文件说明

- `walmart_license_client.py` - 客户端许可证验证程序
- `walmart_scraper.py` - 沃尔玛产品采集程序
- `walmart_license_server.py` - 许可证服务器程序
- `encrypt_tool.py` - 加密工具
- `walmart_client_loader.py` - 客户端加载器（负责解密和执行加密的客户端程序）
- `build_encrypted.py` - 加密和打包脚本
- `build_all.py` - 整体构建脚本（执行所有必要步骤）

## 安装必要的依赖

在开始前，请确保已安装所有必要的依赖：

```bash
pip install pyinstaller requests
```

## 如何使用

### 1. 一键构建（推荐）

使用`build_all.py`可以一次性完成所有步骤：

```bash
python build_all.py
```

这将执行以下操作：
1. 检查必要的依赖和文件
2. 加密客户端和采集程序文件
3. 构建可执行文件
4. 准备服务器文件
5. 清理临时文件

如果需要跳过某些步骤，可以使用命令行选项：

```bash
# 跳过前提条件检查
python build_all.py --skip-checks

# 跳过加密步骤
python build_all.py --skip-encryption

# 跳过构建可执行文件步骤
python build_all.py --skip-build

# 跳过服务器配置检查步骤
python build_all.py --skip-server

# 跳过清理临时文件步骤
python build_all.py --skip-cleanup
```

### 2. 手动执行各个步骤

如果需要更精确控制，也可以分步执行：

#### 2.1 加密文件

```bash
python encrypt_tool.py
```

这将加密`walmart_license_client.py`和`walmart_scraper.py`文件，生成`.encrypted`后缀的加密文件。

#### 2.2 构建加密的可执行文件

```bash
python build_encrypted.py
```

这将构建包含加密客户端文件的可执行程序，输出到`dist`目录。

### 3. 部署

构建完成后：

1. 将`dist`目录中的可执行文件分发给用户
2. 将`server_files`目录中的文件上传到服务器，确保：
   - `walmart_scraper.py.encrypted`文件上传到服务器的`/opt/license_manager/`目录
   - 更新服务器端的`walmart_license_server.py`文件

## 工作原理

1. **加密**：使用基于XOR和Base64的自定义加密算法加密Python源代码
2. **服务器**：提供加密后的采集程序文件而非原始文件
3. **客户端**：解密并执行加密后的采集程序
4. **加载器**：解决模块导入问题，确保加密代码能够正确执行

## 日志和故障排除

所有组件都提供详细的日志，帮助排查问题：

- 客户端日志：位于用户目录的`.walmart_client_logs`文件夹
- 服务器日志：位于服务器的`/var/log/walmart_license_server.log`
- 加载器日志：位于用户目录的`.walmart_client_logs`文件夹下的`loader_*.log`文件

日志文件会自动清理，只保留最近的5个文件，以避免占用过多磁盘空间。

## 安全说明

此加密方案提供基本的代码保护，但不能替代更高级的安全措施。在生产环境中，建议：

1. 使用HTTPS进行服务器通信
2. 考虑商业级代码混淆和加密解决方案
3. 实施更复杂的许可证验证机制

## 常见问题

### Q: 加密后的程序无法运行，提示"No module named 'xxx'"

A: 这通常是因为加载器没有正确导入所有必要的模块。请检查`walmart_client_loader.py`确保导入了程序所需的所有模块。

### Q: 服务器无法找到加密后的文件

A: 确保服务器端的`PROGRAM_PATH`常量指向正确的加密文件路径，并确保该文件已上传到指定位置。 