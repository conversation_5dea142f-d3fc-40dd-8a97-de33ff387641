#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极简单下载器 - 避免所有复杂逻辑，只做最基本的下载
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import requests
import os
import tempfile
import time
import threading

class UltimateSimpleDownloader:
    """终极简单下载器"""
    
    def __init__(self):
        self.server_url = "http://198.23.135.176:5000"
        self.license_key = "ADMIN_BYPASS"
        self.device_id = "ADMIN-DEVICE-001"
        
    def create_gui(self):
        """创建GUI界面"""
        self.root = tk.Tk()
        self.root.title("终极简单下载器")
        self.root.geometry("800x600")
        
        # 设置图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 标题
        title_label = tk.Label(
            self.root,
            text="🔧 终极简单下载器",
            font=("微软雅黑", 16, "bold"),
            pady=10
        )
        title_label.pack()
        
        # 说明
        info_label = tk.Label(
            self.root,
            text="这个工具会逐步测试下载功能，找出问题所在",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        info_label.pack()
        
        # 日志区域
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=25, width=90)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        # 测试按钮
        test_button = tk.Button(
            button_frame,
            text="🔍 开始诊断",
            command=self.start_diagnosis,
            font=("微软雅黑", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        test_button.pack(side=tk.LEFT, padx=10)
        
        # 简单下载按钮
        download_button = tk.Button(
            button_frame,
            text="📥 简单下载",
            command=self.start_simple_download,
            font=("微软雅黑", 12),
            bg="#e74c3c",
            fg="white",
            padx=20,
            pady=10
        )
        download_button.pack(side=tk.LEFT, padx=10)
        
        # 清空日志按钮
        clear_button = tk.Button(
            button_frame,
            text="🗑️ 清空日志",
            command=self.clear_log,
            font=("微软雅黑", 12),
            bg="#95a5a6",
            fg="white",
            padx=20,
            pady=10
        )
        clear_button.pack(side=tk.LEFT, padx=10)
        
        self.log("🔧 终极简单下载器已启动")
        self.log("💡 点击'开始诊断'查看详细问题，点击'简单下载'尝试下载")
        
        return self.root
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.insert(tk.END, log_message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
        print(log_message)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def start_diagnosis(self):
        """开始诊断"""
        self.clear_log()
        self.log("🔍 开始全面诊断...")
        
        def diagnosis_thread():
            try:
                # 1. 测试网络连接
                self.log("\n1️⃣ 测试网络连接...")
                if self.test_network():
                    self.log("✅ 网络连接正常")
                else:
                    self.log("❌ 网络连接失败")
                    return
                
                # 2. 测试更新检查API
                self.log("\n2️⃣ 测试更新检查API...")
                update_info = self.test_check_update()
                if update_info:
                    self.log("✅ 更新检查成功")
                    self.log(f"📊 版本: {update_info.get('version')}")
                    self.log(f"📦 文件大小: {update_info.get('file_size', 0)/1024/1024:.1f}MB")
                else:
                    self.log("❌ 更新检查失败")
                    return
                
                # 3. 测试下载API连接
                self.log("\n3️⃣ 测试下载API连接...")
                if self.test_download_connection():
                    self.log("✅ 下载API连接正常")
                else:
                    self.log("❌ 下载API连接失败")
                    return
                
                # 4. 测试小文件下载
                self.log("\n4️⃣ 测试小文件下载（前1KB）...")
                if self.test_small_download():
                    self.log("✅ 小文件下载成功")
                else:
                    self.log("❌ 小文件下载失败")
                    return
                
                # 5. 测试中等文件下载
                self.log("\n5️⃣ 测试中等文件下载（前10MB）...")
                if self.test_medium_download():
                    self.log("✅ 中等文件下载成功")
                else:
                    self.log("❌ 中等文件下载失败")
                
                self.log("\n🎉 诊断完成！")
                
            except Exception as e:
                self.log(f"❌ 诊断过程异常: {e}")
        
        # 在后台线程运行诊断
        thread = threading.Thread(target=diagnosis_thread, daemon=True)
        thread.start()
    
    def test_network(self):
        """测试网络连接"""
        try:
            response = requests.get(self.server_url, timeout=10)
            self.log(f"📡 服务器响应: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            self.log(f"❌ 网络错误: {e}")
            return False
    
    def test_check_update(self):
        """测试更新检查"""
        try:
            url = f"{self.server_url}/update/check"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'current_version': '2.1.0'
            }
            
            response = requests.get(url, params=params, timeout=15)
            self.log(f"📡 更新检查响应: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('has_update'):
                    return data.get('update_info')
            
            self.log(f"📄 响应内容: {response.text[:200]}")
            return None
            
        except Exception as e:
            self.log(f"❌ 更新检查异常: {e}")
            return None
    
    def test_download_connection(self):
        """测试下载连接"""
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': '2.1.1'
            }
            
            response = requests.head(url, params=params, timeout=15)
            self.log(f"📡 下载连接响应: {response.status_code}")
            
            if response.status_code == 200:
                content_length = response.headers.get('Content-Length')
                if content_length:
                    self.log(f"📦 文件大小: {int(content_length)/1024/1024:.1f}MB")
                return True
            else:
                self.log(f"📄 错误响应: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ 下载连接异常: {e}")
            return False
    
    def test_small_download(self):
        """测试小文件下载"""
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': '2.1.1'
            }
            
            headers = {'Range': 'bytes=0-1023'}  # 前1KB
            
            response = requests.get(url, params=params, headers=headers, timeout=30)
            
            if response.status_code in [200, 206]:
                data_size = len(response.content)
                self.log(f"📦 下载数据: {data_size} 字节")
                return True
            else:
                self.log(f"❌ 小文件下载失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ 小文件下载异常: {e}")
            return False
    
    def test_medium_download(self):
        """测试中等文件下载"""
        try:
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': '2.1.1'
            }
            
            headers = {'Range': 'bytes=0-10485759'}  # 前10MB
            
            self.log("📥 开始下载前10MB...")
            start_time = time.time()
            
            response = requests.get(url, params=params, headers=headers, timeout=120, stream=True)
            
            if response.status_code in [200, 206]:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=32768):
                    if chunk:
                        downloaded += len(chunk)
                        if downloaded % (1024*1024) == 0:  # 每1MB显示进度
                            self.log(f"📊 已下载: {downloaded/1024/1024:.1f}MB")
                
                end_time = time.time()
                speed = downloaded / (end_time - start_time) / 1024  # KB/s
                
                self.log(f"📦 下载大小: {downloaded/1024/1024:.1f}MB")
                self.log(f"⏱️ 下载时间: {end_time-start_time:.1f}秒")
                self.log(f"🚀 下载速度: {speed:.1f}KB/s")
                
                return True
            else:
                self.log(f"❌ 中等文件下载失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ 中等文件下载异常: {e}")
            return False
    
    def start_simple_download(self):
        """开始简单下载"""
        self.log("\n📥 开始简单下载...")
        
        def download_thread():
            try:
                # 删除任何现有的临时文件
                temp_dir = tempfile.gettempdir()
                for filename in os.listdir(temp_dir):
                    if filename.startswith("amazon_blueprint_update_"):
                        temp_file = os.path.join(temp_dir, filename)
                        try:
                            os.remove(temp_file)
                            self.log(f"🗑️ 已删除旧文件: {filename}")
                        except:
                            pass
                
                # 开始全新下载
                url = f"{self.server_url}/update/download"
                params = {
                    'key': self.license_key,
                    'device_id': self.device_id,
                    'version': '2.1.1'
                }
                
                self.log("🚀 开始全新下载（不使用断点续传）...")
                
                response = requests.get(
                    url, 
                    params=params,
                    stream=True,
                    timeout=(60, 3600)  # 1小时超时
                )
                
                if response.status_code != 200:
                    self.log(f"❌ HTTP错误: {response.status_code}")
                    self.log(f"📄 错误内容: {response.text}")
                    return
                
                file_size = int(response.headers.get('Content-Length', 0))
                self.log(f"📦 文件大小: {file_size/1024/1024:.1f}MB")
                
                temp_file = os.path.join(temp_dir, "amazon_blueprint_update_2.1.1.exe")
                downloaded_size = 0
                
                with open(temp_file, 'wb') as f:
                    chunk_size = 32 * 1024  # 32KB chunks
                    
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # 每5MB显示进度
                            if downloaded_size % (5 * 1024 * 1024) == 0:
                                progress = (downloaded_size / file_size) * 100 if file_size > 0 else 0
                                self.log(f"📊 下载进度: {downloaded_size/1024/1024:.1f}MB ({progress:.1f}%)")
                
                self.log(f"✅ 下载完成: {temp_file}")
                self.log(f"📦 最终大小: {downloaded_size/1024/1024:.1f}MB")
                
                # 验证文件
                if os.path.exists(temp_file):
                    actual_size = os.path.getsize(temp_file)
                    self.log(f"📋 文件验证: {actual_size/1024/1024:.1f}MB")
                    
                    if actual_size == file_size:
                        self.log("🎉 文件下载完整！")
                        messagebox.showinfo("成功", f"文件下载成功！\n位置: {temp_file}")
                    else:
                        self.log("⚠️ 文件大小不匹配")
                else:
                    self.log("❌ 文件不存在")
                
            except Exception as e:
                self.log(f"❌ 下载失败: {e}")
                import traceback
                self.log(f"📄 详细错误: {traceback.format_exc()}")
        
        # 在后台线程运行下载
        thread = threading.Thread(target=download_thread, daemon=True)
        thread.start()

def main():
    """主函数"""
    downloader = UltimateSimpleDownloader()
    root = downloader.create_gui()
    root.mainloop()

if __name__ == "__main__":
    main()
