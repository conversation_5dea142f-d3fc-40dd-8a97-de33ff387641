#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度下载诊断工具 - 找出真正的问题根源
"""

import requests
import os
import time
import hashlib
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext
import json

class DownloadDiagnostic:
    """下载诊断工具"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 深度下载诊断工具")
        self.root.geometry("900x700")
        
        # 诊断结果
        self.diagnostic_results = {}
        
        self.create_gui()
        
    def create_gui(self):
        """创建GUI"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="🔍 深度下载诊断工具",
            font=("微软雅黑", 16, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=10)
        
        # 说明
        info_label = tk.Label(
            self.root,
            text="全面诊断下载问题，找出真正的根源",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        info_label.pack()
        
        # 诊断项目框架
        diag_frame = tk.LabelFrame(self.root, text="诊断项目", font=("微软雅黑", 10, "bold"))
        diag_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 诊断按钮
        buttons_frame = tk.Frame(diag_frame)
        buttons_frame.pack(pady=10)
        
        tk.Button(buttons_frame, text="🌐 网络连接测试", command=self.test_network, 
                 bg="#3498db", fg="white", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        tk.Button(buttons_frame, text="📊 服务端响应分析", command=self.analyze_server, 
                 bg="#e74c3c", fg="white", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        tk.Button(buttons_frame, text="📥 实际下载测试", command=self.test_download, 
                 bg="#27ae60", fg="white", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        tk.Button(buttons_frame, text="🔍 全面诊断", command=self.full_diagnostic, 
                 bg="#9b59b6", fg="white", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill=tk.X, padx=20, pady=5)
        
        # 日志区域
        log_frame = tk.LabelFrame(self.root, text="诊断日志", font=("微软雅黑", 10, "bold"))
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=25, font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 结果框架
        result_frame = tk.LabelFrame(self.root, text="诊断结果", font=("微软雅黑", 10, "bold"))
        result_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.result_text = tk.Text(result_frame, height=6, font=("微软雅黑", 9))
        self.result_text.pack(fill=tk.X, padx=10, pady=10)
    
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "ERROR":
            prefix = "❌"
        elif level == "SUCCESS":
            prefix = "✅"
        elif level == "WARNING":
            prefix = "⚠️"
        else:
            prefix = "ℹ️"
        
        log_message = f"[{timestamp}] {prefix} {message}"
        self.log_text.insert(tk.END, log_message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
        print(log_message)
    
    def test_network(self):
        """测试网络连接"""
        def test_thread():
            self.progress.start()
            self.log("开始网络连接测试...", "INFO")
            
            try:
                # 1. 基本连接测试
                self.log("测试基本连接...")
                start_time = time.time()
                response = requests.get("http://**************:5000/", timeout=10)
                connect_time = time.time() - start_time
                
                self.log(f"连接时间: {connect_time:.2f}秒", "SUCCESS")
                self.log(f"HTTP状态码: {response.status_code}", "SUCCESS")
                
                # 2. DNS解析测试
                import socket
                self.log("测试DNS解析...")
                ip = socket.gethostbyname("**************")
                self.log(f"IP地址: {ip}", "SUCCESS")
                
                # 3. 端口连通性测试
                self.log("测试端口连通性...")
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex(("**************", 5000))
                sock.close()
                
                if result == 0:
                    self.log("端口5000连通正常", "SUCCESS")
                else:
                    self.log("端口5000连接失败", "ERROR")
                
                # 4. 网络速度测试
                self.log("测试网络速度...")
                test_url = "http://**************:5000/update/stats"
                start_time = time.time()
                response = requests.get(test_url, timeout=10)
                end_time = time.time()
                
                if response.status_code == 200:
                    speed = len(response.content) / (end_time - start_time) / 1024
                    self.log(f"网络速度: {speed:.2f} KB/s", "SUCCESS")
                
                self.diagnostic_results['network'] = {
                    'status': 'OK',
                    'connect_time': connect_time,
                    'speed': speed if 'speed' in locals() else 0
                }
                
            except Exception as e:
                self.log(f"网络测试失败: {e}", "ERROR")
                self.diagnostic_results['network'] = {'status': 'FAILED', 'error': str(e)}
            
            finally:
                self.progress.stop()
                self.update_results()
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def analyze_server(self):
        """分析服务端响应"""
        def analyze_thread():
            self.progress.start()
            self.log("开始服务端响应分析...", "INFO")
            
            try:
                # 1. 检查更新API分析
                self.log("分析更新检查API...")
                check_url = "http://**************:5000/update/check"
                params = {
                    'key': 'ADMIN_BYPASS',
                    'device_id': 'ADMIN-DEVICE-001',
                    'current_version': '2.1.0'
                }
                
                response = requests.get(check_url, params=params, timeout=10)
                self.log(f"更新检查响应: {response.status_code}", "SUCCESS")
                
                if response.status_code == 200:
                    data = response.json()
                    self.log(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get('has_update'):
                        file_size = data.get('update_info', {}).get('file_size', 0)
                        self.log(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)", "INFO")
                        
                        # 2. 分析下载API
                        self.log("分析下载API响应头...")
                        download_url = f"http://**************:5000/update/download"
                        download_params = {
                            'key': 'ADMIN_BYPASS',
                            'device_id': 'ADMIN-DEVICE-001',
                            'version': data.get('update_info', {}).get('version')
                        }
                        
                        # HEAD请求获取响应头
                        head_response = requests.head(download_url, params=download_params, timeout=10)
                        self.log(f"下载API状态: {head_response.status_code}", "SUCCESS")
                        
                        headers = head_response.headers
                        self.log("响应头分析:")
                        for key, value in headers.items():
                            if key.lower() in ['content-length', 'content-type', 'accept-ranges', 'last-modified']:
                                self.log(f"  {key}: {value}")
                        
                        # 检查Content-Length
                        content_length = int(headers.get('Content-Length', 0))
                        if content_length != file_size:
                            self.log(f"⚠️ 文件大小不匹配! API返回:{file_size}, Header返回:{content_length}", "WARNING")
                        else:
                            self.log("✅ 文件大小匹配", "SUCCESS")
                        
                        # 检查是否支持断点续传
                        accept_ranges = headers.get('Accept-Ranges', '')
                        if 'bytes' in accept_ranges:
                            self.log("✅ 服务器支持断点续传", "SUCCESS")
                        else:
                            self.log("⚠️ 服务器不支持断点续传", "WARNING")
                        
                        self.diagnostic_results['server'] = {
                            'status': 'OK',
                            'file_size_api': file_size,
                            'file_size_header': content_length,
                            'size_match': file_size == content_length,
                            'supports_resume': 'bytes' in accept_ranges
                        }
                
            except Exception as e:
                self.log(f"服务端分析失败: {e}", "ERROR")
                self.diagnostic_results['server'] = {'status': 'FAILED', 'error': str(e)}
            
            finally:
                self.progress.stop()
                self.update_results()
        
        threading.Thread(target=analyze_thread, daemon=True).start()
    
    def test_download(self):
        """实际下载测试"""
        def download_thread():
            self.progress.start()
            self.log("开始实际下载测试...", "INFO")
            
            try:
                # 获取下载信息
                check_url = "http://**************:5000/update/check"
                params = {
                    'key': 'ADMIN_BYPASS',
                    'device_id': 'ADMIN-DEVICE-001',
                    'current_version': '2.1.0'
                }
                
                response = requests.get(check_url, params=params, timeout=10)
                if response.status_code != 200:
                    raise Exception("无法获取更新信息")
                
                data = response.json()
                if not data.get('has_update'):
                    self.log("当前已是最新版本", "INFO")
                    return
                
                # 开始下载测试
                download_url = "http://**************:5000/update/download"
                download_params = {
                    'key': 'ADMIN_BYPASS',
                    'device_id': 'ADMIN-DEVICE-001',
                    'version': data.get('update_info', {}).get('version')
                }
                
                expected_size = data.get('update_info', {}).get('file_size', 0)
                self.log(f"开始下载测试，预期大小: {expected_size:,} 字节")
                
                # 创建临时文件
                temp_file = "download_test.tmp"
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                
                # 开始下载
                start_time = time.time()
                response = requests.get(download_url, params=download_params, stream=True, timeout=(30, 300))
                
                downloaded_size = 0
                chunk_count = 0
                last_log_time = time.time()
                
                with open(temp_file, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            f.flush()
                            downloaded_size += len(chunk)
                            chunk_count += 1
                            
                            # 每秒记录一次进度
                            current_time = time.time()
                            if current_time - last_log_time >= 1.0:
                                progress = (downloaded_size / expected_size) * 100 if expected_size > 0 else 0
                                speed = downloaded_size / (current_time - start_time) / 1024
                                self.log(f"下载进度: {progress:.1f}% ({downloaded_size:,}/{expected_size:,}) 速度: {speed:.1f} KB/s")
                                last_log_time = current_time
                            
                            # 测试前10MB就够了
                            if downloaded_size >= 10 * 1024 * 1024:
                                self.log("测试下载10MB完成，停止测试", "SUCCESS")
                                break
                
                # 强制同步文件
                with open(temp_file, 'r+b') as f:
                    f.flush()
                    os.fsync(f.fileno())
                
                # 检查实际文件大小
                actual_size = os.path.getsize(temp_file)
                self.log(f"实际下载: {actual_size:,} 字节")
                self.log(f"预期下载: {min(downloaded_size, 10*1024*1024):,} 字节")
                
                # 计算MD5
                self.log("计算文件MD5...")
                md5_hash = hashlib.md5()
                with open(temp_file, 'rb') as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        md5_hash.update(chunk)
                file_md5 = md5_hash.hexdigest()
                self.log(f"文件MD5: {file_md5}")
                
                # 清理临时文件
                os.remove(temp_file)
                
                self.diagnostic_results['download'] = {
                    'status': 'OK',
                    'downloaded_size': actual_size,
                    'expected_size': min(downloaded_size, 10*1024*1024),
                    'size_match': actual_size == min(downloaded_size, 10*1024*1024),
                    'md5': file_md5
                }
                
            except Exception as e:
                self.log(f"下载测试失败: {e}", "ERROR")
                self.diagnostic_results['download'] = {'status': 'FAILED', 'error': str(e)}
            
            finally:
                self.progress.stop()
                self.update_results()
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def full_diagnostic(self):
        """全面诊断"""
        def full_thread():
            self.log("开始全面诊断...", "INFO")
            self.test_network()
            time.sleep(2)
            self.analyze_server()
            time.sleep(2)
            self.test_download()
        
        threading.Thread(target=full_thread, daemon=True).start()
    
    def update_results(self):
        """更新诊断结果"""
        self.result_text.delete(1.0, tk.END)
        
        result_summary = "🔍 诊断结果总结:\n\n"
        
        for test_name, result in self.diagnostic_results.items():
            if result['status'] == 'OK':
                result_summary += f"✅ {test_name.upper()}: 正常\n"
            else:
                result_summary += f"❌ {test_name.upper()}: 失败 - {result.get('error', '未知错误')}\n"
        
        # 分析可能的问题
        result_summary += "\n🔍 问题分析:\n"
        
        if 'server' in self.diagnostic_results:
            server_result = self.diagnostic_results['server']
            if server_result['status'] == 'OK':
                if not server_result.get('size_match', True):
                    result_summary += "⚠️ 文件大小不匹配可能导致下载验证失败\n"
                if not server_result.get('supports_resume', False):
                    result_summary += "⚠️ 服务器不支持断点续传\n"
        
        if 'download' in self.diagnostic_results:
            download_result = self.diagnostic_results['download']
            if download_result['status'] == 'OK':
                if not download_result.get('size_match', True):
                    result_summary += "⚠️ 实际下载大小与预期不符\n"
        
        self.result_text.insert(tk.END, result_summary)

def main():
    """主函数"""
    app = DownloadDiagnostic()
    app.root.mainloop()

if __name__ == "__main__":
    main()
