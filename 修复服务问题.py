#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复CentOS服务器部署问题
解决license-manager服务启动失败的问题
"""

import paramiko
import sys
import time

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def fix_license_manager_service():
    """修复license-manager服务"""
    print("🔧 修复license-manager服务问题")
    print("=" * 50)
    
    # 服务器配置
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 步骤1: 停止所有相关进程
    print("🛑 步骤1: 停止所有相关进程...")
    commands = [
        "systemctl stop license-manager",
        "pkill -f license_server.py",
        "pkill -f 'python3.*license_server'"
    ]
    
    for cmd in commands:
        success, output, error = ssh_connect(config['host'], config['username'], config['password'], cmd)
        print(f"   执行: {cmd}")
        if success:
            print(f"   ✅ 成功")
        else:
            print(f"   ⚠️ {error if error else '无输出'}")
    
    time.sleep(2)
    print()
    
    # 步骤2: 检查并修复Python脚本
    print("🐍 步骤2: 检查Python脚本...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"cd {config['deploy_path']} && python3 -m py_compile license_server.py")
    if success:
        print("   ✅ Python脚本语法正确")
    else:
        print(f"   ❌ Python脚本语法错误: {error}")
        return False
    print()
    
    # 步骤3: 检查依赖
    print("📦 步骤3: 检查Python依赖...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "python3 -c 'import flask, requests, cryptography; print(\"依赖检查通过\")'")
    if success:
        print("   ✅ Python依赖完整")
    else:
        print(f"   ❌ 缺少依赖: {error}")
        print("   🔧 重新安装依赖...")
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], 
                                               "pip3 install flask requests cryptography")
        if success2:
            print("   ✅ 依赖重新安装完成")
        else:
            print(f"   ❌ 依赖安装失败: {error2}")
    print()
    
    # 步骤4: 修复systemd服务文件
    print("⚙️ 步骤4: 修复systemd服务文件...")
    service_content = f"""[Unit]
Description=License Manager Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={config['deploy_path']}
ExecStart=/usr/bin/python3 {config['deploy_path']}/license_server.py
Restart=always
RestartSec=10
Environment=PYTHONPATH={config['deploy_path']}
Environment=FLASK_ENV=production
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target"""
    
    # 创建服务文件
    command = f"""cat > /etc/systemd/system/license-manager.service << 'EOF'
{service_content}
EOF"""
    
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], command)
    if success:
        print("   ✅ systemd服务文件更新完成")
    else:
        print(f"   ❌ 服务文件更新失败: {error}")
        return False
    print()
    
    # 步骤5: 重新加载并启动服务
    print("🚀 步骤5: 重新启动服务...")
    commands = [
        "systemctl daemon-reload",
        "systemctl enable license-manager",
        "systemctl start license-manager"
    ]
    
    for cmd in commands:
        success, output, error = ssh_connect(config['host'], config['username'], config['password'], cmd)
        print(f"   执行: {cmd}")
        if success:
            print(f"   ✅ 成功")
        else:
            print(f"   ❌ 失败: {error}")
    
    time.sleep(3)
    print()
    
    # 步骤6: 验证服务状态
    print("🔍 步骤6: 验证服务状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl status license-manager")
    if success and "active (running)" in output:
        print("   ✅ license-manager服务运行正常")
    else:
        print("   ❌ 服务仍有问题")
        print(f"   状态: {output}")
        
        # 查看日志
        print("   📋 查看服务日志...")
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], 
                                               "journalctl -u license-manager --no-pager -n 10")
        if output2:
            print("   日志:")
            for line in output2.split('\n')[-5:]:
                if line.strip():
                    print(f"     {line}")
    print()
    
    # 步骤7: 测试HTTP服务
    print("🌍 步骤7: 测试HTTP服务...")
    time.sleep(2)
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "curl -s -o /dev/null -w 'HTTP状态码: %{http_code}' http://localhost:5000/")
    if success and "200" in output:
        print("   ✅ HTTP服务响应正常")
        print(f"   {output}")
    else:
        print("   ❌ HTTP服务仍无响应")
        print(f"   响应: {output}")
        
        # 检查端口
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], 
                                               "netstat -tlnp | grep 5000")
        if output2:
            print(f"   端口状态: {output2}")
        else:
            print("   端口5000未监听")
    print()
    
    # 总结
    print("📊 修复结果总结")
    print("=" * 30)
    
    # 最终状态检查
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl is-active license-manager")
    if success and "active" in output:
        print("🎉 修复成功！license-manager服务正常运行")
        print(f"🌐 服务地址: http://{config['host']}:5000/")
        return True
    else:
        print("❌ 修复失败，需要手动检查")
        print("💡 建议:")
        print("   • 检查Python脚本是否有语法错误")
        print("   • 检查端口5000是否被占用")
        print("   • 查看详细日志: journalctl -u license-manager -f")
        return False

def main():
    """主函数"""
    try:
        success = fix_license_manager_service()
        
        if success:
            print("\n🎯 下一步操作:")
            print("1. 在浏览器中访问: http://**************:5000/")
            print("2. 测试API接口: http://**************:5000/api/check_update")
            print("3. 如果需要，配置nginx反向代理")
        else:
            print("\n🔧 如果问题仍然存在:")
            print("1. 手动检查Python脚本")
            print("2. 查看系统日志")
            print("3. 重新运行部署工具")
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
