#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查更新API状态
"""

import paramiko
import requests
import json

def check_api_status():
    """检查更新API状态"""
    print("🔍 检查更新API状态")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    try:
        # 1. 检查服务器连接
        print("🌐 步骤1: 检查服务器连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        print("   ✅ SSH连接成功")
        
        # 2. 检查服务状态
        print("🔄 步骤2: 检查服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl status license-manager")
        status_output = stdout.read().decode('utf-8')
        
        if "active (running)" in status_output:
            print("   ✅ license-manager服务正在运行")
        else:
            print("   ❌ license-manager服务未运行")
            print(f"   状态: {status_output}")
        
        # 3. 检查端口监听
        print("🔌 步骤3: 检查端口监听...")
        stdin, stdout, stderr = client.exec_command("netstat -tlnp | grep :5000")
        port_output = stdout.read().decode('utf-8')
        
        if ":5000" in port_output:
            print("   ✅ 端口5000正在监听")
            print(f"   详情: {port_output.strip()}")
        else:
            print("   ❌ 端口5000未监听")
        
        # 4. 检查更新目录
        print("📁 步骤4: 检查更新目录...")
        stdin, stdout, stderr = client.exec_command(f"ls -la {config['deploy_path']}/updates/")
        dir_output = stdout.read().decode('utf-8')
        
        if "total" in dir_output:
            print("   ✅ 更新目录存在")
            print(f"   内容:\n{dir_output}")
        else:
            print("   ❌ 更新目录不存在或为空")
        
        # 5. 检查license_server.py文件
        print("📄 步骤5: 检查license_server.py...")
        stdin, stdout, stderr = client.exec_command(f"grep -c 'check_update' {config['deploy_path']}/license_server.py")
        api_count = stdout.read().decode('utf-8').strip()
        
        if api_count and int(api_count) > 0:
            print(f"   ✅ 检测到{api_count}个更新API函数")
        else:
            print("   ❌ 未检测到更新API函数")
        
        # 6. 检查服务器日志
        print("📋 步骤6: 检查服务器日志...")
        stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '5 minutes ago' --no-pager")
        log_output = stdout.read().decode('utf-8')
        
        if log_output.strip():
            print("   📋 最近5分钟的日志:")
            print(f"   {log_output}")
        else:
            print("   ℹ️ 最近5分钟无日志")
        
        client.close()
        
        # 7. 测试API接口
        print("🧪 步骤7: 测试API接口...")
        server_url = "http://**************:5000"
        
        test_apis = [
            ("健康检查", f"{server_url}/health"),
            ("检查更新", f"{server_url}/api/check_update?current_version=1.0.0"),
            ("版本列表", f"{server_url}/api/list_versions")
        ]
        
        success_count = 0
        for name, url in test_apis:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"   ✅ {name}: HTTP {response.status_code}")
                    
                    # 尝试解析JSON
                    try:
                        data = response.json()
                        if 'success' in data:
                            print(f"      📋 响应: {data}")
                        success_count += 1
                    except:
                        print(f"      📋 响应: {response.text[:100]}...")
                        success_count += 1
                else:
                    print(f"   ❌ {name}: HTTP {response.status_code}")
                    print(f"      错误: {response.text[:100]}...")
            except Exception as e:
                print(f"   ❌ {name}: 连接失败 - {e}")
        
        print(f"\n📊 API测试结果: {success_count}/{len(test_apis)} 个接口正常")
        
        # 8. 提供解决方案
        print("\n💡 解决方案建议:")
        if success_count == 0:
            print("• 🔄 重启服务: systemctl restart license-manager")
            print("• 🔍 检查防火墙: firewall-cmd --list-ports")
            print("• 📋 查看详细日志: journalctl -u license-manager -f")
        elif success_count < len(test_apis):
            print("• 🔍 部分API异常，检查代码语法")
            print("• 📋 查看错误日志: journalctl -u license-manager --since '1 hour ago'")
        else:
            print("• ✅ 所有API正常工作！")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 检查服务器更新API状态")
        print("🌐 服务器: **************:5000")
        print()
        
        if check_api_status():
            print("\n🎉 更新API状态检查完成！")
        else:
            print("\n⚠️ 发现问题，请根据建议进行修复")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
