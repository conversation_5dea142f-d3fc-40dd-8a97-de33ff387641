#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动智能exe管理工具
"""

import sys
import os

def main():
    """启动智能exe管理工具"""
    print("🚀 启动智能exe管理工具...")
    
    try:
        # 导入并运行增强版管理工具
        from 增强版exe管理工具 import main as run_enhanced_manager
        run_enhanced_manager()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 增强版exe管理工具.py 文件存在")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        
    input("按回车键退出...")

if __name__ == "__main__":
    main()
