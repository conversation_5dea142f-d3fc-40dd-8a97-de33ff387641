# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_data_files
from PyInstaller.utils.hooks import collect_submodules

datas = []
hiddenimports = ['fake_useragent', 'fake_useragent.fake', 'fake_useragent.utils', 'openpyxl', 'openpyxl.workbook.workbook', 'openpyxl.worksheet.worksheet', 'pandas', 'pandas.io.excel._openpyxl', 'requests', 'selenium', 'bs4', 'lxml', 'cryptography', 'PIL']
datas += collect_data_files('fake_useragent')
datas += collect_data_files('openpyxl')
datas += collect_data_files('fake_useragent')
hiddenimports += collect_submodules('fake_useragent')
hiddenimports += collect_submodules('openpyxl')
hiddenimports += collect_submodules('openpyxl')


a = Analysis(
    ['license_client.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'scipy', 'numpy.distutils', 'jupyter', 'notebook', 'IPython', 'pytest', 'unittest', 'matplotlib', 'scipy'],
    noarchive=False,
    optimize=2,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [('O', None, 'OPTION'), ('O', None, 'OPTION')],
    name='亚马逊蓝图工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['icon.ico'],
)
