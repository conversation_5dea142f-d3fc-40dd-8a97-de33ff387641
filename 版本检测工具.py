#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本检测工具 - 检测license_client.py的版本信息
"""

import re
import os
from datetime import datetime

def detect_version_info(filename="license_client.py"):
    """检测文件中的版本信息"""
    if not os.path.exists(filename):
        return {"error": f"文件 {filename} 不存在"}
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        version_info = {
            "filename": filename,
            "file_size": len(content),
            "file_size_mb": len(content) / (1024 * 1024),
            "line_count": len(content.split('\n')),
            "versions_found": [],
            "display_versions": [],
            "client_versions": [],
            "inconsistencies": []
        }
        
        # 查找各种版本定义
        patterns = {
            "current_version": r'current_version\s*=\s*["\']([^"\']+)["\']',
            "client_version": r'client_version["\']?\s*:\s*["\']([^"\']+)["\']',
            "display_version": r'蓝图工具\s*v([0-9.]+)',
            "__version__": r'__version__\s*=\s*["\']([^"\']+)["\']',
            "VERSION": r'VERSION\s*=\s*["\']([^"\']+)["\']'
        }
        
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                version_info[f"{pattern_name}_matches"] = matches
                
                if pattern_name == "current_version":
                    version_info["versions_found"].extend(matches)
                elif pattern_name == "display_version":
                    version_info["display_versions"].extend(matches)
                elif pattern_name == "client_version":
                    version_info["client_versions"].extend(matches)
        
        # 检查版本一致性
        all_versions = (
            version_info["versions_found"] + 
            version_info["display_versions"] + 
            version_info["client_versions"]
        )
        
        unique_versions = list(set(all_versions))
        if len(unique_versions) > 1:
            version_info["inconsistencies"] = unique_versions
            version_info["has_inconsistency"] = True
        else:
            version_info["has_inconsistency"] = False
        
        # 确定主版本号
        if version_info["versions_found"]:
            version_info["main_version"] = version_info["versions_found"][0]
        elif unique_versions:
            version_info["main_version"] = unique_versions[0]
        else:
            version_info["main_version"] = "未找到"
        
        return version_info
        
    except Exception as e:
        return {"error": f"读取文件失败: {e}"}

def analyze_version_compatibility():
    """分析版本兼容性"""
    print("🔍 分析license_client.py版本信息")
    print("=" * 60)
    
    # 检测版本信息
    version_info = detect_version_info()
    
    if "error" in version_info:
        print(f"❌ 错误: {version_info['error']}")
        return
    
    # 显示文件基本信息
    print("📁 文件信息:")
    print(f"   文件名: {version_info['filename']}")
    print(f"   文件大小: {version_info['file_size_mb']:.2f} MB")
    print(f"   代码行数: {version_info['line_count']:,} 行")
    
    # 显示版本信息
    print("\n🔢 版本信息:")
    
    if version_info.get("current_version_matches"):
        print(f"   ✅ 程序版本 (current_version): {version_info['current_version_matches']}")
    else:
        print("   ❌ 未找到程序版本定义")
    
    if version_info.get("display_version_matches"):
        print(f"   ✅ 显示版本 (界面显示): {version_info['display_version_matches']}")
    else:
        print("   ❌ 未找到界面显示版本")
    
    if version_info.get("client_version_matches"):
        print(f"   ✅ 客户端版本: {version_info['client_version_matches']}")
    else:
        print("   ⚠️ 未找到客户端版本")
    
    # 版本一致性检查
    print("\n🔍 版本一致性检查:")
    if version_info["has_inconsistency"]:
        print("   ❌ 发现版本不一致!")
        print(f"   发现的版本: {version_info['inconsistencies']}")
        print("   建议: 统一所有版本号")
    else:
        print("   ✅ 版本号一致")
        print(f"   统一版本: {version_info['main_version']}")
    
    # 管理工具兼容性
    print("\n🛠️ 管理工具兼容性:")
    main_version = version_info.get("main_version", "未知")
    
    if main_version != "未找到" and main_version != "未知":
        print(f"   ✅ 可以识别版本: {main_version}")
        print("   ✅ 智能exe管理工具可以识别此版本")
        
        # 检查版本格式
        if re.match(r'^\d+\.\d+\.\d+$', main_version):
            print("   ✅ 版本格式标准 (x.y.z)")
        else:
            print("   ⚠️ 版本格式非标准，建议使用 x.y.z 格式")
    else:
        print("   ❌ 无法识别版本号")
        print("   ❌ 需要添加版本定义")
    
    return version_info

def suggest_version_improvements(version_info):
    """建议版本改进方案"""
    print("\n" + "=" * 60)
    print("💡 版本改进建议")
    print("=" * 60)
    
    suggestions = []
    
    # 检查是否有版本定义
    if not version_info.get("current_version_matches"):
        suggestions.append("添加程序版本定义: current_version = \"2.1.1\"")
    
    # 检查版本一致性
    if version_info["has_inconsistency"]:
        main_version = version_info["main_version"]
        suggestions.append(f"统一所有版本号为: {main_version}")
    
    # 检查显示版本
    if not version_info.get("display_version_matches"):
        main_version = version_info["main_version"]
        suggestions.append(f"添加界面显示版本: 蓝图工具 v{main_version}")
    
    # 检查客户端版本
    if not version_info.get("client_version_matches"):
        suggestions.append("添加客户端版本标识")
    
    if suggestions:
        print("🔧 建议的改进:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
    else:
        print("✅ 版本配置良好，无需改进")
    
    # 管理工具使用建议
    print("\n🚀 管理工具使用建议:")
    main_version = version_info.get("main_version", "2.1.1")
    
    if main_version != "未找到":
        print(f"1. 当前版本: {main_version}")
        print("2. 使用智能exe管理工具上传时:")
        print(f"   - 版本号设为: {main_version}")
        print("   - 或使用'智能递增'功能自动生成下一版本")
        print("3. 构建exe文件时确保版本号一致")
    else:
        print("1. 先修复版本号定义")
        print("2. 然后使用管理工具上传")

def create_version_fix_script():
    """创建版本修复脚本"""
    print("\n" + "=" * 60)
    print("🛠️ 创建版本修复脚本")
    print("=" * 60)
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本修复脚本 - 统一license_client.py中的版本号
"""

import re

def fix_version_consistency(target_version="2.1.1"):
    """修复版本一致性"""
    try:
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        with open('license_client.py.backup_version_fix', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 修复各种版本定义
        fixes = [
            (r'current_version\\s*=\\s*["\']([^"\']+)["\']', f'current_version = "{target_version}"'),
            (r'(蓝图工具\\s*v)([0-9.]+)', f'\\\\1{target_version}'),
            (r'("client_version"\\s*:\\s*")[^"]*(")', f'\\\\1{target_version}\\\\2')
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        # 保存修复后的文件
        with open('license_client.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 版本已统一为: {target_version}")
        print("✅ 原文件已备份为: license_client.py.backup_version_fix")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        target_version = sys.argv[1]
    else:
        target_version = "2.1.1"
    
    fix_version_consistency(target_version)
'''
    
    with open('版本修复脚本.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 已创建 版本修复脚本.py")
    print("使用方法: python 版本修复脚本.py [版本号]")
    print("例如: python 版本修复脚本.py 2.1.2")

def main():
    """主函数"""
    print("🔍 license_client.py 版本检测工具")
    print("=" * 60)
    
    # 分析版本信息
    version_info = analyze_version_compatibility()
    
    if version_info and "error" not in version_info:
        # 提供改进建议
        suggest_version_improvements(version_info)
        
        # 创建修复脚本
        create_version_fix_script()
    
    print("\n" + "=" * 60)
    print("📋 总结")
    print("=" * 60)
    
    if version_info and "error" not in version_info:
        main_version = version_info.get("main_version", "未知")
        
        if main_version != "未找到":
            print(f"✅ license_client.py 可以被识别")
            print(f"✅ 当前版本: {main_version}")
            print("✅ 智能exe管理工具可以处理此文件")
            
            if version_info["has_inconsistency"]:
                print("⚠️ 建议先修复版本不一致问题")
            else:
                print("✅ 版本配置完善")
        else:
            print("❌ license_client.py 无法识别版本")
            print("❌ 需要添加版本定义")
    
    print("\n🚀 下一步操作:")
    print("1. 如有版本问题，运行版本修复脚本")
    print("2. 使用智能exe管理工具上传新版本")
    print("3. 确保服务器版本与客户端版本一致")

if __name__ == "__main__":
    main()
