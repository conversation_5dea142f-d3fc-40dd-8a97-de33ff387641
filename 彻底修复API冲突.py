#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底修复API冲突 - 恢复到工作状态并重新添加API
"""

import paramiko
import sys
import os

def restore_and_fix():
    """恢复备份并重新正确添加API"""
    print("🔧 彻底修复API冲突")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 停止服务
        print("🛑 步骤1: 停止服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        stdout.channel.recv_exit_status()
        print("   ✅ 服务已停止")
        
        # 步骤2: 恢复到最近的工作备份
        print("📁 步骤2: 恢复到工作备份...")
        
        # 查找最近的备份文件
        stdin, stdout, stderr = client.exec_command(f"ls -t {config['deploy_path']}/license_server.py.backup_* | head -1")
        latest_backup = stdout.read().decode('utf-8').strip()
        
        if latest_backup:
            print(f"   找到备份: {latest_backup}")
            stdin, stdout, stderr = client.exec_command(f"cp {latest_backup} {config['deploy_path']}/license_server.py")
            stdout.channel.recv_exit_status()
            print("   ✅ 已恢复备份文件")
        else:
            print("   ❌ 未找到备份文件")
            return False
        
        # 步骤3: 检查恢复后的文件
        print("🔍 步骤3: 检查恢复后的文件...")
        stdin, stdout, stderr = client.exec_command(f"grep -c 'def check_update' {config['deploy_path']}/license_server.py")
        check_count = stdout.read().decode('utf-8').strip()
        print(f"   check_update函数数量: {check_count}")
        
        # 步骤4: 添加新的更新API（使用完全不同的路由）
        print("📝 步骤4: 添加新的更新API...")
        
        # 使用完全不同的路由路径避免冲突
        new_api_code = '''

# ==================== 自动更新API ====================

import hashlib
import mimetypes
from werkzeug.utils import secure_filename

# 更新文件存储目录
UPDATE_DIR = "/opt/license_manager/updates"
CURRENT_VERSION = "2.1.0"

# 确保更新目录存在
os.makedirs(UPDATE_DIR, exist_ok=True)

@app.route('/update/check', methods=['GET'])
def update_check_api():
    """检查更新API - 使用不同路径避免冲突"""
    try:
        # 获取参数
        license_key = request.args.get('key')
        device_id = request.args.get('device_id')
        current_version = request.args.get('current_version', '1.0.0')
        
        # 获取最新版本信息
        latest_version = get_latest_version_info()
        
        if not latest_version:
            return jsonify({
                "success": True,
                "has_update": False,
                "message": "当前已是最新版本"
            })
        
        # 比较版本
        if is_version_newer(latest_version['version'], current_version):
            return jsonify({
                "success": True,
                "has_update": True,
                "update_info": {
                    "version": latest_version['version'],
                    "file_size": latest_version['file_size'],
                    "file_hash": latest_version['file_hash'],
                    "changelog": latest_version['changelog'],
                    "download_url": f"/update/download?version={latest_version['version']}"
                }
            })
        else:
            return jsonify({
                "success": True,
                "has_update": False,
                "message": "当前已是最新版本"
            })
            
    except Exception as e:
        return jsonify({"success": False, "message": f"检查更新失败: {str(e)}"})

@app.route('/update/download', methods=['GET'])
def update_download_api():
    """下载更新文件API"""
    try:
        version = request.args.get('version')
        
        if not version:
            return jsonify({"success": False, "message": "缺少版本参数"})
        
        # 查找更新文件
        update_file = find_update_file_path(version)
        if not update_file:
            return jsonify({"success": False, "message": "更新文件不存在"})
        
        # 返回文件
        return send_file(
            update_file,
            as_attachment=True,
            download_name=f"亚马逊蓝图工具_v{version}.exe",
            mimetype='application/octet-stream'
        )
        
    except Exception as e:
        return jsonify({"success": False, "message": f"下载失败: {str(e)}"})

@app.route('/update/upload', methods=['POST'])
def update_upload_api():
    """上传更新文件API（管理员使用）"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({"success": False, "message": "没有文件"})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "message": "没有选择文件"})
        
        # 获取版本信息
        version = request.form.get('version')
        changelog = request.form.get('changelog', '')
        
        if not version:
            return jsonify({"success": False, "message": "缺少版本信息"})
        
        # 保存文件
        filename = secure_filename(f"亚马逊蓝图工具_v{version}.exe")
        file_path = os.path.join(UPDATE_DIR, filename)
        file.save(file_path)
        
        # 计算文件哈希
        file_hash = calculate_file_hash_value(file_path)
        file_size = os.path.getsize(file_path)
        
        # 保存版本信息
        save_version_info_to_file(version, file_size, file_hash, changelog, filename)
        
        return jsonify({
            "success": True,
            "message": "更新文件上传成功",
            "version": version,
            "file_size": file_size,
            "file_hash": file_hash
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"上传失败: {str(e)}"})

@app.route('/update/versions', methods=['GET'])
def update_versions_api():
    """列出所有版本API"""
    try:
        versions = get_all_version_list()
        return jsonify({
            "success": True,
            "versions": versions,
            "current_version": CURRENT_VERSION
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"获取版本列表失败: {str(e)}"})

# ==================== 辅助函数 ====================

def get_latest_version_info():
    """获取最新版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return None
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        if not versions:
            return None
        
        # 返回最新版本
        latest = max(versions, key=lambda x: version_to_tuple_value(x['version']))
        return latest
        
    except Exception as e:
        print(f"获取最新版本失败: {e}")
        return None

def get_all_version_list():
    """获取所有版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return []
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        # 按版本号排序
        versions.sort(key=lambda x: version_to_tuple_value(x['version']), reverse=True)
        return versions
        
    except Exception as e:
        print(f"获取版本列表失败: {e}")
        return []

def save_version_info_to_file(version, file_size, file_hash, changelog, filename):
    """保存版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        
        # 读取现有版本
        versions = []
        if os.path.exists(version_file):
            with open(version_file, 'r', encoding='utf-8') as f:
                versions = json.load(f)
        
        # 添加新版本
        new_version = {
            "version": version,
            "file_size": file_size,
            "file_hash": file_hash,
            "changelog": changelog,
            "filename": filename,
            "upload_time": datetime.now().isoformat()
        }
        
        # 移除同版本的旧记录
        versions = [v for v in versions if v['version'] != version]
        versions.append(new_version)
        
        # 保存
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(versions, f, ensure_ascii=False, indent=2)
        
    except Exception as e:
        print(f"保存版本信息失败: {e}")

def find_update_file_path(version):
    """查找更新文件"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return None
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        for v in versions:
            if v['version'] == version:
                file_path = os.path.join(UPDATE_DIR, v['filename'])
                if os.path.exists(file_path):
                    return file_path
        
        return None
        
    except Exception as e:
        print(f"查找更新文件失败: {e}")
        return None

def calculate_file_hash_value(file_path):
    """计算文件SHA256哈希"""
    try:
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    except Exception as e:
        print(f"计算文件哈希失败: {e}")
        return ""

def is_version_newer(latest, current):
    """比较版本号"""
    try:
        return version_to_tuple_value(latest) > version_to_tuple_value(current)
    except:
        return False

def version_to_tuple_value(version):
    """将版本号转换为元组用于比较"""
    try:
        return tuple(map(int, version.split('.')))
    except:
        return (0, 0, 0)

# ==================== 更新API结束 ====================
'''
        
        # 将新API代码写入临时文件
        stdin, stdout, stderr = client.exec_command(f"cat > /tmp/new_update_api.py << 'EOF'\n{new_api_code}EOF")
        stdout.channel.recv_exit_status()
        
        # 将新API添加到license_server.py末尾（在app.run之前）
        stdin, stdout, stderr = client.exec_command(f"""
        # 创建新的license_server.py
        head -n -1 {config['deploy_path']}/license_server.py > {config['deploy_path']}/license_server_new.py
        cat /tmp/new_update_api.py >> {config['deploy_path']}/license_server_new.py
        tail -n 1 {config['deploy_path']}/license_server.py >> {config['deploy_path']}/license_server_new.py
        mv {config['deploy_path']}/license_server_new.py {config['deploy_path']}/license_server.py
        rm /tmp/new_update_api.py
        """)
        stdout.channel.recv_exit_status()
        print("   ✅ 新的更新API代码已添加（使用不同路径）")
        
        # 步骤5: 验证语法
        print("🔍 步骤5: 验证Python语法...")
        stdin, stdout, stderr = client.exec_command(f"python3 -m py_compile {config['deploy_path']}/license_server.py")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ Python语法检查通过")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ Python语法错误: {error}")
            return False
        
        # 步骤6: 启动服务
        print("🚀 步骤6: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error}")
            return False
        
        # 等待服务启动
        import time
        time.sleep(8)
        
        # 步骤7: 检查服务状态
        print("🔄 步骤7: 检查服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("   ✅ 服务运行正常")
        else:
            print(f"   ❌ 服务状态异常: {status}")
            return False
        
        # 步骤8: 测试新的API路径
        print("🧪 步骤8: 测试新的API路径...")
        
        import requests
        server_url = "http://**************:5000"
        
        test_apis = [
            ("检查更新", f"{server_url}/update/check?current_version=1.0.0"),
            ("版本列表", f"{server_url}/update/versions")
        ]
        
        success_count = 0
        for name, url in test_apis:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"   ✅ {name}: HTTP {response.status_code}")
                    success_count += 1
                else:
                    print(f"   ❌ {name}: HTTP {response.status_code}")
            except Exception as e:
                print(f"   ❌ {name}: 连接失败 - {e}")
        
        print(f"\n📊 API测试结果: {success_count}/{len(test_apis)} 个接口正常")
        
        # 关闭连接
        client.close()
        
        if success_count >= 1:
            print("\n🎉 API冲突彻底修复成功！")
            print("🌐 新的API路径:")
            print("• 📋 检查更新: http://**************:5000/update/check")
            print("• 📥 下载更新: http://**************:5000/update/download")
            print("• 📤 上传更新: http://**************:5000/update/upload")
            print("• 📊 版本列表: http://**************:5000/update/versions")
            return True
        else:
            print("\n⚠️ 修复完成，但API测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 彻底修复API冲突问题")
        print("🔧 方案: 恢复备份 + 使用不同路径")
        print()
        
        print("📋 新方案:")
        print("• 恢复到工作状态的备份文件")
        print("• 使用完全不同的API路径 (/update/* 而不是 /api/*)")
        print("• 避免与现有函数名冲突")
        print()
        
        if restore_and_fix():
            print("\n✅ 彻底修复成功！")
            print("💡 现在需要更新客户端配置使用新的API路径")
        else:
            print("\n❌ 修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
