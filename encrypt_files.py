import base64
import os
import random
import string
import logging
import sys
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('encrypt.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("EncryptTool")

# 加密密钥
DEFAULT_KEY = "AmazonLicenseSecretKey2023"

def xor_encrypt(data, key):
    """使用XOR对数据进行加密/解密（保留用于向后兼容）"""
    key_bytes = key.encode() if isinstance(key, str) else key
    data_bytes = data.encode() if isinstance(data, str) else data
    
    # 循环使用密钥的每个字节与数据进行异或操作
    key_len = len(key_bytes)
    encrypted = bytearray(len(data_bytes))
    
    for i in range(len(data_bytes)):
        encrypted[i] = data_bytes[i] ^ key_bytes[i % key_len]
    
    return bytes(encrypted)

def aes_encrypt(data, key):
    """使用AES-256-GCM加密数据"""
    # 生成随机IV和salt
    iv = os.urandom(12)
    salt = os.urandom(16)
    
    # 派生密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
        backend=default_backend()
    )
    derived_key = kdf.derive(key.encode() if isinstance(key, str) else key)
    
    # 加密
    cipher = Cipher(
        algorithms.AES(derived_key),
        modes.GCM(iv),
        backend=default_backend()
    )
    encryptor = cipher.encryptor()
    
    # 添加认证数据
    encryptor.authenticate_additional_data(b"AmazonLicense")
    
    # 加密数据
    data_bytes = data.encode() if isinstance(data, str) else data
    ciphertext = encryptor.update(data_bytes) + encryptor.finalize()
    
    # 返回组合的数据: salt + iv + tag + ciphertext
    return salt + iv + encryptor.tag + ciphertext

def aes_decrypt(encrypted_data, key):
    """使用AES-256-GCM解密数据"""
    # 解析组合的数据
    salt = encrypted_data[:16]
    iv = encrypted_data[16:28]
    tag = encrypted_data[28:44]
    ciphertext = encrypted_data[44:]
    
    # 派生密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
        backend=default_backend()
    )
    derived_key = kdf.derive(key.encode() if isinstance(key, str) else key)
    
    # 解密
    cipher = Cipher(
        algorithms.AES(derived_key),
        modes.GCM(iv, tag),
        backend=default_backend()
    )
    decryptor = cipher.decryptor()
    
    # 添加认证数据
    decryptor.authenticate_additional_data(b"AmazonLicense")
    
    # 解密数据
    return decryptor.update(ciphertext) + decryptor.finalize()

def encrypt_file(file_path, output_path=None, key=DEFAULT_KEY):
    """加密文件内容"""
    try:
        # 如果未指定输出路径，则生成默认路径
        if output_path is None:
            output_path = file_path + ".encrypted"
        
        logger.info(f"加密文件: {file_path}")
        
        # 读取文件内容
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # 使用AES加密内容
        try:
            encrypted_content = aes_encrypt(content, key)
            encryption_method = "AES-256-GCM"
        except Exception as e:
            logger.warning(f"AES加密失败，回退到XOR加密: {str(e)}")
            encrypted_content = xor_encrypt(content, key)
            encryption_method = "XOR"
        
        # 添加标识头，用于区分加密方法
        if encryption_method == "AES-256-GCM":
            header = b'AESENC01'  # AES加密标识
        else:
            header = b'XORENC01'  # XOR加密标识
            
        # 组合头部和加密内容
        final_content = header + encrypted_content
        
        # Base64编码
        base64_content = base64.b64encode(final_content)
        
        # 写入加密文件
        with open(output_path, 'wb') as f:
            f.write(base64_content)
        
        logger.info(f"文件加密成功: {output_path} (使用 {encryption_method})")
        return output_path
    
    except Exception as e:
        logger.error(f"加密文件时出错: {str(e)}")
        raise

def decrypt_content(encrypted_content, key=DEFAULT_KEY):
    """解密内容"""
    try:
        # Base64解码
        decoded_content = base64.b64decode(encrypted_content)
        
        # 检查加密方法标识头
        if len(decoded_content) > 8:
            header = decoded_content[:8]
            data = decoded_content[8:]
            
            if header == b'AESENC01':
                # 使用AES解密
                logger.info("检测到AES加密内容")
                try:
                    return aes_decrypt(data, key)
                except Exception as e:
                    logger.error(f"AES解密失败: {str(e)}")
                    raise
            elif header == b'XORENC01':
                # 使用XOR解密
                logger.info("检测到XOR加密内容")
                return xor_encrypt(data, key)
        
        # 如果没有有效的头部或无法识别，尝试兼容模式
        logger.info("未检测到加密标识头，尝试兼容模式")
        return xor_encrypt(decoded_content, key)
    
    except Exception as e:
        logger.error(f"解密内容时出错: {str(e)}")
        raise

def decrypt_file(file_path, output_path=None, key=DEFAULT_KEY):
    """解密文件"""
    try:
        # 如果未指定输出路径，则生成默认路径
        if output_path is None:
            if file_path.endswith(".encrypted"):
                output_path = file_path[:-10]
            else:
                output_path = file_path + ".decrypted"
        
        logger.info(f"解密文件: {file_path}")
        
        # 读取加密文件内容
        with open(file_path, 'rb') as f:
            encrypted_content = f.read()
        
        # 解密内容
        decrypted_content = decrypt_content(encrypted_content, key)
        
        # 写入解密文件
        with open(output_path, 'wb') as f:
            f.write(decrypted_content)
        
        logger.info(f"文件解密成功: {output_path}")
        return output_path
    
    except Exception as e:
        logger.error(f"解密文件时出错: {str(e)}")
        raise

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python encrypt_files.py <文件路径> [输出路径] [--decrypt]")
        sys.exit(1)
    
    file_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 and not sys.argv[2].startswith('--') else None
    is_decrypt = '--decrypt' in sys.argv
    
    try:
        if is_decrypt:
            decrypt_file(file_path, output_path)
        else:
            encrypt_file(file_path, output_path)
    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1) 