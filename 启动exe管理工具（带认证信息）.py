#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动exe管理工具（带认证信息显示）
"""

import subprocess
import sys
import os

def main():
    """启动exe文件管理工具"""
    print("🚀 启动exe文件管理工具")
    print("=" * 50)
    print("🔑 管理员认证信息:")
    print("   密钥: ADMIN_BYPASS")
    print("   设备ID: ADMIN-DEVICE-001")
    print("   服务器: http://198.23.135.176:5000")
    print()
    print("✨ 新功能:")
    print("   - 界面显示管理员认证信息")
    print("   - 实时服务器连接状态")
    print("   - API认证状态指示器")
    print()
    print("🎯 正在启动管理工具...")
    
    try:
        # 检查文件是否存在
        tool_file = "exe文件管理工具.py"
        if not os.path.exists(tool_file):
            print(f"❌ 文件不存在: {tool_file}")
            input("按回车键退出...")
            return
        
        # 启动管理工具
        subprocess.run([sys.executable, tool_file], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消启动")
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
