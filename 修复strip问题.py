#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复PyInstaller strip工具问题
"""

import os
import sys
import subprocess
import platform

def check_strip_available():
    """检查系统是否有strip工具"""
    print("🔍 检查strip工具可用性")
    print("=" * 40)
    
    try:
        # 尝试运行strip --version
        result = subprocess.run(['strip', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ strip工具可用")
            print(f"版本信息: {result.stdout.strip()}")
            return True
        else:
            print("❌ strip工具不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, OSError) as e:
        print(f"❌ strip工具不可用: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n🔧 解决方案")
    print("=" * 40)
    
    system = platform.system()
    
    if system == "Windows":
        print("Windows系统解决方案:")
        print("1. 【推荐】禁用strip功能")
        print("   - 在构建选项中取消勾选'去除调试信息'")
        print("   - 或在代码中设置 strip=False")
        print()
        print("2. 安装MinGW工具链")
        print("   - 下载并安装MinGW-w64")
        print("   - 将MinGW的bin目录添加到PATH")
        print("   - 重启命令行工具")
        print()
        print("3. 安装MSYS2")
        print("   - 下载并安装MSYS2")
        print("   - 运行: pacman -S mingw-w64-x86_64-binutils")
        print("   - 将MSYS2的bin目录添加到PATH")
        
    elif system == "Linux":
        print("Linux系统解决方案:")
        print("1. 安装binutils包")
        print("   Ubuntu/Debian: sudo apt-get install binutils")
        print("   CentOS/RHEL: sudo yum install binutils")
        print("   Fedora: sudo dnf install binutils")
        
    elif system == "Darwin":
        print("macOS系统解决方案:")
        print("1. 安装Xcode Command Line Tools")
        print("   xcode-select --install")
        print("2. 或安装Homebrew binutils")
        print("   brew install binutils")

def fix_build_config():
    """修复构建配置"""
    print("\n🛠️ 修复构建配置")
    print("=" * 40)
    
    # 检查build_gui_advanced.py是否存在
    if not os.path.exists("build_gui_advanced.py"):
        print("❌ 未找到build_gui_advanced.py文件")
        return False
    
    try:
        # 读取文件
        with open("build_gui_advanced.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查当前strip设置
        if "'strip': True" in content:
            print("⚠️ 发现strip设置为True")
            
            # 替换为False
            new_content = content.replace("'strip': True", "'strip': False")
            
            # 写回文件
            with open("build_gui_advanced.py", 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 已将strip设置修改为False")
            return True
        elif "'strip': False" in content:
            print("✅ strip设置已经是False")
            return True
        else:
            print("⚠️ 未找到strip设置")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_pyinstaller_without_strip():
    """测试不使用strip的PyInstaller命令"""
    print("\n🧪 测试PyInstaller命令")
    print("=" * 40)
    
    # 创建一个简单的测试脚本
    test_script = """
import tkinter as tk

def main():
    root = tk.Tk()
    root.title("测试")
    root.geometry("200x100")
    label = tk.Label(root, text="Hello World!")
    label.pack(pady=20)
    root.mainloop()

if __name__ == "__main__":
    main()
"""
    
    try:
        # 写入测试脚本
        with open("test_app.py", 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print("✅ 创建测试脚本: test_app.py")
        
        # 构建PyInstaller命令（不使用strip）
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--clean",
            "--noupx",
            "--name=测试应用",
            "test_app.py"
        ]
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        print("⏳ 构建中...")
        
        # 执行命令
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 测试构建成功！")
            print("✅ 不使用strip可以正常构建")
            
            # 清理测试文件
            try:
                os.remove("test_app.py")
                if os.path.exists("test_app.spec"):
                    os.remove("test_app.spec")
                print("✅ 清理测试文件完成")
            except:
                pass
                
            return True
        else:
            print("❌ 测试构建失败")
            print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 PyInstaller Strip问题修复工具")
    print("=" * 50)
    
    # 检查strip工具
    strip_available = check_strip_available()
    
    # 建议解决方案
    suggest_solutions()
    
    # 修复构建配置
    config_fixed = fix_build_config()
    
    # 测试构建
    if not strip_available:
        print("\n💡 由于strip工具不可用，建议禁用strip功能")
        test_success = test_pyinstaller_without_strip()
    else:
        print("\n✅ strip工具可用，可以正常使用strip功能")
        test_success = True
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 修复结果总结:")
    print(f"   - Strip工具可用: {'✅' if strip_available else '❌'}")
    print(f"   - 配置修复成功: {'✅' if config_fixed else '❌'}")
    print(f"   - 测试构建成功: {'✅' if test_success else '❌'}")
    
    if not strip_available and config_fixed:
        print("\n🎉 修复完成！")
        print("💡 建议:")
        print("1. 重新启动build_gui_advanced.py")
        print("2. 确保'去除调试信息'选项未勾选")
        print("3. 正常进行构建")
    elif strip_available:
        print("\n✅ 系统正常，可以使用strip功能")
    else:
        print("\n⚠️ 需要手动处理")

if __name__ == "__main__":
    main()
