#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复Flask路由问题
创建一个简单的测试服务器
"""

import paramiko
import sys
import time

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def create_simple_server():
    """创建简单的测试服务器"""
    print("🔧 创建简单的测试服务器")
    print("=" * 40)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    # 创建简单的Flask应用
    simple_server = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的授权服务器 - 测试版本
"""

from flask import Flask, jsonify, request
from datetime import datetime
import json
import os

app = Flask(__name__)

@app.route('/')
def index():
    """主页"""
    return jsonify({
        "status": "success",
        "message": "亚马逊蓝图工具授权服务器",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "server": "CentOS 7.4",
        "endpoints": [
            "/",
            "/health",
            "/api/check_update",
            "/api/status",
            "/test"
        ]
    })

@app.route('/health')
def health():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "uptime": "running"
    })

@app.route('/api/status')
def api_status():
    """API状态"""
    return jsonify({
        "api_status": "running",
        "server": "license_manager",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })

@app.route('/api/check_update')
def check_update():
    """检查更新"""
    return jsonify({
        "has_update": False,
        "current_version": "1.0.0",
        "latest_version": "1.0.0",
        "message": "当前版本是最新的",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/test')
def test():
    """测试页面"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    current_iso = datetime.now().isoformat()

    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>亚马逊蓝图工具授权服务器</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            h1 { color: #2c3e50; }
            .status { color: #27ae60; font-weight: bold; }
            .endpoint { margin: 10px 0; }
            .endpoint a { color: #3498db; text-decoration: none; }
            .endpoint a:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <h1>🎉 亚马逊蓝图工具授权服务器</h1>
        <p class="status">✅ 服务器运行正常</p>
        <p>服务器时间: """ + current_time + """</p>
        <p>服务器地址: **************:5000</p>

        <h2>📋 可用接口:</h2>
        <div class="endpoint">🏠 <a href="/">主页 (/)</a> - 服务器信息</div>
        <div class="endpoint">💚 <a href="/health">健康检查 (/health)</a> - 服务状态</div>
        <div class="endpoint">📊 <a href="/api/status">API状态 (/api/status)</a> - API信息</div>
        <div class="endpoint">🔄 <a href="/api/check_update">检查更新 (/api/check_update)</a> - 版本检查</div>
        <div class="endpoint">🧪 <a href="/test">测试页面 (/test)</a> - 当前页面</div>

        <h2>🎯 部署信息:</h2>
        <ul>
            <li>操作系统: CentOS 7.4</li>
            <li>Python版本: 3.6+</li>
            <li>Flask框架: 已安装</li>
            <li>部署路径: /opt/license_manager</li>
            <li>服务状态: 正常运行</li>
        </ul>

        <p><small>🕒 页面生成时间: """ + current_iso + """</small></p>
    </body>
    </html>
    """

    return html_content

if __name__ == "__main__":
    print("🚀 启动亚马逊蓝图工具授权服务器...")
    print(f"📅 启动时间: {datetime.now().isoformat()}")
    print("🌐 监听地址: 0.0.0.0:5000")
    
    app.run(
        host="0.0.0.0",
        port=5000,
        debug=False,
        threaded=True
    )
'''
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 步骤1: 停止当前服务
    print("🛑 步骤1: 停止当前服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl stop license-manager")
    print("   ✅ 服务已停止")
    print()
    
    # 步骤2: 创建新的服务器文件
    print("📝 步骤2: 创建新的服务器文件...")
    
    # 使用cat命令创建文件
    command = f"""cd {config['deploy_path']} && \
cat > simple_server.py << 'ENDOFFILE'
{simple_server}
ENDOFFILE
chmod +x simple_server.py && \
echo "文件创建完成"
"""
    
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], command)
    if success:
        print("   ✅ 简单服务器文件已创建")
    else:
        print(f"   ❌ 文件创建失败: {error}")
        return False
    print()
    
    # 步骤3: 更新systemd服务配置
    print("⚙️ 步骤3: 更新systemd服务配置...")
    
    service_content = f"""[Unit]
Description=License Manager Service - Simple
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={config['deploy_path']}
ExecStart=/usr/bin/python3 {config['deploy_path']}/simple_server.py
Restart=always
RestartSec=10
Environment=PYTHONPATH={config['deploy_path']}
Environment=FLASK_ENV=production

[Install]
WantedBy=multi-user.target"""
    
    command = f"""cat > /etc/systemd/system/license-manager.service << 'EOF'
{service_content}
EOF
systemctl daemon-reload && \
echo "服务配置已更新"
"""
    
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], command)
    if success:
        print("   ✅ systemd服务配置已更新")
    else:
        print(f"   ❌ 服务配置失败: {error}")
        return False
    print()
    
    # 步骤4: 启动新服务
    print("🚀 步骤4: 启动新服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl start license-manager")
    if success:
        print("   ✅ 服务已启动")
    else:
        print(f"   ❌ 服务启动失败: {error}")
        return False
    
    # 等待服务启动
    time.sleep(5)
    print()
    
    # 步骤5: 测试服务
    print("🧪 步骤5: 测试服务...")
    
    test_urls = [
        ("主页", "http://localhost:5000/"),
        ("健康检查", "http://localhost:5000/health"),
        ("测试页面", "http://localhost:5000/test")
    ]
    
    success_count = 0
    for name, url in test_urls:
        success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                           f"curl -s {url}")
        if success and output.strip():
            print(f"   ✅ {name}: 正常")
            success_count += 1
        else:
            print(f"   ❌ {name}: 失败")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_urls)} 个接口正常")
    
    return success_count > 0

def main():
    """主函数"""
    try:
        print("🔍 问题: Flask应用路由配置复杂，导致根路径404")
        print("🎯 解决方案: 创建简单的测试服务器")
        print()
        
        success = create_simple_server()
        
        if success:
            print("\n🎉 简单服务器创建成功！")
            print("\n🌐 现在可以访问:")
            print("• 主页: http://**************:5000/")
            print("• 健康检查: http://**************:5000/health")
            print("• API状态: http://**************:5000/api/status")
            print("• 测试页面: http://**************:5000/test")
            print("• 检查更新: http://**************:5000/api/check_update")
            print("\n💡 这是一个简化版本，包含所有基本功能")
        else:
            print("\n❌ 服务器创建失败")
            print("💡 建议检查系统日志")
            
    except Exception as e:
        print(f"❌ 创建过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
