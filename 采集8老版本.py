﻿# -*- coding: utf-8 -*-
# Amazon Product Scraper Tool - Version 8
# This tool is used to scrape product information from Amazon websites
# Supports multiple countries and languages
# Encoding: UTF-8

import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox, Menu
import threading
import pandas as pd
import time
import re
import os
import json
import psutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.service import Service as ChromeService  
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.common.service import Service  # 添加通用Service类
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from selenium.webdriver.edge.options import Options as EdgeOptions
from webdriver_manager.firefox import GeckoDriverManager
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options as ChromeOptions
from urllib.parse import urlparse, parse_qs
from base64 import b64decode
import platform
import sys
import requests
import hashlib
import random
import openpyxl  # 添加openpyxl导入，用于Excel文件处理
import queue

# 添加线程本地存储，用于管理每个线程的驱动实例
thread_local = threading.local()

# 内置UI主题，避免依赖外部文件
class AmazonUITheme:
    """亚马逊产品分析工具统一界面主题"""
    
    # 颜色方案
    COLORS = {
        "primary": "#356cac",     # 深蓝色 - 主色
        "secondary": "#4a90e2",   # 亮蓝色 - 次要色
        "accent": "#f89406",      # 橙色 - 强调色
        "background": "#f5f5f5",  # 浅灰色 - 背景色
        "text": "#333333",        # 深灰色 - 文本色
        "light_text": "#666666",  # 中灰色 - 次要文本
        "border": "#dddddd",      # 边框色
        "success": "#28a745",     # 成功色
        "warning": "#ffc107",     # 警告色
        "error": "#dc3545",       # 错误色
        "white": "#ffffff",       # 白色
        "light_gray": "#f0f0f0"   # 更浅的灰色
    }
    
    # 字体设置
    FONTS = {
        "title": ("微软雅黑", 12, "bold"),
        "subtitle": ("微软雅黑", 11, "bold"),
        "body": ("微软雅黑", 10),
        "small": ("微软雅黑", 9),
        "menu": ("微软雅黑", 10),
        "button": ("微软雅黑", 10),
        "status": ("微软雅黑", 9)
    }
    
    # 尺寸和间距
    PADDING = {
        "frame": 15,      # 框架内边距
        "button": 8,      # 按钮内边距
        "widget": 5,      # 控件间距
        "section": 10,    # 区块间距
        "tiny": 2,        # 最小间距
    }
    
    @classmethod
    def get_image_path(cls):
        """获取图标路径"""
        try:
            # 确定工作目录
            if getattr(sys, 'frozen', False):
                base_dir = os.path.dirname(sys.executable)
            else:
                base_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 图标文件路径
            icon_path = os.path.join(base_dir, "icon.ico")
            if os.path.exists(icon_path):
                return icon_path
            else:
                print("Warning: Icon file not found:", icon_path)
        except Exception as e:
            print("Error setting icon:", str(e))
        return None
    
    @classmethod
    def setup_styles(cls):
        """设置通用ttk样式"""
        style = ttk.Style()
        
        # 设置全局主题
        try:
            style.theme_use("clam")  # 使用clam主题作为基础
        except:
            pass  # 如果主题不可用，使用默认主题
        
        # 背景配置
        style.configure("TFrame", background=cls.COLORS["background"])
        style.configure("TLabelframe", background=cls.COLORS["background"])
        style.configure("TLabelframe.Label", background=cls.COLORS["background"], 
                        foreground=cls.COLORS["primary"], font=cls.FONTS["subtitle"])
        
        # 按钮风格
        style.configure("TButton", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["button"],
                        padding=cls.PADDING["button"])
        
        style.map("TButton",
                  background=[('active', cls.COLORS["secondary"]), 
                              ('disabled', cls.COLORS["border"])],
                  foreground=[('disabled', cls.COLORS["light_text"])])
        
        # 次要按钮风格
        style.configure("Secondary.TButton", 
                        background=cls.COLORS["secondary"],
                        foreground=cls.COLORS["white"])
        
        # 强调按钮风格
        style.configure("Accent.TButton", 
                        background=cls.COLORS["accent"],
                        foreground=cls.COLORS["white"])
        
        # 标签风格
        style.configure("TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 标题标签风格
        style.configure("Title.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["primary"],
                        font=cls.FONTS["title"])
        
        # 子标题标签风格
        style.configure("Subtitle.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["secondary"],
                        font=cls.FONTS["subtitle"])
        
        # Entry风格
        style.configure("TEntry", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # Combobox风格
        style.configure("TCombobox", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # 复选框风格
        style.configure("TCheckbutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 单选按钮风格
        style.configure("TRadiobutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 进度条风格
        style.configure("TProgressbar", 
                        background=cls.COLORS["primary"],
                        troughcolor=cls.COLORS["light_gray"])
        
        # 分隔符风格
        style.configure("TSeparator", 
                        background=cls.COLORS["border"])
        
        # Treeview (表格) 风格
        style.configure("Treeview", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        font=cls.FONTS["body"])
        
        style.map("Treeview",
                  background=[('selected', cls.COLORS["secondary"])],
                  foreground=[('selected', cls.COLORS["white"])])
        
        style.configure("Treeview.Heading", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["subtitle"],
                        relief="flat")
        
        # Notebook (选项卡) 风格
        style.configure("TNotebook", 
                        background=cls.COLORS["background"],
                        tabmargins=[2, 5, 2, 0])
        
        style.configure("TNotebook.Tab", 
                        background=cls.COLORS["light_gray"],
                        foreground=cls.COLORS["text"],
                        padding=[10, 4],
                        font=cls.FONTS["body"])
        
        style.map("TNotebook.Tab",
                  background=[('selected', cls.COLORS["primary"])],
                  foreground=[('selected', cls.COLORS["white"])],
                  expand=[('selected', [1, 1, 1, 0])])
        
        return style
    
    @classmethod
    def setup_window(cls, root, title, size="800x600", resizable=(True, True), icon=True):
        """设置窗口基本属性"""
        root.title(title)
        root.geometry(size)
        root.resizable(resizable[0], resizable[1])
        root.configure(bg=cls.COLORS["background"])
        
        # 设置图标
        if icon:
            icon_path = cls.get_image_path()
            if icon_path and os.path.exists(icon_path):
                try:
                    root.iconbitmap(icon_path)
                except Exception as e:
                    print(f"设置窗口图标失败: {str(e)}")
        
        # 设置样式 - 确保样式被应用
        cls.setup_styles()
        
        return root
    
    @classmethod
    def create_title_frame(cls, parent, title_text):
        """创建标题栏框架"""
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["frame"], 0))
        
        # 标题标签
        title_label = ttk.Label(title_frame, text=title_text, style="Title.TLabel")
        title_label.pack(side=tk.LEFT, pady=cls.PADDING["section"])
        
        # 添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(0, cls.PADDING["section"]))
        
        return title_frame
    
    @classmethod
    def create_footer_frame(cls, parent):
        """创建底部按钮框架"""
        # 先添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["section"], 0))
        
        footer_frame = ttk.Frame(parent)
        footer_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=cls.PADDING["section"])
        
        return footer_frame
    
    @classmethod
    def create_section_frame(cls, parent, title=None):
        """创建分区框架（可选带标题）"""
        if title:
            section_frame = ttk.LabelFrame(parent, text=title)
        else:
            section_frame = ttk.Frame(parent)
        
        section_frame.pack(fill=tk.BOTH, expand=True, padx=cls.PADDING["frame"], 
                          pady=cls.PADDING["section"])
        
        return section_frame

# Function to check and install required packages
def check_and_install_dependencies():
    """检查并安装必要的依赖"""
    required_packages = {
        "selenium": "selenium",
        "pandas": "pandas",
        "webdriver_manager": "webdriver-manager",  # 修正：导入名使用下划线，pip安装名使用横线
        "psutil": "psutil"
    }
    
    missing_packages = []
    
    # 检查每个必要的包
    for package_name, pip_name in required_packages.items():
        try:
            __import__(package_name)
        except ImportError:
            missing_packages.append(pip_name)
    
    # 如果有缺失的包，提示安装
    if missing_packages:
        message = "缺少以下必要的包:\n"
        for package in missing_packages:
            message += f"- {package}\n"
        message += "\n请使用以下命令安装:\n"
        message += f"pip install {' '.join(missing_packages)}"
        
        print(message)
        
        # 在Windows上可以自动打开命令提示符
        if platform.system() == "Windows":
            try:
                cmd = f'start cmd /k "pip install {" ".join(missing_packages)} && echo 安装完成后请重启程序 && pause"'
                os.system(cmd)
            except:
                pass
        
        # 退出程序
        sys.exit(1)
    
    return True

# Run the dependency check before anything else
if not check_and_install_dependencies():
    messagebox.showerror("初始化失败", "环境配置不正确，请联系管理员")
    sys.exit(1)

# Now import the packages that were installed
import pandas as pd
import psutil
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from webdriver_manager.firefox import GeckoDriverManager
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from urllib.parse import urlparse, parse_qs
from base64 import b64decode
import requests
import hashlib

class AmazonScraper:
    def __init__(self, master):
        self.master = master
        
        # 使用统一UI主题设置窗口
        AmazonUITheme.setup_window(
            self.master,
            "亚马逊产品采集工具", 
            size="980x650", 
            resizable=(True, True)
        )
        
        self.all_amazon_codes = set()
        self.stop_scraping = False
        self.current_keyword_index = 0
        self.progress_file = "scraper_progress.json"
        self.timeout_file = "timeout_keywords.txt"
        self.timeout_keywords = []
        self.realtime_excel_file = None
        self.memory_monitor_active = False
        self.headless_mode = tk.BooleanVar(value=True)  # 默认使用无头模式
        self.enable_translation = tk.BooleanVar(value=True)  # 默认使用翻译
        self.progress_var = tk.DoubleVar()  # 进度条变量
        self.realtime_save = tk.BooleanVar(value=True)  # 实时保存到Excel
        self.selected_country = tk.StringVar(value="美国")  # 默认选择美国
        self.selected_file_var = tk.StringVar()  # 显示选择的文件名
        
        # 添加链接缓存，用于快速检查重复
        self.cached_links = set()
        self.cache_initialized = False
        
        # 添加Excel文件索引，用于处理文件满的情况
        self.excel_file_index = 0
        
        # 添加当前Excel文件行数计数器
        self.current_row_count = 0
        
        # 添加批量保存相关变量
        self.batch_save_count = 0
        self.batch_save_limit = 50  # 每积累50个链接批量保存一次
        self.pending_codes = []  # 等待批量保存的代码列表
        
        self.link_count_var = tk.StringVar(value="已获取 0 个链接")
        self.current_keyword_var = tk.StringVar(value="当前关键词: 无")
        self.timeout_var = tk.StringVar(value="超时关键词: 0")
        self.memory_var = tk.StringVar(value="内存使用: 0 MB")
        
        # 数据结构
        self.countries = {
            "美国": {"domain": "amazon.com", "query_format": "site:amazon.com '{keyword}' 'currently unavailable'", "lang": "en"},
            "加拿大": {"domain": "amazon.ca", "query_format": "site:amazon.ca '{keyword}' 'currently unavailable'", "lang": "en"},
            "日本": {"domain": "amazon.co.jp", "query_format": "site:amazon.co.jp '{keyword}' '在庫切れ'", "lang": "ja"}
        }
        
        # 系统变量
        self.is_32bit = platform.architecture()[0] == '32bit'
        self.log_message_queue = queue.Queue()
        
        # 浏览器管理
        self.browser_instance = None
        self.browser_use_count = 0
        self.browser_last_used = 0
        self.browser_max_idle_time = 180  # 降低为3分钟最大空闲时间
        self.browser_max_uses = 30  # 降低最大使用次数以减少内存泄漏
        
        # 添加批处理大小设置（对32位系统特别有用）
        if self.is_32bit:
            self.batch_size = tk.IntVar(value=10000)  # 32位系统默认一次处理10000个关键词
        else:
            self.batch_size = tk.IntVar(value=10000)
        
        # 设置UI界面
        self.setup_ui()
        
        # 启动内存监控
        if self.is_32bit:
            self.start_memory_monitor()
            
        # 关闭窗口时执行清理
        self.master.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 检测驱动程序是否存在
        self.check_drivers()
    
    def setup_ui(self):
        """设置用户界面"""
        # 确保应用主题样式
        AmazonUITheme.setup_styles()
        
        # 设置复选框风格
        style = ttk.Style()
        style.configure("TCheckbutton", background="#f5f5f5")
        
        # 创建标题栏
        self.create_title_bar()
        
        # 创建控制面板
        self.create_control_panel()
        
        # 创建关键词输入区域
        self.create_keywords_input()
        
        # 创建状态和日志区域
        self.create_status_and_log()
        
        # 创建操作按钮
        self.create_action_buttons()
        
        # 创建文本右键菜单
        self.create_text_context_menu()
        
        # 尝试加载保存的链接缓存
        self.load_link_cache()
        
        # 检测驱动程序是否存在
        self.check_drivers()
    
    def check_drivers(self):
        """检查浏览器驱动是否存在，使用更全面的检查方式"""
        try:
            # 检查浏览器安装情况
            browsers_found = []
            
            # 检查Edge浏览器
            edge_paths = [
                r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe',
                r'C:\Program Files\Microsoft\Edge\Application\msedge.exe'
            ]
            for path in edge_paths:
                if os.path.exists(path):
                    browsers_found.append(f"Microsoft Edge: {path}")
                    break
            
            # 检查Chrome浏览器
            chrome_paths = [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
            ]
            for path in chrome_paths:
                if os.path.exists(path):
                    browsers_found.append(f"Google Chrome: {path}")
                    break
            
            # 检查Firefox浏览器
            firefox_paths = [
                r'C:\Program Files\Mozilla Firefox\firefox.exe',
                r'C:\Program Files (x86)\Mozilla Firefox\firefox.exe'
            ]
            for path in firefox_paths:
                if os.path.exists(path):
                    browsers_found.append(f"Mozilla Firefox: {path}")
                    break
            
            # 检查是否32位系统
            if self.is_32bit:
                # 启动内存监控
                self.start_memory_monitor()
            
            # 返回是否有浏览器安装
            return len(browsers_found) > 0
        except Exception as e:
            return False
    
    def create_title_bar(self):
        """创建标题栏"""
        title_frame = ttk.Frame(self.master)
        title_frame.pack(fill=tk.X, padx=10, pady=(5, 2))
        
        title_label = ttk.Label(
            title_frame, 
            text="亚马逊产品采集工具",
            font=("微软雅黑", 14, "bold"),
            foreground=AmazonUITheme.COLORS["primary"]
        )
        title_label.pack(side=tk.LEFT)
        
        # 添加横向分隔线
        separator = ttk.Separator(self.master, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=10, pady=2)
    
    def create_control_panel(self):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(self.master, text="采集设置", padding=5)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 第一行 - 国家选择和模式设置
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=2)
        
        # 国家选择
        ttk.Label(row1, text="选择国家:").pack(side=tk.LEFT, padx=(0, 5))
        country_combo = ttk.Combobox(
            row1, 
            textvariable=self.selected_country, 
            values=list(self.countries.keys()), 
            state="readonly",
            width=10
        )
        country_combo.pack(side=tk.LEFT, padx=5)
        
        # 翻译功能
        translate_check = ttk.Checkbutton(
            row1, 
            text="启用自动翻译", 
            variable=self.enable_translation
        )
        translate_check.pack(side=tk.LEFT, padx=10)
        
        # 实时保存选项
        self.realtime_save = tk.BooleanVar(value=True)
        realtime_save_check = ttk.Checkbutton(
            row1, 
            text="实时保存到表格", 
            variable=self.realtime_save
        )
        realtime_save_check.pack(side=tk.LEFT, padx=10)
    
    def create_keywords_input(self):
        """创建关键词输入区域"""
        keyword_frame = ttk.LabelFrame(self.master, text="关键词输入", padding=5)
        keyword_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建上方的文件导入区域
        import_frame = ttk.Frame(keyword_frame)
        import_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 导入说明标签
        ttk.Label(
            import_frame, 
            text="已选择文件:",
            font=("微软雅黑", 9, "bold"),
            foreground=AmazonUITheme.COLORS["primary"]
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 显示当前选择的文件名
        self.selected_file_var = tk.StringVar()
        selected_file_label = ttk.Label(
            import_frame, 
            textvariable=self.selected_file_var,
            font=("微软雅黑", 9),
            foreground=AmazonUITheme.COLORS["light_text"]
        )
        selected_file_label.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # 文件类型说明
        file_types_frame = ttk.Frame(keyword_frame)
        file_types_frame.pack(fill=tk.X, pady=(0, 2))
        
        ttk.Label(
            file_types_frame, 
            text="支持的文件格式: Excel文件(.xlsx)、CSV文件(.csv)、文本文件(.txt)",
            font=("微软雅黑", 8),
            foreground=AmazonUITheme.COLORS["light_text"]
        ).pack(anchor=tk.W)
        
        # 添加分隔线
        separator = ttk.Separator(keyword_frame, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=2)
        
        # 关键词输入说明
        ttk.Label(
            keyword_frame, 
            text="输入关键词 (每行一个):",
            font=("微软雅黑", 9, "bold")
        ).pack(anchor=tk.W, pady=(0, 2))
        
        # 关键词文本框 - 减小高度
        self.keywords_text = scrolledtext.ScrolledText(
            keyword_frame, 
            height=8, 
            width=80,
            font=("微软雅黑", 10),
            bg="white",
            wrap=tk.WORD
        )
        self.keywords_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)
    
    
    def create_status_and_log(self):
        """创建状态和日志区域"""
        log_frame = ttk.LabelFrame(self.master, text="采集日志", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建进度条
        progress_frame = ttk.Frame(log_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT, padx=(0, 5))
        progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=300)
        progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(
            log_frame, 
            height=6, 
            width=80,
            font=("微软雅黑", 9),
            bg="#fafafa",
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)  # 设置为只读
    
    def create_action_buttons(self):
        """创建操作按钮区域"""
        # 先添加横向分隔线
        separator = ttk.Separator(self.master, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=10, pady=2)
        
        button_frame = ttk.Frame(self.master)
        button_frame.pack(fill=tk.X, padx=10, pady=(2, 5))
        
        # 导入按钮
        self.import_button = ttk.Button(
            button_frame, 
            text="导入关键词", 
            command=self.import_keywords,
            width=15
        )
        self.import_button.pack(side=tk.LEFT, padx=5)
        
        # 保存按钮
        save_btn = ttk.Button(
            button_frame, 
            text="保存关键词", 
            command=self.save_keywords_to_file,
            width=15
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        # 空白填充
        ttk.Frame(button_frame).pack(side=tk.LEFT, expand=True)
        
        # 开始采集按钮 - 使用强调颜色
        style = ttk.Style()
        style.configure("Accent.TButton", 
                        background=AmazonUITheme.COLORS["accent"],
                        foreground="white")
        
        self.start_button = ttk.Button(
            button_frame, 
            text="开始采集", 
            command=self.start_scraping,
            width=15,
            style="Accent.TButton"
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        # 恢复按钮
        self.resume_button = ttk.Button(
            button_frame, 
            text="恢复采集", 
            command=self.resume_scraping,
            width=15
        )
        self.resume_button.pack(side=tk.LEFT, padx=5)
        
        # 停止按钮
        self.stop_button = ttk.Button(
            button_frame, 
            text="停止采集", 
            command=self.stop_scraping_process,
            width=15
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 导出结果按钮
        export_btn = ttk.Button(
            button_frame, 
            text="导出结果", 
            command=self.export_results,
            width=15
        )
        export_btn.pack(side=tk.LEFT, padx=5)
    
    def create_text_context_menu(self):
        self.text_menu = Menu(self.master, tearoff=0)
        self.text_menu.add_command(label="剪切", command=self.cut_text)
        self.text_menu.add_command(label="复制", command=self.copy_text)
        self.text_menu.add_command(label="粘贴", command=self.paste_text)
        self.text_menu.add_separator()
        self.text_menu.add_command(label="全选", command=self.select_all_text)
        self.text_menu.add_separator()
        self.text_menu.add_command(label="保存为文本文件", command=self.save_keywords_to_file)
        
        self.keywords_text.bind("<Button-3>", self.show_text_menu)
    
    def show_text_menu(self, event):
        self.text_menu.post(event.x_root, event.y_root)
    
    def cut_text(self):
        self.keywords_text.event_generate("<<Cut>>")
    
    def copy_text(self):
        self.keywords_text.event_generate("<<Copy>>")
    
    def paste_text(self):
        self.keywords_text.event_generate("<<Paste>>")
    
    def select_all_text(self):
        self.keywords_text.tag_add(tk.SEL, "1.0", tk.END)
        self.keywords_text.mark_set(tk.INSERT, "1.0")
        self.keywords_text.see(tk.INSERT)
    
    def save_keywords_to_file(self):
        content = self.keywords_text.get(1.0, tk.END).strip()
        if not content:
            self.log_message("没有内容可保存")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_message(f"关键词已保存到: {file_path}")
            except Exception as e:
                self.log_message(f"保存文件时出错: {str(e)}")
    
    def check_resume_availability(self):
        if os.path.exists(self.progress_file):
            self.resume_button.config(state=tk.NORMAL)
        else:
            self.resume_button.config(state=tk.DISABLED)
    
    def log_message(self, message):
        """记录日志消息到文本框，使用队列减少UI更新频率"""
        current_time = time.strftime("%H:%M:%S")
        # 删除显示具体链接、路径等敏感信息，但保留ASIN
        # 常见的敏感模式
        sensitive_patterns = [
            r'https?://[^\s]+',  # URL
            # 移除ASIN码过滤，使ASIN在日志中可见
            r'/[^\s]*\.xlsx',    # Excel文件路径
            r'/[^\s]*\.csv',     # CSV文件路径
            r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'  # IP地址
        ]
        
        # 移除敏感信息
        filtered_message = message
        for pattern in sensitive_patterns:
            filtered_message = re.sub(pattern, "[已隐藏]", filtered_message)
        
        # 将消息放入队列
        formatted_message = f"[{current_time}] {filtered_message}"
        self.log_message_queue.put(formatted_message)
        
        # 检查队列大小，只有累积到一定数量或重要消息才更新UI
        update_now = "错误" in message or "完成" in message or "创建" in message
        
        # 当队列积累一定数量或遇到重要消息时才更新UI
        if self.log_message_queue.qsize() >= 5 or update_now:
            self.update_log_ui()
    
    def update_log_ui(self):
        """批量更新日志UI，减少UI线程负担"""
        if self.log_message_queue.empty():
            return
            
        # 收集所有待处理消息
        messages = []
        while not self.log_message_queue.empty():
            try:
                messages.append(self.log_message_queue.get_nowait())
            except queue.Empty:
                break
        
        if not messages:
            return
            
        # 批量更新UI
        self.log_text.config(state=tk.NORMAL)
        for msg in messages:
            self.log_text.insert(tk.END, f"{msg}\n")
        self.log_text.see(tk.END)  # 自动滚动到最新消息
        self.log_text.config(state=tk.DISABLED)
        
        # 减少UI更新频率，提高响应速度
        self.master.update_idletasks()  # 仅更新重绘，而不处理所有事件
    
    def save_timeout_keywords(self):
        if self.timeout_keywords:
            try:
                with open(self.timeout_file, 'a', encoding='utf-8') as f:
                    for keyword in self.timeout_keywords:
                        f.write(f"{keyword}\n")
                self.log_message(f"已保存 {len(self.timeout_keywords)} 个超时关键词到 {self.timeout_file}")
                self.timeout_keywords = []
            except Exception as e:
                self.log_message(f"保存超时关键词时出错: {str(e)}")
    
    def import_keywords(self):
        file_path = filedialog.askopenfilename(
            title="选择关键词文件",
            filetypes=[
                ("所有支持的文件", "*.xlsx *.csv *.txt"),
                ("Excel文件", "*.xlsx"), 
                ("CSV文件", "*.csv"), 
                ("文本文件", "*.txt")
            ]
        )
        if not file_path:
            return
            
        try:
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    keywords = f.read()
                    
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
                if len(df.columns) > 0:
                    keywords = "\n".join(df.iloc[:, 0].astype(str).tolist())
                else:
                    self.log_message("CSV文件格式错误或为空")
                    return
                    
            elif file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
                if len(df.columns) > 0:
                    keywords = "\n".join(df.iloc[:, 0].astype(str).tolist())
                else:
                    self.log_message("Excel文件格式错误或为空")
                    return
            else:
                self.log_message("不支持的文件格式")
                return
                
            # 更新文本框
            self.keywords_text.delete(1.0, tk.END)
            self.keywords_text.insert(tk.END, keywords)
            
            # 显示文件名
            filename = os.path.basename(file_path)
            self.selected_file_var.set(f"已选择: {filename}")
            
            # 记录日志
            keyword_count = len(keywords.split('\n'))
            self.log_message(f"从 {filename} 成功导入 {keyword_count} 个关键词")
            
        except Exception as e:
            self.log_message(f"导入文件时出错: {str(e)}")
    
    def export_results(self):
        if not self.all_amazon_codes:
            self.log_message("没有找到结果可导出")
            return
            
        try:
            # 使用与实时保存相同的固定文件名格式
            country = self.selected_country.get()
            base_filename = f"amazon_results_{country}"
            file_path = f"{base_filename}.xlsx"
            
            # 检查当前文件索引
            if self.excel_file_index > 0:
                file_path = f"{base_filename}_{self.excel_file_index}.xlsx"
            
            # 确保链接缓存已初始化
            if not self.cache_initialized and os.path.exists(file_path):
                self.initialize_link_cache()
                
            # 32位系统特殊处理，批次导出减少内存使用
            if self.is_32bit and len(self.all_amazon_codes) > 1000:
                self.export_results_in_batches(file_path)
                return
            
            # 创建包含国家信息的数据框
            country_domain = self.countries[country]["domain"]
            
            # 过滤掉已存在的链接，只处理新链接
            new_codes = []
            new_links = []
            new_countries = []
            
            # 统计计数器
            total_links = len(self.all_amazon_codes)
            new_link_count = 0
            
            # 使用缓存优化过滤过程
            for code in self.all_amazon_codes:
                link = f'https://www.{country_domain}/dp/{code}'
                if link not in self.cached_links:
                    new_codes.append(code)
                    new_links.append(link)
                    new_countries.append(country)
                    # 添加到缓存
                    self.cached_links.add(link)
                    new_link_count += 1
            
            # 检查是否有新链接需要添加
            if new_link_count > 0:
                # 创建包含新数据的DataFrame
                new_data = {
                    'Amazon Links': new_links,
                    '国家': new_countries
                }
                new_df = pd.DataFrame(new_data)
                
                try:
                    # 检查文件是否已存在
                    if os.path.exists(file_path):
                        try:
                            # 读取现有数据并合并
                            existing_df = pd.read_excel(file_path)
                            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                        except Exception as e:
                            self.log_message(f"读取现有Excel文件出错，将创建新文件: {str(e)}")
                            combined_df = new_df
                    else:
                        combined_df = new_df
                    
                    # 使用xlsxwriter引擎减少内存使用
                    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                        combined_df.to_excel(writer, index=False)
                        # 优化列宽
                        worksheet = writer.sheets['Sheet1']
                        worksheet.set_column('A:A', 40)
                        worksheet.set_column('B:B', 15)
                    
                    self.log_message(f"已将 {new_link_count} 个新结果添加到文件: {file_path}")
                except Exception as e:
                    error_msg = str(e).lower()
                    if "单元格数量限制" in str(e) or "max_row" in error_msg or "行数" in str(e) or "row" in error_msg:
                        self.log_message("Excel文件已满，创建新文件...")
                        # 创建新文件
                        self.excel_file_index += 1
                        new_file_path = f"{base_filename}_{self.excel_file_index}.xlsx"
                        
                        # 保存新数据到新文件
                        with pd.ExcelWriter(new_file_path, engine='xlsxwriter') as writer:
                            new_df.to_excel(writer, index=False)
                            worksheet = writer.sheets['Sheet1']
                            worksheet.set_column('A:A', 40)
                            worksheet.set_column('B:B', 15)
                        
                        self.log_message(f"已将 {new_link_count} 个新结果保存到新文件: {new_file_path}")
                        # 更新当前Excel文件
                        self.realtime_excel_file = new_file_path
                    else:
                        self.log_message(f"导出结果时出错: {str(e)}")
            else:
                self.log_message(f"所有 {total_links} 个结果都已存在于文件中: {file_path}")
            
            # 设置为实时保存的文件
            self.realtime_excel_file = file_path
            
            # 确保缓存已同步
            self.cache_initialized = True
            
        except Exception as e:
            self.log_message(f"导出结果时出错: {str(e)}")
    
    def export_results_in_batches(self, file_path=None):
        """32位系统使用批处理方式导出大量结果"""
        try:
            country = self.selected_country.get()
            country_domain = self.countries[country]["domain"]
            
            # 如果提供了文件路径，使用批处理方式追加到该文件
            if file_path:
                # 确保链接缓存已初始化
                if not self.cache_initialized and os.path.exists(file_path):
                    self.initialize_link_cache()
                
                # 创建包含所有新数据的DataFrame
                all_data = {
                    'Amazon Links': [],
                    '国家': []
                }
                
                # 统计计数
                processed_count = 0
                new_count = 0
                
                # 批次处理以减少内存使用
                batch_size = 1000
                total_batches = (len(self.all_amazon_codes) + batch_size - 1) // batch_size
                
                for i in range(total_batches):
                    start_idx = i * batch_size
                    end_idx = min((i + 1) * batch_size, len(self.all_amazon_codes))
                    batch_codes = self.all_amazon_codes[start_idx:end_idx]
                    
                    # 处理当前批次的代码
                    for code in batch_codes:
                        processed_count += 1
                        link = f'https://www.{country_domain}/dp/{code}'
                        
                        # 使用缓存检查链接是否已存在
                        if link not in self.cached_links:
                            all_data['Amazon Links'].append(link)
                            all_data['国家'].append(country)
                            self.cached_links.add(link)  # 添加到缓存
                            new_count += 1
                    
                    # 每处理1000个代码输出一次进度
                    if processed_count % 1000 == 0 or processed_count == len(self.all_amazon_codes):
                        self.log_message(f"已处理 {processed_count}/{len(self.all_amazon_codes)} 个代码，新增 {new_count} 个链接")
                
                # 如果有新的链接需要添加
                if all_data['Amazon Links']:
                    # 创建新数据的DataFrame
                    new_df = pd.DataFrame(all_data)
                    
                    if os.path.exists(file_path):
                        # 直接追加模式，避免读取整个文件
                        if len(new_df) < 5000:  # 如果新数据量较小，直接读取合并
                            try:
                                existing_df = pd.read_excel(file_path)
                                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                                
                                # 保存到文件
                                with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                                    combined_df.to_excel(writer, index=False)
                                    # 优化列宽
                                    worksheet = writer.sheets['Sheet1']
                                    worksheet.set_column('A:A', 40)
                                    worksheet.set_column('B:B', 15)
                            except Exception as e:
                                self.log_message(f"读取Excel出错，将创建新文件: {str(e)}")
                                with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                                    new_df.to_excel(writer, index=False)
                                    worksheet = writer.sheets['Sheet1']
                                    worksheet.set_column('A:A', 40)
                                    worksheet.set_column('B:B', 15)
                        else:
                            # 数据量大，采用追加写入模式
                            self.log_message("数据量较大，采用批量追加模式...")
                            
                            # 先读取原文件的标题行
                            try:
                                existing_df_head = pd.read_excel(file_path, nrows=0)
                                
                                # 将新数据分批写入临时CSV文件
                                temp_csv = f"{file_path}.new.csv"
                                new_df.to_csv(temp_csv, index=False)
                                
                                # 然后使用操作系统命令追加到原文件
                                # 这里需要根据不同操作系统调整追加方式
                                # 这只是一个例子，实际需要更复杂的处理
                                self.log_message("正在追加数据到文件...")
                                
                                # 使用pandas追加模式写入
                                with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer:
                                    new_df.to_excel(writer, index=False, header=False, startrow=len(existing_df_head)+1)
                                
                                # 删除临时文件
                                if os.path.exists(temp_csv):
                                    os.remove(temp_csv)
                            except Exception as e:
                                self.log_message(f"追加模式失败，尝试常规模式: {str(e)}")
                                # 如果追加模式失败，退回到常规模式
                                try:
                                    existing_df = pd.read_excel(file_path)
                                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                                    
                                    # 保存到文件
                                    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                                        combined_df.to_excel(writer, index=False)
                                        worksheet = writer.sheets['Sheet1']
                                        worksheet.set_column('A:A', 40)
                                        worksheet.set_column('B:B', 15)
                                except Exception as e2:
                                    self.log_message(f"常规模式也失败，创建新文件: {str(e2)}")
                                    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                                        new_df.to_excel(writer, index=False)
                                        worksheet = writer.sheets['Sheet1']
                                        worksheet.set_column('A:A', 40)
                                        worksheet.set_column('B:B', 15)
                    else:
                        # 文件不存在，直接创建
                        with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                            new_df.to_excel(writer, index=False)
                            worksheet = writer.sheets['Sheet1']
                            worksheet.set_column('A:A', 40)
                            worksheet.set_column('B:B', 15)
                    
                    self.log_message(f"已将 {new_count} 个新结果追加到文件: {file_path}")
                else:
                    self.log_message("没有新的结果需要添加")
                
                # 设置为实时保存的文件
                self.realtime_excel_file = file_path
                
            else:
                # 旧的导出到多个文件的逻辑
                # 创建目录
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                dir_name = f"amazon_results_{country}_{timestamp}"
                os.makedirs(dir_name, exist_ok=True)
                
                # 批次大小
                batch_size = 1000
                total_batches = (len(self.all_amazon_codes) + batch_size - 1) // batch_size
                
                for i in range(total_batches):
                    start_idx = i * batch_size
                    end_idx = min((i + 1) * batch_size, len(self.all_amazon_codes))
                    batch_codes = self.all_amazon_codes[start_idx:end_idx]
                    
                    file_path = os.path.join(dir_name, f"batch_{i+1}_of_{total_batches}.xlsx")
                    
                    # 创建数据
                    data = {
                        'Amazon Links': [f'https://www.{country_domain}/dp/{code}' for code in batch_codes],
                        '国家': [country] * len(batch_codes)
                    }
                    df = pd.DataFrame(data)
                    
                    # 保存Excel
                    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                        df.to_excel(writer, index=False)
                    
                    # 释放内存
                    del df
                    
                    self.log_message(f"已导出批次 {i+1}/{total_batches} 到 {file_path}")
                
                self.log_message(f"所有 {len(self.all_amazon_codes)} 个结果已导出到文件夹: {dir_name}")
        except Exception as e:
            self.log_message(f"批处理导出时出错: {str(e)}")
    
    def save_progress(self):
        try:
            progress_data = {
                "amazon_codes": self.all_amazon_codes,
                "current_keyword_index": self.current_keyword_index,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "country": self.selected_country.get()
            }
            
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False)
            
            self.log_message(f"进度已保存 (已处理 {self.current_keyword_index} 个关键词, 收集 {len(self.all_amazon_codes)} 个结果)")
            
            # 保存超时关键词
            self.save_timeout_keywords()
        except Exception as e:
            self.log_message(f"保存进度时出错: {str(e)}")
    
    def load_progress(self):
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                
                self.all_amazon_codes = progress_data.get("amazon_codes", [])
                self.current_keyword_index = progress_data.get("current_keyword_index", 0)
                timestamp = progress_data.get("timestamp", "未知时间")
                saved_country = progress_data.get("country", "美国")
                
                # 设置国家
                if saved_country in self.countries:
                    self.selected_country.set(saved_country)
                
                self.log_message(f"已加载上次进度 (时间: {timestamp})")
                self.log_message(f"已收集 {len(self.all_amazon_codes)} 个Amazon链接")
                return True
            return False
        except Exception as e:
            self.log_message(f"加载进度时出错: {str(e)}")
            return False
    
    def check_excel_row_count(self, file_path):
        """检查Excel文件的行数，返回行数"""
        try:
            if not os.path.exists(file_path):
                return 0
                
            # 使用openpyxl轻量级读取行数
            wb = openpyxl.load_workbook(file_path, read_only=True)
            sheet = wb.active
            row_count = sheet.max_row - 1  # 减去标题行
            wb.close()
            
            self.log_message(f"检查文件 {file_path} 的行数: {row_count}")
            return row_count
        except Exception as e:
            self.log_message(f"检查Excel行数时出错: {str(e)}")
            return 0

    def setup_realtime_excel(self):
        if self.realtime_save.get():
            country = self.selected_country.get()
            # 使用固定文件名，每个国家一个文件
            base_filename = f"amazon_results_{country}"
            base_file = f"{base_filename}.xlsx"
            self.realtime_excel_file = base_file
            
            # 检查基础文件是否存在且行数是否接近限制
            excel_row_limit = 64000  # 设置一个接近限制的安全值
            
            if os.path.exists(base_file):
                # 只在程序启动时检查一次文件行数
                row_count = self.check_excel_row_count(base_file)
                self.current_row_count = row_count
                
                if row_count >= excel_row_limit:
                    self.log_message(f"基础文件 {base_file} 行数({row_count})接近限制，将创建新文件")
            else:
                self.current_row_count = 0
            
            # 检查文件是否存在，并确定当前文件索引
            self.excel_file_index = 0
            max_usable_index = -1  # 最大可用的索引
            
            while os.path.exists(f"{base_filename}_{self.excel_file_index}.xlsx"):
                # 检查索引文件是否接近限制
                indexed_file = f"{base_filename}_{self.excel_file_index}.xlsx"
                row_count = self.check_excel_row_count(indexed_file)
                
                if row_count < excel_row_limit:
                    # 记录最后一个未满的文件索引
                    max_usable_index = self.excel_file_index
                
                self.excel_file_index += 1
            
            # 如果有可用的索引文件，使用它
            if max_usable_index >= 0:
                self.realtime_excel_file = f"{base_filename}_{max_usable_index}.xlsx"
                self.current_row_count = self.check_excel_row_count(self.realtime_excel_file)
                self.excel_file_index = max_usable_index
            # 如果没有可用的索引文件，且基础文件接近限制，创建新的索引文件
            elif self.current_row_count >= excel_row_limit:
                new_excel_file = f"{base_filename}_{self.excel_file_index}.xlsx"
                self.log_message(f"创建新的索引文件: {new_excel_file}")
                
                # 创建新的Excel文件
                data = {'Amazon Links': [], '国家': []}
                df = pd.DataFrame(data)
                
                if self.is_32bit:
                    # 32位系统使用更优化的写入方式
                    with pd.ExcelWriter(new_excel_file, engine='xlsxwriter', mode='w') as writer:
                        df.to_excel(writer, index=False, sheet_name='Sheet1')
                        worksheet = writer.sheets['Sheet1']
                        worksheet.set_column('A:A', 40)
                        worksheet.set_column('B:B', 15)
                else:
                    df.to_excel(new_excel_file, index=False)
                    
                self.realtime_excel_file = new_excel_file
                self.current_row_count = 0
                self.log_message(f"已创建新的Excel文件: {new_excel_file}")
                
                # 初始化空缓存
                self.cached_links = set()
                self.cache_initialized = True
            # 如果基本文件和索引文件都不存在，创建基本文件
            elif not os.path.exists(self.realtime_excel_file):
                # 对于32位系统，使用更小的初始DataFrame
                country_domain = self.countries[country]["domain"]
                if self.is_32bit:
                    data = {'Amazon Links': [], '国家': []}
                    df = pd.DataFrame(data)
                    # 使用xlsxwriter引擎并优化内存使用
                    with pd.ExcelWriter(self.realtime_excel_file, engine='xlsxwriter', mode='w') as writer:
                        df.to_excel(writer, index=False, sheet_name='Sheet1')
                        # 优化工作表以减少内存使用
                        worksheet = writer.sheets['Sheet1']
                        worksheet.set_column('A:A', 40)
                        worksheet.set_column('B:B', 15)
                else:
                    data = {'Amazon Links': [], '国家': []}
                    df = pd.DataFrame(data)
                    df.to_excel(self.realtime_excel_file, index=False)
                
                self.log_message(f"已创建Excel文件: {self.realtime_excel_file}")
                self.current_row_count = 0
                # 初始化空缓存
                self.cached_links = set()
                self.cache_initialized = True
            
            self.log_message(f"将继续使用Excel文件: {self.realtime_excel_file}")
            # 初始化链接缓存
            self.initialize_link_cache()
    
    def initialize_link_cache(self):
        """从Excel文件中加载现有链接到缓存中，优化查重性能"""
        try:
            # 检查主文件和所有索引文件
            if not self.cache_initialized:
                self.log_message("正在初始化链接缓存...")
                
                # 获取基本文件名和当前文件
                current_file = self.realtime_excel_file
                if current_file and os.path.exists(current_file):
                    # 加载当前文件的链接
                    self.load_links_from_file(current_file)
                
                # 检查是否有索引文件
                file_name_parts = current_file.rsplit('.', 1)
                base_name = file_name_parts[0]
                
                # 如果已经是带索引的文件名，提取基本部分
                if '_' in base_name and base_name.rsplit('_', 1)[1].isdigit():
                    base_name = base_name.rsplit('_', 1)[0]
                
                # 检查其他索引文件
                i = 0
                while True:
                    index_file = f"{base_name}_{i}.xlsx"
                    if index_file != current_file and os.path.exists(index_file):
                        self.load_links_from_file(index_file)
                        i += 1
                    else:
                        if i > 0:  # 如果至少找到了一个索引文件
                            break
                        else:  # 没有找到索引文件，检查基本文件
                            basic_file = f"{base_name}.xlsx"
                            if basic_file != current_file and os.path.exists(basic_file):
                                self.load_links_from_file(basic_file)
                            break
                
                # 检查临时CSV文件
                temp_csv = f"{current_file}.tmp.csv"
                if os.path.exists(temp_csv):
                    try:
                        # 读取CSV文件中的链接
                        df_csv = pd.read_csv(temp_csv, header=None, names=['Amazon Links', '国家'])
                        for link in df_csv['Amazon Links']:
                            self.cached_links.add(link)
                    except:
                        pass
                
                self.cache_initialized = True
                self.log_message(f"链接缓存初始化完成，已缓存 {len(self.cached_links)} 个链接")
            else:
                pass  # 缓存已初始化
        except Exception as e:
            self.log_message(f"初始化链接缓存时出错: {str(e)}")
            # 如果初始化失败，创建空缓存
            self.cached_links = set()
            self.cache_initialized = True
    
    def load_links_from_file(self, file_path):
        """从单个Excel文件加载链接到缓存"""
        try:
            if os.path.exists(file_path):
                self.log_message(f"从文件加载链接: {file_path}")
                # 优化内存使用的读取方式
                if self.is_32bit:
                    # 32位系统使用更保守的方式读取
                    chunks = pd.read_excel(file_path, usecols=['Amazon Links'], chunksize=1000)
                    for chunk in chunks:
                        for link in chunk['Amazon Links']:
                            self.cached_links.add(link)
                else:
                    # 64位系统可以一次性读取
                    df = pd.read_excel(file_path, usecols=['Amazon Links'])
                    for link in df['Amazon Links']:
                        self.cached_links.add(link)
        except Exception as e:
            self.log_message(f"从文件 {file_path} 加载链接时出错: {str(e)}")

    def create_new_excel_file(self, new_link, country):
        """当Excel文件满时，创建新的Excel文件继续保存"""
        try:
            # 获取基本文件名（不含扩展名）
            file_name_parts = self.realtime_excel_file.rsplit('.', 1)
            base_name = file_name_parts[0]
            
            # 如果已经是带索引的文件名，提取基本部分
            if '_' in base_name and base_name.rsplit('_', 1)[1].isdigit():
                base_name = base_name.rsplit('_', 1)[0]
            
            # 创建新的文件名
            self.excel_file_index += 1
            new_excel_file = f"{base_name}_{self.excel_file_index}.xlsx"
            
            # 创建新的Excel文件
            new_data = {
                'Amazon Links': [new_link],
                '国家': [country]
            }
            new_df = pd.DataFrame(new_data)
            
            # 保存到新文件
            with pd.ExcelWriter(new_excel_file, engine='xlsxwriter') as writer:
                new_df.to_excel(writer, index=False)
                # 优化列宽
                worksheet = writer.sheets['Sheet1']
                worksheet.set_column('A:A', 40)
                worksheet.set_column('B:B', 15)
            
            self.log_message(f"Excel文件已满，已创建新文件: {new_excel_file}")
            
            # 更新当前使用的Excel文件
            self.realtime_excel_file = new_excel_file
            
            # 重置行计数器
            self.current_row_count = 1  # 新文件已有一行数据(标题行不算)
            
            # 重置链接缓存初始化状态（因为现在是新文件）
            self.cache_initialized = True
            
            return True
        except Exception as e:
            self.log_message(f"创建新Excel文件时出错: {str(e)}")
            return False
            
    def save_to_excel_realtime(self, new_code):
        if not self.realtime_save.get() or not self.realtime_excel_file:
            return
            
        try:
            country = self.selected_country.get()
            country_domain = self.countries[country]["domain"]
            
            # 构建完整链接
            new_link = f"https://www.{country_domain}/dp/{new_code}"
            
            # 如果缓存未初始化，先初始化
            if not self.cache_initialized:
                self.initialize_link_cache()
            
            # 使用缓存快速检查链接是否已存在
            if new_link in self.cached_links:
                return  # 链接已存在，直接返回
            
            # 添加到缓存
            self.cached_links.add(new_link)
            
            # 32位系统使用资源更少的方式写入Excel
            if self.is_32bit:
                # 直接追加到临时CSV文件
                temp_csv = f"{self.realtime_excel_file}.tmp.csv"
                with open(temp_csv, 'a', encoding='utf-8', newline='') as f:
                    f.write(f"{new_link},{country}\n")
                
                # 提高批处理频率，减少文件IO操作
                # 每100个结果(或最后一个)将CSV转换为Excel
                if len(self.all_amazon_codes) % 100 == 0 or len(self.all_amazon_codes) == 1:
                    self.convert_csv_to_excel_append()
            else:
                # 64位系统读取现有数据并追加
                try:
                    if os.path.exists(self.realtime_excel_file):
                        try:
                            # 简单检查文件中的行数，不加载内容
                            # 使用简单的文件大小估算来判断是否接近限制
                            file_size = os.path.getsize(self.realtime_excel_file)
                            # 一般来说，一个URL大约50字节，加上其他列信息，假设每行100字节
                            # 65000行大约是6.5MB
                            if file_size > 6000000:  # 当文件大于6MB时，尝试读取更详细信息
                                # 只读取前几行来获取标题
                                headers_df = pd.read_excel(self.realtime_excel_file, nrows=0)
                                # 使用低级API统计行数，避免加载全部数据
                                wb = openpyxl.load_workbook(self.realtime_excel_file, read_only=True)
                                sheet = wb.active
                                row_count = sheet.max_row - 1  # 减去标题行
                                wb.close()
                                
                                # 检查是否接近URL限制
                                url_limit = 65000
                                if row_count >= url_limit:
                                    self.create_new_excel_file(new_link, country)
                                    return
                            
                            # 读取现有数据
                            existing_df = pd.read_excel(self.realtime_excel_file)
                            
                            # 检查是否接近URL限制
                            if len(existing_df) >= 65000:
                                self.create_new_excel_file(new_link, country)
                                return
                        except Exception as e:
                            if "URL" in str(e) or len(str(e)) == 0:  # URL警告或文件损坏
                                self.create_new_excel_file(new_link, country)
                                return
                            self.log_message(f"读取Excel文件出错，创建新文件: {str(e)}")
                            existing_df = pd.DataFrame({'Amazon Links': [], '国家': []})
                    else:
                        existing_df = pd.DataFrame({'Amazon Links': [], '国家': []})
                    
                    # 添加新数据
                    new_data = {
                        'Amazon Links': [new_link],
                        '国家': [country]
                    }
                    new_df = pd.DataFrame(new_data)
                    
                    # 合并并保存
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                    combined_df.to_excel(self.realtime_excel_file, index=False)
                except Exception as e:
                    # 如果出错（可能是Excel文件已满），创建新文件
                    if "单元格数量限制" in str(e) or "max_row" in str(e) or "行数" in str(e) or "row" in str(e).lower() or "URL" in str(e):
                        self.create_new_excel_file(new_link, country)
                    else:
                        self.log_message(f"保存到Excel时出错: {str(e)}")
        except Exception as e:
            self.log_message(f"实时保存到Excel时出错: {str(e)}")
            # 尝试创建新文件
            try:
                self.create_new_excel_file(new_link, country)
            except Exception as e2:
                self.log_message(f"创建新Excel文件失败: {str(e2)}")
    
    def convert_csv_to_excel_append(self):
        """将临时CSV文件追加到Excel (用于32位系统)"""
        try:
            temp_csv = f"{self.realtime_excel_file}.tmp.csv"
            if os.path.exists(temp_csv):
                # 读取CSV中的新数据
                new_df = pd.read_csv(temp_csv, header=None, names=['Amazon Links', '国家'])
                
                # 初始化combined_df
                try:
                    if os.path.exists(self.realtime_excel_file):
                        try:
                            # 优化：检查是否有数据需要添加，利用缓存避免不必要的读取
                            # 创建一个集合，用于快速检查是否有新链接
                            csv_links = set(new_df['Amazon Links'])
                            real_new_links = csv_links - self.cached_links
                            
                            # 如果没有真正的新链接，直接返回
                            if not real_new_links:
                                self.log_message("没有新的非重复数据需要添加")
                                # 删除临时CSV
                                os.remove(temp_csv)
                                return
                                
                            # 优化：只保留真正的新链接数据
                            new_df = new_df[new_df['Amazon Links'].isin(real_new_links)]
                            
                            # 检查是否接近Excel行数限制（使用计数器）
                            excel_row_limit = 64000  # 安全阈值
                            
                            if self.current_row_count >= excel_row_limit:
                                self.log_message(f"Excel工作表接近URL链接限制，当前行数: {self.current_row_count}，将创建新文件")
                                
                                # 创建新的Excel文件
                                self.excel_file_index += 1
                                file_name_parts = self.realtime_excel_file.rsplit('.', 1)
                                base_name = file_name_parts[0]
                                # 如果已经是带索引的文件名，提取基本部分
                                if '_' in base_name and base_name.rsplit('_', 1)[1].isdigit():
                                    base_name = base_name.rsplit('_', 1)[0]
                                new_excel_file = f"{base_name}_{self.excel_file_index}.xlsx"
                                
                                # 直接将新数据保存到新文件
                                with pd.ExcelWriter(new_excel_file, engine='xlsxwriter') as writer:
                                    new_df.to_excel(writer, index=False)
                                    worksheet = writer.sheets['Sheet1']
                                    worksheet.set_column('A:A', 40)
                                    worksheet.set_column('B:B', 15)
                                
                                # 更新当前Excel文件路径
                                self.realtime_excel_file = new_excel_file
                                self.log_message(f"已创建新Excel文件: {new_excel_file}")
                                
                                # 更新计数器
                                self.current_row_count = len(new_df)
                                
                                # 更新缓存
                                for link in new_df['Amazon Links']:
                                    self.cached_links.add(link)
                                
                                # 删除临时CSV
                                os.remove(temp_csv)
                                return
                            
                            # 读取Excel文件
                            existing_df = pd.read_excel(self.realtime_excel_file)
                            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                            
                            # 更新计数器
                            self.current_row_count += len(new_df)
                        except Exception as e:
                            self.log_message(f"读取Excel出错: {str(e)}")
                            combined_df = new_df
                            # 如果文件读取失败，只有新数据
                            self.current_row_count = len(new_df)
                    else:
                        combined_df = new_df
                        # 新文件，初始化计数器
                        self.current_row_count = len(new_df)
                    
                    # 写入Excel
                    with pd.ExcelWriter(self.realtime_excel_file, engine='xlsxwriter') as writer:
                        combined_df.to_excel(writer, index=False)
                        # 优化工作表
                        worksheet = writer.sheets['Sheet1']
                        worksheet.set_column('A:A', 40)
                        worksheet.set_column('B:B', 15)
                    
                    # 更新缓存 - 确保缓存与文件同步
                    for link in new_df['Amazon Links']:
                        self.cached_links.add(link)
                    
                    # 删除临时CSV
                    os.remove(temp_csv)
                    self.log_message(f"已将新数据追加到Excel文件: {self.realtime_excel_file}")
                
                except Exception as e:
                    # 检查是否是Excel文件大小限制导致的错误
                    error_msg = str(e).lower()
                    if "单元格数量限制" in str(e) or "max_row" in error_msg or "行数" in str(e) or "row" in error_msg or "URL" in error_msg:
                        self.log_message("Excel文件数据量已满或URL限制，创建新文件...")
                        
                        # 创建新的Excel文件
                        self.excel_file_index += 1
                        file_name_parts = self.realtime_excel_file.rsplit('.', 1)
                        
                        # 更新计数器将在新文件创建后进行
                        self.current_row_count = len(new_df)
                        
                        base_name = file_name_parts[0]
                        # 如果已经是带索引的文件名，提取基本部分
                        if '_' in base_name and base_name.rsplit('_', 1)[1].isdigit():
                            base_name = base_name.rsplit('_', 1)[0]
                        new_excel_file = f"{base_name}_{self.excel_file_index}.xlsx"
                        
                        # 直接将新数据保存到新文件
                        with pd.ExcelWriter(new_excel_file, engine='xlsxwriter') as writer:
                            new_df.to_excel(writer, index=False)
                            worksheet = writer.sheets['Sheet1']
                            worksheet.set_column('A:A', 40)
                            worksheet.set_column('B:B', 15)
                        
                        # 更新当前Excel文件路径
                        self.realtime_excel_file = new_excel_file
                        self.log_message(f"已创建新Excel文件: {new_excel_file}")
                        
                        # 更新缓存
                        for link in new_df['Amazon Links']:
                            self.cached_links.add(link)
                        
                        # 删除临时CSV
                        os.remove(temp_csv)
                    else:
                        self.log_message(f"追加CSV到Excel时出错: {str(e)}")
        except Exception as e:
            self.log_message(f"追加CSV到Excel时出错: {str(e)}")
    
    def start_scraping(self):
        keywords_text = self.keywords_text.get(1.0, tk.END).strip()
        if not keywords_text:
            self.log_message("请先输入关键词")
            return
            
        keywords = [k.strip() for k in keywords_text.split('\n') if k.strip()]
        
        # 获取批处理大小（32位系统会使用较小的值）
        batch_limit = self.batch_size.get()
        if len(keywords) > batch_limit:
            if messagebox.askyesno("确认", f"您输入了{len(keywords)}个关键词，超过了批处理大小{batch_limit}。要截断列表吗？"):
                self.log_message(f"关键词已截断至{batch_limit}个")
                keywords = keywords[:batch_limit]
        
        # 询问是否要清除之前的结果
        if os.path.exists(self.progress_file):
            if messagebox.askyesno("确认", "是否要在之前的结果基础上继续？"):
                # 加载之前的进度
                self.load_progress()
            else:
                # 清除之前的进度并重新开始
                self.all_amazon_codes = []
                self.current_keyword_index = 0
        else:
            # 没有之前的进度，直接开始
            self.all_amazon_codes = []
            self.current_keyword_index = 0
        
        # 设置实时保存Excel
        self.setup_realtime_excel()
        
        self.stop_scraping = False
        self.start_button.config(state=tk.DISABLED)
        self.resume_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.import_button.config(state=tk.DISABLED)
        
        # 清除日志，但保留结果计数
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message(f"已有 {len(self.all_amazon_codes)} 个Amazon链接")
        
        # Start scraping in a separate thread
        thread = threading.Thread(target=self.scraping_process, args=(keywords, self.current_keyword_index))
        thread.daemon = True
        thread.start()
    
    def resume_scraping(self):
        if not self.load_progress():
            self.log_message("没有找到可恢复的进度")
            return
        
        keywords_text = self.keywords_text.get(1.0, tk.END).strip()
        if not keywords_text:
            self.log_message("请先输入关键词")
            return
        
        keywords = [k.strip() for k in keywords_text.split('\n') if k.strip()]
        
        # 获取批处理大小
        batch_limit = self.batch_size.get()
        if len(keywords) > batch_limit:
            if messagebox.askyesno("确认", f"您输入了{len(keywords)}个关键词，超过了批处理大小{batch_limit}。要截断列表吗？"):
                self.log_message(f"关键词已截断至{batch_limit}个")
                keywords = keywords[:batch_limit]
        
        # 检查当前索引是否超出范围
        if self.current_keyword_index >= len(keywords):
            self.log_message("上次爬取已完成所有关键词，请使用'开始爬取'重新开始")
            return
        
        # 设置实时保存Excel
        self.setup_realtime_excel()
        
        self.log_message(f"从第 {self.current_keyword_index+1} 个关键词继续爬取 (国家: {self.selected_country.get()})")
        self.stop_scraping = False
        self.start_button.config(state=tk.DISABLED)
        self.resume_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.import_button.config(state=tk.DISABLED)
        
        # 启动爬虫线程
        thread = threading.Thread(target=self.scraping_process, args=(keywords, self.current_keyword_index))
        thread.daemon = True
        thread.start()
    
    def stop_scraping_process(self):
        self.stop_scraping = True
        self.log_message("正在停止爬取，请等待当前任务完成...")
        
        # 确保任何未保存的批量数据被保存
        if self.pending_codes:
            self.log_message("处理未保存的数据...")
            try:
                self.perform_batch_save()
            except Exception as e:
                self.log_message(f"保存未完成数据时出错: {str(e)}")
        
        # 关闭浏览器释放资源
        if self.browser_instance:
            self.log_message("正在关闭浏览器...")
            self.close_browser()
        
        # 执行垃圾回收
        self.gc_memory()
        
        # 保存进度和超时关键词
        self.save_progress()
        self.save_timeout_keywords()
    
    def scraping_process(self, keywords, start_index=0):
        try:
            total_keywords = len(keywords)
            self.log_message(f"开始爬取，共 {total_keywords} 个关键词，国家: {self.selected_country.get()}")
            
            # 32位系统适应性调整 - 更频繁地清理内存
            cleanup_interval = 5 if self.is_32bit else 20  # 降低清理间隔，更频繁地清理内存
            # 批量保存阈值 - 动态调整
            batch_save_interval = 20 if self.is_32bit else 50
            
            initial_link_count = len(self.all_amazon_codes)
            last_saved_count = initial_link_count
            
            for index in range(start_index, total_keywords):
                if self.stop_scraping:
                    self.log_message("爬取已被用户中止")
                    
                    # 确保保存最新进度
                    self.save_progress()
                    
                    # 如果有未保存的批量数据，执行批量保存
                    if self.pending_codes:
                        self.perform_batch_save()
                        
                    break
                
                self.current_keyword_index = index
                progress = (index / total_keywords) * 100
                self.progress_var.set(progress)
                self.log_message(f"处理关键词 {index+1}/{total_keywords}: {keywords[index]}")
                
                try:
                    # 设置超时标志
                    keyword_timed_out = False
                    
                    # 尝试3次
                    for retry in range(3):
                        try:
                            self.process_keyword(keywords[index])
                            keyword_timed_out = False
                            break
                        except Exception as e:
                            error_message = str(e).lower()
                            if "timeout" in error_message or "timed out" in error_message:
                                self.log_message(f"关键词 '{keywords[index]}' 超时 (尝试 {retry+1}/3): {str(e)}")
                                keyword_timed_out = True
                            else:
                                self.log_message(f"处理关键词 '{keywords[index]}' 出错 (尝试 {retry+1}/3): {str(e)}")
                                keyword_timed_out = False
                            
                            if retry < 2:  # 如果不是最后一次尝试，则等待后重试
                                time.sleep(3)
                    
                    # 如果3次尝试后仍然超时，则记录超时关键词
                    if keyword_timed_out:
                        if keywords[index] not in self.timeout_keywords:
                            self.timeout_keywords.append(keywords[index])
                            self.log_message(f"关键词 '{keywords[index]}' 已添加到超时列表")
                        
                        # 直接保存到文件，不等待批量保存
                        with open(self.timeout_file, 'a', encoding='utf-8') as f:
                            f.write(f"{keywords[index]}\n")
                        self.log_message(f"已保存超时关键词到文件: {keywords[index]}")
                    
                    # 检查是否需要批量保存Excel
                    current_count = len(self.all_amazon_codes)
                    if current_count - last_saved_count >= batch_save_interval:
                        if self.pending_codes:
                            self.perform_batch_save()
                        last_saved_count = current_count
                        # 同时保存进度
                        self.save_progress()
                    
                    # 32位系统定期清理内存
                    if self.is_32bit and index % cleanup_interval == 0 and index > 0:
                        self.gc_memory()
                        
                except Exception as e:
                    self.log_message(f"处理关键词 '{keywords[index]}' 出错: {str(e)}")
                
                # Update status and check progress periodically
                if index % 5 == 0 or index == total_keywords - 1:
                    self.log_message(f"已收集 {len(self.all_amazon_codes)} 个唯一Amazon代码")
            
            # 完成所有关键词处理后，保存最终结果
            if self.pending_codes:
                self.perform_batch_save()
            
            # 最终状态更新
            final_count = len(self.all_amazon_codes)
            new_links = final_count - initial_link_count
            self.log_message(f"爬取完成。共收集 {final_count} 个Amazon代码，本次新增 {new_links} 个")
            
        except Exception as e:
            self.log_message(f"爬取过程中出错: {str(e)}")
        finally:
            self.progress_var.set(100)
            self.start_button.config(state=tk.NORMAL)
            self.check_resume_availability()
            self.stop_button.config(state=tk.DISABLED)
            self.import_button.config(state=tk.NORMAL)
            # 最后保存一次超时关键词
            self.save_timeout_keywords()
            # 保存任何未保存的批量数据
            if self.pending_codes:
                try:
                    self.perform_batch_save()
                except Exception as e:
                    self.log_message(f"最终批量保存失败: {str(e)}")
            # 清理内存
            self.gc_memory()
    
    def process_keyword(self, keyword):
        driver = None
        try:
            # 获取当前选择的国家和搜索格式
            country_info = self.countries[self.selected_country.get()]
            
            # 如果启用了翻译且不是美国，则尝试翻译关键词
            if self.enable_translation.get() and self.selected_country.get() != "美国":
                translated_keyword = self.translate_keyword(keyword, self.selected_country.get())
                if translated_keyword:
                    self.log_message(f"翻译: '{keyword}' -> '{translated_keyword}'")
                    search_query = country_info["query_format"].format(keyword=translated_keyword)
                else:
                    search_query = country_info["query_format"].format(keyword=keyword)
            else:
                search_query = country_info["query_format"].format(keyword=keyword)
            
            # 尝试获取或创建浏览器实例，实现浏览器复用
            driver = self.get_browser_instance()
            if not driver:
                self.log_message("无法初始化任何受支持的浏览器，请安装Firefox、Chrome或Edge")
                return
            
            # 设置页面加载超时时间
            driver.set_page_load_timeout(30)
            
            # 设置总超时时间 - 移除限制或设置更大的值
            start_time = time.time()
            max_total_time = 3600  # 修改为1小时，实际上基本不会达到
            
            for attempt in range(3):
                if self.stop_scraping:
                    break
                    
                try:
                    # 移除总时间检查
                    # if time.time() - start_time > max_total_time:
                    #     self.log_message(f"关键词 '{keyword}' 处理总时间超过 {max_total_time} 秒，放弃继续")
                    #     raise TimeoutError(f"关键词总处理时间超出限制")
                        
                    # 直接构建搜索URL并访问
                    encoded_query = search_query.replace(' ', '+')
                    search_url = f'https://www.bing.com/search?q={encoded_query}'
                    self.log_message(f"访问搜索URL: {search_url}")
                    driver.get(search_url)
                    time.sleep(2)  # 等待搜索结果加载
                    
                    # 增加搜索结果处理稳定性
                    try:
                        # 执行JavaScript检查页面加载状态
                        page_state = driver.execute_script('return document.readyState')
                        if page_state != 'complete':
                            self.log_message("页面未完全加载，等待...")
                            time.sleep(3)  # 额外等待时间
                        
                        # 确保滚动到页面顶部，使内容可见
                        driver.execute_script('window.scrollTo(0, 0);')
                    except Exception as e:
                        self.log_message(f"检查页面状态时出错: {str(e)}")
                        time.sleep(1)  # 出错时短暂等待
                    
                    # Check if results exist
                    if self.check_no_results(driver):
                        self.log_message(f"未找到结果: {keyword}")
                        break
                        
                    # 等待搜索结果加载
                    try:
                        WebDriverWait(driver, 10).until(
                            EC.presence_of_all_elements_located((By.CSS_SELECTOR, '.b_algo'))
                        )
                        break  # Success
                    except:
                        # 如果没有找到.b_algo，尝试查找其他可能的结果元素
                        alternative_selectors = [".b_algo", ".b_results", "#b_results > li", ".results > div"]
                        found = False
                        for selector in alternative_selectors:
                            try:
                                if driver.find_elements(By.CSS_SELECTOR, selector):
                                    found = True
                                    break
                            except:
                                continue
                        
                        if found:
                            break
                        else:
                            # 尝试使用JavaScript检查页面是否加载完成
                            is_loaded = driver.execute_script("return document.readyState") == "complete"
                            if is_loaded:
                                # 如果页面已加载但没有找到结果元素，尝试捕获所有链接
                                links = driver.find_elements(By.TAG_NAME, "a")
                                if len(links) > 0:
                                    self.log_message(f"找到 {len(links)} 个链接，但没有标准搜索结果元素")
                                    break
                            self.log_message("页面未完全加载或无法找到任何可用链接")
                
                except Exception as e:
                    if attempt == 2:
                        self.log_message(f"3次尝试后搜索失败: {str(e)}")
                        # 如果是最后一次尝试，并且失败了，则重新抛出异常
                        if "time" in str(e).lower():
                            raise TimeoutError(f"搜索超时: {str(e)}")
                        raise
                    time.sleep(2)
            
            # Process pages
            page_number = 1
            max_pages = 50  # 修改为采集50页
            consecutive_failures = 0  # 跟踪连续失败次数
            max_consecutive_failures = 3  # 最大连续失败次数
            
            # 跟踪当前页面和上一页的页码，用于检测是否被重定向回某个固定页面
            reported_page_numbers = []
            redirect_threshold = 3  # 如果连续3次报告相同的页码，则认为被重定向
            page_redirect_detected = False
            fixed_page = None
            expected_page = 1  # 期望的页码
            
            # 添加页码倒退计数器
            page_regression_count = 0
            max_page_regression_retries = 3  # 最大页码倒退重试次数
            
            while page_number <= max_pages and not self.stop_scraping:
                try:
                    # 移除总时间检查
                    # if time.time() - start_time > max_total_time:
                    #     self.log_message(f"关键词 '{keyword}' 处理总时间超过 {max_total_time} 秒，放弃继续")
                    #     break
                        
                    self.log_message(f"处理第 {page_number} 页: '{keyword}'")
                    
                    # 检测当前实际页码
                    current_page_number = None
                    try:
                        page_number_elements = driver.find_elements(By.CSS_SELECTOR, "a.sb_pagS, .b_activePage, .current")
                        if page_number_elements:
                            try:
                                current_page_number = int(page_number_elements[0].text.strip())
                                self.log_message(f"当前在第 {current_page_number} 页")
                                
                                # 检测是否发生页码倒退 - 如果当前页码小于预期页码，且不是第一页
                                if current_page_number < expected_page and expected_page > 1:
                                    self.log_message(f"检测到页码倒退！预期第 {expected_page} 页，实际第 {current_page_number} 页")
                                    page_redirect_detected = True
                                    fixed_page = current_page_number
                                    page_regression_count += 1  # 增加页码倒退计数器
                                    
                                    # 检查是否达到最大页码倒退重试次数
                                    if page_regression_count >= max_page_regression_retries:
                                        self.log_message(f"连续 {max_page_regression_retries} 次页码倒退，放弃当前关键词: '{keyword}'")
                                        return  # 跳过当前关键词，继续下一个
                                    
                                    self.log_message(f"检测到页面重定向！连续多次被重定向回第 {fixed_page} 页")
                                    break  # 跳出翻页循环，重启浏览器会话
                                
                                # 记录报告的页码以检测重定向
                                reported_page_numbers.append(current_page_number)
                                if len(reported_page_numbers) > redirect_threshold:
                                    reported_page_numbers.pop(0)  # 保持列表长度为redirect_threshold
                                
                                # 如果连续多次报告相同页码，且与预期页码不符，则判定为重定向
                                if (len(reported_page_numbers) == redirect_threshold and 
                                    all(p == reported_page_numbers[0] for p in reported_page_numbers) and
                                    reported_page_numbers[0] != page_number):
                                    fixed_page = reported_page_numbers[0]
                                    page_redirect_detected = True
                                    page_regression_count += 1  # 增加页码倒退计数器
                                    
                                    # 检查是否达到最大页码倒退重试次数
                                    if page_regression_count >= max_page_regression_retries:
                                        self.log_message(f"连续 {max_page_regression_retries} 次页码倒退，放弃当前关键词: '{keyword}'")
                                        return  # 跳过当前关键词，继续下一个
                                    
                                    self.log_message(f"检测到页面重定向！连续多次被重定向回第 {fixed_page} 页")
                                    break  # 跳出翻页循环，重启浏览器会话
                            except ValueError:
                                pass
                    except:
                        pass
                    
                    # 设置页面处理超时
                    page_start_time = time.time()
                    page_timeout = 60  # 60秒页面处理超时
                    
                    # 记录处理前的链接数，用于稍后比较是否找到新链接
                    links_before = len(self.all_amazon_codes)
                    
                    # 尝试不同的选择器找到结果
                    algo_elements = None
                    selectors = ['.b_algo', '.results > div', '#b_results > li']
                    
                    for selector in selectors:
                        try:
                            algo_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            if algo_elements and len(algo_elements) > 0:
                                break
                        except:
                            continue
                    
                    if not algo_elements or len(algo_elements) == 0:
                        self.log_message("未找到搜索结果元素，尝试使用JavaScript获取链接")
                        # 尝试使用JavaScript获取所有链接
                        try:
                            links = driver.execute_script("""
                                var links = [];
                                var elements = document.querySelectorAll('a[href*="amazon"]');
                                for(var i=0; i<elements.length; i++) {
                                    links.push(elements[i].href);
                                }
                                return links;
                            """)
                            
                            for link in links:
                                if '/dp/' in link or '/product/' in link:
                                    self.process_link(link)
                        except:
                            pass
                    
                    if self.check_no_results(driver):
                        break
                    
                    # 处理搜索结果元素
                    if algo_elements:
                        for algo_element in algo_elements:
                            try:
                                # 检查页面处理超时
                                if time.time() - page_start_time > page_timeout:
                                    self.log_message(f"页面处理超时，跳到下一页")
                                    break
                                    
                                # 尝试多种方式查找链接
                                link_element = None
                                link_selectors = ['a.tilk', 'a', 'h2 a', 'h3 a', 'div.title a']
                                
                                for selector in link_selectors:
                                    try:
                                        links = algo_element.find_elements(By.CSS_SELECTOR, selector)
                                        if links:
                                            link_element = links[0]
                                            break
                                    except:
                                        continue
                                
                                if link_element:
                                    full_link = link_element.get_attribute('href')
                                    if full_link and ('amazon' in full_link or 'amzn' in full_link):
                                        self.process_link(full_link)
                            except Exception as e:
                                # self.log_message(f"处理搜索结果元素时出错: {str(e)}")
                                pass  # Skip problematic elements
                    
                    # 检查链接数量变化，移到点击下一页前
                    links_after = len(self.all_amazon_codes)
                    if links_after == links_before:
                        self.log_message("该页未找到新链接")
                    
                    # Try to click next page using multiple selectors
                    click_success = self.click_next_page(driver)
                    
                    if click_success:
                        page_number += 1
                        expected_page = page_number  # 更新期望的页码
                        consecutive_failures = 0  # 重置连续失败计数
                    else:
                        consecutive_failures += 1
                        self.log_message(f"点击下一页失败 ({consecutive_failures}/{max_consecutive_failures})")
                        
                        # 如果连续多次失败，尝试刷新页面并重试
                        if consecutive_failures < max_consecutive_failures:
                            self.log_message("尝试刷新页面后再次尝试...")
                            driver.refresh()
                            time.sleep(3)
                            
                            # 再次尝试点击下一页
                            if self.click_next_page(driver):
                                self.log_message("下一页")
                                page_number += 1
                                expected_page = page_number  # 更新期望的页码
                                consecutive_failures = 0
                                continue
                        
                        # 如果达到最大连续失败次数，退出循环
                        if consecutive_failures >= max_consecutive_failures:
                            self.log_message(f"达到最大连续失败次数 ({max_consecutive_failures})，停止处理")
                            break
                    
                    # 关闭意外打开的额外窗口
                    if len(driver.window_handles) > 1:
                        driver.close()
                        driver.switch_to.window(driver.window_handles[0])
                except Exception as e:
                    if "time" in str(e).lower():
                        raise TimeoutError(f"处理页面时超时: {str(e)}")
                    # self.log_message(f"处理页面时出错: {str(e)}")
                    consecutive_failures += 1
                    
                    if consecutive_failures >= max_consecutive_failures:
                        self.log_message(f"达到最大连续失败次数 ({max_consecutive_failures})，停止处理")
                        break
                        
                    self.log_message(f"处理页面出错，尝试继续... ({consecutive_failures}/{max_consecutive_failures})")
            
            # 如果检测到页面重定向，重新启动浏览器会话并从第一页继续
            if page_redirect_detected and fixed_page is not None:
                self.log_message(f"检测到被重定向回第 {fixed_page} 页，将重启浏览器会话 (尝试 {page_regression_count}/{max_page_regression_retries})")
                
                # 关闭当前浏览器会话
                if driver:
                    self.log_message("关闭当前浏览器会话")
                    self.close_browser()
                
                # 强制进行内存清理
                self.gc_memory()
                
                # 等待一段时间再重新开始，根据重试次数增加等待时间
                wait_time = 10 + 5 * page_regression_count  # 根据重试次数增加等待时间
                self.log_message(f"等待{wait_time}秒后使用新浏览器会话继续...")
                time.sleep(wait_time)
                
                # 使用递归调用继续处理相同的关键词，但使用新的浏览器会话
                # 注意：这里我们不传递已处理的页码，因为我们希望从第一页重新开始，
                # 但由于已经提取的链接存储在self.all_amazon_codes中，所以不会重复添加相同的链接
                self.log_message(f"使用新的浏览器会话重新开始处理关键词 '{keyword}'")
                self.process_keyword(keyword)
                return  # 重要：返回避免重复处理
                
        except TimeoutError as e:
            # 明确识别超时错误
            self.log_message(f"关键词 '{keyword}' 发生超时错误: {str(e)}")
            raise  # 重新抛出异常，让调用者处理重试逻辑
        except Exception as e:
            self.log_message(f"处理关键词 '{keyword}' 时出错: {str(e)}")
            raise  # 重新抛出异常，让调用者处理重试逻辑
        finally:
            # 不再每次关闭浏览器，而是保持浏览器实例活跃
            # 浏览器实例现在由get_browser_instance和close_browser方法管理
            pass
    
    def check_no_results(self, driver):
        try:
            no_results_element = driver.find_element(By.CSS_SELECTOR, "h2")
            if "没有与此相关的结果" in no_results_element.text or "No results found" in no_results_element.text:
                return True
        except Exception:
            pass
        return False
    
    def click_next_page(self, driver):
        """
        尝试多种方式点击下一页按钮，并验证是否成功点击到下一页。
        返回是否成功点击到下一页的布尔值。
        """
        try:
            # 保存当前URL和页面源码长度，用于后续验证点击是否成功
            current_url = driver.current_url
            current_source_length = len(driver.page_source)
            
            # 记录当前页码，通常页码会出现在URL中
            current_page_number = None
            url_page_match = re.search(r'(page=|pg=|p=)(\d+)', current_url)
            if url_page_match:
                current_page_number = int(url_page_match.group(2))
                self.log_message(f"当前检测到页码: {current_page_number}")
            
            # 尝试多种可能的"下一页"按钮选择器
            next_page_selectors = [
                "a.sb_pagN", "a.pagnNext", "li.a-last a", ".s-pagination-next",
                "a[aria-label='Next']", "a[title='Next']", 
                "a.next", "div.pagination a:last-child",
                "a.next-page", ".pagination .next", "a:contains('Next')",
                "a[rel='next']", ".b_nextpage a", ".b_pag a.sb_pagN",
                "a.yui3-pagination-link.pag-next",
                "a.pag-next", "#pnnext", "#botstuff a#pnnext",
                "a.page-next", "a.gsc-cursor-next-page"
            ]
            
            # 最多尝试3次点击下一页按钮
            for attempt in range(3):
                try:
                    for selector in next_page_selectors:
                        try:
                            # 尝试使用CSS选择器找到下一页按钮
                            next_buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                            for next_button in next_buttons:
                                # 检查按钮是否可见和可点击
                                if next_button.is_displayed() and next_button.is_enabled():
                                    # 尝试滚动到按钮位置以确保可见
                                    driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", next_button)
                                    time.sleep(1)
                                    # 尝试直接点击
                                    next_button.click()
                                    # 等待页面加载
                                    time.sleep(3)
                                    
                                    # 验证点击是否成功
                                    if driver.current_url != current_url or len(driver.page_source) != current_source_length:
                                        self.log_message("下一页")
                                        return True
                        except Exception as e:
                            continue
                    
                    # 如果上面的选择器都没有成功，尝试查找含有"下一页"文本的链接
                    try:
                        # 尝试使用XPath找到文本包含"Next"的链接
                        next_text_links = driver.find_elements(By.XPATH, "//a[contains(text(), 'Next') or contains(text(), 'next') or contains(@aria-label, 'Next')]")
                        for link in next_text_links:
                            if link.is_displayed() and link.is_enabled():
                                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", link)
                                time.sleep(1)
                                link.click()
                                time.sleep(3)
                                
                                if driver.current_url != current_url or len(driver.page_source) != current_source_length:
                                    self.log_message("下一页")
                                    return True
                    except:
                        pass
                    
                    # 如果当前页码已知且在URL中，尝试查找并点击下一个页码
                    if current_page_number is not None:
                        next_page_number = current_page_number + 1
                        try:
                            # 尝试查找链接中的页码
                            next_page_links = driver.find_elements(By.XPATH, f"//a[contains(text(), '{next_page_number}')]")
                            for link in next_page_links:
                                if link.is_displayed() and link.is_enabled():
                                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", link)
                                    time.sleep(1)
                                    link.click()
                                    time.sleep(3)
                                    
                                    if driver.current_url != current_url or len(driver.page_source) != current_source_length:
                                        self.log_message("下一页")
                                        return True
                        except:
                            pass
                    
                    # 尝试使用键盘右箭头键导航
                    try:
                        body = driver.find_element(By.TAG_NAME, 'body')
                        body.send_keys(Keys.ARROW_RIGHT)
                        time.sleep(3)
                        
                        if driver.current_url != current_url or len(driver.page_source) != current_source_length:
                            self.log_message("下一页")
                            return True
                    except:
                        pass
                    
                    # 尝试直接修改URL
                    if current_page_number is not None:
                        try:
                            # 构造下一页的URL
                            next_url = re.sub(r'(page=|pg=|p=)(\d+)', f'\\g<1>{current_page_number + 1}', current_url)
                            
                            if next_url != current_url:
                                driver.get(next_url)
                                time.sleep(3)
                                self.log_message("下一页")
                                return True
                        except:
                            pass
                    
                except Exception as e:
                    self.log_message(f"点击下一页时出错 (尝试 {attempt+1}/3): {str(e)}")
                    time.sleep(2)
            
            # 如果所有尝试都失败，则记录错误
            self.log_message("无法找到或点击下一页按钮，可能已到最后一页或网页结构已更改")
            return False
            
        except Exception as e:
            self.log_message(f"click_next_page方法出错: {str(e)}")
            return False
    
    def process_link(self, full_link):
        try:
            # 获取当前选择的国家
            domain = self.countries[self.selected_country.get()]["domain"]
            
            # 调试日志，输出完整链接
            # self.log_message(f"处理链接: {full_link}")
            
            # Direct link extraction
            if '/dp/B' in full_link:
                code = self.extract_amazon_code(full_link)
                if code and code not in self.all_amazon_codes:
                    self.all_amazon_codes.append(code)
                    self.log_message(f"找到Amazon代码: {code}")
                    # 实时保存到Excel
                    self.save_to_excel_realtime(code)
            # Bing重定向链接处理（包含'bing.com/ck'特征）
            elif 'bing.com/ck' in full_link:
                url_parts = urlparse(full_link)
                query = parse_qs(url_parts.query)
                encoded_amazon_url = query.get('u', [None])[0]
                
                if encoded_amazon_url:
                    # 提取Base64编码部分
                    if "aHR0" in encoded_amazon_url:
                        encoded_part = encoded_amazon_url.split('aHR0')[-1]
                        decoded_base64_url = self.decode_base64_url('aHR0' + encoded_part)
                    else:
                        decoded_base64_url = self.decode_base64_url(encoded_amazon_url)
                    
                    # 检查是否需要添加前缀
                    if decoded_base64_url.startswith('http'):
                        amazon_url = decoded_base64_url
                    else:
                        amazon_url = f'https://{decoded_base64_url}'
                    
                    # 尝试提取代码
                    code = self.extract_amazon_code(amazon_url)
                    if code and code not in self.all_amazon_codes:
                        self.all_amazon_codes.append(code)
                        self.log_message(f"找到Amazon代码: {code}")
                        # 实时保存到Excel
                        self.save_to_excel_realtime(code)
            # 其他URL参数提取
            else:
                url_parts = urlparse(full_link)
                query = parse_qs(url_parts.query)
                encoded_amazon_url = query.get('u', [None])[0]
                
                if encoded_amazon_url:
                    decoded_base64_url = self.decode_base64_url(encoded_amazon_url.split('aHR0')[-1]) if "aHR0" in encoded_amazon_url else ''
                    amazon_url = f'https://{decoded_base64_url}'
                    code = self.extract_amazon_code(amazon_url)
                    if code and code not in self.all_amazon_codes:
                        self.all_amazon_codes.append(code)
                        self.log_message(f"找到Amazon代码: {code}")
                        # 实时保存到Excel
                        self.save_to_excel_realtime(code)
        except Exception as e:
            self.log_message(f"处理链接时出错: {str(e)}")
    
    def extract_amazon_code(self, amazon_url):
        # 修改正则表达式以匹配不同长度的Amazon产品代码（B后面跟着9-10位字母或数字）
        regex = re.compile(r'/dp/(B[A-Z0-9]{9,10})')
        match = regex.search(amazon_url)
        return match.group(1) if match else None
    
    def decode_base64_url(self, encoded_url):
        try:
            # 如果为空，直接返回
            if not encoded_url:
                return ''
                
            # 确保处理的是标准的Base64字符串
            encoded_url = encoded_url.replace('-', '+').replace('_', '/')
            
            # 添加缺失的填充
            missing_padding = len(encoded_url) % 4
            if missing_padding:
                encoded_url += '=' * (4 - missing_padding)
                
            # 尝试解码
            try:
                decoded_bytes = b64decode(encoded_url)
                return decoded_bytes.decode('utf-8', errors='ignore')
            except Exception as e:
                # 如果解码失败，可能是因为编码不是标准Base64，尝试不同的处理方式
                if 'aHR0' in encoded_url:  # 这可能是URL的Base64编码特征
                    try:
                        # 尝试从aHR0（http的Base64编码开头）开始解码
                        encoded_part = encoded_url.split('aHR0')[-1]
                        full_encoded = 'aHR0' + encoded_part
                        full_encoded = full_encoded.replace('-', '+').replace('_', '/')
                        
                        # 添加缺失的填充
                        missing_padding = len(full_encoded) % 4
                        if missing_padding:
                            full_encoded += '=' * (4 - missing_padding)
                        
                        decoded_bytes = b64decode(full_encoded)
                        return decoded_bytes.decode('utf-8', errors='ignore')
                    except:
                        self.log_message("解码Base64编码URL时出错")
                        return encoded_url  # 返回原始编码作为备用
                else:
                    self.log_message(f"Base64解码错误: {str(e)}")
                    return encoded_url  # 返回原始编码作为备用
        except Exception as e:
            self.log_message(f"处理Base64编码URL时出错: {str(e)}")
            return ''
    
    def initialize_browser(self):
        """初始化Selenium浏览器，采用更高效的自动发现机制"""
        global thread_local
        try:
            # 检查线程本地存储中是否已有driver实例
            if hasattr(thread_local, 'driver') and thread_local.driver:
                self.log_message("使用线程本地存储的浏览器实例")
                return thread_local.driver
            
            self.log_message("正在初始化浏览器...")
            
            # 检测是否为Win7系统
            is_win7 = 'Windows-7' in platform.platform() or '6.1.' in platform.version()
            if is_win7:
                self.log_message("检测到Windows 7系统，将使用兼容模式")
            
            # 创建Edge选项
            options = webdriver.EdgeOptions()
            
            # 设置用户数据目录(可选)
            user_data_dir = self.get_user_data_dir()
            if user_data_dir:
                options.add_argument(f"--user-data-dir={user_data_dir}")
            
            # 设置随机用户代理
            user_agent = self.get_random_user_agent()
            options.add_argument(f'--user-agent={user_agent}')
            
            # 隐藏自动化特征
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 通用选项设置
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            options.add_argument("--disable-notifications")
            options.add_argument("--disable-popup-blocking")
            
            # 降低日志级别，减少控制台输出
            options.add_argument("--log-level=3")
            
            # 是否使用无头模式
            if self.headless_mode.get():
                if not is_win7:  # Win10系统使用新无头模式
                    options.add_argument("--headless=new")  # 新版无头模式
                else:  # Win7系统使用旧无头模式
                    options.add_argument("--headless")  # 传统无头模式
                
                # 无头模式下的额外设置
                options.add_argument("--window-size=1920,1080")
                options.add_argument("--start-maximized")
                # 无头模式下的WebGL设置
                options.add_argument("--disable-webgl")
                options.add_argument("--disable-canvas-aa")
                options.add_argument("--disable-2d-canvas-clip-aa")
                
                # 移除无头模式日志
            else:
                # 设置窗口大小
                options.add_argument("--window-size=1920,1080")
            
            # 创建Edge浏览器实例 - 不指定驱动路径，使用自动发现
            try:
                # 不再使用webdriver_manager，让Selenium自动发现驱动
                service = EdgeService()
                driver = webdriver.Edge(service=service, options=options)
                self.log_message("成功初始化Edge浏览器")
                
                # 设置页面加载超时
                driver.set_page_load_timeout(30)
                
                # 执行CDP命令绕过检测
                self.execute_cdp_commands(driver)
                
                # 最大化窗口（非无头模式）
                if not self.headless_mode.get():
                    driver.maximize_window()
                
                # 保存到线程本地存储
                thread_local.driver = driver
                
                # 更新UI状态
                if hasattr(self, 'browser_status_label') and self.browser_status_label:
                    self.browser_status_label.config(text="浏览器状态: 已初始化", fg="green")
                
                return driver
                
            except Exception as e:
                self.log_message(f"Edge浏览器初始化失败: {str(e)}")
                self.log_message("尝试初始化Chrome浏览器...")
                
                # 尝试使用Chrome
                chrome_options = webdriver.ChromeOptions()
                
                # 设置随机用户代理
                chrome_options.add_argument(f'--user-agent={user_agent}')
                
                # 隐藏自动化特征
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                # 无头模式设置
                if self.headless_mode.get():
                    if not is_win7:
                        chrome_options.add_argument('--headless=new')
                    else:
                        chrome_options.add_argument('--headless')
                    chrome_options.add_argument('--window-size=1920,1080')
                
                # 复用相同的其他选项配置
                chrome_options.add_argument("--disable-gpu")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-web-security")
                chrome_options.add_argument("--allow-running-insecure-content")
                chrome_options.add_argument("--disable-notifications")
                chrome_options.add_argument("--disable-popup-blocking")
                chrome_options.add_argument("--log-level=3")
                
                # 不指定驱动路径，使用自动发现
                chrome_service = ChromeService()
                driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
                self.log_message("成功初始化Chrome浏览器")
                
                # 设置页面加载超时
                driver.set_page_load_timeout(30)
                
                # 执行CDP命令绕过检测
                self.execute_cdp_commands(driver)
                
                # 最大化窗口（非无头模式）
                if not self.headless_mode.get():
                    driver.maximize_window()
                
                # 保存到线程本地存储
                thread_local.driver = driver
                
                # 更新UI状态
                if hasattr(self, 'browser_status_label') and self.browser_status_label:
                    self.browser_status_label.config(text="浏览器状态: 已初始化", fg="green")
                
                return driver
        except Exception as e:
            error_message = f"浏览器初始化失败: {str(e)}"
            self.log_message(error_message)
            
            # 更新UI状态
            if hasattr(self, 'browser_status_label') and self.browser_status_label:
                self.browser_status_label.config(text="浏览器状态: 初始化失败", fg="red")
            
            # 最后尝试Firefox作为备选
            try:
                self.log_message("尝试初始化Firefox浏览器...")
                
                firefox_options = webdriver.FirefoxOptions()
                
                # 设置用户代理
                firefox_options.set_preference("general.useragent.override", user_agent)
                
                # 隐藏自动化特征
                firefox_options.set_preference("dom.webdriver.enabled", False)
                firefox_options.set_preference("useAutomationExtension", False)
                
                # 无头模式设置
                if self.headless_mode.get():
                    firefox_options.add_argument("--headless")
                    firefox_options.add_argument('--width=1920')
                    firefox_options.add_argument('--height=1080')
                
                # 不指定驱动路径，使用自动发现
                firefox_service = FirefoxService()
                driver = webdriver.Firefox(service=firefox_service, options=firefox_options)
                self.log_message("成功初始化Firefox浏览器")
                
                # 设置页面加载超时
                driver.set_page_load_timeout(30)
                
                # 保存到线程本地存储
                thread_local.driver = driver
                
                # 最大化窗口（非无头模式）
                if not self.headless_mode.get():
                    driver.maximize_window()
                
                # 更新UI状态
                if hasattr(self, 'browser_status_label') and self.browser_status_label:
                    self.browser_status_label.config(text="浏览器状态: 已初始化", fg="green")
                
                return driver
            except Exception as firefox_e:
                self.log_message(f"Firefox浏览器初始化也失败: {str(firefox_e)}")
                if hasattr(self, 'browser_status_label') and self.browser_status_label:
                    self.browser_status_label.config(text="浏览器状态: 所有初始化尝试均失败", fg="red")
                raise Exception(f"所有浏览器初始化均失败: Edge/Chrome/Firefox")

    def on_closing(self):
        """程序关闭时的清理操作"""
        try:
            # 停止爬取过程
            self.stop_scraping = True
            
            # 停止内存监控
            self.memory_monitor_active = False
            
            # 保存超时关键词
            self.save_timeout_keywords()
            
            # 关闭浏览器实例
            self.close_browser()
            
            # 关闭线程驱动
            try:
                if hasattr(thread_local, 'driver') and thread_local.driver:
                    try:
                        thread_local.driver.quit()
                    except:
                        pass
                    thread_local.driver = None
            except:
                pass
            
            # 执行任何其他必要的清理
            try:
                # 批量保存未保存的数据
                if self.pending_codes and self.realtime_excel_file:
                    self.perform_batch_save()
            except:
                pass
            
            # 关闭主窗口
            self.master.destroy()
        except Exception as e:
            print(f"关闭错误: {str(e)}")
            self.master.destroy()
    
    def start_memory_monitor(self):
        """启动内存监控，当内存使用过高时提醒用户"""
        if not self.is_32bit:
            return
            
        self.memory_monitor_active = True
        threading.Thread(target=self.monitor_memory, daemon=True).start()
    
    def monitor_memory(self):
        """监控内存使用情况"""
        while self.memory_monitor_active:
            try:
                process = psutil.Process(os.getpid())
                memory_info = process.memory_info()
                memory_usage_mb = memory_info.rss / 1024 / 1024  # 转换为MB
                
                # 32位Windows系统内存限制约为2GB
                memory_limit_mb = 1800  # 保守设置为1.8GB
                
                if memory_usage_mb > memory_limit_mb:
                    # 在UI线程中显示警告
                    self.master.after(0, lambda: self.show_memory_warning(memory_usage_mb))
                
                # 每5秒检查一次
                time.sleep(5)
            except Exception:
                time.sleep(5)
                continue
    
    def show_memory_warning(self, memory_usage):
        """显示内存警告"""
        messagebox.showwarning(
            "内存不足警告", 
            f"程序当前内存使用: {memory_usage:.1f}MB\n"
            "由于32位系统限制，程序可能即将耗尽内存。\n"
            "建议保存当前进度并重启程序，或减少批处理大小。"
        )
        # 自动保存进度
        self.save_progress()
        
    def gc_memory(self):
        """强制进行内存清理"""
        try:
            # 显示当前内存使用
            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            
            # 对大型变量进行特殊处理
            if hasattr(self, 'keywords_text') and self.keywords_text:
                # 清除文本框缓存的撤销/重做历史
                try:
                    self.keywords_text.edit_reset()
                except:
                    pass
            
            # 强制进行垃圾回收
            import gc
            # 设置垃圾回收的阈值更小，以便更频繁地回收
            old_threshold = gc.get_threshold()
            gc.set_threshold(100, 5, 2)  # 大幅降低阈值
            
            # 执行完整的垃圾回收循环
            gc.collect(0)  # 收集第0代（最年轻的对象）
            gc.collect(1)  # 收集第1代
            gc.collect(2)  # 收集第2代（最老的对象）
            
            # 恢复原来的阈值
            gc.set_threshold(*old_threshold)
            
            # 再次检查内存使用
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            self.log_message(f"内存清理: {memory_before:.1f}MB -> {memory_after:.1f}MB")
        except Exception as e:
            self.log_message(f"内存清理异常: {str(e)}")
            pass
    
    def get_random_user_agent(self):
        """返回一个随机的用户代理"""
        user_agents = [
            # Windows 10 + Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 Edg/98.0.1108.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36 Edg/99.0.1150.39",
            # Windows 10 + Chrome
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
            # Windows 7 + Chrome/Edge (适合Win7系统)
            "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
            "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36 Edg/92.0.902.84",
            "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]
        return random.choice(user_agents)

    def show_help(self):
        """显示帮助信息，包括浏览器驱动下载指南"""
        help_text = """
Edge浏览器驱动下载指南:

1. 查看您的Edge浏览器版本:
   - 打开Edge浏览器，点击右上角"..."
   - 选择"设置" -> "关于Microsoft Edge"
   - 记下版本号，例如：112.0.1722.58

2. 下载匹配版本的Edge驱动:
   - 访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
   - 下载与您Edge浏览器版本匹配的驱动
   - 解压下载的文件

3. 放置驱动文件:
   - 将msedgedriver.exe文件放置在以下位置:
     C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedgedriver.exe
   - 或者在"配置"中设置自定义路径

注意事项:
- 驱动版本必须与浏览器版本匹配
- 如果收到"无法初始化浏览器"错误，请确认驱动版本是否正确
- 程序仅支持Edge浏览器
        """
        
        # 创建帮助对话框
        help_dialog = tk.Toplevel(self.master)
        help_dialog.title("Edge浏览器驱动帮助")
        help_dialog.geometry("600x400")
        help_dialog.resizable(True, True)
        
        # 添加帮助内容
        help_text_widget = scrolledtext.ScrolledText(help_dialog, wrap=tk.WORD)
        help_text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        help_text_widget.insert(tk.END, help_text)
        help_text_widget.config(state=tk.DISABLED)
        
        # 添加关闭按钮
        close_button = ttk.Button(help_dialog, text="关闭", command=help_dialog.destroy)
        close_button.pack(pady=10)
        
        # 设置模态
        help_dialog.transient(self.master)
        help_dialog.grab_set()
        self.master.wait_window(help_dialog)

    def show_config(self):
        """显示配置对话框，允许用户设置Edge驱动路径"""
        # 创建配置对话框
        config_dialog = tk.Toplevel(self.master)
        config_dialog.title("配置")
        config_dialog.geometry("600x130")
        config_dialog.resizable(True, False)
        
        # 创建表单
        frame = ttk.Frame(config_dialog, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Edge驱动路径
        ttk.Label(frame, text="Edge 驱动路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        edge_path = tk.StringVar(value=r"C:\Program Files (x86)\Microsoft\Edge\Application\msedgedriver.exe")
        edge_entry = ttk.Entry(frame, textvariable=edge_path, width=50)
        edge_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5)
        ttk.Button(frame, text="浏览", command=lambda: self.browse_driver(edge_path)).grid(row=0, column=2)
        
        # 显示提示信息
        ttk.Label(frame, text="注意: 必须使用与Edge浏览器版本匹配的驱动文件", foreground="red").grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # 保存配置回调
        def save_config():
            # 保存驱动路径到配置文件
            config = {
                "edge_driver_path": edge_path.get(),
                "first_run": False
            }
            try:
                with open("amazon_scraper_config.json", 'w') as f:
                    json.dump(config, f)
                self.log_message("配置已保存")
                messagebox.showinfo("成功", "Edge驱动路径配置已保存")
                config_dialog.destroy()
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
        
        # 加载现有配置
        try:
            if os.path.exists("amazon_scraper_config.json"):
                with open("amazon_scraper_config.json", 'r') as f:
                    config = json.load(f)
                    if "edge_driver_path" in config and config["edge_driver_path"]:
                        edge_path.set(config["edge_driver_path"])
        except:
            pass
            
        # 按钮区域
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="保存", command=save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=config_dialog.destroy).pack(side=tk.LEFT, padx=5)
        
        # 设置模态
        config_dialog.transient(self.master)
        config_dialog.grab_set()
        self.master.wait_window(config_dialog)
    
    def browse_driver(self, path_var):
        """浏览文件对话框，选择驱动路径"""
        file_path = filedialog.askopenfilename(
            filetypes=[("驱动文件", "*.exe"), ("所有文件", "*.*")]
        )
        if file_path:
            path_var.set(file_path)
            
    def translate_keyword(self, keyword, target_country):
        """使用百度翻译API翻译关键词"""
        try:
            # 目标语言代码
            lang_code = "ja" if target_country == "日本" else "en"
            
            # 简单翻译实现 - 使用免费API
            url = "https://api.fanyi.baidu.com/api/trans/vip/translate"
            
            # 替换为您自己的百度翻译API密钥
            app_id = "YOUR_APP_ID"  # 需要替换为实际的百度翻译API ID
            secret_key = "YOUR_SECRET_KEY"  # 需要替换为实际的百度翻译API密钥
            
            # 如果未设置API密钥，使用备用简单翻译方法
            if app_id == "YOUR_APP_ID" or secret_key == "YOUR_SECRET_KEY":
                return self.simple_translate(keyword, lang_code)
            
            # 使用百度翻译API
            salt = str(round(time.time() * 1000))
            sign = app_id + keyword + salt + secret_key
            sign = hashlib.md5(sign.encode()).hexdigest()
            
            payload = {
                'q': keyword,
                'from': 'zh',
                'to': lang_code,
                'appid': app_id,
                'salt': salt,
                'sign': sign
            }
            
            response = requests.post(url, params=payload)
            result = response.json()
            
            if 'trans_result' in result:
                return result['trans_result'][0]['dst']
            else:
                self.log_message(f"翻译失败: {result.get('error_msg', '未知错误')}")
                return keyword
                
        except Exception as e:
            self.log_message(f"翻译出错: {str(e)}")
            return keyword
    
    def simple_translate(self, keyword, lang_code):
        """简单翻译方案（当API密钥未设置时使用）"""
        try:
            # 使用公共免费翻译API
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                "client": "gtx",
                "sl": "zh-CN",
                "tl": lang_code,
                "dt": "t",
                "q": keyword
            }
            
            response = requests.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and len(result[0]) > 0:
                    return result[0][0][0]
            
            return keyword
            
        except Exception as e:
            self.log_message(f"简易翻译出错: {str(e)}")
            return keyword

    def load_link_cache(self):
        """加载保存的链接缓存"""
        try:
            cache_file = "amazon_links_cache.txt"
            if os.path.exists(cache_file):
                # 逐行读取文件，避免一次性加载整个文件到内存
                print("Initializing link cache...")
                
                # 检测系统是否为32位
                is_32bit = platform.architecture()[0] == '32bit' or '32bit' in sys.version
                
                # 优化内存使用的读取方式
                if is_32bit:
                    # 32位系统使用更保守的方式读取
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        count = 0
                        while True:
                            lines = f.readlines(10000)  # 每次读取10000行
                            if not lines:
                                break
                            for line in lines:
                                link = line.strip()
                                if link:  # 确保不添加空行
                                    self.cached_links.add(link)
                                    count += 1
                else:
                    # 64位系统可以一次性读取
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        count = 0
                        for line in f:
                            link = line.strip()
                            if link:
                                self.cached_links.add(link)
                                count += 1
                
                self.cache_initialized = True
                print(f"Loaded link cache: {count} links")
                return True
            return False
        except Exception as e:
            print("Error loading link cache:", str(e))
            # 重置缓存
            self.cached_links = set()
            self.cache_initialized = False
            return False

    def execute_cdp_commands(self, driver):
        """执行CDP命令绕过浏览器自动化检测"""
        try:
            # 设置随机用户代理
            user_agent = self.get_random_user_agent()
            driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": user_agent,
                "platform": "Windows",
                "acceptLanguage": "zh-CN,zh;q=0.9,en;q=0.8"
            })
            
            # 修改navigator.webdriver属性
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    // 覆盖webdriver属性
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    
                    // 修改语言设置
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en-US', 'en']
                    });
                    
                    // 添加插件信息
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => {
                            return [
                                {
                                    "description": "Portable Document Format",
                                    "filename": "internal-pdf-viewer",
                                    "length": 1,
                                    "name": "PDF Viewer"
                                },
                                {
                                    "description": "Chrome PDF Viewer",
                                    "filename": "internal-pdf-viewer",
                                    "length": 1,
                                    "name": "Chrome PDF Viewer"
                                },
                                {
                                    "description": "Microsoft Edge PDF Viewer",
                                    "filename": "internal-pdf-viewer",
                                    "length": 1,
                                    "name": "Microsoft Edge PDF Viewer"
                                }
                            ];
                        }
                    });
                    
                    // 模拟connection属性
                    Object.defineProperty(navigator, 'connection', {
                        get: () => {
                            return {
                                effectiveType: '4g',
                                rtt: 50,
                                downlink: 10,
                                saveData: false
                            };
                        }
                    });
                    
                    // 覆盖Permissions API
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' || 
                        parameters.name === 'geolocation' ||
                        parameters.name === 'midi' || 
                        parameters.name === 'camera' || 
                        parameters.name === 'microphone'
                    ) 
                    ? Promise.resolve({state: 'prompt', onchange: null})
                    : originalQuery(parameters);
                    
                    // 覆盖WebGL信息
                    const getParameter = WebGLRenderingContext.prototype.getParameter;
                    WebGLRenderingContext.prototype.getParameter = function(parameter) {
                        // UNMASKED_VENDOR_WEBGL
                        if (parameter === 37445) {
                            return 'Intel Inc.';
                        }
                        // UNMASKED_RENDERER_WEBGL
                        if (parameter === 37446) {
                            return 'Intel Iris OpenGL Engine';
                        }
                        return getParameter.apply(this, arguments);
                    };
                    
                    // 修改Chrome对象
                    if (window.chrome) {
                        window.chrome = {
                            ...window.chrome,
                            runtime: {},
                            loadTimes: function() { return {}; },
                            csi: function() { return {}; },
                            app: {}
                        };
                    }
                    
                    // 覆盖WebDriver相关方法
                    if (window.document.documentElement) {
                        Object.defineProperty(window.document.documentElement, 'webdriver', {
                            get: () => false
                        });
                    }
                    
                    // 修改screen属性使其更自然
                    Object.defineProperty(window.screen, 'width', { 
                        get: () => 1920 
                    });
                    Object.defineProperty(window.screen, 'height', { 
                        get: () => 1080 
                    });
                    Object.defineProperty(window.screen, 'availWidth', { 
                        get: () => 1920 
                    });
                    Object.defineProperty(window.screen, 'availHeight', { 
                        get: () => 1040 
                    });
                    Object.defineProperty(window.screen, 'colorDepth', { 
                        get: () => 24 
                    });
                    Object.defineProperty(window.screen, 'pixelDepth', { 
                        get: () => 24 
                    });
                '''
            })
            
            # 屏蔽自动化特征
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    // 隐藏自动化控制台信息
                    window.console.debug = () => {};
                    
                    // 修改驱动相关属性
                    if (window.chrome && window.chrome.loadTimes) {
                        const originalFunction = window.chrome.loadTimes;
                        window.chrome.loadTimes = () => {
                            return {
                                firstPaintTime: originalFunction().firstPaintTime,
                                requestTime: originalFunction().requestTime,
                                startLoadTime: originalFunction().startLoadTime,
                                commitLoadTime: originalFunction().commitLoadTime,
                                finishDocumentLoadTime: originalFunction().finishDocumentLoadTime,
                                finishLoadTime: originalFunction().finishLoadTime,
                                firstPaintAfterLoadTime: originalFunction().firstPaintAfterLoadTime,
                                navigationType: originalFunction().navigationType,
                                wasFetchedViaSpdy: originalFunction().wasFetchedViaSpdy,
                                wasNpnNegotiated: originalFunction().wasNpnNegotiated,
                                npnNegotiatedProtocol: originalFunction().npnNegotiatedProtocol,
                                wasAlternateProtocolAvailable: originalFunction().wasAlternateProtocolAvailable,
                                connectionInfo: originalFunction().connectionInfo,
                                wasAltRsvAvailable: true
                            };
                        };
                    }
                '''
            })
            
            self.log_message("已执行CDP命令绕过浏览器自动化检测")
            return True
        except Exception as e:
            self.log_message(f"执行CDP命令失败: {str(e)}")
            return False

    def get_user_data_dir(self):
        """获取浏览器用户数据目录"""
        try:
            # 用户数据目录的基本路径
            base_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Amazon_Scraper_UserData")
            
            # 确保目录存在
            os.makedirs(base_dir, exist_ok=True)
            
            # 返回用户数据目录路径
            return base_dir
        except Exception as e:
            self.log_message(f"创建用户数据目录失败: {str(e)}")
            return None

    def get_browser_instance(self):
        """获取或创建浏览器实例，实现浏览器复用"""
        current_time = time.time()
        
        # 检查现有浏览器实例
        if self.browser_instance:
            # 检查是否空闲时间过长需要重新创建
            if current_time - self.browser_last_used > self.browser_max_idle_time:
                self.log_message("浏览器空闲时间过长，重新创建实例")
                self.close_browser()
                self.browser_instance = None
                # 强制进行垃圾回收
                self.gc_memory()
            # 检查是否使用次数过多需要重新创建
            elif self.browser_use_count >= self.browser_max_uses:
                self.log_message(f"浏览器已使用 {self.browser_use_count} 次，超过最大使用次数限制，重新创建实例")
                self.close_browser()
                self.browser_instance = None
                # 强制进行垃圾回收
                self.gc_memory()
            else:
                # 可以继续使用现有实例
                self.browser_use_count += 1
                self.browser_last_used = current_time
                
                # 防止浏览器内存泄漏，定期清理缓存
                if self.browser_use_count % 10 == 0:
                    try:
                        # 执行JavaScript清理内存
                        self.browser_instance.execute_script("""
                        try {
                            window.performance.memory.usedJSHeapSize = 0;
                            window.performance.memory.totalJSHeapSize = 0;
                        } catch(e) {}
                        """)
                        self.log_message("已执行浏览器内存清理")
                    except:
                        pass
                
                return self.browser_instance
        
        # 创建新的浏览器实例
        try:
            # 先进行垃圾回收以确保有足够内存
            self.gc_memory()
            self.browser_instance = self.initialize_browser()
            if not self.browser_instance:
                # 初始化失败，再次尝试清理并重试
                self.log_message("浏览器初始化失败，进行内存清理后重试")
                self.gc_memory()
                time.sleep(2)
                self.browser_instance = self.initialize_browser()
            
            self.browser_use_count = 1
            self.browser_last_used = current_time
            return self.browser_instance
        except Exception as e:
            self.log_message(f"创建浏览器实例出错: {str(e)}")
            return None
    
    def close_browser(self):
        """安全关闭浏览器实例"""
        if self.browser_instance:
            try:
                self.browser_instance.quit()
            except Exception as e:
                self.log_message(f"关闭浏览器时出错: {str(e)}")
            finally:
                self.browser_instance = None
                self.browser_use_count = 0

    def perform_batch_save(self):
        """执行批量保存操作"""
        if not self.pending_codes or not self.realtime_excel_file:
            return
            
        try:
            self.log_message(f"正在批量保存 {len(self.pending_codes)} 个链接...")
            
            # 准备批量数据
            links = []
            countries = []
            for code, country in self.pending_codes:
                country_domain = self.countries[country]["domain"]
                link = f"https://www.{country_domain}/dp/{code}"
                links.append(link)
                countries.append(country)
                
            # 创建新数据DataFrame
            new_data = {
                'Amazon Links': links,
                '国家': countries
            }
            new_df = pd.DataFrame(new_data)
            
            # 32位系统使用更高效的保存方法
            if self.is_32bit:
                # 追加到临时CSV，稍后一次性转换
                temp_csv = f"{self.realtime_excel_file}.tmp.csv"
                new_df.to_csv(temp_csv, mode='a', header=False, index=False)
                
                # 如果数据量较大，立即转换
                if len(self.pending_codes) > 200:
                    self.convert_csv_to_excel_append()
            else:
                # 标准Excel保存流程
                if os.path.exists(self.realtime_excel_file):
                    try:
                        # 检查是否接近行数限制（使用计数器）
                        excel_row_limit = 64000  # 安全阈值
                        if self.current_row_count >= excel_row_limit:
                            self.log_message(f"Excel文件行数({self.current_row_count})接近限制，创建新文件")
                            self.create_new_excel_file(links[0], countries[0])
                            # 递归调用，使用新文件保存
                            return self.perform_batch_save()
                        
                        # 读取现有数据
                        existing_df = pd.read_excel(self.realtime_excel_file)
                        
                        # 合并并保存
                        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                        with pd.ExcelWriter(self.realtime_excel_file, engine='xlsxwriter') as writer:
                            combined_df.to_excel(writer, index=False)
                            # 优化列宽
                            worksheet = writer.sheets['Sheet1']
                            worksheet.set_column('A:A', 40)
                            worksheet.set_column('B:B', 15)
                        
                        # 更新行计数器
                        self.current_row_count += len(new_df)
                        self.log_message(f"更新Excel文件完成，当前行数: {self.current_row_count}")
                        
                    except Exception as e:
                        self.log_message(f"读取或合并Excel文件出错: {str(e)}")
                        
                        # 可能是Excel已满或损坏，创建新文件
                        if "单元格数量限制" in str(e) or "max_row" in str(e) or "行数" in str(e) or "row" in str(e).lower() or "URL" in str(e):
                            self.create_new_excel_file(links[0], countries[0])
                            # 递归调用，使用新文件保存
                            return self.perform_batch_save()
                        
                        # 其他错误，尝试直接创建新文件
                        with pd.ExcelWriter(self.realtime_excel_file, engine='xlsxwriter') as writer:
                            new_df.to_excel(writer, index=False)
                            # 优化列宽
                            worksheet = writer.sheets['Sheet1']
                            worksheet.set_column('A:A', 40)
                            worksheet.set_column('B:B', 15)
                        
                        # 更新行计数器
                        self.current_row_count = len(new_df)
                else:
                    # 文件不存在，直接创建
                    with pd.ExcelWriter(self.realtime_excel_file, engine='xlsxwriter') as writer:
                        new_df.to_excel(writer, index=False)
                        # 优化列宽
                        worksheet = writer.sheets['Sheet1']
                        worksheet.set_column('A:A', 40)
                        worksheet.set_column('B:B', 15)
                    
                    # 更新行计数器
                    self.current_row_count = len(new_df)
            
            # 清空待保存队列和计数器
            self.pending_codes = []
            self.batch_save_count = 0
            self.log_message(f"批量保存完成，共 {len(links)} 个链接")
            
        except Exception as e:
            self.log_message(f"批量保存到Excel时出错: {str(e)}")
            # 错误情况下，保留队列数据，等待下次尝试

def main():
    """程序入口函数"""
    root = tk.Tk()
    # 使用AmazonUITheme来设置窗口，这样可以确保在打包后依然能正确设置图标
    AmazonUITheme.setup_window(root, "亚马逊产品采集", "980x650")
    
    # 如果是Windows系统，额外设置任务栏图标
    try:
        if platform.system() == "Windows":
            import ctypes
            # 设置应用ID，以便任务栏图标正确显示
            app_id = "amazon.scraper.v8"
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
            
            # 定义Windows API常量
            ICON_SMALL = 0
            ICON_BIG = 1
            WM_SETICON = 0x0080
            IMAGE_ICON = 1
            LR_LOADFROMFILE = 0x0010
            
            # 获取窗口句柄
            hwnd = ctypes.windll.user32.GetParent(root.winfo_id())
            
            # 获取图标路径 - 使用AmazonUITheme的方法
            icon_path = AmazonUITheme.get_image_path()
            
            if icon_path and os.path.exists(icon_path):
                try:
                    # 使用绝对路径
                    abs_icon_path = os.path.abspath(icon_path)
                    # 使用ASCII字符输出路径，避免编码问题
                    print("Using icon path:", abs_icon_path)
                    
                    # 加载小图标
                    h_icon_small = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 16, 16, LR_LOADFROMFILE
                    )
                    if h_icon_small:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                    else:
                        print("Failed to load small icon")
                    
                    # 加载大图标
                    h_icon_big = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 32, 32, LR_LOADFROMFILE
                    )
                    if h_icon_big:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                    else:
                        print("Failed to load big icon")
                    
                    # 刷新窗口以应用更改
                    ctypes.windll.user32.UpdateWindow(hwnd)
                    
                    # 在窗口显示后再次设置图标 - 使用事件回调
                    def set_icon_after_visible():
                        try:
                            hwnd = ctypes.windll.user32.GetForegroundWindow()
                            if h_icon_small:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                            if h_icon_big:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                        except Exception as e:
                            print("Error setting icon after window visible:", str(e))
                    
                    # 在100ms后执行以确保窗口已完全加载
                    root.after(100, set_icon_after_visible)
                except Exception as e:
                    print("Error setting Windows taskbar icon:", str(e))
            else:
                print("Valid icon path not found")
    except Exception as e:
        print("Error setting application icon:", str(e))
    
    app = AmazonScraper(root)
    root.mainloop()

if __name__ == "__main__":
    main()
