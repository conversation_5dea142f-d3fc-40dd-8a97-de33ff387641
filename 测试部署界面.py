#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试部署界面工具
验证新的进度显示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

def test_gui():
    """测试GUI界面"""
    print("🔍 测试部署界面工具...")
    
    # 检查必要文件
    required_files = [
        "一键部署界面工具.py",
        "license_server.py",
        "auto_updater.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("\n✅ 所有必要文件检查通过")
    
    # 检查Python模块
    try:
        import tkinter
        print("✅ tkinter 模块可用")
    except ImportError:
        print("❌ tkinter 模块不可用")
        return False
    
    try:
        import subprocess
        print("✅ subprocess 模块可用")
    except ImportError:
        print("❌ subprocess 模块不可用")
        return False
    
    print("\n🚀 启动部署界面工具...")
    
    try:
        # 启动GUI工具
        subprocess.Popen([sys.executable, "一键部署界面工具.py"])
        print("✅ 部署界面工具启动成功")
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def create_test_files():
    """创建测试用的必要文件"""
    print("📝 创建测试文件...")
    
    # 创建简单的license_server.py
    if not os.path.exists("license_server.py"):
        license_server_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用授权服务器
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def index():
    return "授权服务器运行中"

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000)
'''
        with open("license_server.py", "w", encoding="utf-8") as f:
            f.write(license_server_content)
        print("✅ 创建 license_server.py")
    
    # 创建简单的auto_updater.py
    if not os.path.exists("auto_updater.py"):
        auto_updater_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用自动更新器
"""

def check_update():
    """检查更新"""
    return False

if __name__ == "__main__":
    print("自动更新器运行中")
'''
        with open("auto_updater.py", "w", encoding="utf-8") as f:
            f.write(auto_updater_content)
        print("✅ 创建 auto_updater.py")
    
    # 创建version_info.json
    if not os.path.exists("version_info.json"):
        import json
        version_info = {
            "version": "1.0.0",
            "build_date": "2024-01-01",
            "description": "测试版本"
        }
        with open("version_info.json", "w", encoding="utf-8") as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        print("✅ 创建 version_info.json")

def show_features():
    """显示新功能特性"""
    print("\n🎯 新增功能特性:")
    print("=" * 50)
    print("📊 详细进度显示:")
    print("  • 实时进度条 (0-100%)")
    print("  • 当前步骤显示")
    print("  • 步骤详情说明")
    print()
    print("🔧 部署步骤:")
    print("  1. 准备配置 (5%)")
    print("  2. 测试连接 (15%)")
    print("  3. 创建目录 (25%)")
    print("  4. 上传文件 (40%)")
    print("  5. 安装依赖 (60%)")
    print("  6. 配置服务 (75%)")
    print("  7. 配置防火墙 (85%)")
    print("  8. 启动服务 (95%)")
    print("  9. 验证部署 (100%)")
    print()
    print("⚡ 实时状态:")
    print("  • 显示当前执行的步骤")
    print("  • 显示详细的错误信息")
    print("  • 显示卡住的具体位置")
    print("  • 实时日志输出")

def main():
    """主函数"""
    print("🧪 部署界面测试工具")
    print("=" * 50)
    
    # 显示新功能
    show_features()
    
    # 创建测试文件
    create_test_files()
    
    print("\n🔍 开始测试...")
    
    # 测试GUI
    success = test_gui()
    
    if success:
        print("\n🎉 测试完成！")
        print("\n📋 使用说明:")
        print("1. 在GUI中配置服务器信息")
        print("2. 点击'🔗 测试连接'验证连接")
        print("3. 点击'🚀 开始部署'开始部署")
        print("4. 观察进度条和步骤显示")
        print("5. 查看详细日志了解进度")
        print("\n⚠️ 注意:")
        print("• 需要安装sshpass工具")
        print("• 确保服务器信息正确")
        print("• 网络连接稳定")
    else:
        print("\n❌ 测试失败")
        print("请检查错误信息并修复问题")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
