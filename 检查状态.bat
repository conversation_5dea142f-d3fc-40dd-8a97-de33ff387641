@echo off
chcp 65001 >nul
title 🔍 检查服务器状态

echo.
echo ==========================================
echo 🔍 检查服务器实际状态
echo ==========================================
echo.
echo 🔍 当前问题:
echo • ✅ 修复脚本显示成功
echo • ❌ 网页仍然显示404错误
echo • ❓ 服务实际状态未知
echo.
echo 🎯 检查内容:
echo • 🔧 systemd服务状态
echo • 🔌 端口监听状态
echo • 🐍 Python进程状态
echo • 📋 服务日志分析
echo • 📄 文件存在性检查
echo • 🧪 手动运行测试
echo • 🔥 防火墙配置
echo • 🌍 网络连通性
echo.

echo 🔍 开始状态检查...
echo.

REM 运行检查脚本
python "检查服务状态.py"

echo.
echo 👋 检查完成
pause
