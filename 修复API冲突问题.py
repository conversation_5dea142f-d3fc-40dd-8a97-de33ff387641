#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复API冲突问题 - 解决Flask路由重复定义
"""

import paramiko
import sys
import os

def fix_api_conflict():
    """修复API冲突问题"""
    print("🔧 修复API冲突问题")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 停止服务
        print("🛑 步骤1: 停止服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        stdout.channel.recv_exit_status()
        print("   ✅ 服务已停止")
        
        # 步骤2: 备份当前文件
        print("📁 步骤2: 备份当前文件...")
        from datetime import datetime
        backup_name = f"license_server.py.backup_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        stdin, stdout, stderr = client.exec_command(
            f"cp {config['deploy_path']}/license_server.py {config['deploy_path']}/{backup_name}"
        )
        stdout.channel.recv_exit_status()
        print(f"   ✅ 已备份到: {backup_name}")
        
        # 步骤3: 检查现有的check_update函数
        print("🔍 步骤3: 检查现有函数...")
        stdin, stdout, stderr = client.exec_command(
            f"grep -n 'def check_update\\|@app.route.*check_update' {config['deploy_path']}/license_server.py"
        )
        existing_functions = stdout.read().decode('utf-8')
        print(f"   现有函数:\n{existing_functions}")
        
        # 步骤4: 移除重复的API定义
        print("🗑️ 步骤4: 移除重复的API定义...")
        
        # 创建修复脚本
        fix_script = f"""
# 移除重复的更新API定义，保留原有的check_update函数
sed -i '/^# ==================== 自动更新API ====================/,/^# ==================== 更新API结束 ====================/d' {config['deploy_path']}/license_server.py

# 检查是否成功移除
echo "修复后的文件行数:"
wc -l {config['deploy_path']}/license_server.py
"""
        
        stdin, stdout, stderr = client.exec_command(fix_script)
        output = stdout.read().decode('utf-8')
        print(f"   修复结果: {output}")
        
        # 步骤5: 添加正确的更新API（使用不同的函数名）
        print("📝 步骤5: 添加正确的更新API...")
        
        # 新的更新API代码（使用不同的函数名避免冲突）
        new_api_code = '''

# ==================== 自动更新API ====================

import hashlib
import mimetypes
from werkzeug.utils import secure_filename

# 更新文件存储目录
UPDATE_DIR = "/opt/license_manager/updates"
CURRENT_VERSION = "2.1.0"

# 确保更新目录存在
os.makedirs(UPDATE_DIR, exist_ok=True)

@app.route('/api/check_update', methods=['GET'])
def api_check_update():
    """检查更新API"""
    try:
        # 获取参数
        license_key = request.args.get('key')
        device_id = request.args.get('device_id')
        current_version = request.args.get('current_version', '1.0.0')
        
        # 获取最新版本信息
        latest_version = get_latest_version_info()
        
        if not latest_version:
            return jsonify({
                "success": True,
                "has_update": False,
                "message": "当前已是最新版本"
            })
        
        # 比较版本
        if is_version_newer(latest_version['version'], current_version):
            return jsonify({
                "success": True,
                "has_update": True,
                "update_info": {
                    "version": latest_version['version'],
                    "file_size": latest_version['file_size'],
                    "file_hash": latest_version['file_hash'],
                    "changelog": latest_version['changelog'],
                    "download_url": f"/api/download_update?version={latest_version['version']}"
                }
            })
        else:
            return jsonify({
                "success": True,
                "has_update": False,
                "message": "当前已是最新版本"
            })
            
    except Exception as e:
        return jsonify({"success": False, "message": f"检查更新失败: {str(e)}"})

@app.route('/api/download_update', methods=['GET'])
def api_download_update():
    """下载更新文件API"""
    try:
        version = request.args.get('version')
        
        if not version:
            return jsonify({"success": False, "message": "缺少版本参数"})
        
        # 查找更新文件
        update_file = find_update_file_path(version)
        if not update_file:
            return jsonify({"success": False, "message": "更新文件不存在"})
        
        # 返回文件
        return send_file(
            update_file,
            as_attachment=True,
            download_name=f"亚马逊蓝图工具_v{version}.exe",
            mimetype='application/octet-stream'
        )
        
    except Exception as e:
        return jsonify({"success": False, "message": f"下载失败: {str(e)}"})

@app.route('/api/upload_update', methods=['POST'])
def api_upload_update():
    """上传更新文件API（管理员使用）"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({"success": False, "message": "没有文件"})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "message": "没有选择文件"})
        
        # 获取版本信息
        version = request.form.get('version')
        changelog = request.form.get('changelog', '')
        
        if not version:
            return jsonify({"success": False, "message": "缺少版本信息"})
        
        # 保存文件
        filename = secure_filename(f"亚马逊蓝图工具_v{version}.exe")
        file_path = os.path.join(UPDATE_DIR, filename)
        file.save(file_path)
        
        # 计算文件哈希
        file_hash = calculate_file_hash_value(file_path)
        file_size = os.path.getsize(file_path)
        
        # 保存版本信息
        save_version_info_to_file(version, file_size, file_hash, changelog, filename)
        
        return jsonify({
            "success": True,
            "message": "更新文件上传成功",
            "version": version,
            "file_size": file_size,
            "file_hash": file_hash
        })
        
    except Exception as e:
        return jsonify({"success": False, "message": f"上传失败: {str(e)}"})

@app.route('/api/list_versions', methods=['GET'])
def api_list_versions():
    """列出所有版本API"""
    try:
        versions = get_all_version_list()
        return jsonify({
            "success": True,
            "versions": versions,
            "current_version": CURRENT_VERSION
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"获取版本列表失败: {str(e)}"})

# ==================== 辅助函数 ====================

def get_latest_version_info():
    """获取最新版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return None
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        if not versions:
            return None
        
        # 返回最新版本
        latest = max(versions, key=lambda x: version_to_tuple_value(x['version']))
        return latest
        
    except Exception as e:
        print(f"获取最新版本失败: {e}")
        return None

def get_all_version_list():
    """获取所有版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return []
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        # 按版本号排序
        versions.sort(key=lambda x: version_to_tuple_value(x['version']), reverse=True)
        return versions
        
    except Exception as e:
        print(f"获取版本列表失败: {e}")
        return []

def save_version_info_to_file(version, file_size, file_hash, changelog, filename):
    """保存版本信息"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        
        # 读取现有版本
        versions = []
        if os.path.exists(version_file):
            with open(version_file, 'r', encoding='utf-8') as f:
                versions = json.load(f)
        
        # 添加新版本
        new_version = {
            "version": version,
            "file_size": file_size,
            "file_hash": file_hash,
            "changelog": changelog,
            "filename": filename,
            "upload_time": datetime.now().isoformat()
        }
        
        # 移除同版本的旧记录
        versions = [v for v in versions if v['version'] != version]
        versions.append(new_version)
        
        # 保存
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(versions, f, ensure_ascii=False, indent=2)
        
    except Exception as e:
        print(f"保存版本信息失败: {e}")

def find_update_file_path(version):
    """查找更新文件"""
    try:
        version_file = os.path.join(UPDATE_DIR, "versions.json")
        if not os.path.exists(version_file):
            return None
        
        with open(version_file, 'r', encoding='utf-8') as f:
            versions = json.load(f)
        
        for v in versions:
            if v['version'] == version:
                file_path = os.path.join(UPDATE_DIR, v['filename'])
                if os.path.exists(file_path):
                    return file_path
        
        return None
        
    except Exception as e:
        print(f"查找更新文件失败: {e}")
        return None

def calculate_file_hash_value(file_path):
    """计算文件SHA256哈希"""
    try:
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    except Exception as e:
        print(f"计算文件哈希失败: {e}")
        return ""

def is_version_newer(latest, current):
    """比较版本号"""
    try:
        return version_to_tuple_value(latest) > version_to_tuple_value(current)
    except:
        return False

def version_to_tuple_value(version):
    """将版本号转换为元组用于比较"""
    try:
        return tuple(map(int, version.split('.')))
    except:
        return (0, 0, 0)

# ==================== 更新API结束 ====================
'''
        
        # 将新API代码写入临时文件
        stdin, stdout, stderr = client.exec_command(f"cat > /tmp/new_update_api.py << 'EOF'\n{new_api_code}EOF")
        stdout.channel.recv_exit_status()
        
        # 将新API添加到license_server.py末尾（在app.run之前）
        stdin, stdout, stderr = client.exec_command(f"""
        # 创建新的license_server.py
        head -n -1 {config['deploy_path']}/license_server.py > {config['deploy_path']}/license_server_new.py
        cat /tmp/new_update_api.py >> {config['deploy_path']}/license_server_new.py
        tail -n 1 {config['deploy_path']}/license_server.py >> {config['deploy_path']}/license_server_new.py
        mv {config['deploy_path']}/license_server_new.py {config['deploy_path']}/license_server.py
        rm /tmp/new_update_api.py
        """)
        stdout.channel.recv_exit_status()
        print("   ✅ 新的更新API代码已添加")
        
        # 步骤6: 验证语法
        print("🔍 步骤6: 验证Python语法...")
        stdin, stdout, stderr = client.exec_command(f"python3 -m py_compile {config['deploy_path']}/license_server.py")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ Python语法检查通过")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ Python语法错误: {error}")
            return False
        
        # 步骤7: 启动服务
        print("🚀 步骤7: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error}")
            return False
        
        # 等待服务启动
        import time
        time.sleep(5)
        
        # 步骤8: 检查服务状态
        print("🔄 步骤8: 检查服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("   ✅ 服务运行正常")
        else:
            print(f"   ❌ 服务状态异常: {status}")
            
            # 查看错误日志
            stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '1 minute ago' --no-pager")
            log_output = stdout.read().decode('utf-8')
            print(f"   错误日志:\n{log_output}")
            return False
        
        # 关闭连接
        client.close()
        
        print("\n🎉 API冲突修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 修复Flask API路由冲突问题")
        print("🔧 方案: 重命名API函数避免冲突")
        print()
        
        print("📋 问题分析:")
        print("• 现有license_server.py中已有check_update函数")
        print("• 新添加的@app.route('/api/check_update')与现有函数冲突")
        print("• Flask不允许重复定义相同的路由端点")
        print()
        
        print("🔧 修复方案:")
        print("• 移除重复的API定义")
        print("• 使用不同的函数名（api_check_update等）")
        print("• 保持路由路径不变")
        print()
        
        confirm = input("确认开始修复？(y/n): ").lower().strip()
        
        if confirm in ['y', 'yes', '是']:
            if fix_api_conflict():
                print("\n✅ API冲突修复成功！")
                print("💡 现在可以正常使用自动更新功能了")
            else:
                print("\n❌ 修复失败，请检查错误信息")
        else:
            print("❌ 用户取消修复")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
