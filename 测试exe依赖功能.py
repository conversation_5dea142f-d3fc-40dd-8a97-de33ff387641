#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的exe依赖检测功能
"""

import sys
import os
import subprocess
import tempfile
import json
from pathlib import Path

def create_test_exe():
    """创建一个简单的测试exe文件（模拟）"""
    print("🔧 创建测试exe文件...")
    
    # 创建dist目录
    dist_dir = Path("dist")
    dist_dir.mkdir(exist_ok=True)
    
    # 创建一个简单的Python脚本作为"exe"
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""模拟的exe程序"""

import sys
import time

def main():
    print("测试exe程序启动成功")
    print("正在运行...")
    time.sleep(1)
    print("程序结束")
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
    
    # 保存为.py文件（模拟exe）
    test_exe_path = dist_dir / "test_program.py"
    with open(test_exe_path, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✅ 测试文件创建: {test_exe_path}")
    return test_exe_path

def test_import_functionality():
    """测试导入功能"""
    print("\n📦 测试导入功能...")
    
    test_modules = [
        'fake_useragent', 'openpyxl', 'pandas', 'requests',
        'selenium', 'bs4', 'lxml', 'cryptography', 'PIL'
    ]
    
    results = {}
    
    for module in test_modules:
        try:
            __import__(module)
            results[module] = {'success': True, 'error': None}
            print(f"✅ {module} - 导入成功")
        except ImportError as e:
            results[module] = {'success': False, 'error': str(e)}
            print(f"❌ {module} - 导入失败: {e}")
        except Exception as e:
            results[module] = {'success': False, 'error': str(e)}
            print(f"⚠️ {module} - 异常: {e}")
    
    total = len(results)
    passed = sum(1 for r in results.values() if r['success'])
    
    print(f"\n📊 导入测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    return results

def test_functionality():
    """测试功能性"""
    print("\n🔧 测试功能性...")
    
    tests = {}
    
    # 测试fake_useragent
    try:
        from fake_useragent import UserAgent
        ua = UserAgent()
        user_agent = ua.random
        tests['fake_useragent'] = {'success': True, 'details': f'生成用户代理: {user_agent[:50]}...'}
        print("✅ fake_useragent - 功能正常")
    except Exception as e:
        tests['fake_useragent'] = {'success': False, 'details': str(e)}
        print(f"❌ fake_useragent - 功能异常: {e}")
    
    # 测试openpyxl
    try:
        from openpyxl import Workbook
        wb = Workbook()
        ws = wb.active
        ws['A1'] = 'Test'
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            wb.save(tmp.name)
            file_size = os.path.getsize(tmp.name)
            os.unlink(tmp.name)
        
        tests['openpyxl'] = {'success': True, 'details': f'Excel文件创建成功，大小: {file_size} bytes'}
        print("✅ openpyxl - 功能正常")
    except Exception as e:
        tests['openpyxl'] = {'success': False, 'details': str(e)}
        print(f"❌ openpyxl - 功能异常: {e}")
    
    # 测试requests
    try:
        import requests
        response = requests.get('https://httpbin.org/get', timeout=5)
        tests['requests'] = {'success': response.status_code == 200, 'details': f'HTTP状态码: {response.status_code}'}
        print(f"✅ requests - 功能正常 (状态码: {response.status_code})")
    except Exception as e:
        tests['requests'] = {'success': False, 'details': str(e)}
        print(f"❌ requests - 功能异常: {e}")
    
    # 测试cryptography
    try:
        from cryptography.fernet import Fernet
        key = Fernet.generate_key()
        f = Fernet(key)
        
        message = b"test message"
        encrypted = f.encrypt(message)
        decrypted = f.decrypt(encrypted)
        
        success = decrypted == message
        tests['cryptography'] = {'success': success, 'details': f'加密解密测试: {"成功" if success else "失败"}'}
        print(f"✅ cryptography - 功能正常")
    except Exception as e:
        tests['cryptography'] = {'success': False, 'details': str(e)}
        print(f"❌ cryptography - 功能异常: {e}")
    
    total = len(tests)
    passed = sum(1 for r in tests.values() if r['success'])
    
    print(f"\n📊 功能测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    return tests

def test_performance():
    """测试性能"""
    print("\n⚡ 测试性能...")
    
    # 模拟文件大小测试
    test_file = Path("dist/test_program.py")
    if test_file.exists():
        file_size = test_file.stat().st_size
        file_size_mb = file_size / (1024 * 1024)
        
        print(f"📁 文件大小: {file_size_mb:.3f} MB")
        
        if file_size_mb < 0.001:  # 小于1KB
            print("✅ 文件大小正常（测试文件）")
            return {'success': True, 'details': f'文件大小: {file_size_mb:.3f} MB'}
        else:
            print("ℹ️ 实际exe文件会更大")
            return {'success': True, 'details': f'测试文件大小: {file_size_mb:.3f} MB'}
    else:
        print("❌ 测试文件不存在")
        return {'success': False, 'details': '测试文件不存在'}

def test_startup():
    """测试启动"""
    print("\n🚀 测试启动...")
    
    test_file = Path("dist/test_program.py")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return {'success': False, 'details': '测试文件不存在'}
    
    try:
        # 运行测试脚本
        result = subprocess.run([
            sys.executable, str(test_file)
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 启动测试成功")
            print(f"输出: {result.stdout.strip()}")
            return {'success': True, 'details': f'启动成功，输出: {result.stdout.strip()}'}
        else:
            print(f"❌ 启动测试失败: {result.stderr}")
            return {'success': False, 'details': result.stderr}
            
    except subprocess.TimeoutExpired:
        print("❌ 启动测试超时")
        return {'success': False, 'details': '启动超时'}
    except Exception as e:
        print(f"❌ 启动测试异常: {e}")
        return {'success': False, 'details': str(e)}

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 开始exe依赖功能综合测试")
    print("=" * 60)
    
    # 创建测试文件
    test_exe = create_test_exe()
    
    # 运行各项测试
    tests = [
        ("导入测试", test_import_functionality),
        ("功能测试", test_functionality),
        ("性能测试", test_performance),
        ("启动测试", test_startup)
    ]
    
    all_results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            all_results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            all_results[test_name] = {'success': False, 'details': str(e)}
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    for test_name, result in all_results.items():
        if isinstance(result, dict) and 'success' in result:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {test_name}: {result.get('details', '无详情')}")
        else:
            # 处理导入测试和功能测试的特殊格式
            if isinstance(result, dict):
                passed = sum(1 for r in result.values() if r.get('success', False))
                total = len(result)
                status = "✅" if passed == total else "⚠️"
                print(f"{status} {test_name}: {passed}/{total} 通过")
    
    print("\n🎯 测试完成！")
    print("💡 提示: 这是模拟测试，实际的exe测试会更加复杂")
    
    # 清理测试文件
    try:
        test_exe.unlink()
        print(f"🗑️ 已清理测试文件: {test_exe}")
    except:
        pass

def main():
    """主函数"""
    try:
        run_comprehensive_test()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
