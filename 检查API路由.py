#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查license_server.py中的API路由
"""

import paramiko
import sys

def check_api_routes():
    """检查API路由"""
    print("🔍 检查license_server.py中的API路由")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 检查1: 查找所有路由定义
        print("🔍 检查1: 查找所有路由定义")
        stdin, stdout, stderr = client.exec_command(
            f"grep -n '@app.route' {config['deploy_path']}/license_server.py"
        )
        output = stdout.read().decode('utf-8')
        
        if output.strip():
            print("   📊 当前API路由列表:")
            routes = []
            for line in output.split('\n'):
                if line.strip():
                    print(f"   📄 {line}")
                    # 提取路由路径
                    import re
                    route_match = re.search(r"'([^']+)'", line)
                    if route_match:
                        routes.append(route_match.group(1))
            
            print(f"\n   📊 总计: {len(routes)} 个路由")
        else:
            print("   ❌ 未找到路由定义")
            return False
        
        # 检查2: 检查更新相关的API
        print("\n🔄 检查2: 检查更新相关的API")
        update_apis = [
            '/api/check_update',
            '/api/download_update', 
            '/api/get_version',
            '/update/',
            '/download/'
        ]
        
        found_update_apis = []
        missing_update_apis = []
        
        for api in update_apis:
            if api in routes:
                found_update_apis.append(api)
                print(f"   ✅ {api}: 存在")
            else:
                missing_update_apis.append(api)
                print(f"   ❌ {api}: 缺失")
        
        print(f"\n   📊 更新API统计: {len(found_update_apis)}/{len(update_apis)} 个存在")
        
        # 检查3: 检查许可证相关的API
        print("\n🔑 检查3: 检查许可证相关的API")
        license_apis = [
            '/license/check',
            '/license/generate',
            '/license/list',
            '/license/info',
            '/license/modify',
            '/license/delete'
        ]
        
        found_license_apis = []
        for api in license_apis:
            if api in routes:
                found_license_apis.append(api)
                print(f"   ✅ {api}: 存在")
            else:
                print(f"   ❌ {api}: 缺失")
        
        print(f"\n   📊 许可证API统计: {len(found_license_apis)}/{len(license_apis)} 个存在")
        
        # 检查4: 对比simple_server.py的路由
        print("\n📄 检查4: 对比simple_server.py的路由")
        stdin, stdout, stderr = client.exec_command(
            f"grep -n '@app.route' {config['deploy_path']}/simple_server.py 2>/dev/null"
        )
        simple_output = stdout.read().decode('utf-8')
        
        if simple_output.strip():
            print("   📊 simple_server.py的路由:")
            simple_routes = []
            for line in simple_output.split('\n'):
                if line.strip():
                    print(f"   📄 {line}")
                    import re
                    route_match = re.search(r"'([^']+)'", line)
                    if route_match:
                        simple_routes.append(route_match.group(1))
            
            # 检查simple_server.py是否有更新API
            simple_has_update = any('/api/check_update' in route for route in simple_routes)
            if simple_has_update:
                print("   ✅ simple_server.py包含更新API")
            else:
                print("   ❌ simple_server.py也没有更新API")
        
        # 检查5: 搜索更新相关的函数
        print("\n🔍 检查5: 搜索更新相关的函数")
        search_terms = ['check_update', 'download_update', 'get_version', 'auto_update']
        
        for term in search_terms:
            stdin, stdout, stderr = client.exec_command(
                f"grep -n '{term}' {config['deploy_path']}/license_server.py"
            )
            output = stdout.read().decode('utf-8')
            
            if output.strip():
                print(f"   ✅ 找到 '{term}' 相关代码")
                for line in output.split('\n')[:3]:  # 只显示前3行
                    if line.strip():
                        print(f"     📄 {line}")
            else:
                print(f"   ❌ 未找到 '{term}' 相关代码")
        
        # 关闭连接
        client.close()
        
        # 分析结果
        print("\n📊 分析结果")
        print("=" * 30)
        
        if missing_update_apis:
            print("❌ 问题确认: license_server.py缺少更新相关的API")
            print("🔍 缺失的API:")
            for api in missing_update_apis:
                print(f"   • {api}")
            
            print("\n💡 解决方案:")
            print("1. 🔄 添加更新API到当前license_server.py")
            print("2. 📁 使用包含更新功能的完整版本")
            print("3. 🔗 创建独立的更新服务")
            
            return False
        else:
            print("✅ license_server.py包含所有必需的更新API")
            return True
        
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 检查为什么/api/check_update返回404")
        print("🔍 方法: 分析license_server.py中的所有API路由")
        print()
        
        if check_api_routes():
            print("\n✅ API路由检查完成，更新功能正常")
        else:
            print("\n❌ 发现问题，需要添加更新相关的API")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
