# 🔄 亚马逊蓝图工具 - 自动更新完整指南

## 📋 目录
1. [系统概述](#系统概述)
2. [服务器端配置](#服务器端配置)
3. [客户端配置](#客户端配置)
4. [使用流程](#使用流程)
5. [API接口说明](#api接口说明)
6. [故障排除](#故障排除)

## 🎯 系统概述

### 架构图
```
客户端exe文件 ←→ 服务器API ←→ 文件存储
     ↓              ↓           ↓
  自动检查更新    版本管理     exe文件库
  下载新版本      API接口     /opt/license_manager/updates/
  安装更新        授权验证     versions.json
```

### 核心功能
- ✅ **自动检查更新**: 客户端启动时自动检查新版本
- ✅ **安全下载**: 通过API下载，支持哈希验证
- ✅ **版本管理**: 服务器端统一管理所有版本
- ✅ **授权集成**: 与现有授权系统集成
- ✅ **备份恢复**: 自动备份旧版本，支持回滚

## 🌐 服务器端配置

### 1. 添加自动更新API

运行以下脚本为服务器添加更新API：

```bash
python "添加自动更新API到license_server.py"
```

这将添加以下API接口：
- `/api/check_update` - 检查更新
- `/api/download_update` - 下载更新文件
- `/api/upload_update` - 上传新版本（管理员）
- `/api/list_versions` - 列出所有版本

### 2. 服务器目录结构

```
/opt/license_manager/
├── license_server.py          # 主服务器文件（已添加更新API）
├── updates/                   # 更新文件目录
│   ├── versions.json         # 版本信息文件
│   ├── 亚马逊蓝图工具_v2.1.0.exe
│   ├── 亚马逊蓝图工具_v2.2.0.exe
│   └── ...
└── logs/                     # 日志目录
```

### 3. 版本信息格式

`versions.json` 文件格式：
```json
[
  {
    "version": "2.2.0",
    "file_size": 52428800,
    "file_hash": "sha256_hash_here",
    "changelog": "修复了若干bug，增加了新功能",
    "filename": "亚马逊蓝图工具_v2.2.0.exe",
    "upload_time": "2024-01-15T10:30:00"
  }
]
```

## 💻 客户端配置

### 1. 自动更新模块

客户端包含以下文件：
- `auto_updater.py` - 自动更新核心模块
- `update_config.py` - 更新配置文件
- `license_client.py` - 主程序（已集成自动更新）

### 2. 配置服务器地址

在 `update_config.py` 中配置：
```python
SERVER_CONFIG = {
    "license_server_url": "http://198.23.135.176:5000/",
    "current_version": "2.1.0",
    "check_interval": 3600,  # 1小时检查一次
    "auto_check_enabled": True
}
```

### 3. 客户端更新流程

1. **启动检查**: 程序启动1秒后自动检查更新
2. **版本比较**: 与服务器最新版本比较
3. **用户确认**: 发现新版本时弹出更新对话框
4. **下载安装**: 用户确认后下载并安装更新
5. **重启程序**: 更新完成后重启到新版本

## 🔄 使用流程

### 管理员发布新版本

1. **构建新版本exe**
   ```bash
   python build_with_spec.py
   ```

2. **启动管理工具**
   ```bash
   启动exe管理工具.bat
   ```

3. **上传新版本**
   - 在管理界面中选择exe文件
   - 输入版本号（如：2.2.0）
   - 填写更新说明
   - 点击"上传版本"

4. **验证上传**
   - 点击"刷新列表"查看新版本
   - 点击"测试更新API"验证功能

### 用户自动更新

1. **自动检查**: 用户启动程序时自动检查
2. **更新提示**: 发现新版本时显示更新对话框
3. **用户选择**: 
   - "立即更新" - 下载并安装新版本
   - "稍后提醒" - 跳过本次更新
4. **自动安装**: 下载完成后自动替换exe文件并重启

## 📡 API接口说明

### 1. 检查更新 API

**请求**: `GET /api/check_update`

**参数**:
- `key`: 授权码（可选）
- `device_id`: 设备ID（可选）
- `current_version`: 当前版本号

**响应**:
```json
{
  "success": true,
  "has_update": true,
  "update_info": {
    "version": "2.2.0",
    "file_size": 52428800,
    "file_hash": "sha256_hash",
    "changelog": "更新内容说明",
    "download_url": "/api/download_update?version=2.2.0"
  }
}
```

### 2. 下载更新 API

**请求**: `GET /api/download_update`

**参数**:
- `version`: 要下载的版本号
- `key`: 授权码（可选）
- `device_id`: 设备ID（可选）

**响应**: 直接返回exe文件流

### 3. 上传更新 API

**请求**: `POST /api/upload_update`

**参数**:
- `file`: exe文件（multipart/form-data）
- `version`: 版本号
- `changelog`: 更新说明

**响应**:
```json
{
  "success": true,
  "message": "更新文件上传成功",
  "version": "2.2.0",
  "file_size": 52428800,
  "file_hash": "sha256_hash"
}
```

### 4. 版本列表 API

**请求**: `GET /api/list_versions`

**响应**:
```json
{
  "success": true,
  "versions": [
    {
      "version": "2.2.0",
      "file_size": 52428800,
      "file_hash": "sha256_hash",
      "changelog": "更新说明",
      "filename": "亚马逊蓝图工具_v2.2.0.exe",
      "upload_time": "2024-01-15T10:30:00"
    }
  ],
  "current_version": "2.1.0"
}
```

## 🔧 故障排除

### 常见问题

#### 1. 客户端无法检查更新
**症状**: 程序启动时不显示更新检查
**解决方案**:
- 检查网络连接
- 确认服务器地址配置正确
- 查看 `update_config.py` 中的 `auto_check_enabled` 设置

#### 2. 服务器API返回404错误
**症状**: 客户端提示"检查更新失败"
**解决方案**:
```bash
# 检查服务器状态
systemctl status license-manager

# 查看服务器日志
journalctl -u license-manager -f

# 重启服务器
systemctl restart license-manager
```

#### 3. 文件上传失败
**症状**: 管理工具上传时报错
**解决方案**:
- 检查文件大小（不超过100MB）
- 确认服务器磁盘空间充足
- 检查文件权限

#### 4. 下载的exe文件损坏
**症状**: 更新后程序无法启动
**解决方案**:
- 启用哈希验证功能
- 检查网络稳定性
- 使用备份文件恢复

### 调试工具

#### 1. 测试更新API
```bash
# 测试检查更新
curl "http://198.23.135.176:5000/api/check_update?current_version=1.0.0"

# 测试版本列表
curl "http://198.23.135.176:5000/api/list_versions"
```

#### 2. 查看服务器日志
```bash
# 实时查看日志
tail -f /var/log/license-manager.log

# 查看systemd日志
journalctl -u license-manager --since "1 hour ago"
```

#### 3. 检查文件权限
```bash
# 检查更新目录权限
ls -la /opt/license_manager/updates/

# 修复权限
chown -R root:root /opt/license_manager/
chmod -R 755 /opt/license_manager/
```

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 错误信息截图
2. 客户端版本号
3. 服务器日志
4. 网络环境信息

---

## 🎉 总结

通过本指南，您已经完成了：
- ✅ 服务器端自动更新API配置
- ✅ 客户端自动更新功能集成
- ✅ 版本管理工具部署
- ✅ 完整的更新流程建立

现在您的亚马逊蓝图工具具备了完整的自动更新能力！🚀
