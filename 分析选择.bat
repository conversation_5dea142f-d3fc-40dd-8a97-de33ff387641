@echo off
chcp 65001 >nul
title 🔍 分析服务器选择

echo.
echo ==========================================
echo 🔍 分析license_server.py选择方案
echo ==========================================
echo.
echo 🤔 当前问题:
echo • ❌ license_manager.py连接不上
echo • 🔍 端口已修改为5000但仍然失败
echo • 📊 当前运行simple_server.py (缺少许可证API)
echo • 📊 服务器上有原始license_server.py
echo • 📊 本地也有license_server.py
echo.
echo 🎯 分析内容:
echo • 📄 检查服务器上的文件
echo • 🔧 查看当前运行的服务
echo • 📋 分析原始license_server.py的API
echo • 📋 分析本地license_server.py的功能
echo • 💡 提供最佳方案建议
echo.

REM 检查paramiko模块
python -c "import paramiko" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少paramiko模块，正在安装...
    python -m pip install paramiko
    if errorlevel 1 (
        echo ❌ paramiko安装失败
        pause
        exit /b 1
    )
    echo ✅ paramiko安装完成
    echo.
)

echo 🔍 开始分析服务器情况...
echo.

REM 运行分析脚本
python "分析服务器选择.py"

echo.
echo 👋 分析完成
pause
