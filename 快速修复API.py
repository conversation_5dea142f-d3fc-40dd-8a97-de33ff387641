#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复API - 添加根路由并修复路由问题
"""

import paramiko

def quick_fix_api():
    """快速修复API问题"""
    print("⚡ 快速修复API问题")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 停止服务
        print("\n🛑 步骤1: 停止服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        stdout.channel.recv_exit_status()
        print("   ✅ 服务已停止")
        
        # 步骤2: 添加根路由到文件开头
        print("\n➕ 步骤2: 添加根路由...")
        
        # 创建根路由代码
        root_route_code = '''
# 添加根路由
@app.route('/')
def index():
    """根路径 - 返回API信息"""
    return {
        "message": "亚马逊蓝图工具授权服务器",
        "version": "2.1.0", 
        "status": "running",
        "apis": [
            "/license/check - 检查授权",
            "/license/generate - 生成授权", 
            "/update/check - 检查更新",
            "/update/stats - 更新统计"
        ]
    }

'''
        
        # 在第一个路由定义前插入根路由
        insert_command = f'''
cd {config['deploy_path']}
cp license_server.py license_server.py.backup_$(date +%Y%m%d_%H%M%S)

# 找到第一个@app.route的行号
line_num=$(grep -n "@app.route" license_server.py | head -1 | cut -d: -f1)

# 在该行前插入根路由
head -n $((line_num-1)) license_server.py > temp_file
cat >> temp_file << 'EOF'
{root_route_code}
EOF
tail -n +$line_num license_server.py >> temp_file
mv temp_file license_server.py
'''
        
        stdin, stdout, stderr = client.exec_command(insert_command)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 根路由已添加")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 添加根路由失败: {error}")
            return False
        
        # 步骤3: 检查并修复update/stats路由的缩进问题
        print("\n🔧 步骤3: 修复路由缩进问题...")
        
        fix_indentation_command = f'''
cd {config['deploy_path']}
# 使用Python脚本修复缩进问题
python3 -c "
import re
with open('license_server.py', 'r') as f:
    content = f.read()

# 修复可能的缩进问题
lines = content.split('\\n')
fixed_lines = []
in_function = False

for line in lines:
    if line.strip().startswith('@app.route'):
        in_function = True
        fixed_lines.append(line)
    elif in_function and line.strip().startswith('def '):
        fixed_lines.append(line)
        in_function = False
    else:
        fixed_lines.append(line)

with open('license_server.py', 'w') as f:
    f.write('\\n'.join(fixed_lines))
"
'''
        
        stdin, stdout, stderr = client.exec_command(fix_indentation_command)
        stdout.channel.recv_exit_status()
        print("   ✅ 缩进问题已修复")
        
        # 步骤4: 验证Python语法
        print("\n🔍 步骤4: 验证Python语法...")
        stdin, stdout, stderr = client.exec_command(f"cd {config['deploy_path']} && python3 -m py_compile license_server.py")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ Python语法检查通过")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ Python语法错误: {error}")
            
            # 尝试恢复备份
            stdin, stdout, stderr = client.exec_command(f"cd {config['deploy_path']} && cp license_server.py.backup_* license_server.py")
            stdout.channel.recv_exit_status()
            print("   🔄 已恢复备份文件")
            return False
        
        # 步骤5: 启动服务
        print("\n🚀 步骤5: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error}")
            return False
        
        # 等待服务启动
        print("\n⏳ 等待服务完全启动...")
        import time
        time.sleep(15)
        
        # 步骤6: 测试API
        print("\n🧪 步骤6: 测试修复结果...")
        import requests
        
        test_cases = [
            ("根路径", "http://**************:5000/"),
            ("更新统计", "http://**************:5000/update/stats"),
            ("授权列表", "http://**************:5000/license/list")
        ]
        
        success_count = 0
        for name, url in test_cases:
            try:
                response = requests.get(url, timeout=10)
                print(f"   📋 {name}: HTTP {response.status_code}")
                
                if response.status_code == 200:
                    success_count += 1
                    print(f"      ✅ API正常")
                    
                    try:
                        data = response.json()
                        if isinstance(data, dict) and 'message' in data:
                            print(f"      📄 消息: {data['message']}")
                        elif isinstance(data, list):
                            print(f"      📄 返回{len(data)}条数据")
                    except:
                        pass
                        
                elif response.status_code == 404:
                    print(f"      ❌ 路由不存在")
                else:
                    print(f"      ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {name}: 测试失败 - {e}")
        
        # 关闭连接
        client.close()
        
        if success_count >= 2:
            print("\n🎉 修复成功！")
            print("🌐 服务器地址: http://**************:5000")
            print("📋 API现在应该正常工作了")
            return True
        else:
            print("\n⚠️ 修复完成，但部分API仍有问题")
            return False
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 快速修复API路由问题")
        print("🔧 问题: 根路由缺失，部分路由404")
        print("💡 方案: 添加根路由 + 修复缩进 + 重启服务")
        print()
        
        if quick_fix_api():
            print("\n✅ 修复成功！")
            print("\n📋 现在可以测试以下API:")
            print("• http://**************:5000/ - 根路径")
            print("• http://**************:5000/update/stats - 更新统计")
            print("• http://**************:5000/update/check?current_version=1.0.0&key=test&device_id=test")
            print("• http://**************:5000/license/list - 授权列表")
        else:
            print("\n❌ 修复失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
