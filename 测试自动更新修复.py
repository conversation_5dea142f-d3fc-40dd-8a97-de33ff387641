#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动更新功能修复效果
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_update_config():
    """测试更新配置"""
    print("🔧 测试更新配置")
    print("=" * 50)
    
    try:
        from update_config import get_config, CURRENT_VERSION
        
        config = get_config()
        print("✅ 成功导入update_config")
        print(f"📋 授权服务器: {config['license_server_url']}")
        print(f"📋 更新服务器: {config['update_server_url']}")
        print(f"📋 当前版本: {config['current_version']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入update_config失败: {e}")
        return False

def test_auto_updater():
    """测试自动更新器"""
    print("\n🔄 测试自动更新器")
    print("=" * 50)
    
    try:
        from auto_updater import check_and_update, AutoUpdater
        
        print("✅ 成功导入auto_updater")
        
        # 测试配置获取
        from update_config import get_config
        config = get_config()
        
        print(f"📋 配置中的服务器地址: {config['license_server_url']}")
        
        # 创建更新器实例测试
        updater = AutoUpdater(
            current_version="2.1.0",
            license_server_url=config["license_server_url"],
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        print("✅ 成功创建AutoUpdater实例")
        print(f"📋 更新器服务器地址: {updater.license_server_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试auto_updater失败: {e}")
        return False

def test_update_check():
    """测试更新检查功能"""
    print("\n🧪 测试更新检查功能")
    print("=" * 50)
    
    try:
        from auto_updater import AutoUpdater
        from update_config import get_config
        
        config = get_config()
        
        # 使用管理员权限测试
        updater = AutoUpdater(
            current_version="2.0.0",  # 使用较低版本触发更新检查
            license_server_url=config["license_server_url"],
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        print("🔍 正在检查更新...")
        update_info = updater.check_for_updates()
        
        if update_info:
            print("✅ 检查更新成功")
            print(f"📋 更新信息: {update_info}")
        else:
            print("ℹ️ 暂无可用更新或检查失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 更新检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_license_client_integration():
    """测试license_client.py集成"""
    print("\n🔗 测试license_client.py集成")
    print("=" * 50)
    
    try:
        # 检查license_client.py中的服务器配置
        with open("license_client.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 查找服务器URL配置
        for line_num, line in enumerate(content.split('\n'), 1):
            if 'self.server_url = ' in line and 'http' in line:
                print(f"📋 第{line_num}行: {line.strip()}")
                
                # 检查是否使用正确的端口
                if ':5000' in line:
                    print("✅ 客户端使用正确的端口5000")
                elif ':44285' in line:
                    print("❌ 客户端仍使用错误的端口44285")
                    return False
                break
        
        # 检查自动更新导入
        if 'from auto_updater import check_and_update' in content:
            print("✅ 客户端已导入自动更新模块")
        else:
            print("⚠️ 客户端未导入自动更新模块")
            
        # 检查更新检查调用
        if 'check_and_update(' in content:
            print("✅ 客户端调用自动更新功能")
        else:
            print("⚠️ 客户端未调用自动更新功能")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查license_client.py失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 测试自动更新功能修复效果")
    print("🔧 修复内容:")
    print("  1. license_client.py端口从44285改为5000")
    print("  2. auto_updater.py使用正确的服务器配置")
    print("  3. 验证配置文件和模块导入")
    print()
    
    tests = [
        ("配置文件测试", test_update_config),
        ("自动更新器测试", test_auto_updater),
        ("更新检查测试", test_update_check),
        ("客户端集成测试", test_license_client_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
        print()
    
    # 总结
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！自动更新功能修复成功！")
        print()
        print("✅ 现在license_client.py应该可以正常:")
        print("  - 连接到正确的服务器端口5000")
        print("  - 进行激活码验证")
        print("  - 执行自动更新检查")
        print("  - 下载和安装更新")
        
        print("\n🚀 建议下一步:")
        print("  1. 启动license_client.py测试连接")
        print("  2. 输入有效的激活码进行验证")
        print("  3. 观察自动更新功能是否正常工作")
        
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
