#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版更新器 - 支持断点续传和网络中断恢复
"""

import requests
import os
import tempfile
import hashlib
import time
import json

class EnhancedUpdater:
    """增强版更新器"""
    
    def __init__(self, license_key="ADMIN_BYPASS", device_id="ADMIN-DEVICE-001"):
        self.license_key = license_key
        self.device_id = device_id
        self.server_url = "http://198.23.135.176:5000"
    
    def download_with_resume(self, version="2.1.1", progress_callback=None):
        """支持断点续传的下载"""
        print(f"📥 开始下载版本 {version}")
        
        url = f"{self.server_url}/update/download"
        params = {
            'key': self.license_key,
            'device_id': self.device_id,
            'version': version
        }
        
        # 创建临时文件
        temp_file = os.path.join(tempfile.gettempdir(), f"update_{version}.exe")
        
        # 检查是否有未完成的下载
        downloaded_size = 0
        if os.path.exists(temp_file):
            downloaded_size = os.path.getsize(temp_file)
            print(f"🔄 发现未完成下载，已下载 {downloaded_size} 字节")
        
        max_retries = 10  # 增加重试次数
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 设置断点续传头
                headers = {}
                if downloaded_size > 0:
                    headers['Range'] = f'bytes={downloaded_size}-'
                    print(f"📡 使用断点续传，从 {downloaded_size} 字节开始")
                
                # 发送请求
                response = requests.get(
                    url, 
                    params=params, 
                    headers=headers,
                    stream=True,
                    timeout=(60, 3600)  # 1分钟连接，1小时读取
                )
                
                # 检查响应
                if response.status_code not in [200, 206]:
                    raise Exception(f"HTTP错误: {response.status_code}")
                
                # 获取文件总大小
                if response.status_code == 200:
                    file_size = int(response.headers.get('Content-Length', 0))
                    downloaded_size = 0  # 重新开始
                    mode = 'wb'
                else:  # 206 Partial Content
                    content_range = response.headers.get('Content-Range', '')
                    if content_range:
                        file_size = int(content_range.split('/')[-1])
                    else:
                        file_size = downloaded_size + int(response.headers.get('Content-Length', 0))
                    mode = 'ab'
                
                print(f"📦 文件总大小: {file_size} 字节")
                print(f"📊 已下载: {downloaded_size} 字节 ({downloaded_size/file_size*100:.1f}%)")
                
                # 下载文件
                with open(temp_file, mode) as f:
                    chunk_size = 64 * 1024  # 64KB chunks
                    
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # 更新进度
                            if progress_callback and file_size > 0:
                                progress = (downloaded_size / file_size) * 100
                                progress_callback(progress)
                            
                            # 每10MB显示一次进度
                            if downloaded_size % (10 * 1024 * 1024) == 0:
                                print(f"📊 已下载: {downloaded_size/1024/1024:.1f}MB / {file_size/1024/1024:.1f}MB")
                
                # 检查下载是否完成
                if downloaded_size >= file_size:
                    print(f"✅ 下载完成: {temp_file}")
                    return temp_file
                else:
                    print(f"⚠️ 下载不完整，继续重试...")
                    retry_count += 1
                    time.sleep(5)
                    
            except (requests.exceptions.ChunkedEncodingError,
                    requests.exceptions.ConnectionError,
                    ConnectionError) as e:
                retry_count += 1
                wait_time = min(retry_count * 10, 60)  # 最多等待60秒
                print(f"❌ 下载中断 (重试 {retry_count}/{max_retries}): {e}")
                print(f"⏱️ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                
            except Exception as e:
                print(f"❌ 下载失败: {e}")
                return None
        
        print(f"❌ 下载失败，已重试 {max_retries} 次")
        return None

def test_enhanced_download():
    """测试增强版下载"""
    print("🧪 测试增强版下载器")
    
    def progress_callback(progress):
        print(f"\r📊 下载进度: {progress:.1f}%", end="", flush=True)
    
    updater = EnhancedUpdater()
    result = updater.download_with_resume("2.1.1", progress_callback)
    
    if result:
        print(f"\n✅ 下载成功: {result}")
        print(f"📦 文件大小: {os.path.getsize(result)} 字节")
    else:
        print("\n❌ 下载失败")

if __name__ == "__main__":
    test_enhanced_download()
