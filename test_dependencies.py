#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖测试脚本 - 验证所有必需的库是否正确安装
"""

import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_import(package_name, import_name=None):
    """测试单个包的导入"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = __import__(import_name)
        version = getattr(module, '__version__', 'Unknown')
        logger.info(f"✓ {package_name} ({version}) - 导入成功")
        return True
    except ImportError as e:
        logger.error(f"✗ {package_name} - 导入失败: {str(e)}")
        return False

def test_excel_functionality():
    """测试Excel功能"""
    logger.info("测试Excel读写功能...")
    
    try:
        import pandas as pd
        import openpyxl
        
        # 创建测试数据
        data = {
            'Product': ['Test Product 1', 'Test Product 2'],
            'Price': [19.99, 29.99],
            'Available': [True, False]
        }
        df = pd.DataFrame(data)
        
        # 测试写入
        test_file = 'test_excel.xlsx'
        df.to_excel(test_file, index=False, engine='openpyxl')
        
        # 测试读取
        df_read = pd.read_excel(test_file, engine='openpyxl')
        
        # 验证
        if len(df_read) == len(df) and list(df_read.columns) == list(df.columns):
            logger.info("✓ Excel读写功能正常")
            success = True
        else:
            logger.error("✗ Excel数据不一致")
            success = False
        
        # 清理
        import os
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return success
        
    except Exception as e:
        logger.error(f"✗ Excel功能测试失败: {str(e)}")
        return False

def test_selenium_basic():
    """测试Selenium基本功能"""
    logger.info("测试Selenium基本功能...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        logger.info("✓ Selenium相关模块导入成功")
        return True
        
    except Exception as e:
        logger.error(f"✗ Selenium测试失败: {str(e)}")
        return False

def test_cryptography():
    """测试加密功能"""
    logger.info("测试加密功能...")
    
    try:
        from cryptography.fernet import Fernet
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
        
        # 简单加密测试
        key = Fernet.generate_key()
        f = Fernet(key)
        
        test_data = b"Hello, World!"
        encrypted = f.encrypt(test_data)
        decrypted = f.decrypt(encrypted)
        
        if decrypted == test_data:
            logger.info("✓ 加密功能正常")
            return True
        else:
            logger.error("✗ 加密解密不一致")
            return False
            
    except Exception as e:
        logger.error(f"✗ 加密功能测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("开始依赖测试...")
    
    # 定义要测试的包
    packages = [
        ('selenium', 'selenium'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('xlsxwriter', 'xlsxwriter'),
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('webdriver_manager', 'webdriver_manager'),
        ('psutil', 'psutil'),
        ('cryptography', 'cryptography'),
        ('fake_useragent', 'fake_useragent'),
        ('amazoncaptcha', 'amazoncaptcha'),
        ('lxml', 'lxml'),
        ('pillow', 'PIL'),
    ]
    
    # 测试基本导入
    failed_imports = []
    for package_name, import_name in packages:
        if not test_import(package_name, import_name):
            failed_imports.append(package_name)
    
    # 功能测试
    logger.info("\n开始功能测试...")
    
    excel_ok = test_excel_functionality()
    selenium_ok = test_selenium_basic()
    crypto_ok = test_cryptography()
    
    # 总结
    logger.info("\n=== 测试总结 ===")
    
    if failed_imports:
        logger.error(f"导入失败的包: {', '.join(failed_imports)}")
    else:
        logger.info("所有包导入成功")
    
    logger.info(f"Excel功能: {'✓' if excel_ok else '✗'}")
    logger.info(f"Selenium功能: {'✓' if selenium_ok else '✗'}")
    logger.info(f"加密功能: {'✓' if crypto_ok else '✗'}")
    
    # 判断整体结果
    overall_success = (
        len(failed_imports) == 0 and 
        excel_ok and 
        selenium_ok and 
        crypto_ok
    )
    
    if overall_success:
        logger.info("\n🎉 所有测试通过！系统已准备好进行构建。")
        return True
    else:
        logger.error("\n❌ 部分测试失败，请检查依赖安装。")
        logger.info("建议运行: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
