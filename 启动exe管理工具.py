#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动exe管理工具
"""

import sys
import os
import subprocess

def main():
    """启动exe管理工具"""
    try:
        print("🚀 启动亚马逊蓝图工具 - exe文件管理")
        print("=" * 50)
        
        # 检查文件是否存在
        exe_manager_file = "exe文件管理工具.py"
        if not os.path.exists(exe_manager_file):
            print(f"❌ 找不到文件: {exe_manager_file}")
            input("按回车键退出...")
            return
        
        print("✅ 找到管理工具文件")
        print("🔧 正在启动界面...")
        
        # 启动管理工具
        subprocess.run([sys.executable, exe_manager_file])
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
