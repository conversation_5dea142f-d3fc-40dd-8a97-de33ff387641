#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新系统测试脚本
用于测试自动更新功能是否正常工作
"""

import os
import sys
import json
import requests
import time
from datetime import datetime

def test_license_server_connection(server_url):
    """测试授权服务器连接"""
    print(f"🔗 测试服务器连接: {server_url}")
    
    try:
        response = requests.get(server_url, timeout=10)
        print(f"✅ 服务器响应: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_update_check_api(server_url, license_key, device_id, current_version="2.0.0"):
    """测试更新检查API"""
    print(f"\n🔍 测试更新检查API")
    print(f"   服务器: {server_url}")
    print(f"   授权码: {license_key[:8]}..." if license_key else "   授权码: 未提供")
    print(f"   设备ID: {device_id[:8]}..." if device_id else "   设备ID: 未提供")
    print(f"   当前版本: {current_version}")
    
    try:
        url = f"{server_url}update/check"
        params = {
            'key': license_key,
            'device_id': device_id,
            'current_version': current_version
        }
        
        response = requests.get(url, params=params, timeout=10)
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应成功: {result.get('success', False)}")
            
            if result.get('success'):
                print(f"   有更新: {result.get('has_update', False)}")
                print(f"   当前版本: {result.get('current_version')}")
                print(f"   最新版本: {result.get('latest_version')}")
                
                if result.get('has_update') and result.get('update_info'):
                    update_info = result['update_info']
                    print(f"   文件大小: {update_info.get('file_size', 0) / (1024*1024):.1f}MB")
                    print(f"   发布日期: {update_info.get('release_date')}")
                    
                return result
            else:
                print(f"   错误信息: {result.get('message')}")
                return None
        else:
            print(f"   HTTP错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return None

def test_update_download_api(server_url, license_key, device_id, version=None):
    """测试更新下载API"""
    print(f"\n📥 测试更新下载API")
    
    try:
        url = f"{server_url}update/download"
        params = {
            'key': license_key,
            'device_id': device_id
        }
        
        if version:
            params['version'] = version
            print(f"   指定版本: {version}")
        else:
            print(f"   下载最新版本")
        
        # 只测试请求头，不实际下载
        response = requests.head(url, params=params, timeout=10)
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            content_length = response.headers.get('Content-Length')
            if content_length:
                size_mb = int(content_length) / (1024 * 1024)
                print(f"   文件大小: {size_mb:.1f}MB")
            
            content_type = response.headers.get('Content-Type')
            print(f"   文件类型: {content_type}")
            
            print("✅ 下载API测试成功")
            return True
        else:
            print(f"❌ 下载API测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 下载API测试失败: {e}")
        return False

def test_static_update_server(update_server_url):
    """测试静态更新服务器"""
    print(f"\n📄 测试静态更新服务器: {update_server_url}")
    
    try:
        version_url = f"{update_server_url}version_info.json"
        response = requests.get(version_url, timeout=10)
        
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ 版本信息获取成功")
            print(f"   版本: {version_info.get('version')}")
            print(f"   文件大小: {version_info.get('file_size', 0) / (1024*1024):.1f}MB")
            print(f"   下载URL: {version_info.get('download_url')}")
            return version_info
        else:
            print(f"❌ 版本信息获取失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 静态服务器测试失败: {e}")
        return None

def test_auto_updater():
    """测试自动更新器"""
    print(f"\n🤖 测试自动更新器")
    
    try:
        from auto_updater import AutoUpdater
        
        # 测试不同的初始化方式
        print("   测试默认初始化...")
        updater1 = AutoUpdater()
        print(f"   当前版本: {updater1.current_version}")
        print(f"   服务器URL: {updater1.license_server_url}")
        
        print("   测试带参数初始化...")
        updater2 = AutoUpdater(
            current_version="2.0.0",
            license_server_url="https://test-server.com/",
            license_key="test_key",
            device_id="test_device"
        )
        print(f"   当前版本: {updater2.current_version}")
        print(f"   服务器URL: {updater2.license_server_url}")
        
        print("✅ 自动更新器测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 自动更新器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 亚马逊蓝图工具 - 更新系统测试")
    print("=" * 60)
    
    # 配置测试参数
    SERVER_URL = "http://localhost:5000/"  # 本地测试服务器
    UPDATE_SERVER_URL = "http://localhost:8000/updates/"  # 静态文件服务器
    
    # 测试用的授权信息（请替换为真实的）
    TEST_LICENSE_KEY = "test_license_key_12345"
    TEST_DEVICE_ID = "test_device_id_67890"
    
    print(f"📋 测试配置:")
    print(f"   授权服务器: {SERVER_URL}")
    print(f"   静态服务器: {UPDATE_SERVER_URL}")
    print(f"   测试授权码: {TEST_LICENSE_KEY}")
    print(f"   测试设备ID: {TEST_DEVICE_ID}")
    
    # 1. 测试自动更新器
    test_auto_updater()
    
    # 2. 测试服务器连接
    server_online = test_license_server_connection(SERVER_URL)
    
    if server_online:
        # 3. 测试更新检查API
        update_result = test_update_check_api(
            SERVER_URL, 
            TEST_LICENSE_KEY, 
            TEST_DEVICE_ID, 
            "2.0.0"
        )
        
        # 4. 测试下载API
        if update_result and update_result.get('has_update'):
            test_update_download_api(
                SERVER_URL, 
                TEST_LICENSE_KEY, 
                TEST_DEVICE_ID
            )
    else:
        print("\n⚠️  授权服务器离线，跳过API测试")
    
    # 5. 测试静态更新服务器
    test_static_update_server(UPDATE_SERVER_URL)
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
    
    print("\n📝 测试建议:")
    print("1. 确保license_server.py正在运行")
    print("2. 确保更新文件已正确部署")
    print("3. 使用真实的授权码和设备ID进行测试")
    print("4. 检查服务器日志以获取详细信息")

if __name__ == "__main__":
    main()
