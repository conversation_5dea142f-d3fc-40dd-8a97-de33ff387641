#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新功能
"""

import requests
import sys

def test_update_api():
    """测试更新API"""
    print("🧪 测试更新功能...")
    
    try:
        # 测试更新检查
        url = "http://198.23.135.176:5000/update/check"
        params = {
            'key': 'ADMIN_BYPASS',
            'device_id': 'ADMIN-DEVICE-001', 
            'current_version': '2.1.0'
        }
        
        print("🔍 检查更新...")
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API响应正常: {data}")
            
            if data.get('success') and data.get('has_update'):
                print(f"📦 发现更新版本: {data.get('version')}")
                return True
            else:
                print("📦 当前已是最新版本")
                return True
        else:
            print(f"❌ API响应错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_update_api()
    sys.exit(0 if success else 1)
