#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复下载超时问题
"""

import os
import re

def fix_download_timeout():
    """修复下载超时问题"""
    print("🔧 修复下载超时问题")
    print("=" * 50)
    
    if not os.path.exists("auto_updater.py"):
        print("❌ 未找到auto_updater.py文件")
        return False
    
    try:
        # 读取文件
        with open("auto_updater.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ 读取auto_updater.py文件成功")
        
        # 修复1: 增加超时时间
        old_timeout = 'timeout=(10, 300)'
        new_timeout = 'timeout=(15, 900)'  # 连接15秒，读取15分钟
        
        if old_timeout in content:
            content = content.replace(old_timeout, new_timeout)
            print("✅ 增加下载超时时间到15分钟")
        
        # 修复2: 增加重试次数
        old_retries = 'max_retries = 3'
        new_retries = 'max_retries = 5'
        
        if old_retries in content:
            content = content.replace(old_retries, new_retries)
            print("✅ 增加重试次数到5次")
        
        # 修复3: 增加块大小
        old_chunk = 'chunk_size = 32768'
        new_chunk = 'chunk_size = 65536'  # 64KB
        
        if old_chunk in content:
            content = content.replace(old_chunk, new_chunk)
            print("✅ 增加下载块大小到64KB")
        
        # 修复4: 添加更好的错误处理
        error_handling_code = '''                except requests.exceptions.ConnectionError as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise Exception(f"连接错误，已重试{max_retries}次: {str(e)}")
                    wait_time = min(2 ** retry_count, 30)
                    print(f"连接错误，等待{wait_time}秒后重试 ({retry_count}/{max_retries})...")
                    time.sleep(wait_time)
                except requests.exceptions.ChunkedEncodingError as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise Exception(f"数据传输错误，已重试{max_retries}次: {str(e)}")
                    wait_time = min(2 ** retry_count, 30)
                    print(f"数据传输错误，等待{wait_time}秒后重试 ({retry_count}/{max_retries})...")
                    time.sleep(wait_time)'''
        
        # 查找现有的异常处理位置
        timeout_except_pattern = r'(except requests\.exceptions\.Timeout as e:.*?time\.sleep\(2\))'
        
        if re.search(timeout_except_pattern, content, re.DOTALL):
            # 在timeout异常处理后添加其他异常处理
            content = re.sub(
                r'(except requests\.exceptions\.Timeout as e:.*?time\.sleep\(2\))',
                r'\1' + error_handling_code,
                content,
                flags=re.DOTALL
            )
            print("✅ 添加连接错误和数据传输错误处理")
        
        # 写回文件
        with open("auto_updater.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_download_test():
    """创建下载测试脚本"""
    print("\n🧪 创建下载测试脚本")
    print("=" * 30)
    
    test_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载功能
"""

import requests
import time
import os
from pathlib import Path

def test_download_connection():
    """测试下载连接"""
    print("🔍 测试服务器连接...")
    
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': '2.1.1'
    }
    
    try:
        # 测试HEAD请求，获取文件信息
        response = requests.head(url, params=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if 'content-length' in response.headers:
            size = int(response.headers['content-length'])
            print(f"文件大小: {size // 1024 // 1024}MB")
        
        if 'accept-ranges' in response.headers:
            print(f"支持断点续传: {response.headers['accept-ranges']}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

def test_small_download():
    """测试小文件下载"""
    print("\\n📥 测试小文件下载...")
    
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': '2.1.1'
    }
    
    try:
        # 只下载前1MB测试
        headers = {'Range': 'bytes=0-1048575'}  # 1MB
        
        response = requests.get(
            url, 
            params=params, 
            headers=headers,
            stream=True, 
            timeout=(10, 60)
        )
        
        if response.status_code in [200, 206]:
            downloaded = 0
            start_time = time.time()
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    downloaded += len(chunk)
                    if downloaded >= 1048576:  # 1MB
                        break
            
            elapsed = time.time() - start_time
            speed = downloaded / elapsed / 1024 / 1024  # MB/s
            
            print(f"下载速度: {speed:.2f}MB/s")
            print(f"下载 {downloaded} 字节，用时 {elapsed:.2f} 秒")
            
            return speed > 0.1  # 至少100KB/s
        else:
            print(f"下载失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"下载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 下载功能测试")
    print("=" * 40)
    
    # 测试连接
    connection_ok = test_download_connection()
    
    if connection_ok:
        # 测试下载速度
        download_ok = test_small_download()
        
        print("\\n" + "=" * 40)
        print("📊 测试结果:")
        print(f"   - 服务器连接: {'✅' if connection_ok else '❌'}")
        print(f"   - 下载速度: {'✅' if download_ok else '❌'}")
        
        if connection_ok and download_ok:
            print("\\n🎉 网络连接正常，可以进行更新下载")
        else:
            print("\\n⚠️ 网络连接有问题，可能影响更新下载")
    else:
        print("\\n❌ 无法连接到更新服务器")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("测试下载连接.py", 'w', encoding='utf-8') as f:
            f.write(test_code)
        print("✅ 创建测试文件: 测试下载连接.py")
        return True
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 下载超时问题修复工具")
    print("=" * 60)
    
    try:
        # 修复下载超时
        fix_success = fix_download_timeout()
        
        # 创建测试脚本
        test_success = create_download_test()
        
        print("\n" + "=" * 60)
        print("📊 修复结果:")
        print(f"   - 超时修复: {'✅' if fix_success else '❌'}")
        print(f"   - 测试脚本: {'✅' if test_success else '❌'}")
        
        if fix_success:
            print("\n🎉 修复完成！")
            print("💡 修复内容:")
            print("   1. 增加下载超时时间到15分钟")
            print("   2. 增加重试次数到5次")
            print("   3. 增加下载块大小到64KB")
            print("   4. 添加更好的错误处理")
            
            print("\n🧪 测试建议:")
            print("   1. 运行: python 测试下载连接.py")
            print("   2. 检查网络连接和下载速度")
            print("   3. 重新尝试更新下载")
        else:
            print("\n⚠️ 修复可能不完整，请手动检查")
            
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")

if __name__ == "__main__":
    main()
