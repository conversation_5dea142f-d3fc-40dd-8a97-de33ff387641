#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部版本文件方案 - 使用外部文件存储版本信息，自动更新
"""

import os
import json

def create_version_file():
    """创建版本配置文件"""
    version_info = {
        "version": "2.1.1",
        "build_date": "2025-08-01",
        "description": "亚马逊蓝图工具",
        "auto_updated": True
    }
    
    with open('version.json', 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)
    
    print("✅ 已创建 version.json 文件")
    return version_info

def create_version_reader():
    """创建版本读取函数"""
    code = '''def get_current_version():
    """从外部文件读取当前版本号"""
    try:
        import json
        import os
        
        # 尝试从version.json读取
        if os.path.exists('version.json'):
            with open('version.json', 'r', encoding='utf-8') as f:
                version_info = json.load(f)
                return version_info.get('version', '2.1.0')
        
        # 如果文件不存在，返回默认版本
        return '2.1.0'
        
    except Exception as e:
        print(f"读取版本文件失败: {e}")
        return '2.1.0'

def update_version_file(new_version):
    """更新版本文件"""
    try:
        import json
        import os
        from datetime import datetime
        
        version_info = {
            "version": new_version,
            "build_date": datetime.now().strftime("%Y-%m-%d"),
            "description": "亚马逊蓝图工具",
            "auto_updated": True,
            "last_update": datetime.now().isoformat()
        }
        
        with open('version.json', 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 版本已更新为: {new_version}")
        return True
        
    except Exception as e:
        print(f"更新版本文件失败: {e}")
        return False'''
    
    with open('version_utils.py', 'w', encoding='utf-8') as f:
        f.write(code)
    
    print("✅ 已创建 version_utils.py 工具文件")

def modify_license_client():
    """修改license_client.py使用外部版本文件"""
    try:
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在文件开头添加导入
        if 'from version_utils import get_current_version' not in content:
            # 找到导入部分
            import_pos = content.find('import uuid')
            if import_pos != -1:
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'import uuid' in line:
                        lines.insert(i + 1, 'from version_utils import get_current_version')
                        break
                content = '\n'.join(lines)
        
        # 替换硬编码的版本号
        content = content.replace(
            'current_version = "2.1.1"  # 当前版本号',
            'current_version = get_current_version()  # 从外部文件读取版本号'
        )
        
        with open('license_client.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已修改 license_client.py 使用外部版本文件")
        return True
        
    except Exception as e:
        print(f"修改失败: {e}")
        return False

def modify_auto_updater():
    """修改auto_updater.py在更新后自动更新版本文件"""
    try:
        with open('auto_updater.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找文件替换成功的位置
        if 'version_utils import update_version_file' not in content:
            # 在文件开头添加导入
            import_pos = content.find('import os')
            if import_pos != -1:
                content = content.replace(
                    'import os',
                    'import os\nfrom version_utils import update_version_file'
                )
        
        # 在文件替换成功后添加版本更新
        if 'update_version_file(' not in content:
            # 查找替换成功的位置
            success_pattern = 'print("✅ 更新完成，程序将重启")'
            if success_pattern in content:
                content = content.replace(
                    success_pattern,
                    f'{success_pattern}\n                        # 更新版本文件\n                        update_version_file(update_info.get("version", "2.1.1"))'
                )
        
        with open('auto_updater.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已修改 auto_updater.py 支持自动版本更新")
        return True
        
    except Exception as e:
        print(f"修改失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 外部版本文件方案")
    print("=" * 50)
    
    # 1. 创建版本文件
    print("1️⃣ 创建版本配置文件...")
    create_version_file()
    
    # 2. 创建版本工具
    print("\n2️⃣ 创建版本工具...")
    create_version_reader()
    
    # 3. 修改license_client.py
    print("\n3️⃣ 修改license_client.py...")
    if modify_license_client():
        print("   ✅ 修改成功")
    else:
        print("   ❌ 修改失败")
    
    # 4. 修改auto_updater.py
    print("\n4️⃣ 修改auto_updater.py...")
    if modify_auto_updater():
        print("   ✅ 修改成功")
    else:
        print("   ❌ 修改失败")
    
    print("\n" + "=" * 50)
    print("🎉 外部版本文件方案部署完成!")
    print("\n✅ 优势:")
    print("  - 版本号存储在外部文件中")
    print("  - 更新时自动修改版本号")
    print("  - 不需要修改源代码")
    print("  - 支持版本历史记录")
    
    print("\n📁 创建的文件:")
    print("  - version.json (版本配置)")
    print("  - version_utils.py (版本工具)")
    
    print("\n🚀 使用方法:")
    print("1. 运行 python license_client.py")
    print("2. 程序会自动从version.json读取版本号")
    print("3. 更新时会自动更新version.json")
    print("4. 不再需要手动修改版本号")

if __name__ == "__main__":
    main()
