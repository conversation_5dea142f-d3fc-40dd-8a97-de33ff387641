#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析当前服务器情况，帮助选择最佳方案
"""

import paramiko
import sys
import os

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def analyze_current_situation():
    """分析当前服务器情况"""
    print("🔍 分析当前服务器情况")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 检查1: 查看服务器上的文件
    print("📄 检查1: 服务器上的文件...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"ls -la {config['deploy_path']}/*.py")
    if success:
        print("服务器上的Python文件:")
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
    else:
        print(f"   ❌ 无法列出文件: {error}")
    print()
    
    # 检查2: 查看当前运行的服务
    print("🔧 检查2: 当前运行的服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl status license-manager --no-pager")
    print("当前服务状态:")
    for line in output.split('\n'):
        if 'ExecStart' in line or 'Main PID' in line or 'Active:' in line:
            print(f"   {line.strip()}")
    print()
    
    # 检查3: 查看原始license_server.py的内容
    print("📋 检查3: 原始license_server.py的API接口...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"grep -n '@app.route' {config['deploy_path']}/license_server.py | head -10")
    if success:
        print("原始license_server.py的路由:")
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
    else:
        print(f"   ❌ 无法读取原始文件: {error}")
    print()
    
    # 检查4: 查看simple_server.py的内容
    print("📋 检查4: 当前simple_server.py的API接口...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"grep -n '@app.route' {config['deploy_path']}/simple_server.py")
    if success:
        print("当前simple_server.py的路由:")
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
    else:
        print(f"   ❌ 无法读取simple_server.py: {error}")
    print()
    
    return True

def check_local_license_server():
    """检查本地license_server.py文件"""
    print("📄 检查本地license_server.py文件")
    print("=" * 40)
    
    local_file = "license_server.py"
    
    if os.path.exists(local_file):
        print(f"✅ 找到本地文件: {local_file}")
        
        # 读取文件并分析API接口
        try:
            with open(local_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找路由
            lines = content.split('\n')
            routes = []
            for i, line in enumerate(lines):
                if '@app.route' in line:
                    routes.append(f"第{i+1}行: {line.strip()}")
            
            if routes:
                print("本地license_server.py的路由:")
                for route in routes[:10]:  # 显示前10个路由
                    print(f"   {route}")
                if len(routes) > 10:
                    print(f"   ... 还有 {len(routes)-10} 个路由")
            else:
                print("   ❌ 未找到路由定义")
                
            # 检查文件大小
            file_size = len(content)
            print(f"   📊 文件大小: {file_size} 字符")
            
            # 检查是否包含许可证管理功能
            license_keywords = ['/license/generate', '/license/list', '/license/info', '/license/modify', '/license/delete']
            found_keywords = []
            for keyword in license_keywords:
                if keyword in content:
                    found_keywords.append(keyword)
            
            if found_keywords:
                print("   ✅ 包含许可证管理功能:")
                for keyword in found_keywords:
                    print(f"     • {keyword}")
            else:
                print("   ❌ 不包含许可证管理功能")
                
            return True, len(found_keywords) > 0
            
        except Exception as e:
            print(f"   ❌ 读取文件失败: {e}")
            return False, False
    else:
        print(f"❌ 本地文件不存在: {local_file}")
        return False, False

def provide_recommendations():
    """提供建议方案"""
    print("\n📋 方案建议")
    print("=" * 30)
    
    print("🎯 根据分析结果，有以下几种方案:")
    print()
    
    print("方案1: 使用原始license_server.py")
    print("✅ 优点:")
    print("   • 包含完整的许可证管理功能")
    print("   • 与license_manager.py完全兼容")
    print("   • 功能最全面")
    print("❌ 缺点:")
    print("   • 可能比较复杂")
    print("   • 需要确保端口配置正确")
    print()
    
    print("方案2: 使用本地license_server.py")
    print("✅ 优点:")
    print("   • 可能是最新版本")
    print("   • 可以自定义修改")
    print("❌ 缺点:")
    print("   • 需要验证功能完整性")
    print("   • 可能需要适配服务器环境")
    print()
    
    print("方案3: 创建混合版本")
    print("✅ 优点:")
    print("   • 结合两者优点")
    print("   • 保留测试功能")
    print("   • 添加许可证管理")
    print("❌ 缺点:")
    print("   • 需要开发时间")
    print("   • 可能有兼容性问题")
    print()
    
    print("🎯 推荐方案:")
    print("1. 如果原始license_server.py功能完整 → 使用方案1")
    print("2. 如果本地license_server.py更新 → 使用方案2")
    print("3. 如果都有问题 → 使用方案3")

def main():
    """主函数"""
    try:
        print("🔍 license_manager.py连接问题分析")
        print("🎯 目标: 确定使用哪个license_server.py")
        print()
        
        # 分析服务器情况
        analyze_current_situation()
        
        # 检查本地文件
        local_exists, has_license_features = check_local_license_server()
        
        # 提供建议
        provide_recommendations()
        
        print("\n" + "="*50)
        print("🤔 请根据以上分析选择方案:")
        print("1. 使用服务器上的原始license_server.py")
        print("2. 使用本地的license_server.py")
        print("3. 创建混合版本")
        print("4. 查看更详细的文件对比")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n✅ 您选择了方案1: 使用原始license_server.py")
            print("💡 建议: 修改systemd配置使用原始license_server.py")
        elif choice == "2":
            print("\n✅ 您选择了方案2: 使用本地license_server.py")
            if has_license_features:
                print("💡 建议: 部署本地license_server.py到服务器")
            else:
                print("⚠️ 警告: 本地文件可能缺少许可证管理功能")
        elif choice == "3":
            print("\n✅ 您选择了方案3: 创建混合版本")
            print("💡 建议: 基于simple_server.py添加许可证管理功能")
        elif choice == "4":
            print("\n🔍 需要查看详细对比")
            print("💡 建议: 运行文件对比工具")
        else:
            print("\n❌ 无效选择")
        
    except Exception as e:
        print(f"❌ 分析过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
