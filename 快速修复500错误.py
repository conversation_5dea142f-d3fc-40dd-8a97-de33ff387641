#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复500错误
"""

import requests
import json

def test_current_error():
    """测试当前的500错误"""
    print("🔍 测试当前的500错误")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 使用管理员权限测试
    params = {
        "current_version": "2.1.0",
        "key": "ADMIN_BYPASS",
        "device_id": "ADMIN-DEVICE-001"
    }
    
    print(f"📋 测试参数: {params}")
    
    try:
        response = requests.get(
            f"{server_url}/update/check",
            params=params,
            timeout=10
        )
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 500:
            print("❌ 确认是500错误")
            return True
        else:
            print("✅ 不是500错误")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_other_endpoints():
    """测试其他端点是否正常"""
    print("\n🔍 测试其他端点")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    endpoints = [
        {
            "name": "根路径",
            "url": "/",
            "method": "GET"
        },
        {
            "name": "更新统计",
            "url": "/update/stats", 
            "method": "GET"
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n📡 测试: {endpoint['name']}")
        
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(f"{server_url}{endpoint['url']}", timeout=5)
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 端点正常")
                try:
                    data = response.json()
                    print(f"📄 响应: {data}")
                except:
                    print(f"📄 响应: {response.text[:100]}...")
            else:
                print("⚠️ 端点异常")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def create_simple_version_file():
    """创建简单的版本文件解决方案"""
    print("\n🔧 创建简单版本文件解决方案")
    print("=" * 50)
    
    # 创建一个本地的版本信息文件
    version_info = {
        "version": "2.1.0",
        "release_date": "2025-01-01T00:00:00",
        "description": "亚马逊蓝图工具 v2.1.0",
        "file_size": 0,
        "sha256": "",
        "upload_time": "2025-01-01T00:00:00",
        "download_url": "/update/download?version=2.1.0"
    }
    
    try:
        with open("version_info.json", "w", encoding="utf-8") as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        
        print("✅ 本地版本文件创建成功")
        print(f"📄 文件内容: {json.dumps(version_info, ensure_ascii=False, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建版本文件失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 50)
    
    print("🔧 方案1: 手动创建版本信息文件")
    print("   在服务器上执行:")
    print("   mkdir -p /opt/license_manager/updates")
    print("   echo '{}' > /opt/license_manager/updates/version_info.json".format(json.dumps({
        "version": "2.1.0",
        "release_date": "2025-01-01T00:00:00",
        "description": "亚马逊蓝图工具 v2.1.0",
        "file_size": 0,
        "sha256": "",
        "upload_time": "2025-01-01T00:00:00",
        "download_url": "/update/download?version=2.1.0"
    }, ensure_ascii=False)))
    print("   systemctl restart license-manager")
    
    print("\n🔧 方案2: 修改服务器代码")
    print("   在license_server.py中添加默认版本信息处理")
    
    print("\n🔧 方案3: 使用exe文件管理工具上传版本")
    print("   使用管理工具上传一个exe文件，自动创建版本信息")

def main():
    """主函数"""
    print("🎯 快速诊断和修复500错误")
    print("🔧 错误: 500 Server Error for /update/check")
    print("💡 可能原因: version_info.json文件不存在")
    print()
    
    # 测试当前错误
    has_error = test_current_error()
    
    # 测试其他端点
    test_other_endpoints()
    
    # 创建本地版本文件作为参考
    create_simple_version_file()
    
    # 建议解决方案
    suggest_solutions()
    
    if has_error:
        print("\n" + "=" * 50)
        print("📋 问题确认:")
        print("❌ /update/check API返回500错误")
        print("💡 原因: 服务器缺少 /opt/license_manager/updates/version_info.json")
        
        print("\n🚀 快速解决:")
        print("1. 使用SSH连接到服务器")
        print("2. 创建必要的目录和文件")
        print("3. 重启license-manager服务")
        print("4. 或者使用exe文件管理工具上传一个文件")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
