@echo off
chcp 65001 >nul
title 🔧 修复CentOS服务器问题

echo.
echo ==========================================
echo 🔧 修复license-manager服务问题
echo ==========================================
echo.
echo 🎯 检测到的问题:
echo • ❌ license-manager服务自动重启中
echo • ❌ HTTP服务端口5000无响应
echo • ⚠️ 可能有旧进程冲突
echo.
echo 🔧 修复步骤:
echo • 🛑 停止所有相关进程
echo • 🐍 检查Python脚本语法
echo • 📦 验证依赖完整性
echo • ⚙️ 修复systemd服务文件
echo • 🚀 重新启动服务
echo • 🔍 验证服务状态
echo • 🌍 测试HTTP响应
echo.

REM 检查paramiko
python -c "import paramiko" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少paramiko模块，正在安装...
    python -m pip install paramiko
    if errorlevel 1 (
        echo ❌ paramiko安装失败
        pause
        exit /b 1
    )
    echo ✅ paramiko安装完成
    echo.
)

echo 🔧 开始修复服务问题...
echo.

REM 运行修复脚本
python "修复服务问题.py"

echo.
echo 👋 修复完成
pause
