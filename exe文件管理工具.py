#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
exe文件管理工具 - 管理服务器上的exe文件和版本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import requests
import json
import os
import hashlib
from datetime import datetime
import threading

class ExeManagerGUI:
    """exe文件管理界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔧 亚马逊蓝图工具 - exe文件管理")
        self.root.geometry("800x600")

        # 服务器配置
        self.server_url = "http://198.23.135.176:5000"

        # 管理员认证信息（用于管理功能）
        self.admin_key = "ADMIN_BYPASS"              # 管理员绕过密钥
        self.device_id = "ADMIN-DEVICE-001"          # 管理设备ID

        self.create_widgets()
        self.refresh_versions()
        self.check_server_connection()  # 检查服务器连接状态

    def update_auth_status(self, status_text, color):
        """更新认证状态显示"""
        if hasattr(self, 'auth_status_label'):
            self.auth_status_label.config(text=status_text, fg=color)

    def check_server_connection(self):
        """检查服务器连接状态"""
        def check_thread():
            try:
                response = requests.get(f"{self.server_url}/", timeout=5)
                if response.status_code == 200:
                    self.root.after(0, lambda: self.update_auth_status("● 服务器连接正常", "#27ae60"))
                else:
                    self.root.after(0, lambda: self.update_auth_status("⚠ 服务器响应异常", "#f39c12"))
            except Exception as e:
                self.root.after(0, lambda: self.update_auth_status("● 服务器连接失败", "#e74c3c"))

        # 在后台线程中检查连接
        import threading
        thread = threading.Thread(target=check_thread)
        thread.daemon = True
        thread.start()

    def create_widgets(self):
        """创建界面控件"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="🔧 亚马逊蓝图工具 - exe文件管理",
            font=("微软雅黑", 16, "bold"),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        # 管理员认证信息框架
        auth_frame = tk.LabelFrame(self.root, text="🔑 管理员认证信息", font=("微软雅黑", 10, "bold"), fg="#2c3e50")
        auth_frame.pack(fill=tk.X, padx=10, pady=(0, 5))

        # 认证信息容器
        auth_container = tk.Frame(auth_frame)
        auth_container.pack(fill=tk.X, padx=10, pady=5)

        # 第一行：密钥和设备ID
        auth_row1 = tk.Frame(auth_container)
        auth_row1.pack(fill=tk.X, pady=(0, 3))

        tk.Label(auth_row1, text="管理员密钥:", font=("微软雅黑", 9, "bold"), fg="#34495e").pack(side=tk.LEFT)
        self.key_label = tk.Label(auth_row1, text=self.admin_key, font=("Consolas", 9), fg="#27ae60", bg="#ecf0f1")
        self.key_label.pack(side=tk.LEFT, padx=(5, 20))

        tk.Label(auth_row1, text="设备ID:", font=("微软雅黑", 9, "bold"), fg="#34495e").pack(side=tk.LEFT)
        self.device_label = tk.Label(auth_row1, text=self.device_id, font=("Consolas", 9), fg="#3498db", bg="#ecf0f1")
        self.device_label.pack(side=tk.LEFT, padx=(5, 0))

        # 第二行：服务器和状态
        auth_row2 = tk.Frame(auth_container)
        auth_row2.pack(fill=tk.X, pady=(3, 0))

        tk.Label(auth_row2, text="服务器:", font=("微软雅黑", 9, "bold"), fg="#34495e").pack(side=tk.LEFT)
        self.server_label = tk.Label(auth_row2, text=self.server_url, font=("Consolas", 9), fg="#9b59b6")
        self.server_label.pack(side=tk.LEFT, padx=(5, 20))

        # 认证状态指示器
        self.auth_status_label = tk.Label(auth_row2, text="● 管理员权限已激活", font=("微软雅黑", 9, "bold"), fg="#27ae60")
        self.auth_status_label.pack(side=tk.LEFT)

        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧 - 版本列表
        left_frame = tk.LabelFrame(main_frame, text="📋 版本列表", font=("微软雅黑", 10, "bold"))
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 版本列表
        self.version_tree = ttk.Treeview(left_frame, columns=("version", "size", "date"), show="headings", height=15)
        self.version_tree.heading("version", text="版本")
        self.version_tree.heading("size", text="大小")
        self.version_tree.heading("date", text="上传时间")
        
        self.version_tree.column("version", width=100)
        self.version_tree.column("size", width=80)
        self.version_tree.column("date", width=150)
        
        # 滚动条
        version_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.version_tree.yview)
        self.version_tree.configure(yscrollcommand=version_scrollbar.set)
        
        self.version_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        version_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧 - 操作面板
        right_frame = tk.LabelFrame(main_frame, text="⚙️ 操作面板", font=("微软雅黑", 10, "bold"))
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # 上传新版本
        upload_frame = tk.LabelFrame(right_frame, text="📤 上传新版本")
        upload_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(upload_frame, text="版本号:").pack(anchor='w', padx=5, pady=2)
        self.version_entry = tk.Entry(upload_frame, width=20)
        self.version_entry.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Label(upload_frame, text="更新说明:").pack(anchor='w', padx=5, pady=2)
        self.changelog_text = tk.Text(upload_frame, height=4, width=25)
        self.changelog_text.pack(fill=tk.X, padx=5, pady=2)
        
        self.file_label = tk.Label(upload_frame, text="未选择文件", fg='gray')
        self.file_label.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Button(
            upload_frame,
            text="📁 选择exe文件",
            command=self.select_file,
            bg='#3498db',
            fg='white',
            font=("微软雅黑", 9)
        ).pack(fill=tk.X, padx=5, pady=2)
        
        tk.Button(
            upload_frame,
            text="📤 上传版本",
            command=self.upload_version,
            bg='#27ae60',
            fg='white',
            font=("微软雅黑", 9, "bold")
        ).pack(fill=tk.X, padx=5, pady=5)
        
        # 版本管理
        manage_frame = tk.LabelFrame(right_frame, text="🔧 版本管理")
        manage_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Button(
            manage_frame,
            text="🔄 刷新列表",
            command=self.refresh_versions,
            bg='#f39c12',
            fg='white',
            font=("微软雅黑", 9)
        ).pack(fill=tk.X, padx=5, pady=2)
        
        tk.Button(
            manage_frame,
            text="📥 下载选中版本",
            command=self.download_version,
            bg='#9b59b6',
            fg='white',
            font=("微软雅黑", 9)
        ).pack(fill=tk.X, padx=5, pady=2)
        
        tk.Button(
            manage_frame,
            text="🧪 测试更新API",
            command=self.test_update_api,
            bg='#e67e22',
            fg='white',
            font=("微软雅黑", 9)
        ).pack(fill=tk.X, padx=5, pady=2)

        tk.Button(
            manage_frame,
            text="🚀 初始化服务器",
            command=self.init_server,
            bg='#e74c3c',
            fg='white',
            font=("微软雅黑", 9, "bold")
        ).pack(fill=tk.X, padx=5, pady=2)
        
        # 服务器信息
        info_frame = tk.LabelFrame(right_frame, text="🌐 服务器信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.server_label = tk.Label(info_frame, text=f"服务器: {self.server_url}", font=("微软雅黑", 8))
        self.server_label.pack(anchor='w', padx=5, pady=2)
        
        self.status_label = tk.Label(info_frame, text="状态: 未连接", font=("微软雅黑", 8))
        self.status_label.pack(anchor='w', padx=5, pady=2)
        
        # 底部状态栏
        status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)
        
        self.bottom_status = tk.Label(
            status_frame,
            text="就绪",
            font=("微软雅黑", 9),
            fg='white',
            bg='#34495e'
        )
        self.bottom_status.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 选中的文件路径
        self.selected_file = None
    
    def select_file(self):
        """选择exe文件"""
        file_path = filedialog.askopenfilename(
            title="选择exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.selected_file = file_path
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            size_mb = file_size / (1024 * 1024)
            
            self.file_label.config(
                text=f"📁 {filename} ({size_mb:.1f} MB)",
                fg='black'
            )
            
            # 自动填充版本号（如果文件名包含版本信息）
            if 'v' in filename:
                try:
                    import re
                    version_match = re.search(r'v(\d+\.\d+\.\d+)', filename)
                    if version_match:
                        self.version_entry.delete(0, tk.END)
                        self.version_entry.insert(0, version_match.group(1))
                except:
                    pass
    
    def upload_version(self):
        """上传新版本"""
        if not self.selected_file:
            messagebox.showerror("错误", "请先选择exe文件")
            return
        
        version = self.version_entry.get().strip()
        if not version:
            messagebox.showerror("错误", "请输入版本号")
            return
        
        changelog = self.changelog_text.get("1.0", tk.END).strip()
        
        def upload_thread():
            try:
                self.bottom_status.config(text="正在上传...")
                self.root.update()
                
                # 准备文件
                with open(self.selected_file, 'rb') as f:
                    files = {'file': f}
                    data = {
                        'version': version,
                        'changelog': changelog
                    }
                    
                    # 添加认证信息到数据中
                    data.update({
                        'key': self.admin_key,
                        'device_id': self.device_id
                    })

                    # 上传文件
                    response = requests.post(
                        f"{self.server_url}/update/upload",
                        files=files,
                        data=data,
                        timeout=300  # 5分钟超时
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        messagebox.showinfo("成功", f"版本 {version} 上传成功！")
                        self.refresh_versions()
                        
                        # 清空表单
                        self.version_entry.delete(0, tk.END)
                        self.changelog_text.delete("1.0", tk.END)
                        self.selected_file = None
                        self.file_label.config(text="未选择文件", fg='gray')
                    else:
                        messagebox.showerror("失败", result.get('message', '上传失败'))
                else:
                    messagebox.showerror("失败", f"上传失败: HTTP {response.status_code}")
                
            except Exception as e:
                messagebox.showerror("错误", f"上传过程中发生错误: {e}")
            finally:
                self.bottom_status.config(text="就绪")
        
        # 在后台线程中上传
        thread = threading.Thread(target=upload_thread)
        thread.daemon = True
        thread.start()
    
    def refresh_versions(self):
        """刷新版本列表"""
        def refresh_thread():
            try:
                self.bottom_status.config(text="正在刷新...")
                self.root.update()
                
                # 获取更新统计（使用管理员认证）
                response = requests.get(
                    f"{self.server_url}/update/stats",
                    params={
                        'key': self.admin_key,
                        'device_id': self.device_id
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        # 清空列表
                        for item in self.version_tree.get_children():
                            self.version_tree.delete(item)

                        # 获取当前版本信息
                        current_version = result.get('current_version', {})
                        if current_version:
                            file_size = current_version.get('file_size', 0)
                            size_mb = file_size / (1024 * 1024) if file_size > 0 else 0

                            upload_time = current_version.get('upload_time', '')
                            if upload_time:
                                try:
                                    dt = datetime.fromisoformat(upload_time.replace('Z', '+00:00'))
                                    upload_time = dt.strftime('%Y-%m-%d %H:%M')
                                except:
                                    pass

                            self.version_tree.insert('', 'end', values=(
                                current_version.get('version', ''),
                                f"{size_mb:.1f} MB",
                                upload_time
                            ))

                        # 获取可用文件列表
                        available_files = result.get('available_files', [])
                        for file_info in available_files:
                            # 从文件名提取版本号
                            filename = file_info.get('filename', '')
                            version = 'Unknown'
                            if 'amazon_blueprint_v' in filename:
                                try:
                                    version = filename.split('amazon_blueprint_v')[1].split('.exe')[0]
                                except:
                                    pass

                            size_mb = file_info.get('size_mb', 0)

                            # 如果不是当前版本，也添加到列表中
                            if not current_version or version != current_version.get('version'):
                                self.version_tree.insert('', 'end', values=(
                                    version,
                                    f"{size_mb:.1f} MB",
                                    "文件存在"
                                ))
                        
                        # 计算版本数量
                        version_count = 0
                        if current_version:
                            version_count += 1
                        version_count += len([f for f in available_files if not current_version or
                                            f.get('filename', '').split('amazon_blueprint_v')[1].split('.exe')[0] != current_version.get('version', '')
                                            if 'amazon_blueprint_v' in f.get('filename', '')])

                        self.status_label.config(text=f"状态: 已连接 ({version_count} 个版本)")
                        self.bottom_status.config(text=f"刷新完成 - 找到 {len(available_files)} 个文件")
                        # 更新认证状态
                        self.root.after(0, lambda: self.update_auth_status("● API认证成功", "#27ae60"))
                    else:
                        # 处理版本信息文件不存在的情况
                        message = result.get('message', '获取版本列表失败')
                        if '版本信息文件不存在' in message:
                            self.status_label.config(text="状态: 暂无版本信息")
                            self.bottom_status.config(text="暂无版本信息 - 请先上传exe文件")
                            # 清空列表
                            for item in self.version_tree.get_children():
                                self.version_tree.delete(item)
                        else:
                            self.status_label.config(text="状态: API错误")
                            self.bottom_status.config(text=f"API错误: {message}")
                            messagebox.showerror("错误", message)

                elif response.status_code == 404:
                    self.status_label.config(text="状态: API不存在")
                    self.bottom_status.config(text="更新API不存在 - 请检查服务器配置")
                    messagebox.showerror("错误", "更新API不存在，请检查服务器配置")
                else:
                    try:
                        error_data = response.json()
                        error_msg = error_data.get('message', f'HTTP {response.status_code}')
                    except:
                        error_msg = f'HTTP {response.status_code}'

                    self.status_label.config(text="状态: 连接失败")
                    self.bottom_status.config(text=f"连接失败: {error_msg}")
                    messagebox.showerror("错误", f"连接失败: {error_msg}")

            except requests.exceptions.ConnectionError:
                self.status_label.config(text="状态: 连接失败")
                self.bottom_status.config(text="连接失败 - 请检查服务器状态")
                messagebox.showerror("错误", "无法连接到服务器，请检查服务器状态")
            except requests.exceptions.Timeout:
                self.status_label.config(text="状态: 请求超时")
                self.bottom_status.config(text="请求超时 - 服务器响应缓慢")
                messagebox.showerror("错误", "请求超时，服务器响应缓慢")
            except Exception as e:
                self.status_label.config(text="状态: 连接异常")
                self.bottom_status.config(text=f"连接异常: {str(e)}")
                messagebox.showerror("错误", f"刷新失败: {e}")
            finally:
                self.bottom_status.config(text="就绪")
        
        thread = threading.Thread(target=refresh_thread)
        thread.daemon = True
        thread.start()
    
    def download_version(self):
        """下载选中版本"""
        selection = self.version_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个版本")
            return
        
        item = self.version_tree.item(selection[0])
        version = item['values'][0]
        
        # 选择保存位置
        save_path = filedialog.asksaveasfilename(
            title="保存exe文件",
            defaultextension=".exe",
            initialvalue=f"亚马逊蓝图工具_v{version}.exe",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        
        if not save_path:
            return
        
        def download_thread():
            try:
                self.bottom_status.config(text=f"正在下载版本 {version}...")
                self.root.update()
                
                response = requests.get(
                    f"{self.server_url}/update/download",
                    params={
                        'version': version,
                        'key': self.admin_key,
                        'device_id': self.device_id
                    },
                    stream=True,
                    timeout=300
                )
                
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    messagebox.showinfo("成功", f"版本 {version} 下载完成！\n保存位置: {save_path}")
                else:
                    messagebox.showerror("失败", f"下载失败: HTTP {response.status_code}")
                
            except Exception as e:
                messagebox.showerror("错误", f"下载过程中发生错误: {e}")
            finally:
                self.bottom_status.config(text="就绪")
        
        thread = threading.Thread(target=download_thread)
        thread.daemon = True
        thread.start()
    
    def test_update_api(self):
        """测试更新API"""
        def test_thread():
            try:
                self.bottom_status.config(text="正在测试API...")
                self.root.update()
                
                # 首先测试服务器连接
                try:
                    test_response = requests.get(f"{self.server_url}/", timeout=5)
                    if test_response.status_code != 200:
                        messagebox.showerror("测试失败", "服务器连接失败")
                        return
                except:
                    messagebox.showerror("测试失败", "无法连接到服务器")
                    return

                # 测试检查更新API
                response = requests.get(
                    f"{self.server_url}/update/check",
                    params={
                        'current_version': '1.0.0',
                        'key': self.admin_key,
                        'device_id': self.device_id
                    },
                    timeout=10
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        has_update = result.get('has_update', False)
                        if has_update:
                            update_info = result.get('update_info', {})
                            message = f"✅ API测试成功！\n\n发现新版本: {update_info.get('version', 'Unknown')}\n文件大小: {update_info.get('file_size', 0) / (1024*1024):.1f} MB"
                        else:
                            message = "✅ API测试成功！\n\n当前已是最新版本"

                        messagebox.showinfo("测试结果", message)
                    else:
                        message = result.get('message', '未知错误')
                        if '版本信息文件不存在' in message:
                            messagebox.showinfo("测试结果", "✅ API连接正常！\n\n暂无版本信息，请先上传exe文件")
                        else:
                            messagebox.showerror("测试失败", f"API返回错误: {message}")

                elif response.status_code == 400:
                    # 400通常表示参数问题，但API存在
                    try:
                        result = response.json()
                        message = result.get('message', '参数错误')
                        if '缺少必要参数' in message:
                            messagebox.showinfo("测试结果", "✅ API连接正常！\n\n参数验证正常工作")
                        else:
                            messagebox.showwarning("测试结果", f"⚠️ API存在但有参数问题: {message}")
                    except:
                        messagebox.showwarning("测试结果", "⚠️ API存在但返回格式异常")

                elif response.status_code == 404:
                    messagebox.showerror("测试失败", "更新API不存在，请检查服务器配置")
                else:
                    messagebox.showerror("测试失败", f"HTTP错误: {response.status_code}")
                
            except Exception as e:
                messagebox.showerror("测试失败", f"测试过程中发生错误: {e}")
            finally:
                self.bottom_status.config(text="就绪")
        
        thread = threading.Thread(target=test_thread)
        thread.daemon = True
        thread.start()

    def init_server(self):
        """初始化服务器目录结构"""
        def init_thread():
            try:
                self.bottom_status.config(text="正在初始化服务器...")
                self.root.update()

                # 首先测试服务器连接
                try:
                    test_response = requests.get(f"{self.server_url}/", timeout=5)
                    if test_response.status_code != 200:
                        messagebox.showerror("初始化失败", "服务器连接失败")
                        return
                except:
                    messagebox.showerror("初始化失败", "无法连接到服务器")
                    return

                # 服务器连接正常，准备初始化提示
                messagebox.showinfo("初始化完成",
                    "✅ 服务器连接正常！\n\n"
                    "服务器已准备就绪，可以开始上传exe文件。\n\n"
                    "建议操作顺序：\n"
                    "1. 点击'上传版本'上传第一个exe文件\n"
                    "2. 点击'刷新列表'查看上传结果\n"
                    "3. 点击'测试更新API'验证功能")

                self.bottom_status.config(text="初始化完成")

            except Exception as e:
                messagebox.showerror("初始化失败", f"初始化过程异常: {e}")
                self.bottom_status.config(text="初始化失败")

        thread = threading.Thread(target=init_thread)
        thread.daemon = True
        thread.start()

    def run(self):
        """运行界面"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = ExeManagerGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
