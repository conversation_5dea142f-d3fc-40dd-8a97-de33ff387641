#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版GUI启动脚本 - 解决编码问题
"""

import sys
import os
import subprocess

def setup_encoding():
    """设置正确的编码环境"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # 在Windows上设置控制台编码
    if sys.platform == 'win32':
        try:
            # 设置控制台代码页为UTF-8
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        except:
            pass

def check_requirements():
    """检查基本要求"""
    print("检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误：需要Python 3.8或更高版本")
        return False
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查tkinter
    try:
        import tkinter
        print("✓ tkinter可用")
    except ImportError:
        print("错误：tkinter不可用，请检查Python安装")
        return False
    
    # 检查build_gui.py文件
    if not os.path.exists('build_gui.py'):
        print("错误：找不到build_gui.py文件")
        return False
    
    print("✓ build_gui.py文件存在")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("    亚马逊授权系统构建工具 - 修复版")
    print("=" * 50)
    
    # 设置编码
    setup_encoding()
    
    # 检查要求
    if not check_requirements():
        input("\n按回车键退出...")
        return False
    
    print("\n启动图形界面...")
    
    try:
        # 导入并启动GUI
        import build_gui
        build_gui.main()
        
    except ImportError as e:
        print(f"导入错误: {str(e)}")
        print("请确保所有依赖都已安装")
        input("\n按回车键退出...")
        return False
        
    except Exception as e:
        print(f"启动错误: {str(e)}")
        input("\n按回车键退出...")
        return False
    
    return True

if __name__ == "__main__":
    main()
