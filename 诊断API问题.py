#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断API问题 - 检查服务器上的代码和日志
"""

import paramiko
import requests

def diagnose_api_issues():
    """诊断API问题"""
    print("🔍 诊断API问题")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 步骤1: 检查服务器上的文件内容
        print("\n📋 步骤1: 检查服务器上的路由定义...")
        stdin, stdout, stderr = client.exec_command(f"grep -n '@app.route' {config['deploy_path']}/license_server.py")
        routes_output = stdout.read().decode('utf-8')
        
        if routes_output.strip():
            print("✅ 找到的路由定义:")
            for line in routes_output.strip().split('\n'):
                print(f"   {line}")
        else:
            print("❌ 未找到路由定义")
        
        # 步骤2: 检查根路由
        print("\n📋 步骤2: 检查根路由定义...")
        stdin, stdout, stderr = client.exec_command(f"grep -A 5 \"@app.route('/')\" {config['deploy_path']}/license_server.py")
        root_route = stdout.read().decode('utf-8')
        
        if root_route.strip():
            print("✅ 根路由定义:")
            print(f"   {root_route}")
        else:
            print("❌ 未找到根路由定义")
        
        # 步骤3: 检查update相关路由
        print("\n📋 步骤3: 检查update相关路由...")
        stdin, stdout, stderr = client.exec_command(f"grep -A 3 '/update/' {config['deploy_path']}/license_server.py")
        update_routes = stdout.read().decode('utf-8')
        
        if update_routes.strip():
            print("✅ update路由定义:")
            print(f"   {update_routes}")
        else:
            print("❌ 未找到update路由定义")
        
        # 步骤4: 检查最近的错误日志
        print("\n📋 步骤4: 检查最近的错误日志...")
        stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '10 minutes ago' --no-pager | grep -i error")
        error_logs = stdout.read().decode('utf-8')
        
        if error_logs.strip():
            print("⚠️ 发现错误日志:")
            print(f"   {error_logs}")
        else:
            print("✅ 未发现错误日志")
        
        # 步骤5: 检查完整的最近日志
        print("\n📋 步骤5: 检查完整的最近日志...")
        stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '5 minutes ago' --no-pager")
        full_logs = stdout.read().decode('utf-8')
        
        print("最近5分钟的完整日志:")
        print(f"{full_logs}")
        
        # 步骤6: 检查Flask应用启动信息
        print("\n📋 步骤6: 检查Flask应用启动信息...")
        stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '10 minutes ago' --no-pager | grep -i flask")
        flask_logs = stdout.read().decode('utf-8')
        
        if flask_logs.strip():
            print("Flask启动信息:")
            print(f"   {flask_logs}")
        else:
            print("❌ 未找到Flask启动信息")
        
        # 步骤7: 手动测试API
        print("\n📋 步骤7: 详细测试API...")
        
        test_cases = [
            {
                "name": "根路径",
                "url": "http://**************:5000/",
                "expected": "应该返回欢迎信息或重定向"
            },
            {
                "name": "更新统计",
                "url": "http://**************:5000/update/stats",
                "expected": "应该返回版本统计信息"
            },
            {
                "name": "检查更新(无参数)",
                "url": "http://**************:5000/update/check",
                "expected": "应该返回400(缺少参数)"
            },
            {
                "name": "检查更新(有参数)",
                "url": "http://**************:5000/update/check?current_version=1.0.0",
                "expected": "应该返回200或400"
            }
        ]
        
        for test in test_cases:
            try:
                print(f"\n   🧪 测试: {test['name']}")
                print(f"   🌐 URL: {test['url']}")
                
                response = requests.get(test['url'], timeout=10)
                print(f"   📊 状态码: {response.status_code}")
                print(f"   💡 期望: {test['expected']}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   📄 JSON响应: {data}")
                    except:
                        print(f"   📄 文本响应: {response.text[:200]}...")
                elif response.status_code == 400:
                    try:
                        data = response.json()
                        print(f"   📄 错误信息: {data}")
                    except:
                        print(f"   📄 错误文本: {response.text[:200]}...")
                elif response.status_code == 404:
                    print(f"   ❌ 路由不存在")
                else:
                    print(f"   ⚠️ 异常状态码: {response.text[:100]}...")
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
        
        # 步骤8: 检查Python进程详情
        print("\n📋 步骤8: 检查Python进程详情...")
        stdin, stdout, stderr = client.exec_command("ps aux | grep license_server.py | grep -v grep")
        process_info = stdout.read().decode('utf-8')
        
        if process_info.strip():
            print(f"Python进程信息:\n{process_info}")
        else:
            print("❌ 未找到Python进程")
        
        # 关闭连接
        client.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {e}")
        return False

def suggest_fixes():
    """建议修复方案"""
    print("\n💡 可能的修复方案:")
    print("=" * 30)
    
    print("1. 如果根路由不存在:")
    print("   - 添加 @app.route('/') 路由")
    print("   - 返回基本信息或重定向")
    
    print("\n2. 如果update路由不存在:")
    print("   - 检查路由定义语法")
    print("   - 确保所有update路由都已定义")
    
    print("\n3. 如果有Python错误:")
    print("   - 检查语法错误")
    print("   - 检查导入错误")
    print("   - 检查缩进问题")
    
    print("\n4. 如果Flask应用未正确启动:")
    print("   - 检查app.run()调用")
    print("   - 检查端口绑定")
    print("   - 检查主函数定义")

def main():
    """主函数"""
    try:
        print("🎯 目标: 诊断API问题")
        print("🔍 问题: 部分API返回404")
        print("💡 方案: 检查代码、日志、测试API")
        print()
        
        if diagnose_api_issues():
            suggest_fixes()
        else:
            print("❌ 诊断失败")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
