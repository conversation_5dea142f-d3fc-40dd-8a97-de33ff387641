#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活管理员密钥 - 激活管理员密钥以获得完整权限
"""

import requests
import json

def activate_admin_key():
    """激活管理员密钥"""
    print("🔑 激活管理员密钥")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    admin_key = "V09M4HCC-20350729-def2f7"
    device_id = "ADMIN-DEVICE-001"
    
    try:
        print("🌐 正在激活管理员密钥...")
        
        # 激活密钥
        response = requests.post(
            f"{server_url}/license/check",
            json={
                'key': admin_key,
                'device_id': device_id
            },
            timeout=30
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('valid'):
                print("✅ 管理员密钥激活成功！")
                print(f"🔒 权限: {result.get('permissions', [])}")
                print(f"📅 到期时间: {result.get('expire_date', 'N/A')}")
                
                # 再次测试更新API
                print("\n🧪 测试更新API...")
                test_response = requests.get(
                    f"{server_url}/update/check",
                    params={
                        'current_version': '1.0.0',
                        'key': admin_key,
                        'device_id': device_id
                    },
                    timeout=10
                )
                
                print(f"📊 更新API状态码: {test_response.status_code}")
                if test_response.status_code == 200:
                    print("✅ 更新API访问成功！")
                elif test_response.status_code == 400:
                    print("✅ 更新API连接正常（参数验证）")
                else:
                    print(f"⚠️ 更新API状态: {test_response.status_code}")
                    print(f"响应: {test_response.text[:200]}")
                
                return True
                
            else:
                message = result.get('message', '未知错误')
                print(f"❌ 激活失败: {message}")
                
                if '激活码不存在' in message:
                    print("💡 提示: 密钥可能未正确生成或已被删除")
                elif '设备已绑定' in message:
                    print("💡 提示: 密钥已绑定到其他设备")
                elif '激活码已过期' in message:
                    print("💡 提示: 密钥已过期，需要重新生成")
                
                return False
                
        else:
            print(f"❌ 激活请求失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data.get('message', '未知错误')}")
            except:
                print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请检查服务器状态")
        return False
    except Exception as e:
        print(f"❌ 激活异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 激活管理员密钥获得完整权限")
        print("🔑 密钥: V09M4HCC-20350729-def2f7")
        print("📱 设备: ADMIN-DEVICE-001")
        print()
        
        if activate_admin_key():
            print("\n" + "=" * 50)
            print("🎉 管理员密钥激活成功！")
            print("\n📋 现在你可以:")
            print("1. 启动exe文件管理工具")
            print("2. 使用所有管理功能（上传、下载、统计）")
            print("3. 访问所有更新API")
            print("\n💡 建议: 重新运行 '测试管理员权限.py' 验证权限")
        else:
            print("\n❌ 管理员密钥激活失败")
            print("\n🔧 可能的解决方案:")
            print("1. 重新运行 '创建管理员密钥.py' 生成新密钥")
            print("2. 检查服务器上的授权数据库")
            print("3. 确认服务器服务正常运行")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
