#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署完整的license_server.py到服务器
"""

import paramiko
import os
import sys
from datetime import datetime

def deploy_license_server():
    """部署完整的license_server.py"""
    print("🚀 部署完整的license_server.py")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    local_file = "license_server.py"
    
    # 检查本地文件
    if not os.path.exists(local_file):
        print(f"❌ 本地文件不存在: {local_file}")
        return False
    
    print(f"📁 本地文件: {local_file}")
    print(f"🌐 目标服务器: {config['host']}")
    print(f"📂 部署路径: {config['deploy_path']}")
    print()
    
    try:
        # 建立SSH连接
        print("🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 建立SCP连接
        scp = paramiko.SFTPClient.from_transport(client.get_transport())
        
        # 步骤1: 备份原文件
        print("📁 步骤1: 备份原文件...")
        backup_name = f"license_server.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = f"{config['deploy_path']}/{backup_name}"
        
        stdin, stdout, stderr = client.exec_command(
            f"cp {config['deploy_path']}/license_server.py {backup_path}"
        )
        stdout.channel.recv_exit_status()
        print(f"   ✅ 已备份到: {backup_name}")
        
        # 步骤2: 停止服务
        print("🛑 步骤2: 停止服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        exit_status = stdout.channel.recv_exit_status()
        if exit_status == 0:
            print("   ✅ 服务已停止")
        else:
            print("   ⚠️ 停止服务可能失败，继续部署...")
        
        # 步骤3: 上传新文件
        print("📤 步骤3: 上传新文件...")
        remote_path = f"{config['deploy_path']}/license_server.py"
        scp.put(local_file, remote_path)
        print(f"   ✅ 文件已上传到: {remote_path}")
        
        # 步骤4: 设置文件权限
        print("🔧 步骤4: 设置文件权限...")
        stdin, stdout, stderr = client.exec_command(f"chmod +x {remote_path}")
        stdout.channel.recv_exit_status()
        print("   ✅ 权限设置完成")
        
        # 步骤5: 修改systemd配置
        print("⚙️ 步骤5: 修改systemd配置...")
        systemd_config = f"""[Unit]
Description=License Manager Service - Full Version
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={config['deploy_path']}
ExecStart=/usr/bin/python3 {config['deploy_path']}/license_server.py
Restart=always
RestartSec=3
Environment=PYTHONPATH={config['deploy_path']}
Environment=PYTHONIOENCODING=utf-8

[Install]
WantedBy=multi-user.target
"""
        
        # 写入systemd配置
        stdin, stdout, stderr = client.exec_command(
            f"cat > /etc/systemd/system/license-manager.service << 'EOF'\n{systemd_config}EOF"
        )
        stdout.channel.recv_exit_status()
        print("   ✅ systemd配置已更新")
        
        # 步骤6: 重新加载systemd
        print("🔄 步骤6: 重新加载systemd...")
        stdin, stdout, stderr = client.exec_command("systemctl daemon-reload")
        stdout.channel.recv_exit_status()
        print("   ✅ systemd已重新加载")
        
        # 步骤7: 启动服务
        print("🚀 步骤7: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error_output = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error_output}")
            return False
        
        # 步骤8: 验证服务状态
        print("🔍 步骤8: 验证服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("   ✅ 服务运行正常")
        else:
            print(f"   ❌ 服务状态异常: {status}")
            
            # 查看服务日志
            print("   📋 查看服务日志...")
            stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --no-pager -n 10")
            log_output = stdout.read().decode('utf-8')
            print("   最近日志:")
            for line in log_output.split('\n')[-5:]:
                if line.strip():
                    print(f"     {line}")
            return False
        
        # 步骤9: 测试API接口
        print("🧪 步骤9: 测试API接口...")
        test_commands = [
            "curl -s http://localhost:5000/",
            "curl -s http://localhost:5000/health",
            "curl -s http://localhost:5000/license/list"
        ]
        
        for cmd in test_commands:
            stdin, stdout, stderr = client.exec_command(cmd)
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            endpoint = cmd.split('/')[-1] if '/' in cmd else 'root'
            if output and not error:
                print(f"   ✅ {endpoint} 接口正常")
            else:
                print(f"   ❌ {endpoint} 接口异常")
        
        # 关闭连接
        scp.close()
        client.close()
        
        print("\n🎉 部署完成！")
        print("🌐 现在可以使用license_manager.py连接到服务器了")
        print("📋 服务地址: http://**************:5000")
        
        return True
        
    except Exception as e:
        print(f"❌ 部署过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🔍 问题: 服务器上的license_server.py是空文件")
        print("🎯 解决: 部署本地完整的license_server.py")
        print()
        
        # 确认部署
        print("📋 即将执行的操作:")
        print("• 🛑 停止当前服务")
        print("• 📁 备份空的license_server.py")
        print("• 📤 上传完整的license_server.py")
        print("• ⚙️ 更新systemd配置")
        print("• 🚀 启动新服务")
        print("• 🧪 测试API接口")
        print()
        
        confirm = input("确认开始部署？(y/n): ").lower().strip()
        
        if confirm in ['y', 'yes', '是']:
            if deploy_license_server():
                print("\n✅ 部署成功！license_manager.py现在应该可以正常连接了")
            else:
                print("\n❌ 部署失败，请检查错误信息")
        else:
            print("❌ 用户取消部署")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
