#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复 - 测试移除认证后的API访问
"""

import requests
import json

def test_api_without_auth():
    """测试无认证的API访问"""
    print("🔧 测试无认证API访问")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    test_cases = [
        {
            "name": "更新统计API",
            "url": f"{server_url}/update/stats",
            "method": "GET",
            "expected": [200, 404]
        },
        {
            "name": "下载API（无版本）",
            "url": f"{server_url}/update/download",
            "method": "GET",
            "params": {"version": "1.0.0"},
            "expected": [200, 404]
        },
        {
            "name": "根路径",
            "url": f"{server_url}/",
            "method": "GET",
            "expected": [200]
        }
    ]
    
    print("🧪 开始测试...")
    print()
    
    success_count = 0
    
    for i, test in enumerate(test_cases, 1):
        print(f"📋 测试 {i}: {test['name']}")
        print(f"   🌐 URL: {test['url']}")
        
        try:
            if test['method'] == 'GET':
                response = requests.get(
                    test['url'],
                    params=test.get('params', {}),
                    timeout=10
                )
            
            status_code = response.status_code
            print(f"   📊 状态码: {status_code}")
            
            if status_code in test['expected']:
                success_count += 1
                print(f"   ✅ 访问成功")
                
                # 显示响应内容
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = response.json()
                        if isinstance(data, dict):
                            if 'success' in data:
                                print(f"   📄 成功: {data['success']}")
                            if 'message' in data:
                                message = data['message'][:50]
                                print(f"   📄 消息: {message}...")
                            if 'version_count' in data:
                                print(f"   📊 版本数: {data['version_count']}")
                except:
                    content = response.text[:100]
                    print(f"   📄 响应: {content}...")
                    
            else:
                print(f"   ❌ 访问失败 (期望: {test['expected']})")
                if status_code == 401:
                    print(f"   🚫 仍需要认证")
                elif status_code == 404:
                    print(f"   🚫 API不存在")
                else:
                    print(f"   📄 错误: {response.text[:100]}...")
            
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        
        print()
    
    print("=" * 50)
    print(f"🎯 测试结果: {success_count}/{len(test_cases)} 个API正常")
    
    if success_count >= len(test_cases) * 0.8:
        print("✅ API访问基本正常，可以启动exe文件管理工具")
        return True
    else:
        print("⚠️ 多个API仍有问题")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 验证移除认证后的API访问")
        print("🔧 功能: 测试管理API是否可以无认证访问")
        print("💡 服务器: 198.23.135.176:5000")
        print()
        
        if test_api_without_auth():
            print("\n🎉 API访问测试通过！")
            print("\n📋 现在你可以:")
            print("1. 启动exe文件管理工具")
            print("2. 上传exe文件进行版本管理")
            print("3. 下载和管理exe版本")
            print("\n💡 建议: 运行 'python exe文件管理工具.py' 开始使用")
        else:
            print("\n⚠️ API访问仍有问题")
            print("建议检查服务器状态和配置")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
