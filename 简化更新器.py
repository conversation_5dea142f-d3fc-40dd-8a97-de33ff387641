#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的更新器 - 只显示进度，自动重启
"""

import tkinter as tk
from tkinter import ttk
import requests
import os
import sys
import subprocess
import threading
import time
import tempfile
import shutil
from pathlib import Path

class SimpleUpdateDialog:
    """简化的更新对话框"""
    
    def __init__(self, parent, update_info):
        self.parent = parent
        self.update_info = update_info
        self.dialog = None
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar()
        self.result = False
        
    def show(self):
        """显示更新对话框"""
        # 创建对话框
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("正在更新")
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 设置图标（与主程序相同）
        self.set_dialog_icon()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (150 // 2)
        self.dialog.geometry(f"+{x}+{y}")

        # 设置背景
        self.dialog.configure(bg="#ffffff")
        
        # 标题
        title_label = tk.Label(
            self.dialog,
            text="🔄 正在更新到最新版本",
            font=("微软雅黑", 12, "bold"),
            bg="#ffffff",
            fg="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # 状态文本
        self.status_label = tk.Label(
            self.dialog,
            textvariable=self.status_var,
            font=("微软雅黑", 10),
            bg="#ffffff",
            fg="#7f8c8d"
        )
        self.status_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress_bar = ttk.Progressbar(
            self.dialog,
            variable=self.progress_var,
            maximum=100,
            length=300,
            mode='determinate'
        )
        self.progress_bar.pack(pady=(0, 20))
        
        # 初始状态
        self.status_var.set("正在准备下载...")
        self.progress_var.set(0)
        
        # 禁用关闭按钮
        self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # 自动开始更新
        self.dialog.after(1000, self.start_update)
        
        # 等待对话框关闭
        self.dialog.wait_window()
        return self.result

    def set_dialog_icon(self):
        """设置对话框图标，与主程序保持一致"""
        try:
            # 尝试多种图标路径
            icon_paths = []

            # 1. 检查是否是打包环境
            if getattr(sys, 'frozen', False):
                # 打包环境下的图标路径
                exe_dir = os.path.dirname(sys.executable)
                icon_paths.append(os.path.join(exe_dir, "icon.ico"))

                # PyInstaller临时目录
                import tempfile
                temp_dir = tempfile._get_default_tempdir()
                for mei_dir in ["_MEI", "_MEIxxxx"]:
                    temp_icon = os.path.join(temp_dir, mei_dir, "icon.ico")
                    icon_paths.append(temp_icon)
            else:
                # 开发环境下的图标路径
                base_dir = os.path.dirname(os.path.abspath(__file__))
                icon_paths.append(os.path.join(base_dir, "icon.ico"))

            # 2. 检查应用数据目录
            try:
                import os
                app_data = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
                icon_paths.append(os.path.join(app_data, "icon.ico"))
            except:
                pass

            # 3. 检查用户目录
            icon_paths.append(os.path.join(os.path.expanduser("~"), "icon.ico"))

            # 4. 检查当前目录
            icon_paths.append("icon.ico")

            # 尝试设置图标
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    try:
                        self.dialog.iconbitmap(icon_path)
                        return True
                    except Exception as e:
                        continue

            # 如果没有找到图标文件，尝试从父窗口复制图标
            if self.parent:
                try:
                    # 获取父窗口的图标
                    parent_icon = self.parent.iconbitmap()
                    if parent_icon:
                        self.dialog.iconbitmap(parent_icon)
                        return True
                except:
                    pass

        except Exception as e:
            pass

        return False
    
    def start_update(self):
        """开始更新"""
        def update_thread():
            try:
                # 下载更新
                success = self.download_update()
                if success:
                    self.result = True
                    # 在主线程中关闭对话框
                    self.dialog.after(0, self.dialog.destroy)
                else:
                    # 更新失败
                    self.dialog.after(0, lambda: self.status_var.set("❌ 更新失败"))
                    self.dialog.after(3000, self.dialog.destroy)
            except Exception as e:
                self.dialog.after(0, lambda: self.status_var.set(f"❌ 更新出错: {str(e)}"))
                self.dialog.after(3000, self.dialog.destroy)
        
        # 在后台线程中执行更新
        thread = threading.Thread(target=update_thread)
        thread.daemon = True
        thread.start()
    
    def download_update(self):
        """下载更新文件"""
        try:
            url = "http://198.23.135.176:5000/update/download"
            params = {
                'key': 'ADMIN_BYPASS',
                'device_id': 'ADMIN-DEVICE-001',
                'version': self.update_info.get('version', '2.1.1')
            }
            
            # 更新状态
            self.dialog.after(0, lambda: self.status_var.set("正在连接服务器..."))
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, f"amazon_blueprint_update.exe")
            
            # 下载文件
            max_retries = 3
            for retry in range(max_retries):
                try:
                    response = requests.get(
                        url, 
                        params=params, 
                        stream=True, 
                        timeout=(15, 600)
                    )
                    response.raise_for_status()
                    break
                except Exception as e:
                    if retry == max_retries - 1:
                        raise e
                    self.dialog.after(0, lambda r=retry+1: self.status_var.set(f"连接失败，重试中... ({r}/{max_retries})"))
                    time.sleep(2)
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            self.dialog.after(0, lambda: self.status_var.set("正在下载更新文件..."))
            
            # 下载文件
            with open(temp_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=65536):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 更新进度
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            self.dialog.after(0, lambda p=progress: self.progress_var.set(p))
                            
                            # 更新状态文本
                            mb_downloaded = downloaded_size // 1024 // 1024
                            mb_total = total_size // 1024 // 1024
                            self.dialog.after(0, lambda d=mb_downloaded, t=mb_total: 
                                            self.status_var.set(f"正在下载... {d}MB/{t}MB"))
            
            # 安装更新
            self.dialog.after(0, lambda: self.status_var.set("正在安装更新..."))
            self.dialog.after(0, lambda: self.progress_var.set(100))
            
            # 替换当前程序
            current_exe = sys.executable if getattr(sys, 'frozen', False) else __file__
            if self.install_update(temp_file, current_exe):
                self.dialog.after(0, lambda: self.status_var.set("✅ 更新完成，正在重启..."))
                time.sleep(1)
                return True
            else:
                return False
                
        except Exception as e:
            return False
    
    def install_update(self, temp_file, current_exe):
        """安装更新"""
        try:
            # 如果是exe程序
            if getattr(sys, 'frozen', False):
                current_exe = sys.executable
                backup_exe = current_exe + ".backup"
                
                # 备份当前程序
                if os.path.exists(backup_exe):
                    os.remove(backup_exe)
                shutil.copy2(current_exe, backup_exe)
                
                # 替换程序文件
                shutil.copy2(temp_file, current_exe)
                
                # 启动新程序
                subprocess.Popen([current_exe], cwd=os.path.dirname(current_exe))
                
                # 延迟退出当前程序
                self.dialog.after(2000, lambda: os._exit(0))
                
                return True
            else:
                # 开发模式，只是提示
                return True
                
        except Exception as e:
            return False

def check_and_update_simple(parent_window, current_version="2.1.0"):
    """简化的更新检查"""
    try:
        # 检查更新
        url = "http://198.23.135.176:5000/update/check"
        params = {
            'key': 'ADMIN_BYPASS',
            'device_id': 'ADMIN-DEVICE-001',
            'current_version': current_version
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get('success') and result.get('has_update'):
            # 有更新，直接显示简化对话框
            update_info = {
                'version': result.get('version', '2.1.1'),
                'has_update': True
            }
            
            dialog = SimpleUpdateDialog(parent_window, update_info)
            return dialog.show()
        
        return False
        
    except Exception as e:
        return False

def main():
    """测试函数"""
    root = tk.Tk()
    root.title("更新测试")
    root.geometry("300x200")
    
    def test_update():
        check_and_update_simple(root)
    
    test_button = tk.Button(
        root,
        text="🔄 测试简化更新",
        command=test_update,
        font=("微软雅黑", 12),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10
    )
    test_button.pack(expand=True)
    
    root.mainloop()

if __name__ == "__main__":
    main()
