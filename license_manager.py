import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
from datetime import datetime

class LicenseManager:
    def __init__(self, root):
        self.root = root
        self.root.title("激活码管理系统")
        self.root.geometry("800x600")
        
        # 服务器地址
        self.server_url = "http://198.23.135.176:5000"
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 添加服务器路径配置信息
        server_frame = ttk.LabelFrame(self.main_frame, text="服务器配置", padding="5")
        server_frame.pack(fill=tk.X, pady=5)
        
        # 显示程序文件路径
        ttk.Label(server_frame, text="程序文件路径: /opt/license_manager/").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(server_frame, text="价格程序: 历史价格7.py").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(server_frame, text="采集程序: 采集8.py").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(server_frame, text="筛品程序: 筛品终极版1.py").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(server_frame, text="专利程序: 专利1.py").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(server_frame, text="欧陆扩展: oalur-extension-V1.9.3_3.zip").grid(row=5, column=0, sticky=tk.W, padx=5, pady=2)
        
        # 创建标签页
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 生成激活码标签页
        self.generate_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.generate_frame, text="生成激活码")
        
        # 管理激活码标签页
        self.manage_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.manage_frame, text="管理激活码")
        
        # 激活码列表标签页
        self.list_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.list_frame, text="激活码列表")
        
        # 初始化界面
        self.init_generate_tab()
        self.init_manage_tab()
        self.init_list_tab()
        
    def init_generate_tab(self):
        # 有效期设置
        ttk.Label(self.generate_frame, text="有效期(天):").grid(row=0, column=0, padx=5, pady=5)
        self.days_var = tk.StringVar(value="30")
        ttk.Entry(self.generate_frame, textvariable=self.days_var, width=10).grid(row=0, column=1, padx=5, pady=5)
        
        # 数量设置
        ttk.Label(self.generate_frame, text="生成数量:").grid(row=1, column=0, padx=5, pady=5)
        self.quantity_var = tk.StringVar(value="1")
        ttk.Entry(self.generate_frame, textvariable=self.quantity_var, width=10).grid(row=1, column=1, padx=5, pady=5)
        
        # 权限级别设置
        ttk.Label(self.generate_frame, text="权限级别:").grid(row=2, column=0, padx=5, pady=5)
        self.level_var = tk.StringVar(value="1")
        level_frame = ttk.Frame(self.generate_frame)
        level_frame.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Radiobutton(level_frame, text="1级(仅采集)", variable=self.level_var, value="1").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(level_frame, text="2级(采集+筛品)", variable=self.level_var, value="2").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(level_frame, text="3级(采集+筛品+价格)", variable=self.level_var, value="3").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(level_frame, text="4级(采集+筛品+价格+专利)", variable=self.level_var, value="4").pack(side=tk.LEFT, padx=5)
        
        # 生成按钮
        ttk.Button(self.generate_frame, text="生成激活码", command=self.generate_keys).grid(row=4, column=0, columnspan=2, pady=10)
        
        # 结果显示区域
        self.result_text = tk.Text(self.generate_frame, height=10, width=50)
        self.result_text.grid(row=5, column=0, columnspan=2, pady=10)
        
    def init_manage_tab(self):
        # 激活码输入
        ttk.Label(self.manage_frame, text="激活码:").grid(row=0, column=0, padx=5, pady=5)
        self.key_var = tk.StringVar()
        ttk.Entry(self.manage_frame, textvariable=self.key_var, width=40).grid(row=0, column=1, padx=5, pady=5)
        
        # 查询按钮
        ttk.Button(self.manage_frame, text="查询", command=self.query_key).grid(row=0, column=2, padx=5, pady=5)
        
        # 操作按钮
        button_frame = ttk.Frame(self.manage_frame)
        button_frame.grid(row=1, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="延长有效期", command=lambda: self.modify_key("extend")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="使失效", command=lambda: self.modify_key("revoke")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置设备", command=self.reset_device).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除激活码", command=self.delete_key).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置使用次数", command=self.reset_usage_count).pack(side=tk.LEFT, padx=5)
        
        # 延长天数输入
        ttk.Label(self.manage_frame, text="延长天数:").grid(row=3, column=0, padx=5, pady=5)
        self.extend_days_var = tk.StringVar(value="30")
        ttk.Entry(self.manage_frame, textvariable=self.extend_days_var, width=10).grid(row=3, column=1, padx=5, pady=5)
        
        # 修改权限级别
        ttk.Label(self.manage_frame, text="修改权限级别:").grid(row=4, column=0, padx=5, pady=5)
        self.modify_level_var = tk.StringVar(value="1")
        level_frame = ttk.Frame(self.manage_frame)
        level_frame.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Radiobutton(level_frame, text="1级(仅采集)", variable=self.modify_level_var, value="1").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(level_frame, text="2级(采集+筛品)", variable=self.modify_level_var, value="2").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(level_frame, text="3级(采集+筛品+价格)", variable=self.modify_level_var, value="3").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(level_frame, text="4级(采集+筛品+价格+专利)", variable=self.modify_level_var, value="4").pack(side=tk.LEFT, padx=5)
        
        ttk.Button(self.manage_frame, text="修改权限", command=lambda: self.modify_key("change_level")).grid(row=4, column=2, padx=5, pady=5)
        
        # 创建信息显示框架
        info_frame = ttk.LabelFrame(self.manage_frame, text="激活码详细信息", padding="5")
        info_frame.grid(row=5, column=0, columnspan=3, pady=10, sticky=tk.W+tk.E)
        
        # 添加详细信息标签
        info_labels_frame = ttk.Frame(info_frame)
        info_labels_frame.pack(fill=tk.X, expand=True, padx=5, pady=5)
        
        # 第一列：基本信息
        basic_frame = ttk.Frame(info_labels_frame)
        basic_frame.pack(side=tk.LEFT, fill=tk.Y, expand=True)
        
        self.status_label = ttk.Label(basic_frame, text="状态: 未知")
        self.status_label.pack(anchor=tk.W, pady=2)
        
        self.expire_label = ttk.Label(basic_frame, text="过期日期: 未知")
        self.expire_label.pack(anchor=tk.W, pady=2)
        
        self.permission_label = ttk.Label(basic_frame, text="权限级别: 未知")
        self.permission_label.pack(anchor=tk.W, pady=2)
        
        # 第二列：设备信息
        device_frame = ttk.Frame(info_labels_frame)
        device_frame.pack(side=tk.LEFT, fill=tk.Y, expand=True)
        
        self.device_id_label = ttk.Label(device_frame, text="设备ID: 未绑定")
        self.device_id_label.pack(anchor=tk.W, pady=2)
        
        self.activated_at_label = ttk.Label(device_frame, text="激活时间: 未知")
        self.activated_at_label.pack(anchor=tk.W, pady=2)
        
        self.last_used_label = ttk.Label(device_frame, text="最后使用时间: 未知")
        self.last_used_label.pack(anchor=tk.W, pady=2)
        
        # 使用次数信息
        self.usage_frame = ttk.Frame(info_labels_frame)
        self.usage_frame.pack(side=tk.LEFT, fill=tk.Y, expand=True)
        
        self.today_usage_label = ttk.Label(self.usage_frame, text="今日使用次数: 0/20")
        self.today_usage_label.pack(anchor=tk.W, pady=2)
        
        self.total_usage_label = ttk.Label(self.usage_frame, text="总使用次数: 0")
        self.total_usage_label.pack(anchor=tk.W, pady=2)
        
        # 当前激活码对象
        self.current_license_info = None
        
        # 添加说明文本
        note_label = ttk.Label(self.manage_frame, text="注意: 重置设备将会自动扣除1天的有效期", foreground="red")
        note_label.grid(row=6, column=0, columnspan=3, pady=5)
        
    def init_list_tab(self):
        # 创建按钮框架
        button_frame = ttk.Frame(self.list_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        # 刷新按钮
        refresh_button = ttk.Button(button_frame, text="刷新激活码列表", command=self.refresh_license_list)
        refresh_button.pack(side=tk.LEFT, padx=5)
        
        # 删除选中按钮
        delete_selected_button = ttk.Button(button_frame, text="删除选中激活码", command=self.delete_selected_licenses)
        delete_selected_button.pack(side=tk.LEFT, padx=5)
        
        # 创建表格
        columns = ('key', 'status', 'expire_date', 'remaining_days', 'device_id', 
                  'permission_level', 'today_usage_count', 'total_usage_count')
        
        self.license_table = ttk.Treeview(self.list_frame, columns=columns, show='headings')
        
        # 定义表头
        self.license_table.heading('key', text='激活码')
        self.license_table.heading('status', text='状态')
        self.license_table.heading('expire_date', text='过期日期')
        self.license_table.heading('remaining_days', text='剩余天数')
        self.license_table.heading('device_id', text='设备ID')
        self.license_table.heading('permission_level', text='权限级别')
        self.license_table.heading('today_usage_count', text='今日使用次数')
        self.license_table.heading('total_usage_count', text='总使用次数')
        
        # 设置列宽
        self.license_table.column('key', width=120)
        self.license_table.column('status', width=60)
        self.license_table.column('expire_date', width=80)
        self.license_table.column('remaining_days', width=60)
        self.license_table.column('device_id', width=120)
        self.license_table.column('permission_level', width=60)
        self.license_table.column('today_usage_count', width=90)
        self.license_table.column('total_usage_count', width=90)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.list_frame, orient=tk.VERTICAL, command=self.license_table.yview)
        self.license_table.configure(yscroll=scrollbar.set)
        
        # 放置表格和滚动条
        self.license_table.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        scrollbar.pack(fill=tk.Y, side=tk.RIGHT)
        
        # 绑定双击事件
        self.license_table.bind("<Double-1>", self.on_license_double_click)
        
        # 创建右键菜单
        self.license_context_menu = tk.Menu(self.root, tearoff=0)
        self.license_context_menu.add_command(label="查看/管理", command=self.view_selected_license)
        self.license_context_menu.add_command(label="删除激活码", command=self.delete_selected_license)
        
        # 绑定右键点击事件
        self.license_table.bind("<Button-3>", self.show_license_context_menu)
        
        # 初始加载激活码列表
        self.refresh_license_list()
    
    def show_license_context_menu(self, event):
        """显示激活码右键菜单"""
        # 选中点击的项
        item = self.license_table.identify_row(event.y)
        if item:
            self.license_table.selection_set(item)
            self.license_context_menu.post(event.x_root, event.y_root)
    
    def view_selected_license(self):
        """查看选中的激活码"""
        selected_item = self.license_table.focus()
        if selected_item:
            item_data = self.license_table.item(selected_item)
            if 'values' in item_data:
                key = item_data['values'][0]
                self.key_var.set(key)
                self.notebook.select(1)  # 切换到管理标签页
                self.query_key()  # 查询该激活码的详细信息
    
    def delete_selected_license(self):
        """删除选中的激活码"""
        selected_item = self.license_table.focus()
        if selected_item:
            item_data = self.license_table.item(selected_item)
            if 'values' in item_data:
                key = item_data['values'][0]
                # 询问确认
                confirm = messagebox.askokcancel(
                    "确认删除激活码",
                    f"确定要删除激活码: {key} 吗？此操作不可逆。",
                    icon="warning"
                )
                
                if confirm:
                    self.delete_license(key)
    
    def delete_selected_licenses(self):
        """删除多个选中的激活码"""
        selected_items = self.license_table.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择要删除的激活码")
            return
            
        # 获取选中的激活码
        keys = []
        for item in selected_items:
            item_data = self.license_table.item(item)
            if 'values' in item_data:
                keys.append(item_data['values'][0])
                
        # 询问确认
        confirm = messagebox.askokcancel(
            "确认批量删除",
            f"确定要删除选中的 {len(keys)} 个激活码吗？此操作不可逆。",
            icon="warning"
        )
        
        if confirm:
            # 执行批量删除
            success_count = 0
            fail_count = 0
            
            for key in keys:
                try:
                    response = requests.post(
                        f"{self.server_url}/license/delete",
                        json={"key": key}
                    )
                    
                    if response.status_code == 200 and response.json().get("success", False):
                        success_count += 1
                    else:
                        fail_count += 1
                        
                except Exception:
                    fail_count += 1
                    
            # 刷新列表
            self.refresh_license_list()
            
            # 显示结果
            messagebox.showinfo(
                "批量删除结果", 
                f"成功删除: {success_count} 个激活码\n"
                f"失败: {fail_count} 个激活码"
            )
    
    def on_license_double_click(self, event):
        """双击激活码时的操作"""
        self.view_selected_license()
    
    def refresh_license_list(self):
        """刷新激活码列表"""
        try:
            # 清空现有数据
            for row in self.license_table.get_children():
                self.license_table.delete(row)
                
            # 获取激活码列表
            response = requests.get(f"{self.server_url}/license/list")
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    for license_info in data["licenses"]:
                        key = license_info["key"]
                        status = license_info["status"]
                        expire_date = license_info["expire_date"]
                        remaining_days = license_info.get("remaining_days", "未知")
                        device_id = license_info.get("device_id", "未绑定")
                        if device_id and len(device_id) > 20:  # 截断长设备ID
                            device_id = device_id[:20] + "..."
                        permission_level = license_info.get("permission_level", 1)
                        today_usage_count = license_info.get("today_usage_count", 0)
                        total_usage_count = license_info.get("total_usage_count", 0)
                        
                        self.license_table.insert('', 'end', values=(
                            key, status, expire_date, remaining_days, device_id,
                            permission_level, today_usage_count, total_usage_count
                        ))
                        
            else:
                messagebox.showerror("错误", "服务器请求失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"刷新激活码列表时出错: {str(e)}")

    def generate_keys(self):
        try:
            days = int(self.days_var.get())
            quantity = int(self.quantity_var.get())
            permission_level = int(self.level_var.get())
            
            response = requests.post(f"{self.server_url}/license/generate", 
                                  json={
                                      "expire_days": days, 
                                      "quantity": quantity,
                                      "permission_level": permission_level
                                  })
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    self.result_text.delete(1.0, tk.END)
                    for key in data["keys"]:
                        self.result_text.insert(tk.END, f"{key}\n")
                    messagebox.showinfo("成功", f"成功生成 {quantity} 个激活码，权限级别: {permission_level}")
                else:
                    messagebox.showerror("错误", data["message"])
            else:
                messagebox.showerror("错误", "服务器响应错误")
                
        except Exception as e:
            messagebox.showerror("错误", f"生成激活码时出错: {str(e)}")
            
    def query_key(self):
        try:
            key = self.key_var.get().strip()
            if not key:
                messagebox.showwarning("警告", "请输入激活码")
                return
                
            response = requests.get(f"{self.server_url}/license/info", params={"key": key})
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    info = data["info"]
                    # 保存当前激活码信息
                    self.current_license_info = info
                    
                    # 重置信息标签
                    self._reset_info_labels()
                    
                    # 更新基本信息
                    self.status_label.config(text=f"状态: {info.get('status', '未知')}")
                    self.expire_label.config(text=f"过期日期: {info.get('expire_date', '未知')}")
                    
                    # 计算剩余天数
                    if 'expire_date' in info:
                        expire_date = datetime.strptime(info['expire_date'], "%Y%m%d")
                        remaining_days = (expire_date - datetime.now()).days
                        remaining_text = f"剩余天数: {remaining_days}" if remaining_days > 0 else "已过期"
                        self.expire_label.config(text=f"过期日期: {info['expire_date']} ({remaining_text})")
                    
                    # 显示权限级别
                    permission_level = info.get('permission_level', 1)
                    level_desc = {
                        1: "1级(仅采集)",
                        2: "2级(采集+筛品)",
                        3: "3级(采集+筛品+价格)",
                        4: "4级(采集+筛品+价格+专利)"
                    }.get(permission_level, f"{permission_level}级")
                    self.permission_label.config(text=f"权限级别: {level_desc}")
                    
                    # 更新设备信息
                    if 'device_id' in info:
                        self.device_id_label.config(text=f"设备ID: {info['device_id']}")
                    if 'activated_at' in info:
                        self.activated_at_label.config(text=f"激活时间: {info['activated_at']}")
                    if 'last_used_time' in info and info['last_used_time']:
                        self.last_used_label.config(text=f"最后使用时间: {info['last_used_time']}")
                    
                    # 更新使用次数信息
                    daily_limit = info.get('daily_limit', 20)
                    today_usage_count = info.get('today_usage_count', 0)
                    total_usage_count = info.get('total_usage_count', 0)
                    
                    self.today_usage_label.config(text=f"今日使用次数: {today_usage_count}/{daily_limit}")
                    self.total_usage_label.config(text=f"总使用次数: {total_usage_count}")
                    
                    # 设置当前权限级别为激活码的权限级别
                    self.modify_level_var.set(str(permission_level))
                else:
                    messagebox.showerror("错误", data["message"])
            else:
                messagebox.showerror("错误", "服务器响应错误")
                
        except Exception as e:
            messagebox.showerror("错误", f"查询激活码时出错: {str(e)}")
            
    def _reset_info_labels(self):
        """重置信息标签为默认状态"""
        self.status_label.config(text="状态: 未知")
        self.expire_label.config(text="过期日期: 未知")
        self.permission_label.config(text="权限级别: 未知")
        self.device_id_label.config(text="设备ID: 未绑定")
        self.activated_at_label.config(text="激活时间: 未知")
        self.last_used_label.config(text="最后使用时间: 未知")
        self.today_usage_label.config(text="今日使用次数: 0/20")
        self.total_usage_label.config(text="总使用次数: 0")
        
    def reset_usage_count(self):
        """重置激活码的使用次数"""
        try:
            key = self.key_var.get().strip()
            if not key:
                messagebox.showwarning("警告", "请输入激活码")
                return
                
            # 确认对话框
            confirm = messagebox.askokcancel(
                "确认重置使用次数",
                "确定要重置该激活码的今日使用次数吗？\n这将允许客户在今天继续使用软件。",
                icon="warning"
            )
            
            if not confirm:
                return
                
            data = {"key": key, "action": "reset_usage"}
            
            response = requests.post(f"{self.server_url}/license/modify", json=data)
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    messagebox.showinfo("成功", "重置使用次数成功")
                    self.query_key()  # 刷新信息
                else:
                    messagebox.showerror("错误", data["message"])
            else:
                messagebox.showerror("错误", "服务器响应错误")
                
        except Exception as e:
            messagebox.showerror("错误", f"重置使用次数时出错: {str(e)}")

    def modify_key(self, action):
        try:
            key = self.key_var.get().strip()
            if not key:
                messagebox.showwarning("警告", "请输入激活码")
                return
                
            data = {"key": key, "action": action}
            if action == "extend":
                days = int(self.extend_days_var.get())
                data["days"] = days
            elif action == "change_level":
                permission_level = int(self.modify_level_var.get())
                data["permission_level"] = permission_level
                
            response = requests.post(f"{self.server_url}/license/modify", json=data)
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    action_desc = {
                        "extend": "延长有效期",
                        "revoke": "撤销激活码",
                        "reset": "重置设备绑定",
                        "change_level": f"修改权限级别为 {self.modify_level_var.get()} 级"
                    }.get(action, action)
                    messagebox.showinfo("成功", f"{action_desc}操作成功")
                    self.query_key()  # 刷新信息
                else:
                    messagebox.showerror("错误", data["message"])
            else:
                messagebox.showerror("错误", "服务器响应错误")
                
        except Exception as e:
            messagebox.showerror("错误", f"修改激活码时出错: {str(e)}")

    def reset_device(self):
        """重置设备绑定并提醒用户会扣除1天有效期"""
        key = self.key_var.get().strip()
        if not key:
            messagebox.showwarning("警告", "请输入激活码")
            return
            
        # 警告确认对话框
        confirm = messagebox.askokcancel(
            "确认重置设备",
            "重置设备将会扣除1天的有效期，确定要继续吗？\n\n此操作用于允许客户在更换设备时继续使用软件。",
            icon="warning"
        )
        
        if confirm:
            self.modify_key("reset")
            
    def delete_license(self, key=None):
        """删除激活码"""
        try:
            if key is None:
                key = self.key_var.get().strip()
                
            if not key:
                messagebox.showerror("输入错误", "请输入激活码")
                return
                
            # 发送请求
            response = requests.post(
                f"{self.server_url}/license/delete",
                json={"key": key}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    messagebox.showinfo("成功", data["message"])
                    # 重置信息标签
                    self._reset_info_labels()
                else:
                    messagebox.showerror("错误", data["message"])
            else:
                messagebox.showerror("错误", "服务器响应错误")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除激活码时出错: {str(e)}")
            
    def delete_key(self):
        """删除当前激活码"""
        key = self.key_var.get().strip()
        if not key:
            messagebox.showerror("输入错误", "请输入激活码")
            return
            
        # 询问确认
        confirm = messagebox.askokcancel(
            "确认删除激活码",
            f"确定要删除激活码: {key} 吗？此操作不可逆。",
            icon="warning"
        )
        
        if confirm:
            self.delete_license(key)

if __name__ == "__main__":
    root = tk.Tk()
    app = LicenseManager(root)
    root.mainloop() 