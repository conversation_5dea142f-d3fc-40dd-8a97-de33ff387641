#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细诊断下载问题 - 找出真正的问题所在
"""

import sys
import time
import os
import tempfile
import requests
from auto_updater import AutoUpdater

def detailed_download_test():
    """详细的下载测试"""
    print("🔍 详细下载诊断")
    print("=" * 60)
    
    try:
        # 1. 创建AutoUpdater实例
        print("📱 创建AutoUpdater实例...")
        updater = AutoUpdater(
            current_version="2.1.0",
            license_server_url="http://198.23.135.176:5000",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        print("✅ AutoUpdater实例创建成功")
        
        # 2. 检查更新
        print("\n🔍 检查更新...")
        update_info = updater.check_for_updates()
        
        if not update_info:
            print("📦 当前已是最新版本")
            return
        
        print(f"✅ 发现更新: {update_info.get('version', 'Unknown')}")
        file_size = update_info.get('file_size', 0)
        print(f"📁 预期文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
        
        # 3. 手动下载测试
        print("\n🧪 手动下载测试...")
        url = f"{updater.server_url.rstrip('/')}/update/download"
        params = {
            'key': updater.license_key,
            'device_id': updater.device_id,
            'version': update_info.get('version')
        }
        
        print(f"URL: {url}")
        print(f"参数: {params}")
        
        # 创建临时文件
        temp_file = os.path.join(tempfile.gettempdir(), f"manual_test_{int(time.time())}.exe")
        print(f"临时文件: {temp_file}")
        
        # 开始下载
        print("\n🚀 开始手动下载...")
        
        try:
            response = requests.get(url, params=params, stream=True, timeout=(30, 1800))
            response.raise_for_status()
            
            # 获取实际文件大小
            actual_total = int(response.headers.get('Content-Length', 0))
            print(f"📊 服务器返回大小: {actual_total:,} 字节")
            
            if actual_total != file_size:
                print(f"⚠️ 大小不匹配! 预期: {file_size:,}, 实际: {actual_total:,}")
            
            # 下载文件
            downloaded = 0
            chunk_size = 32 * 1024  # 32KB
            last_report = 0
            
            with open(temp_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # 每下载5MB报告一次
                        if downloaded - last_report >= 5 * 1024 * 1024:
                            progress = (downloaded / actual_total) * 100 if actual_total > 0 else 0
                            print(f"📥 已下载: {downloaded:,} 字节 ({progress:.1f}%)")
                            last_report = downloaded
                
                # 强制刷新
                f.flush()
                os.fsync(f.fileno())
            
            # 验证下载结果
            final_size = os.path.getsize(temp_file)
            print(f"\n📋 下载完成!")
            print(f"📁 最终文件大小: {final_size:,} 字节")
            
            if actual_total > 0:
                completion = (final_size / actual_total) * 100
                print(f"📊 完成率: {completion:.2f}%")
                
                if completion >= 99.5:
                    print("✅ 下载成功 (>99.5%)")
                    result = "SUCCESS"
                elif completion >= 95.0:
                    print("⚠️ 下载基本成功 (>95%)")
                    result = "PARTIAL"
                else:
                    print("❌ 下载失败 (<95%)")
                    result = "FAILED"
            else:
                print("⚠️ 无法验证完成率")
                result = "UNKNOWN"
            
            # 清理文件
            try:
                os.remove(temp_file)
                print("🗑️ 已清理临时文件")
            except:
                pass
            
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return "NETWORK_ERROR"
        
        except Exception as e:
            print(f"❌ 下载过程出错: {e}")
            return "DOWNLOAD_ERROR"
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return "TEST_ERROR"

def test_updater_download():
    """测试AutoUpdater的下载方法"""
    print("\n" + "=" * 60)
    print("🧪 测试AutoUpdater下载方法")
    print("=" * 60)
    
    try:
        # 创建AutoUpdater实例
        updater = AutoUpdater(
            current_version="2.1.0",
            license_server_url="http://198.23.135.176:5000",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        # 检查更新
        update_info = updater.check_for_updates()
        if not update_info:
            print("📦 无更新可测试")
            return "NO_UPDATE"
        
        # 进度回调
        def progress_callback(progress):
            print(f"\r📥 下载进度: {progress:.1f}%", end="", flush=True)
        
        print("🚀 使用AutoUpdater下载...")
        downloaded_file = updater.download_update(update_info, progress_callback)
        print()  # 换行
        
        if downloaded_file and os.path.exists(downloaded_file):
            actual_size = os.path.getsize(downloaded_file)
            expected_size = update_info.get('file_size', 0)
            
            print(f"✅ AutoUpdater下载成功!")
            print(f"📁 文件路径: {downloaded_file}")
            print(f"📊 文件大小: {actual_size:,} 字节")
            
            if expected_size > 0:
                completion = (actual_size / expected_size) * 100
                print(f"📈 完成率: {completion:.2f}%")
            
            # 清理文件
            try:
                os.remove(downloaded_file)
                print("🗑️ 已清理测试文件")
            except:
                pass
            
            return "SUCCESS"
        else:
            print("❌ AutoUpdater下载失败")
            return "FAILED"
            
    except Exception as e:
        print(f"❌ AutoUpdater测试失败: {e}")
        return "ERROR"

def main():
    """主函数"""
    print("🔍 详细诊断下载问题")
    print("=" * 60)
    
    # 1. 手动下载测试
    manual_result = detailed_download_test()
    
    # 2. AutoUpdater下载测试
    updater_result = test_updater_download()
    
    # 3. 总结
    print("\n" + "=" * 60)
    print("📋 诊断结果总结")
    print("=" * 60)
    
    print(f"🔧 手动下载测试: {manual_result}")
    print(f"🤖 AutoUpdater测试: {updater_result}")
    
    if manual_result == "SUCCESS" and updater_result == "SUCCESS":
        print("\n🎉 所有测试都成功!")
        print("✅ 下载功能正常工作")
        print("✅ 99.8%问题已解决")
    elif manual_result == "SUCCESS" and updater_result != "SUCCESS":
        print("\n⚠️ 手动下载成功，但AutoUpdater有问题")
        print("🔧 需要检查AutoUpdater的download_update方法")
    elif manual_result != "SUCCESS":
        print("\n❌ 基础网络下载有问题")
        print("🌐 可能是网络连接或服务器问题")
    
    print("\n🚀 建议:")
    if manual_result == "SUCCESS":
        print("1. 网络和服务器正常")
        print("2. 可以正常使用更新功能")
        print("3. 99.8%问题已解决")
    else:
        print("1. 检查网络连接")
        print("2. 检查服务器状态")
        print("3. 可能需要使用其他下载方案")

if __name__ == "__main__":
    main()
