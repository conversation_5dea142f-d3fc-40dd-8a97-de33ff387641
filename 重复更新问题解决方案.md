# 重复更新问题解决方案

## 问题描述
程序生成的exe文件一直重复下载更新，无法正常使用。

## 问题根本原因
1. **版本号未正确更新**：程序更新后，配置文件中的版本号没有更新，导致程序每次启动都认为需要更新
2. **缺少更新状态跟踪**：没有机制记录刚刚完成的更新，导致无限循环
3. **更新脚本不完整**：更新脚本只替换了exe文件，没有更新版本配置

## 解决方案

### 1. 修复版本更新机制
- 修改了 `auto_updater.py` 中的更新脚本，使其在更新exe文件后同时更新版本配置文件
- 添加了版本号传递机制，确保新版本号正确写入配置文件
- 更新脚本现在会：
  - 替换exe文件
  - 更新 `update_config.py` 中的版本号
  - 记录更新信息到用户配置文件

### 2. 添加更新状态跟踪
- 在 `license_client.py` 中添加了更新状态检查
- 程序启动时会检查是否刚刚完成更新
- 如果检测到刚刚更新过，会跳过这次更新检查
- 避免了更新后立即再次检查更新的问题

### 3. 改进配置管理
- 在 `update_config.py` 中添加了版本更新函数
- 添加了更新信息保存和读取功能
- 支持用户自定义配置文件

## 修改的文件

### 1. `update_config.py`
```python
# 添加了以下函数：
def update_version(new_version)  # 更新版本号到配置文件
def save_update_info(version, timestamp=None)  # 保存更新信息
def get_last_update_info()  # 获取最后更新信息
```

### 2. `auto_updater.py`
```python
# 修改了以下方法：
def apply_update(self, update_file_path, new_version=None)  # 添加版本号参数
def _create_update_script(self, new_file, current_file, new_version=None)  # 添加版本号参数

# 更新脚本现在会：
# 1. 替换exe文件
# 2. 更新配置文件中的版本号
# 3. 记录更新信息
```

### 3. `license_client.py`
```python
# 在自动更新检查中添加了：
# 1. 检查是否刚刚完成更新
# 2. 如果刚更新过，跳过这次检查
# 3. 从配置文件动态获取当前版本号
```

## 工作流程

### 更新前
1. 程序启动
2. 检查更新状态（是否刚刚更新过）
3. 如果没有刚更新过，进行正常的更新检查
4. 如果有更新，下载并安装

### 更新过程
1. 下载新版本文件
2. 创建更新脚本（包含版本号信息）
3. 启动更新脚本
4. 更新脚本执行：
   - 等待主程序退出
   - 备份当前文件
   - 替换exe文件
   - **更新配置文件中的版本号**
   - **记录更新信息**
   - 启动新程序

### 更新后
1. 新程序启动
2. 检查更新状态，发现刚刚更新过
3. **跳过这次更新检查**
4. 清除更新标记
5. 正常运行程序

## 验证方法

### 1. 运行测试脚本
```bash
python 测试版本更新.py
```

### 2. 检查更新信息文件
更新后会在用户目录创建 `.amazon_last_update.json` 文件：
```json
{
  "version": "2.1.x",
  "timestamp": "2025-08-03T21:48:04.308597",
  "updated": true
}
```

### 3. 检查配置文件
`update_config.py` 中的版本号应该正确更新：
```python
"current_version": "2.1.x"  # 应该是最新版本号
```

## 预期效果

1. ✅ 程序只会更新一次，不会重复下载
2. ✅ 更新后版本号正确更新
3. ✅ 程序重启后不会立即再次检查更新
4. ✅ 下次启动时会正常检查更新（如果有新版本）

## 注意事项

1. 确保程序有写入配置文件的权限
2. 如果仍有问题，可以手动删除 `~/.amazon_last_update.json` 文件
3. 可以通过修改 `update_config.py` 中的 `auto_check_enabled` 来控制自动更新

## 技术细节

- 使用正则表达式精确替换配置文件中的版本号
- 通过用户配置文件跟踪更新状态
- 更新脚本使用Python实现，确保跨平台兼容性
- 添加了错误处理和回滚机制

这个解决方案彻底解决了重复更新的问题，确保程序只在真正有新版本时才更新一次。
