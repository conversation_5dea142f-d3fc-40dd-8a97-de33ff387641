#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将增强版下载功能应用到主程序
"""

import os
import shutil

def apply_enhanced_download_to_auto_updater():
    """将增强版下载功能应用到auto_updater.py"""
    print("🔧 应用增强版下载功能到 auto_updater.py")
    print("=" * 50)
    
    # 备份原文件
    if os.path.exists("auto_updater.py"):
        shutil.copy2("auto_updater.py", "auto_updater_original.py")
        print("✅ 已备份原文件")
    
    # 读取原文件
    with open("auto_updater.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到download_update方法并替换
    old_method_start = "    def download_update(self, update_info, progress_callback=None):"
    old_method_end = "            return None"
    
    # 新的下载方法
    new_download_method = '''    def download_update(self, update_info, progress_callback=None):
        """
        下载更新文件 - 增强版支持断点续传
        
        Args:
            update_info: 更新信息
            progress_callback: 进度回调函数
            
        Returns:
            str: 下载的文件路径，失败返回None
        """
        try:
            # 检查必要参数
            if not self.license_key or not self.device_id:
                print("缺少授权信息，无法下载更新")
                return None
            
            # 获取下载信息
            version = update_info.get('version')
            file_size = update_info.get('file_size', 0)
            file_hash = update_info.get('file_hash')
            
            if not version:
                print("缺少版本信息")
                return None
            
            # 创建临时文件
            temp_file = os.path.join(tempfile.gettempdir(), f"amazon_blueprint_update_{version}.exe")
            
            # 检查是否有未完成的下载
            downloaded_size = 0
            if os.path.exists(temp_file):
                downloaded_size = os.path.getsize(temp_file)
                print(f"发现未完成下载，已下载 {downloaded_size} 字节")
            
            # 构建下载URL
            url = urljoin(self.server_url, "/update/download")
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': version
            }
            
            max_retries = 10  # 增加重试次数
            retry_count = 0
            
            while retry_count < max_retries:
                try:
                    # 设置断点续传头
                    headers = {}
                    if downloaded_size > 0:
                        headers['Range'] = f'bytes={downloaded_size}-'
                        print(f"使用断点续传，从 {downloaded_size} 字节开始")
                    
                    # 发送请求
                    response = requests.get(
                        url, 
                        params=params, 
                        headers=headers,
                        stream=True,
                        timeout=(60, 3600)  # 1分钟连接，1小时读取
                    )
                    response.raise_for_status()
                    
                    # 检查响应状态
                    if response.status_code not in [200, 206]:
                        raise Exception(f"HTTP错误: {response.status_code}")
                    
                    # 获取文件总大小
                    if response.status_code == 200:
                        # 完整下载
                        actual_file_size = int(response.headers.get('Content-Length', file_size))
                        downloaded_size = 0
                        mode = 'wb'
                    else:  # 206 Partial Content
                        # 断点续传
                        content_range = response.headers.get('Content-Range', '')
                        if content_range:
                            actual_file_size = int(content_range.split('/')[-1])
                        else:
                            actual_file_size = downloaded_size + int(response.headers.get('Content-Length', 0))
                        mode = 'ab'
                    
                    print(f"开始下载，文件大小: {actual_file_size} 字节")
                    
                    # 下载文件
                    with open(temp_file, mode) as f:
                        chunk_size = 64 * 1024  # 64KB chunks
                        
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)
                                
                                # 更新进度
                                if progress_callback and actual_file_size > 0:
                                    progress = (downloaded_size / actual_file_size) * 100
                                    progress_callback(progress)
                    
                    # 检查下载是否完成
                    if downloaded_size >= actual_file_size:
                        print(f"下载完成: {temp_file}")
                        
                        # 验证文件哈希
                        if file_hash and not self._verify_file_hash(temp_file, file_hash):
                            print("文件哈希验证失败")
                            os.remove(temp_file)
                            return None
                        
                        return temp_file
                    else:
                        print(f"下载不完整，继续重试...")
                        retry_count += 1
                        time.sleep(5)
                        
                except (requests.exceptions.ChunkedEncodingError,
                        requests.exceptions.ConnectionError) as e:
                    retry_count += 1
                    wait_time = min(retry_count * 10, 60)  # 最多等待60秒
                    print(f"下载中断 (重试 {retry_count}/{max_retries}): {e}")
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
                except requests.exceptions.Timeout as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise Exception(f"下载超时，已重试{max_retries}次: {str(e)}")
                    print(f"下载超时，正在重试 ({retry_count}/{max_retries})...")
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"下载失败: {e}")
                    return None
            
            print(f"下载失败，已重试 {max_retries} 次")
            return None
            
        except Exception as e:
            print(f"下载更新失败: {e}")
            return None'''
    
    # 找到原方法的位置
    start_pos = content.find(old_method_start)
    if start_pos == -1:
        print("❌ 找不到download_update方法")
        return False
    
    # 找到方法结束位置（下一个方法开始或类结束）
    lines = content[start_pos:].split('\n')
    method_lines = []
    indent_level = None
    
    for i, line in enumerate(lines):
        if i == 0:
            method_lines.append(line)
            continue
            
        # 确定缩进级别
        if indent_level is None and line.strip():
            indent_level = len(line) - len(line.lstrip())
        
        # 如果遇到同级别或更少缩进的非空行，说明方法结束
        if line.strip() and indent_level is not None:
            current_indent = len(line) - len(line.lstrip())
            if current_indent <= 4:  # 类级别的缩进
                break
        
        method_lines.append(line)
    
    old_method = '\n'.join(method_lines)
    
    # 替换方法
    content = content.replace(old_method, new_download_method)
    
    # 写入文件
    with open("auto_updater.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已应用增强版下载功能")
    return True

def test_updated_client():
    """测试更新后的客户端"""
    print("\n🧪 测试更新后的客户端")
    print("=" * 50)
    
    test_code = '''
import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from auto_updater import check_and_update
    
    def test_update():
        """测试更新功能"""
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        result = check_and_update(
            parent_window=None,
            current_version="2.1.0",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        if result:
            messagebox.showinfo("成功", "更新功能测试成功！")
        else:
            messagebox.showinfo("信息", "没有可用更新或用户取消")
        
        root.destroy()
    
    if __name__ == "__main__":
        test_update()
        
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
'''
    
    with open("测试更新后的客户端.py", 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print("✅ 已创建测试文件: 测试更新后的客户端.py")

def main():
    """主函数"""
    print("🚀 应用增强版下载功能到主程序")
    print("=" * 60)
    
    # 应用修复
    if apply_enhanced_download_to_auto_updater():
        print("✅ 修复应用成功")
        
        # 创建测试文件
        test_updated_client()
        
        print("\n" + "=" * 60)
        print("📊 修复完成！")
        print("✅ auto_updater.py 已更新为增强版")
        print("✅ 支持断点续传和网络中断恢复")
        print("✅ 增加了重试次数和超时时间")
        
        print("\n💡 测试方法:")
        print("1. 运行: python license_client.py")
        print("2. 或运行: python 测试更新后的客户端.py")
        print("3. 现在更新应该能够成功完成！")
        
    else:
        print("❌ 修复应用失败")

if __name__ == "__main__":
    main()
