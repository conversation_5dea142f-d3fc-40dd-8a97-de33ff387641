#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速版本修复 - 统一license_client.py中的版本号
"""

import re
import shutil
from datetime import datetime

def fix_version_consistency(target_version="2.1.1"):
    """修复版本一致性"""
    try:
        print(f"🔧 开始修复版本号为: {target_version}")
        
        # 备份原文件
        backup_name = f"license_client.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2('license_client.py', backup_name)
        print(f"✅ 已备份原文件: {backup_name}")
        
        # 读取文件
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复程序版本号 (current_version)
        pattern1 = r'current_version\s*=\s*["\'][^"\']+["\']'
        replacement1 = f'current_version = "{target_version}"'
        content = re.sub(pattern1, replacement1, content)
        print("✅ 已修复程序版本号")
        
        # 修复界面显示版本 (蓝图工具 v2.0)
        pattern2 = r'蓝图工具\s*v[0-9.]+'
        replacement2 = f'蓝图工具 v{target_version}'
        content = re.sub(pattern2, replacement2, content)
        print("✅ 已修复界面显示版本")
        
        # 修复客户端版本 ("client_version": "1.0.0")
        pattern3 = r'"client_version":\s*"[^"]*"'
        replacement3 = f'"client_version": "{target_version}"'
        content = re.sub(pattern3, replacement3, content)
        print("✅ 已修复客户端版本")
        
        # 保存修复后的文件
        with open('license_client.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"🎉 版本修复完成! 所有版本已统一为: {target_version}")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def verify_fix(target_version="2.1.1"):
    """验证修复结果"""
    try:
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查各种版本
        current_versions = re.findall(r'current_version\s*=\s*["\']([^"\']+)["\']', content)
        display_versions = re.findall(r'蓝图工具\s*v([0-9.]+)', content)
        client_versions = re.findall(r'"client_version":\s*"([^"]*)"', content)
        
        print("\n🔍 验证修复结果:")
        print(f"   程序版本: {current_versions}")
        print(f"   显示版本: {display_versions}")
        print(f"   客户端版本: {client_versions}")
        
        # 检查是否都是目标版本
        all_versions = current_versions + display_versions + client_versions
        all_correct = all(v == target_version for v in all_versions)
        
        if all_correct and all_versions:
            print("✅ 所有版本号已统一!")
            return True
        else:
            print("❌ 仍有版本号不一致")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速版本修复工具")
    print("=" * 50)
    
    target_version = "2.1.1"
    
    # 执行修复
    if fix_version_consistency(target_version):
        # 验证修复结果
        if verify_fix(target_version):
            print("\n🎉 修复成功!")
            print("✅ license_client.py 版本已统一")
            print("✅ 现在可以使用智能exe管理工具")
            print("✅ 上传时版本号会正确识别")
        else:
            print("\n⚠️ 修复可能不完整，请检查")
    else:
        print("\n❌ 修复失败")
    
    print("\n📋 下一步:")
    print("1. 运行 python license_client.py 测试")
    print("2. 使用智能exe管理工具上传新版本")
    print("3. 确保版本号一致性")

if __name__ == "__main__":
    main()
