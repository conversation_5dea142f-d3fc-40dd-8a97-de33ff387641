#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动版本更新方案 - 让程序能够自动修改自己的版本号
"""

import os
import re
import shutil

def create_version_updater():
    """创建版本自动更新器"""
    code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本自动更新器 - 更新license_client.py中的版本号
"""

import re
import os
import shutil
from datetime import datetime

def update_version_in_file(filename, new_version):
    """在文件中更新版本号"""
    try:
        # 备份原文件
        backup_name = f"{filename}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(filename, backup_name)
        print(f"✅ 已备份: {backup_name}")
        
        # 读取文件
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换版本号
        pattern = r'current_version\\s*=\\s*["\']([^"\']+)["\']'
        matches = re.findall(pattern, content)
        
        if matches:
            print(f"📦 找到版本号: {matches}")
            
            # 替换所有版本号
            new_content = re.sub(
                pattern,
                f'current_version = "{new_version}"',
                content
            )
            
            # 保存文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 版本号已更新为: {new_version}")
            return True
        else:
            print("❌ 未找到版本号定义")
            return False
            
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def auto_update_version(new_version):
    """自动更新版本号"""
    print(f"🚀 自动更新版本号为: {new_version}")
    
    files_to_update = ['license_client.py']
    
    success_count = 0
    for filename in files_to_update:
        if os.path.exists(filename):
            if update_version_in_file(filename, new_version):
                success_count += 1
        else:
            print(f"⚠️ 文件不存在: {filename}")
    
    if success_count > 0:
        print(f"🎉 成功更新 {success_count} 个文件")
        return True
    else:
        print("❌ 没有文件被更新")
        return False

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        new_version = sys.argv[1]
        auto_update_version(new_version)
    else:
        print("用法: python version_updater.py <新版本号>")
        print("例如: python version_updater.py 2.1.1")
'''
    
    with open('version_updater.py', 'w', encoding='utf-8') as f:
        f.write(code)
    
    print("✅ 已创建 version_updater.py")

def modify_auto_updater_with_version_update():
    """修改auto_updater.py集成版本自动更新"""
    try:
        with open('auto_updater.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找更新成功的位置
        success_pattern = 'print("🎉 终极下载器成功!")'
        
        if success_pattern in content and 'subprocess.call([sys.executable, "version_updater.py"' not in content:
            # 在成功下载后添加版本更新逻辑
            update_code = '''
                    # 自动更新版本号
                    try:
                        import subprocess
                        import sys
                        new_version = update_info.get('version', '2.1.1')
                        print(f"🔄 自动更新版本号为: {new_version}")
                        
                        # 调用版本更新器
                        result = subprocess.call([
                            sys.executable, 
                            "version_updater.py", 
                            new_version
                        ], cwd=os.path.dirname(os.path.abspath(__file__)))
                        
                        if result == 0:
                            print("✅ 版本号自动更新成功")
                        else:
                            print("⚠️ 版本号自动更新失败")
                            
                    except Exception as e:
                        print(f"⚠️ 版本号自动更新出错: {e}")'''
            
            content = content.replace(
                success_pattern,
                success_pattern + update_code
            )
            
            with open('auto_updater.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已修改 auto_updater.py 支持自动版本更新")
            return True
        else:
            print("⚠️ 未找到合适的插入位置或已经修改过")
            return False
            
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        return False

def test_version_updater():
    """测试版本更新器"""
    print("🧪 测试版本更新器...")
    
    # 创建测试文件
    test_content = '''
def some_function():
    current_version = "2.1.0"  # 当前版本号
    return current_version

def another_function():
    current_version = "2.1.0"  # 另一个版本号
    print(current_version)
'''
    
    with open('test_version.py', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    # 测试更新
    import subprocess
    import sys
    
    result = subprocess.call([
        sys.executable, 
        "version_updater.py", 
        "2.1.1"
    ])
    
    if result == 0:
        # 检查结果
        with open('test_version.py', 'r', encoding='utf-8') as f:
            updated_content = f.read()
        
        if '2.1.1' in updated_content:
            print("✅ 版本更新器测试成功")
            # 清理测试文件
            try:
                os.remove('test_version.py')
                # 删除备份文件
                for f in os.listdir('.'):
                    if f.startswith('test_version.py.backup_'):
                        os.remove(f)
            except:
                pass
            return True
        else:
            print("❌ 版本更新器测试失败")
            return False
    else:
        print("❌ 版本更新器执行失败")
        return False

def main():
    """主函数"""
    print("🚀 自动版本更新方案")
    print("=" * 50)
    
    # 1. 创建版本更新器
    print("1️⃣ 创建版本自动更新器...")
    create_version_updater()
    
    # 2. 测试版本更新器
    print("\n2️⃣ 测试版本更新器...")
    if test_version_updater():
        print("   ✅ 测试通过")
    else:
        print("   ❌ 测试失败")
        return
    
    # 3. 修改auto_updater.py
    print("\n3️⃣ 修改auto_updater.py...")
    if modify_auto_updater_with_version_update():
        print("   ✅ 修改成功")
    else:
        print("   ❌ 修改失败")
    
    print("\n" + "=" * 50)
    print("🎉 自动版本更新方案部署完成!")
    print("\n✅ 优势:")
    print("  - 下载更新后自动修改版本号")
    print("  - 无需手动干预")
    print("  - 支持多文件版本更新")
    print("  - 自动备份原文件")
    
    print("\n📁 创建的文件:")
    print("  - version_updater.py (版本更新器)")
    
    print("\n🚀 工作原理:")
    print("1. 下载更新成功后")
    print("2. 自动调用version_updater.py")
    print("3. 自动修改license_client.py中的版本号")
    print("4. 重启后不再提示更新")

if __name__ == "__main__":
    main()
