#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断更新失败原因
"""

import requests
import json
import time
import traceback

def test_basic_connection():
    """测试基本网络连接"""
    print("🌐 测试基本网络连接")
    print("-" * 30)
    
    server_url = "http://198.23.135.176:5000"
    
    try:
        # 测试根路径
        response = requests.get(server_url, timeout=10)
        print(f"✅ 服务器连接成功: {response.status_code}")
        if response.status_code == 200:
            print(f"📄 响应内容: {response.text[:200]}...")
        return True
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

def test_update_check_api():
    """测试更新检查API"""
    print("\n🔍 测试更新检查API")
    print("-" * 30)
    
    url = "http://198.23.135.176:5000/update/check"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'current_version': '2.1.0'
    }
    
    try:
        response = requests.get(url, params=params, timeout=15)
        print(f"📡 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ API响应成功")
                print(f"📊 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success'):
                    if data.get('has_update'):
                        print("🎉 有可用更新")
                        return True, data
                    else:
                        print("ℹ️ 没有可用更新")
                        return True, data
                else:
                    print(f"❌ API返回失败: {data.get('message', '未知错误')}")
                    return False, data
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效JSON: {response.text}")
                return False, None
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        traceback.print_exc()
        return False, None

def test_download_api():
    """测试下载API"""
    print("\n📥 测试下载API")
    print("-" * 30)
    
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': '2.1.1'
    }
    
    try:
        # 只请求前1KB来测试连接
        headers = {'Range': 'bytes=0-1023'}
        response = requests.get(url, params=params, headers=headers, timeout=15)
        
        print(f"📡 下载API状态: {response.status_code}")
        
        if response.status_code == 206:  # Partial Content
            print("✅ 下载API正常，支持断点续传")
            print(f"📦 内容长度: {response.headers.get('Content-Length', '未知')}")
            print(f"📄 内容类型: {response.headers.get('Content-Type', '未知')}")
            return True
        elif response.status_code == 200:
            print("✅ 下载API正常")
            print(f"📦 内容长度: {response.headers.get('Content-Length', '未知')}")
            return True
        elif response.status_code == 404:
            print("❌ 更新文件不存在")
            return False
        else:
            print(f"❌ 下载API失败: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 下载测试异常: {e}")
        return False

def test_download_speed():
    """测试下载速度"""
    print("\n⚡ 测试下载速度")
    print("-" * 30)
    
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': '2.1.1'
    }
    
    try:
        # 下载前100KB测试速度
        headers = {'Range': 'bytes=0-102399'}  # 100KB
        
        start_time = time.time()
        response = requests.get(url, params=params, headers=headers, timeout=30)
        end_time = time.time()
        
        if response.status_code in [200, 206]:
            download_time = end_time - start_time
            data_size = len(response.content)
            speed_kbps = (data_size / 1024) / download_time
            
            print(f"✅ 下载测试成功")
            print(f"📦 下载大小: {data_size / 1024:.1f} KB")
            print(f"⏱️ 下载时间: {download_time:.2f} 秒")
            print(f"🚀 下载速度: {speed_kbps:.1f} KB/s")
            
            if speed_kbps < 10:
                print("⚠️ 下载速度较慢，可能导致超时")
                return False
            else:
                print("✅ 下载速度正常")
                return True
        else:
            print(f"❌ 下载测试失败: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 下载超时")
        return False
    except Exception as e:
        print(f"❌ 下载速度测试异常: {e}")
        return False

def test_full_update_flow():
    """测试完整更新流程"""
    print("\n🔄 测试完整更新流程")
    print("-" * 30)
    
    # 1. 检查更新
    print("1️⃣ 检查更新...")
    success, update_data = test_update_check_api()
    if not success:
        print("❌ 更新检查失败")
        return False
    
    if not update_data or not update_data.get('has_update'):
        print("ℹ️ 没有可用更新，无法测试下载")
        return True
    
    # 2. 测试下载
    print("\n2️⃣ 测试下载...")
    if not test_download_api():
        print("❌ 下载测试失败")
        return False
    
    # 3. 测试下载速度
    print("\n3️⃣ 测试下载速度...")
    if not test_download_speed():
        print("❌ 下载速度测试失败")
        return False
    
    print("\n✅ 完整更新流程测试通过")
    return True

def main():
    """主函数"""
    print("🔧 更新失败诊断工具")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("基本连接", test_basic_connection),
        ("更新检查API", lambda: test_update_check_api()[0]),
        ("下载API", test_download_api),
        ("下载速度", test_download_speed),
        ("完整流程", test_full_update_flow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📈 总体结果: {passed}/{total} 项测试通过")
    
    # 建议
    print("\n💡 问题分析和建议:")
    
    if not results.get("基本连接"):
        print("🔧 服务器连接失败 - 检查网络连接和服务器状态")
    elif not results.get("更新检查API"):
        print("🔧 更新检查API失败 - 检查API认证和服务器配置")
    elif not results.get("下载API"):
        print("🔧 下载API失败 - 检查更新文件是否存在")
    elif not results.get("下载速度"):
        print("🔧 下载速度过慢 - 可能导致超时，建议增加超时时间")
    elif all(results.values()):
        print("🎉 所有测试通过，更新功能应该正常工作")
        print("💡 如果仍然失败，可能是客户端代码问题")

if __name__ == "__main__":
    main()
