#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改Flask应用端口为5000
"""

import paramiko
import sys
import time

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def change_port_to_5000():
    """修改端口为5000"""
    print("🔧 修改Flask应用端口为5000")
    print("=" * 40)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 步骤1: 停止服务
    print("🛑 步骤1: 停止当前服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl stop license-manager")
    if success:
        print("✅ 服务已停止")
    else:
        print(f"⚠️ 停止服务: {error}")
    print()
    
    # 步骤2: 修改端口配置
    print("🔧 步骤2: 修改端口配置...")
    
    # 使用sed命令修改端口
    command = f"""cd {config['deploy_path']} && \
cp license_server.py license_server.py.backup && \
sed -i 's/port=44285/port=5000/g' license_server.py && \
sed -i 's/app.run(host="0.0.0.0", port=44285)/app.run(host="0.0.0.0", port=5000)/g' license_server.py && \
echo "端口修改完成"
"""
    
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], command)
    if success:
        print("✅ 端口配置已修改为5000")
    else:
        print(f"❌ 端口修改失败: {error}")
        return False
    print()
    
    # 步骤3: 添加防火墙规则
    print("🔥 步骤3: 添加防火墙规则...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "firewall-cmd --permanent --add-port=5000/tcp && firewall-cmd --reload")
    if success:
        print("✅ 防火墙规则已添加")
    else:
        print(f"⚠️ 防火墙配置: {error}")
    print()
    
    # 步骤4: 启动服务
    print("🚀 步骤4: 启动服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl start license-manager")
    if success:
        print("✅ 服务已启动")
    else:
        print(f"❌ 服务启动失败: {error}")
        return False
    
    # 等待服务启动
    time.sleep(5)
    print()
    
    # 步骤5: 验证端口
    print("🔍 步骤5: 验证端口监听...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "netstat -tlnp | grep 5000")
    if success and output.strip():
        print("✅ 端口5000正在监听")
        print(f"   {output.strip()}")
    else:
        print("❌ 端口5000未监听")
        # 检查服务状态
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], 
                                               "systemctl status license-manager")
        print(f"   服务状态: {output2[:200]}...")
    print()
    
    # 步骤6: 测试HTTP服务
    print("🌍 步骤6: 测试HTTP服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "curl -s http://localhost:5000/")
    if success and output.strip():
        print("✅ HTTP服务响应正常")
        print(f"   响应: {output[:100]}...")
        return True
    else:
        print("❌ HTTP服务无响应")
        print(f"   错误: {error}")
        return False

def main():
    """主函数"""
    print("🎯 当前状态:")
    print("✅ Flask应用正在运行在端口44285")
    print("🌐 当前可访问: http://**************:44285/")
    print()
    
    choice = input("是否要修改为端口5000？(y/n): ").lower().strip()
    
    if choice == 'y' or choice == 'yes':
        try:
            success = change_port_to_5000()
            
            if success:
                print("\n🎉 端口修改成功！")
                print("🌐 新地址: http://**************:5000/")
                print("🔍 API测试: http://**************:5000/api/check_update")
            else:
                print("\n❌ 端口修改失败")
                print("💡 您仍可以使用原端口: http://**************:44285/")
                
        except Exception as e:
            print(f"❌ 修改过程异常: {e}")
    else:
        print("\n✅ 保持当前配置")
        print("🌐 服务地址: http://**************:44285/")
        print("🔍 API地址: http://**************:44285/api/check_update")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
