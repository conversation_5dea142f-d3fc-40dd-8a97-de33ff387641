#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本修复脚本 - 统一license_client.py中的版本号
"""

import re

def fix_version_consistency(target_version="2.1.1"):
    """修复版本一致性"""
    try:
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        with open('license_client.py.backup_version_fix', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 修复各种版本定义
        fixes = [
            (r'current_version\s*=\s*["']([^"']+)["']', f'current_version = "{target_version}"'),
            (r'(蓝图工具\s*v)([0-9.]+)', f'\\1{target_version}'),
            (r'("client_version"\s*:\s*")[^"]*(")', f'\\1{target_version}\\2')
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        # 保存修复后的文件
        with open('license_client.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 版本已统一为: {target_version}")
        print("✅ 原文件已备份为: license_client.py.backup_version_fix")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        target_version = sys.argv[1]
    else:
        target_version = "2.1.1"
    
    fix_version_consistency(target_version)
