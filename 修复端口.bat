@echo off
chcp 65001 >nul
title 🔧 修复license_manager端口配置

echo.
echo ==========================================
echo 🔧 修复license_manager.py端口配置
echo ==========================================
echo.
echo 🔍 问题分析:
echo • ❌ license_manager.py连接不上服务器
echo • 🔍 原因: 端口配置不匹配
echo • 📊 当前服务器运行在端口5000
echo • 📊 license_manager.py配置的是端口44285
echo.
echo 🎯 修复方案:
echo • 🔧 自动修复端口配置 (44285 → 5000)
echo • 📁 备份原文件
echo • 🧪 测试服务器连接
echo • 📋 提供下一步建议
echo.

echo 🔧 开始修复端口配置...
echo.

REM 运行修复脚本
python "修复license_manager端口.py"

echo.
echo 👋 修复完成
pause
