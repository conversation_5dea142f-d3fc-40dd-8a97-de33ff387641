#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的更新测试
"""

def test_simple_update():
    """简单的更新测试"""
    print("🧪 开始简单更新测试")
    
    try:
        # 导入模块
        print("📦 导入auto_updater...")
        from auto_updater import check_and_update
        print("✅ 导入成功")
        
        # 调用更新函数
        print("🚀 调用check_and_update...")
        result = check_and_update(
            parent_window=None,
            current_version="2.1.0",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        print(f"📊 更新结果: {result}")
        
        if result:
            print("✅ 更新成功")
        else:
            print("ℹ️ 没有更新或用户取消")
            
    except Exception as e:
        print(f"❌ 更新测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_update()
