@echo off
chcp 65001 >nul
title 🔍 检查CentOS服务器部署状态

echo.
echo ==========================================
echo 🔍 CentOS服务器部署状态检查工具
echo ==========================================
echo.
echo 🎯 检查项目:
echo • 🔗 SSH连接测试
echo • 📁 文件部署状态
echo • 🔧 license-manager服务
echo • 🌐 nginx服务状态
echo • 🔌 端口监听状态
echo • 🌍 HTTP服务响应
echo.

REM 检查Python和paramiko
python -c "import paramiko" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少paramiko模块，正在安装...
    python -m pip install paramiko
    if errorlevel 1 (
        echo ❌ paramiko安装失败
        pause
        exit /b 1
    )
    echo ✅ paramiko安装完成
    echo.
)

echo 🔍 开始检查服务器状态...
echo.

REM 运行检查脚本
python "检查部署状态.py"

echo.
echo 👋 检查完成
pause
