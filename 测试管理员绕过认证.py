#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试管理员绕过认证 - 验证ADMIN_BYPASS密钥是否有效
"""

import requests
import json

def test_admin_bypass():
    """测试管理员绕过认证"""
    print("🔑 测试管理员绕过认证")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    admin_key = "ADMIN_BYPASS"
    device_id = "ADMIN-DEVICE-001"
    
    test_cases = [
        {
            "name": "检查更新API",
            "url": f"{server_url}/update/check",
            "method": "GET",
            "params": {
                'current_version': '1.0.0',
                'key': admin_key,
                'device_id': device_id
            },
            "expected_status": [200, 400]
        },
        {
            "name": "更新统计API",
            "url": f"{server_url}/update/stats",
            "method": "GET",
            "params": {
                'key': admin_key,
                'device_id': device_id
            },
            "expected_status": [200, 404]
        },
        {
            "name": "下载更新API",
            "url": f"{server_url}/update/download",
            "method": "GET",
            "params": {
                'version': '1.0.0',
                'key': admin_key,
                'device_id': device_id
            },
            "expected_status": [200, 404]
        },
        {
            "name": "根路径API",
            "url": f"{server_url}/",
            "method": "GET",
            "expected_status": [200]
        }
    ]
    
    print("🧪 开始测试管理员绕过认证...")
    print(f"🔑 密钥: {admin_key}")
    print(f"📱 设备ID: {device_id}")
    print()
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test in enumerate(test_cases, 1):
        print(f"📋 测试 {i}/{total_count}: {test['name']}")
        print(f"   🌐 URL: {test['url']}")
        
        try:
            if test['method'] == 'GET':
                response = requests.get(
                    test['url'],
                    params=test.get('params', {}),
                    timeout=10
                )
            
            status_code = response.status_code
            print(f"   📊 状态码: {status_code}")
            
            # 检查状态码是否符合预期
            expected = test['expected_status']
            if status_code in expected:
                success_count += 1
                print(f"   ✅ 认证通过")
                
                # 尝试解析响应
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = response.json()
                        
                        if isinstance(data, dict):
                            if 'success' in data:
                                print(f"   📄 成功: {data['success']}")
                            if 'message' in data:
                                message = data['message'][:50]
                                print(f"   📄 消息: {message}...")
                            if 'version_count' in data:
                                print(f"   📊 版本数: {data['version_count']}")
                            if 'has_update' in data:
                                print(f"   🔄 有更新: {data['has_update']}")
                                
                except Exception as e:
                    content = response.text[:100]
                    print(f"   📄 响应: {content}...")
                    
            else:
                print(f"   ❌ 认证失败 (期望: {expected})")
                if status_code == 401:
                    print(f"   🚫 未授权 - 绕过认证可能未生效")
                elif status_code == 400:
                    print(f"   ⚠️ 参数错误 - API可能需要额外参数")
                elif status_code == 404:
                    print(f"   🚫 API不存在或资源不存在")
                else:
                    print(f"   📄 错误: {response.text[:100]}...")
            
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        
        print()
    
    # 总结
    print("=" * 50)
    print(f"🎯 测试完成: {success_count}/{total_count} 个API认证正常")
    
    if success_count == total_count:
        print("🎉 所有管理员绕过认证测试通过！")
        return True
    elif success_count >= total_count * 0.75:
        print("✅ 大部分管理员认证正常")
        return True
    else:
        print("⚠️ 多个API认证仍有问题")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 测试管理员绕过认证功能")
        print("🔧 功能: 验证ADMIN_BYPASS密钥是否有效")
        print("💡 服务器: 198.23.135.176:5000")
        print()
        
        if test_admin_bypass():
            print("\n✅ 管理员绕过认证验证成功！")
            print("\n📋 现在你可以:")
            print("1. 启动exe文件管理工具")
            print("2. 使用所有管理功能")
            print("3. 上传和下载exe文件")
            print("\n💡 建议: 运行 'python exe文件管理工具.py' 开始使用")
        else:
            print("\n⚠️ 部分认证验证失败")
            print("\n🔧 可能的解决方案:")
            print("1. 检查服务器端绕过逻辑是否正确实现")
            print("2. 重新部署修复后的license_server.py")
            print("3. 检查服务器服务状态")
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
