#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上传修复后的服务器文件 - 将修复后的license_server.py上传到服务器
"""

import paramiko
import os

def upload_fixed_server_file():
    """上传修复后的服务器文件"""
    print("📤 上传修复后的服务器文件")
    print("=" * 50)
    
    # 服务器配置
    server_config = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0'
    }
    
    local_file = "license_server.py"
    remote_file = "/opt/license_manager/license_server.py"
    
    try:
        # 检查本地文件是否存在
        if not os.path.exists(local_file):
            print(f"❌ 本地文件不存在: {local_file}")
            return False
        
        print(f"📁 本地文件: {local_file}")
        print(f"📁 远程文件: {remote_file}")
        print()
        
        print("🌐 连接服务器...")
        
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(**server_config, timeout=10)
        print("✅ SSH连接成功")
        
        # 备份原文件
        print("💾 备份原文件...")
        backup_cmd = f"cp {remote_file} {remote_file}.backup.$(date +%Y%m%d_%H%M%S)"
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        backup_error = stderr.read().decode('utf-8').strip()
        
        if backup_error:
            print(f"⚠️ 备份警告: {backup_error}")
        else:
            print("✅ 原文件已备份")
        
        # 上传文件
        print("📤 上传修复后的文件...")
        sftp = ssh.open_sftp()
        sftp.put(local_file, remote_file)
        sftp.close()
        print("✅ 文件上传成功")
        
        # 设置文件权限
        print("🔒 设置文件权限...")
        chmod_cmd = f"chmod 644 {remote_file}"
        stdin, stdout, stderr = ssh.exec_command(chmod_cmd)
        
        # 验证文件
        print("🔍 验证上传的文件...")
        verify_cmd = f"ls -la {remote_file}"
        stdin, stdout, stderr = ssh.exec_command(verify_cmd)
        file_info = stdout.read().decode('utf-8').strip()
        print(f"📄 文件信息: {file_info}")
        
        # 检查Python语法
        print("🐍 检查Python语法...")
        syntax_cmd = f"python3 -m py_compile {remote_file}"
        stdin, stdout, stderr = ssh.exec_command(syntax_cmd)
        syntax_error = stderr.read().decode('utf-8').strip()
        
        if syntax_error:
            print(f"❌ 语法错误: {syntax_error}")
            return False
        else:
            print("✅ Python语法检查通过")
        
        # 重启服务
        print("🔄 重启license-manager服务...")
        restart_cmd = "systemctl restart license-manager"
        stdin, stdout, stderr = ssh.exec_command(restart_cmd)
        
        # 等待服务启动
        import time
        time.sleep(5)
        
        # 检查服务状态
        print("📊 检查服务状态...")
        status_cmd = "systemctl is-active license-manager"
        stdin, stdout, stderr = ssh.exec_command(status_cmd)
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("✅ 服务重启成功")
        else:
            print(f"⚠️ 服务状态: {status}")
            
            # 查看服务日志
            log_cmd = "journalctl -u license-manager --no-pager -n 10"
            stdin, stdout, stderr = ssh.exec_command(log_cmd)
            logs = stdout.read().decode('utf-8').strip()
            print("📋 服务日志:")
            for line in logs.split('\n')[-5:]:  # 显示最后5行
                if line.strip():
                    print(f"   {line}")
        
        # 测试API
        print("🧪 测试API连接...")
        import requests
        try:
            response = requests.get("http://**************:5000/", timeout=5)
            if response.status_code == 200:
                print("✅ API连接正常")
            else:
                print(f"⚠️ API状态码: {response.status_code}")
        except Exception as e:
            print(f"⚠️ API测试失败: {e}")
        
        ssh.close()
        print("\n🎉 服务器文件上传和部署完成！")
        return True
        
    except paramiko.AuthenticationException:
        print("❌ SSH认证失败 - 请检查用户名和密码")
        return False
    except paramiko.SSHException as e:
        print(f"❌ SSH连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 上传修复后的license_server.py到服务器")
        print("🔧 功能: 部署管理员绕过认证功能")
        print("💡 修复: 添加ADMIN_BYPASS密钥支持")
        print()
        
        if upload_fixed_server_file():
            print("\n" + "=" * 50)
            print("✅ 服务器文件部署成功！")
            print("\n📋 下一步操作:")
            print("1. 运行 '测试管理员绕过认证.py' 验证修复")
            print("2. 启动exe文件管理工具")
            print("3. 开始使用所有管理功能")
            print("\n💡 管理员认证信息:")
            print("   密钥: ADMIN_BYPASS")
            print("   设备ID: ADMIN-DEVICE-001")
        else:
            print("\n❌ 服务器文件部署失败")
            print("请检查错误信息并重试")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
