('C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
 '副本\\build\\亚马逊蓝图工具\\PYZ-00.pyz',
 [('PIL',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-2'),
  ('PIL.AvifImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BlpImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BmpImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BufrStubImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.CurImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DcxImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DdsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.EpsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ExifTags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-2'),
  ('PIL.FitsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FliImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FpxImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FtexImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GbrImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GifImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GimpGradientFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-2'),
  ('PIL.GimpPaletteFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GribStubImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcnsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcoImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Image',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-2'),
  ('PIL.ImageChops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-2'),
  ('PIL.ImageCms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-2'),
  ('PIL.ImageColor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-2'),
  ('PIL.ImageFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-2'),
  ('PIL.ImageFilter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-2'),
  ('PIL.ImageMath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-2'),
  ('PIL.ImageMode',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-2'),
  ('PIL.ImageOps',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-2'),
  ('PIL.ImagePalette',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-2'),
  ('PIL.ImageQt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-2'),
  ('PIL.ImageSequence',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-2'),
  ('PIL.ImageShow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-2'),
  ('PIL.ImageTk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-2'),
  ('PIL.ImageWin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-2'),
  ('PIL.ImtImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IptcImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegPresets',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-2'),
  ('PIL.McIdasImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MicImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpegImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpoImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MspImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PaletteFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-2'),
  ('PIL.PalmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcdImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcxImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfParser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-2'),
  ('PIL.PixarImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PngImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PpmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PsdImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.QoiImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SgiImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SpiderImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SunImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TgaImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffTags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-2'),
  ('PIL.WebPImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WmfImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XVThumbImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XbmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XpmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL._binary',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-2'),
  ('PIL._deprecate',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-2'),
  ('PIL._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-2'),
  ('PIL._util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-2'),
  ('PIL._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-2'),
  ('PIL.features',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE-2'),
  ('__future__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE-2'),
  ('_aix_support',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_aix_support.py',
   'PYMODULE-2'),
  ('_bootsubprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE-2'),
  ('_compat_pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('_compression',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE-2'),
  ('_markupbase',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_markupbase.py',
   'PYMODULE-2'),
  ('_py_abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_py_abc.py',
   'PYMODULE-2'),
  ('_pydecimal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE-2'),
  ('_strptime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE-2'),
  ('_threading_local',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE-2'),
  ('argparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\argparse.py',
   'PYMODULE-2'),
  ('ast',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ast.py',
   'PYMODULE-2'),
  ('asyncio',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE-2'),
  ('asyncio.base_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.locks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE-2'),
  ('asyncio.log',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE-2'),
  ('asyncio.mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.tasks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE-2'),
  ('asyncio.threads',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.unix_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('auto_updater',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - 副本\\auto_updater.py',
   'PYMODULE-2'),
  ('base64',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\base64.py',
   'PYMODULE-2'),
  ('bcrypt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE-2'),
  ('bdb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bdb.py',
   'PYMODULE-2'),
  ('bisect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bisect.py',
   'PYMODULE-2'),
  ('brotli',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\brotli.py',
   'PYMODULE-2'),
  ('bs4',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE-2'),
  ('bs4._deprecation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE-2'),
  ('bs4._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE-2'),
  ('bs4._warnings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE-2'),
  ('bs4.builder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE-2'),
  ('bs4.builder._html5lib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE-2'),
  ('bs4.builder._htmlparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE-2'),
  ('bs4.builder._lxml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE-2'),
  ('bs4.css',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\css.py',
   'PYMODULE-2'),
  ('bs4.dammit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE-2'),
  ('bs4.element',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE-2'),
  ('bs4.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE-2'),
  ('bs4.filter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\filter.py',
   'PYMODULE-2'),
  ('bs4.formatter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE-2'),
  ('bz2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bz2.py',
   'PYMODULE-2'),
  ('calendar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\calendar.py',
   'PYMODULE-2'),
  ('certifi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-2'),
  ('certifi.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE-2'),
  ('cgi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\cgi.py',
   'PYMODULE-2'),
  ('charset_normalizer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-2'),
  ('charset_normalizer.cd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-2'),
  ('charset_normalizer.constant',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-2'),
  ('charset_normalizer.legacy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-2'),
  ('charset_normalizer.models',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-2'),
  ('charset_normalizer.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-2'),
  ('charset_normalizer.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-2'),
  ('cmd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\cmd.py',
   'PYMODULE-2'),
  ('code',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\code.py',
   'PYMODULE-2'),
  ('codeop',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\codeop.py',
   'PYMODULE-2'),
  ('colorama',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE-2'),
  ('colorama.ansi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE-2'),
  ('colorama.ansitowin32',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE-2'),
  ('colorama.initialise',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE-2'),
  ('colorama.win32',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE-2'),
  ('colorama.winterm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE-2'),
  ('colorsys',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\colorsys.py',
   'PYMODULE-2'),
  ('commctrl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE-2'),
  ('concurrent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('configparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\configparser.py',
   'PYMODULE-2'),
  ('contextlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE-2'),
  ('contextvars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE-2'),
  ('copy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copy.py',
   'PYMODULE-2'),
  ('cryptography',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.__about__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE-2'),
  ('cryptography.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE-2'),
  ('cryptography.fernet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE-2'),
  ('cryptography.hazmat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat._oid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.backends',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.decrepit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.decrepit.ciphers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives._asymmetric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives._serialization',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.hmac',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.padding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE-2'),
  ('cryptography.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE-2'),
  ('cryptography.x509',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.x509.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE-2'),
  ('cryptography.x509.certificate_transparency',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE-2'),
  ('cryptography.x509.extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE-2'),
  ('cryptography.x509.general_name',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE-2'),
  ('cryptography.x509.name',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE-2'),
  ('cryptography.x509.oid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE-2'),
  ('cryptography.x509.verification',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE-2'),
  ('csv',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\csv.py',
   'PYMODULE-2'),
  ('ctypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE-2'),
  ('ctypes._endian',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('ctypes.wintypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE-2'),
  ('dataclasses',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE-2'),
  ('datetime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\datetime.py',
   'PYMODULE-2'),
  ('dateutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-2'),
  ('dateutil._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-2'),
  ('dateutil._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-2'),
  ('dateutil.easter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-2'),
  ('dateutil.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.parser._parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-2'),
  ('dateutil.parser.isoparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-2'),
  ('dateutil.relativedelta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-2'),
  ('dateutil.rrule',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-2'),
  ('dateutil.tz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.tz._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-2'),
  ('dateutil.tz._factories',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-2'),
  ('dateutil.tz.tz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-2'),
  ('dateutil.tz.win',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-2'),
  ('dateutil.zoneinfo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-2'),
  ('decimal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\decimal.py',
   'PYMODULE-2'),
  ('difflib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\difflib.py',
   'PYMODULE-2'),
  ('dis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dis.py',
   'PYMODULE-2'),
  ('doctest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\doctest.py',
   'PYMODULE-2'),
  ('email',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE-2'),
  ('email._encoded_words',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('email._policybase',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email.charset',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.encoders',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('email.generator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email.header',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.headerregistry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email.iterators',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.message',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.policy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE-2'),
  ('et_xmlfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE-2'),
  ('et_xmlfile.incremental_tree',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE-2'),
  ('et_xmlfile.xmlfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE-2'),
  ('fake_useragent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\__init__.py',
   'PYMODULE-2'),
  ('fake_useragent.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\errors.py',
   'PYMODULE-2'),
  ('fake_useragent.fake',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\fake.py',
   'PYMODULE-2'),
  ('fake_useragent.get_version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\get_version.py',
   'PYMODULE-2'),
  ('fake_useragent.log',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\log.py',
   'PYMODULE-2'),
  ('fake_useragent.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\utils.py',
   'PYMODULE-2'),
  ('fileinput',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fileinput.py',
   'PYMODULE-2'),
  ('fnmatch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fnmatch.py',
   'PYMODULE-2'),
  ('fractions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE-2'),
  ('fsspec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\__init__.py',
   'PYMODULE-2'),
  ('fsspec._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\_version.py',
   'PYMODULE-2'),
  ('fsspec.archive',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\archive.py',
   'PYMODULE-2'),
  ('fsspec.asyn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\asyn.py',
   'PYMODULE-2'),
  ('fsspec.caching',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\caching.py',
   'PYMODULE-2'),
  ('fsspec.callbacks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\callbacks.py',
   'PYMODULE-2'),
  ('fsspec.compression',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\compression.py',
   'PYMODULE-2'),
  ('fsspec.config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\config.py',
   'PYMODULE-2'),
  ('fsspec.conftest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\conftest.py',
   'PYMODULE-2'),
  ('fsspec.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\core.py',
   'PYMODULE-2'),
  ('fsspec.dircache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\dircache.py',
   'PYMODULE-2'),
  ('fsspec.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\exceptions.py',
   'PYMODULE-2'),
  ('fsspec.fuse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\fuse.py',
   'PYMODULE-2'),
  ('fsspec.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\generic.py',
   'PYMODULE-2'),
  ('fsspec.gui',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\gui.py',
   'PYMODULE-2'),
  ('fsspec.implementations',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\__init__.py',
   'PYMODULE-2'),
  ('fsspec.implementations.arrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\arrow.py',
   'PYMODULE-2'),
  ('fsspec.implementations.asyn_wrapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\asyn_wrapper.py',
   'PYMODULE-2'),
  ('fsspec.implementations.cache_mapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\cache_mapper.py',
   'PYMODULE-2'),
  ('fsspec.implementations.cache_metadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\cache_metadata.py',
   'PYMODULE-2'),
  ('fsspec.implementations.cached',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\cached.py',
   'PYMODULE-2'),
  ('fsspec.implementations.dask',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\dask.py',
   'PYMODULE-2'),
  ('fsspec.implementations.data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\data.py',
   'PYMODULE-2'),
  ('fsspec.implementations.dbfs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\dbfs.py',
   'PYMODULE-2'),
  ('fsspec.implementations.dirfs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\dirfs.py',
   'PYMODULE-2'),
  ('fsspec.implementations.ftp',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\ftp.py',
   'PYMODULE-2'),
  ('fsspec.implementations.gist',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\gist.py',
   'PYMODULE-2'),
  ('fsspec.implementations.git',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\git.py',
   'PYMODULE-2'),
  ('fsspec.implementations.github',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\github.py',
   'PYMODULE-2'),
  ('fsspec.implementations.http',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\http.py',
   'PYMODULE-2'),
  ('fsspec.implementations.http_sync',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\http_sync.py',
   'PYMODULE-2'),
  ('fsspec.implementations.jupyter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\jupyter.py',
   'PYMODULE-2'),
  ('fsspec.implementations.libarchive',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\libarchive.py',
   'PYMODULE-2'),
  ('fsspec.implementations.local',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\local.py',
   'PYMODULE-2'),
  ('fsspec.implementations.memory',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\memory.py',
   'PYMODULE-2'),
  ('fsspec.implementations.reference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\reference.py',
   'PYMODULE-2'),
  ('fsspec.implementations.sftp',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\sftp.py',
   'PYMODULE-2'),
  ('fsspec.implementations.smb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\smb.py',
   'PYMODULE-2'),
  ('fsspec.implementations.tar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\tar.py',
   'PYMODULE-2'),
  ('fsspec.implementations.webhdfs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\webhdfs.py',
   'PYMODULE-2'),
  ('fsspec.implementations.zip',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\zip.py',
   'PYMODULE-2'),
  ('fsspec.json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\json.py',
   'PYMODULE-2'),
  ('fsspec.mapping',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\mapping.py',
   'PYMODULE-2'),
  ('fsspec.parquet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\parquet.py',
   'PYMODULE-2'),
  ('fsspec.registry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\registry.py',
   'PYMODULE-2'),
  ('fsspec.spec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\spec.py',
   'PYMODULE-2'),
  ('fsspec.transaction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\transaction.py',
   'PYMODULE-2'),
  ('fsspec.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\utils.py',
   'PYMODULE-2'),
  ('ftplib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ftplib.py',
   'PYMODULE-2'),
  ('getopt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getopt.py',
   'PYMODULE-2'),
  ('getpass',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getpass.py',
   'PYMODULE-2'),
  ('gettext',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gettext.py',
   'PYMODULE-2'),
  ('glob',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\glob.py',
   'PYMODULE-2'),
  ('gzip',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gzip.py',
   'PYMODULE-2'),
  ('hashlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hashlib.py',
   'PYMODULE-2'),
  ('hmac',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hmac.py',
   'PYMODULE-2'),
  ('html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\__init__.py',
   'PYMODULE-2'),
  ('html.entities',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE-2'),
  ('html.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\parser.py',
   'PYMODULE-2'),
  ('http',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE-2'),
  ('http.client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE-2'),
  ('http.cookiejar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('http.cookies',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookies.py',
   'PYMODULE-2'),
  ('http.server',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\server.py',
   'PYMODULE-2'),
  ('idna',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-2'),
  ('idna.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\core.py',
   'PYMODULE-2'),
  ('idna.idnadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-2'),
  ('idna.intranges',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-2'),
  ('idna.package_data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-2'),
  ('idna.uts46data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-2'),
  ('importlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('importlib._abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib._adapters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE-2'),
  ('importlib.abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE-2'),
  ('importlib.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('inspect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\inspect.py',
   'PYMODULE-2'),
  ('ipaddress',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ipaddress.py',
   'PYMODULE-2'),
  ('jinja2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE-2'),
  ('jinja2._identifier',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE-2'),
  ('jinja2.async_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE-2'),
  ('jinja2.bccache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE-2'),
  ('jinja2.compiler',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE-2'),
  ('jinja2.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE-2'),
  ('jinja2.debug',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE-2'),
  ('jinja2.defaults',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE-2'),
  ('jinja2.environment',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE-2'),
  ('jinja2.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE-2'),
  ('jinja2.ext',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE-2'),
  ('jinja2.filters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE-2'),
  ('jinja2.idtracking',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE-2'),
  ('jinja2.lexer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE-2'),
  ('jinja2.loaders',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE-2'),
  ('jinja2.nodes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE-2'),
  ('jinja2.optimizer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE-2'),
  ('jinja2.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE-2'),
  ('jinja2.runtime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE-2'),
  ('jinja2.sandbox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE-2'),
  ('jinja2.tests',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE-2'),
  ('jinja2.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE-2'),
  ('jinja2.visitor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE-2'),
  ('json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE-2'),
  ('json.decoder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE-2'),
  ('json.encoder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE-2'),
  ('json.scanner',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE-2'),
  ('logging',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE-2'),
  ('lxml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE-2'),
  ('lxml.ElementInclude',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE-2'),
  ('lxml.cssselect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE-2'),
  ('lxml.doctestcompare',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE-2'),
  ('lxml.html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE-2'),
  ('lxml.html.ElementSoup',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE-2'),
  ('lxml.html._diffcommand',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE-2'),
  ('lxml.html._html5builder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE-2'),
  ('lxml.html._setmixin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE-2'),
  ('lxml.html.builder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE-2'),
  ('lxml.html.clean',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE-2'),
  ('lxml.html.defs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE-2'),
  ('lxml.html.formfill',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE-2'),
  ('lxml.html.html5parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE-2'),
  ('lxml.html.soupparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE-2'),
  ('lxml.html.usedoctest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE-2'),
  ('lxml.includes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE-2'),
  ('lxml.includes.extlibs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE-2'),
  ('lxml.includes.libexslt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE-2'),
  ('lxml.includes.libxml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE-2'),
  ('lxml.includes.libxslt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE-2'),
  ('lxml.isoschematron',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE-2'),
  ('lxml.pyclasslookup',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE-2'),
  ('lxml.usedoctest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE-2'),
  ('lzma',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\lzma.py',
   'PYMODULE-2'),
  ('markupsafe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE-2'),
  ('markupsafe._native',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE-2'),
  ('mimetypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE-2'),
  ('multiprocessing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.context',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('multiprocessing.queues',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.reduction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.spawn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('nacl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\__init__.py',
   'PYMODULE-2'),
  ('nacl.bindings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\__init__.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_aead',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_aead.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_box',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_box.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_core.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_generichash',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_generichash.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_hash',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_hash.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_kx',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_kx.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_pwhash',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_pwhash.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_scalarmult',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_scalarmult.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_secretbox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_secretbox.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_secretstream',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_secretstream.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_shorthash',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_shorthash.py',
   'PYMODULE-2'),
  ('nacl.bindings.crypto_sign',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\crypto_sign.py',
   'PYMODULE-2'),
  ('nacl.bindings.randombytes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\randombytes.py',
   'PYMODULE-2'),
  ('nacl.bindings.sodium_core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\sodium_core.py',
   'PYMODULE-2'),
  ('nacl.bindings.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\bindings\\utils.py',
   'PYMODULE-2'),
  ('nacl.encoding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\encoding.py',
   'PYMODULE-2'),
  ('nacl.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\exceptions.py',
   'PYMODULE-2'),
  ('nacl.public',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\public.py',
   'PYMODULE-2'),
  ('nacl.signing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\signing.py',
   'PYMODULE-2'),
  ('nacl.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\utils.py',
   'PYMODULE-2'),
  ('netrc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\netrc.py',
   'PYMODULE-2'),
  ('nturl2path',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE-2'),
  ('numbers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\numbers.py',
   'PYMODULE-2'),
  ('numpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy._core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy._core._internal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy._core.tests', '-', 'PYMODULE-2'),
  ('numpy._core.tests._natype',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy.char',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.f2py',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py.__version__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._backend',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._distutils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._meson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-2'),
  ('numpy.f2py._isocbind',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-2'),
  ('numpy.f2py.auxfuncs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.capi_maps',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-2'),
  ('numpy.f2py.cb_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.cfuncs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.common_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.crackfortran',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-2'),
  ('numpy.f2py.diagnose',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-2'),
  ('numpy.f2py.f2py2e',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-2'),
  ('numpy.f2py.f90mod_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.func2subr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-2'),
  ('numpy.f2py.rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.symbolic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-2'),
  ('numpy.f2py.use_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.recfunctions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing._private',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing._private.extbuild',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE-2'),
  ('numpy.testing._private.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE-2'),
  ('numpy.testing.overrides',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('opcode',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\opcode.py',
   'PYMODULE-2'),
  ('openpyxl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl._constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE-2'),
  ('openpyxl.cell',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.cell._writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.cell.cell',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.cell.read_only',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE-2'),
  ('openpyxl.cell.rich_text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE-2'),
  ('openpyxl.cell.text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE-2'),
  ('openpyxl.chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chart._3d',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE-2'),
  ('openpyxl.chart._chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.area_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.axis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bar_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bubble_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.chartspace',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE-2'),
  ('openpyxl.chart.data_source',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE-2'),
  ('openpyxl.chart.descriptors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE-2'),
  ('openpyxl.chart.error_bar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE-2'),
  ('openpyxl.chart.label',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE-2'),
  ('openpyxl.chart.layout',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE-2'),
  ('openpyxl.chart.legend',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE-2'),
  ('openpyxl.chart.line_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.marker',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE-2'),
  ('openpyxl.chart.picture',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pie_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pivot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE-2'),
  ('openpyxl.chart.plotarea',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE-2'),
  ('openpyxl.chart.print_settings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.chart.radar_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE-2'),
  ('openpyxl.chart.scatter_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series_factory',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE-2'),
  ('openpyxl.chart.shapes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE-2'),
  ('openpyxl.chart.stock_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.surface_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE-2'),
  ('openpyxl.chart.title',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE-2'),
  ('openpyxl.chart.trendline',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE-2'),
  ('openpyxl.chart.updown_bars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.publish',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.relation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.views',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.comments',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.comments.author',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comment_sheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comments',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE-2'),
  ('openpyxl.comments.shape_writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE-2'),
  ('openpyxl.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.compat.abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\abc.py',
   'PYMODULE-2'),
  ('openpyxl.compat.numbers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.compat.product',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE-2'),
  ('openpyxl.compat.singleton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\singleton.py',
   'PYMODULE-2'),
  ('openpyxl.compat.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.container',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.namespace',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.nested',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.sequence',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.serialisable',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.slots',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\slots.py',
   'PYMODULE-2'),
  ('openpyxl.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.colors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.connector',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.effect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.fill',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.geometry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.graphic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.image',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.line',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.picture',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.relation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.xdr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE-2'),
  ('openpyxl.formatting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.formatting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.rule',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE-2'),
  ('openpyxl.formula',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formula.tokenizer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE-2'),
  ('openpyxl.formula.translate',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE-2'),
  ('openpyxl.packaging',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.extended',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.interface',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\interface.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.manifest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.relationship',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.pivot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.cache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.fields',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.record',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE-2'),
  ('openpyxl.reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.reader.drawings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE-2'),
  ('openpyxl.reader.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.reader.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.reader.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.styles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.styles.alignment',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE-2'),
  ('openpyxl.styles.borders',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE-2'),
  ('openpyxl.styles.builtins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE-2'),
  ('openpyxl.styles.cell_style',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE-2'),
  ('openpyxl.styles.colors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.styles.differential',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fills',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fonts',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE-2'),
  ('openpyxl.styles.named_styles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE-2'),
  ('openpyxl.styles.numbers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.styles.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.styles.proxy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE-2'),
  ('openpyxl.styles.styleable',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE-2'),
  ('openpyxl.styles.stylesheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE-2'),
  ('openpyxl.styles.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE-2'),
  ('openpyxl.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE-2'),
  ('openpyxl.utils.cell',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.utils.dataframe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE-2'),
  ('openpyxl.utils.datetime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE-2'),
  ('openpyxl.utils.escape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE-2'),
  ('openpyxl.utils.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE-2'),
  ('openpyxl.utils.formulas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE-2'),
  ('openpyxl.utils.indexed_list',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE-2'),
  ('openpyxl.utils.inference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\inference.py',
   'PYMODULE-2'),
  ('openpyxl.utils.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.utils.units',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE-2'),
  ('openpyxl.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook._writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.child',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.defined_name',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link.external',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_reference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.function_group',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.smart_tags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.views',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.web',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._read_only',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._write_only',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.cell_range',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.cell_watch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\cell_watch.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.controls',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\controls.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.copier',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.dimensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\errors.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.filters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.formula',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.header_footer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.merge',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.ole',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\ole.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.page',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.picture',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.print_settings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.related',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.scenario',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.smart_tag',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\smart_tag.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.views',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.worksheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE-2'),
  ('openpyxl.writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.writer.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.writer.theme',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE-2'),
  ('openpyxl.xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.xml.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE-2'),
  ('openpyxl.xml.functions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE-2'),
  ('optparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\optparse.py',
   'PYMODULE-2'),
  ('pandas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config.config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-2'),
  ('pandas._config.dates',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-2'),
  ('pandas._config.display',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-2'),
  ('pandas._config.localization',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-2'),
  ('pandas._libs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.tslibs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.window',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing._io',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-2'),
  ('pandas._testing._warnings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-2'),
  ('pandas._testing.asserters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-2'),
  ('pandas._testing.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-2'),
  ('pandas._testing.contexts',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-2'),
  ('pandas._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-2'),
  ('pandas._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-2'),
  ('pandas._version_meson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE-2'),
  ('pandas.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.indexers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.interchange',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE-2'),
  ('pandas.arrays',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat._constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-2'),
  ('pandas.compat._optional',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-2'),
  ('pandas.compat.compressors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy.function',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-2'),
  ('pandas.compat.pickle_compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-2'),
  ('pandas.compat.pyarrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-2'),
  ('pandas.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.executor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-2'),
  ('pandas.core._numba.extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.mean_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.min_max_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.shared',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.sum_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.var_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-2'),
  ('pandas.core.accessor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.algorithms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-2'),
  ('pandas.core.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-2'),
  ('pandas.core.apply',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_accumulations',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_reductions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.putmask',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.quantile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.replace',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.take',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.transforms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-2'),
  ('pandas.core.arraylike',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-2'),
  ('pandas.core.arrays',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._arrow_string_mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._ranges',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.accessors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.array',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.extension_types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.boolean',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.categorical',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimelike',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.floating',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.integer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.interval',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.masked',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numpy_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.period',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.accessor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.array',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_arrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.timedeltas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-2'),
  ('pandas.core.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-2'),
  ('pandas.core.computation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.computation.align',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-2'),
  ('pandas.core.computation.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-2'),
  ('pandas.core.computation.check',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-2'),
  ('pandas.core.computation.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-2'),
  ('pandas.core.computation.engines',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-2'),
  ('pandas.core.computation.eval',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expressions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-2'),
  ('pandas.core.computation.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.computation.parsing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-2'),
  ('pandas.core.computation.pytables',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-2'),
  ('pandas.core.computation.scope',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-2'),
  ('pandas.core.config_init',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-2'),
  ('pandas.core.construction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.astype',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.cast',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.concat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.dtypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.inference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.missing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.flags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-2'),
  ('pandas.core.frame',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-2'),
  ('pandas.core.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.groupby',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.categorical',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.groupby',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.grouper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.indexing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.numba_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.indexers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.objects',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.indexes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.accessors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.category',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimelike',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.extension',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.frozen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.interval',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.multi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.period',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.range',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.timedeltas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.indexing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.interchange',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.buffer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.column',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe_protocol',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.from_dataframe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.internals',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.internals.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-2'),
  ('pandas.core.internals.array_manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-2'),
  ('pandas.core.internals.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-2'),
  ('pandas.core.internals.blocks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-2'),
  ('pandas.core.internals.concat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.internals.construction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.internals.managers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-2'),
  ('pandas.core.internals.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.methods',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.methods.describe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-2'),
  ('pandas.core.methods.selectn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-2'),
  ('pandas.core.methods.to_dict',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-2'),
  ('pandas.core.missing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.nanops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-2'),
  ('pandas.core.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.ops.array_ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-2'),
  ('pandas.core.ops.dispatch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-2'),
  ('pandas.core.ops.docstrings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-2'),
  ('pandas.core.ops.invalid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-2'),
  ('pandas.core.ops.mask_ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.missing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.resample',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-2'),
  ('pandas.core.reshape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.concat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.encoding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.melt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.merge',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.pivot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.reshape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.tile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-2'),
  ('pandas.core.roperator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-2'),
  ('pandas.core.sample',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-2'),
  ('pandas.core.series',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-2'),
  ('pandas.core.shared_docs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-2'),
  ('pandas.core.sorting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-2'),
  ('pandas.core.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.strings.accessor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.strings.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-2'),
  ('pandas.core.strings.object_array',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-2'),
  ('pandas.core.tools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.tools.datetimes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.tools.numeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.tools.timedeltas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.tools.times',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-2'),
  ('pandas.core.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.util.hashing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-2'),
  ('pandas.core.util.numba_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.window.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-2'),
  ('pandas.core.window.doc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-2'),
  ('pandas.core.window.ewm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-2'),
  ('pandas.core.window.expanding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-2'),
  ('pandas.core.window.numba_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window.online',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-2'),
  ('pandas.core.window.rolling',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-2'),
  ('pandas.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io._util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-2'),
  ('pandas.io.clipboard',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.clipboards',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-2'),
  ('pandas.io.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-2'),
  ('pandas.io.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.excel._base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-2'),
  ('pandas.io.excel._calamine',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odfreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odswriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-2'),
  ('pandas.io.excel._openpyxl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-2'),
  ('pandas.io.excel._pyxlsb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-2'),
  ('pandas.io.excel._util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlrd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlsxwriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-2'),
  ('pandas.io.feather_format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-2'),
  ('pandas.io.formats',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.formats._color_data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-2'),
  ('pandas.io.formats.console',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-2'),
  ('pandas.io.formats.css',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-2'),
  ('pandas.io.formats.csvs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-2'),
  ('pandas.io.formats.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-2'),
  ('pandas.io.formats.format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-2'),
  ('pandas.io.formats.html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-2'),
  ('pandas.io.formats.info',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-2'),
  ('pandas.io.formats.printing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-2'),
  ('pandas.io.formats.string',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style_render',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-2'),
  ('pandas.io.formats.xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-2'),
  ('pandas.io.gbq',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-2'),
  ('pandas.io.html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-2'),
  ('pandas.io.json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.json._json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-2'),
  ('pandas.io.json._normalize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-2'),
  ('pandas.io.json._table_schema',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-2'),
  ('pandas.io.orc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-2'),
  ('pandas.io.parquet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-2'),
  ('pandas.io.parsers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.base_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.c_parser_wrapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.python_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.readers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-2'),
  ('pandas.io.pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-2'),
  ('pandas.io.pytables',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-2'),
  ('pandas.io.sas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas7bdat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_xport',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sasreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-2'),
  ('pandas.io.spss',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-2'),
  ('pandas.io.sql',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-2'),
  ('pandas.io.stata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-2'),
  ('pandas.io.xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-2'),
  ('pandas.plotting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-2'),
  ('pandas.plotting._core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-2'),
  ('pandas.plotting._misc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-2'),
  ('pandas.testing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-2'),
  ('pandas.tseries',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-2'),
  ('pandas.tseries.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-2'),
  ('pandas.tseries.frequencies',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-2'),
  ('pandas.tseries.holiday',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE-2'),
  ('pandas.tseries.offsets',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-2'),
  ('pandas.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.util._decorators',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-2'),
  ('pandas.util._exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-2'),
  ('pandas.util._print_versions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-2'),
  ('pandas.util._tester',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-2'),
  ('pandas.util._validators',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-2'),
  ('pandas.util.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-2'),
  ('paramiko',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\__init__.py',
   'PYMODULE-2'),
  ('paramiko._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\_version.py',
   'PYMODULE-2'),
  ('paramiko._winapi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\_winapi.py',
   'PYMODULE-2'),
  ('paramiko.agent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\agent.py',
   'PYMODULE-2'),
  ('paramiko.auth_handler',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\auth_handler.py',
   'PYMODULE-2'),
  ('paramiko.auth_strategy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\auth_strategy.py',
   'PYMODULE-2'),
  ('paramiko.ber',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\ber.py',
   'PYMODULE-2'),
  ('paramiko.buffered_pipe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\buffered_pipe.py',
   'PYMODULE-2'),
  ('paramiko.channel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\channel.py',
   'PYMODULE-2'),
  ('paramiko.client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\client.py',
   'PYMODULE-2'),
  ('paramiko.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\common.py',
   'PYMODULE-2'),
  ('paramiko.compress',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\compress.py',
   'PYMODULE-2'),
  ('paramiko.config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\config.py',
   'PYMODULE-2'),
  ('paramiko.dsskey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\dsskey.py',
   'PYMODULE-2'),
  ('paramiko.ecdsakey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\ecdsakey.py',
   'PYMODULE-2'),
  ('paramiko.ed25519key',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\ed25519key.py',
   'PYMODULE-2'),
  ('paramiko.file',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\file.py',
   'PYMODULE-2'),
  ('paramiko.hostkeys',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\hostkeys.py',
   'PYMODULE-2'),
  ('paramiko.kex_curve25519',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\kex_curve25519.py',
   'PYMODULE-2'),
  ('paramiko.kex_ecdh_nist',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\kex_ecdh_nist.py',
   'PYMODULE-2'),
  ('paramiko.kex_gex',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\kex_gex.py',
   'PYMODULE-2'),
  ('paramiko.kex_group1',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\kex_group1.py',
   'PYMODULE-2'),
  ('paramiko.kex_group14',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\kex_group14.py',
   'PYMODULE-2'),
  ('paramiko.kex_group16',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\kex_group16.py',
   'PYMODULE-2'),
  ('paramiko.kex_gss',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\kex_gss.py',
   'PYMODULE-2'),
  ('paramiko.message',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\message.py',
   'PYMODULE-2'),
  ('paramiko.packet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\packet.py',
   'PYMODULE-2'),
  ('paramiko.pipe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\pipe.py',
   'PYMODULE-2'),
  ('paramiko.pkey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\pkey.py',
   'PYMODULE-2'),
  ('paramiko.primes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\primes.py',
   'PYMODULE-2'),
  ('paramiko.proxy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\proxy.py',
   'PYMODULE-2'),
  ('paramiko.rsakey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\rsakey.py',
   'PYMODULE-2'),
  ('paramiko.server',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\server.py',
   'PYMODULE-2'),
  ('paramiko.sftp',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\sftp.py',
   'PYMODULE-2'),
  ('paramiko.sftp_attr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\sftp_attr.py',
   'PYMODULE-2'),
  ('paramiko.sftp_client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\sftp_client.py',
   'PYMODULE-2'),
  ('paramiko.sftp_file',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\sftp_file.py',
   'PYMODULE-2'),
  ('paramiko.sftp_handle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\sftp_handle.py',
   'PYMODULE-2'),
  ('paramiko.sftp_server',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\sftp_server.py',
   'PYMODULE-2'),
  ('paramiko.sftp_si',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\sftp_si.py',
   'PYMODULE-2'),
  ('paramiko.ssh_exception',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\ssh_exception.py',
   'PYMODULE-2'),
  ('paramiko.ssh_gss',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\ssh_gss.py',
   'PYMODULE-2'),
  ('paramiko.transport',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\transport.py',
   'PYMODULE-2'),
  ('paramiko.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\util.py',
   'PYMODULE-2'),
  ('paramiko.win_openssh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\win_openssh.py',
   'PYMODULE-2'),
  ('paramiko.win_pageant',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\paramiko\\win_pageant.py',
   'PYMODULE-2'),
  ('pathlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pathlib.py',
   'PYMODULE-2'),
  ('pdb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pdb.py',
   'PYMODULE-2'),
  ('pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickle.py',
   'PYMODULE-2'),
  ('pickletools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickletools.py',
   'PYMODULE-2'),
  ('pkgutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pkgutil.py',
   'PYMODULE-2'),
  ('platform',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\platform.py',
   'PYMODULE-2'),
  ('pprint',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pprint.py',
   'PYMODULE-2'),
  ('psutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE-2'),
  ('psutil._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE-2'),
  ('psutil._pswindows',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE-2'),
  ('py_compile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE-2'),
  ('pydoc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc.py',
   'PYMODULE-2'),
  ('pydoc_data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE-2'),
  ('pydoc_data.topics',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('pythoncom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pythoncom.py',
   'PYMODULE-2'),
  ('pytz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-2'),
  ('pytz.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-2'),
  ('pytz.lazy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-2'),
  ('pytz.tzfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-2'),
  ('pytz.tzinfo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-2'),
  ('pywin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE-2'),
  ('pywin.dialogs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE-2'),
  ('pywin.dialogs.list',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE-2'),
  ('pywin.dialogs.status',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE-2'),
  ('pywin.mfc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE-2'),
  ('pywin.mfc.dialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE-2'),
  ('pywin.mfc.object',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE-2'),
  ('pywin.mfc.thread',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE-2'),
  ('pywin.mfc.window',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE-2'),
  ('pywin32_system32', '-', 'PYMODULE-2'),
  ('pywintypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE-2'),
  ('queue',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\queue.py',
   'PYMODULE-2'),
  ('quopri',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\quopri.py',
   'PYMODULE-2'),
  ('random',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\random.py',
   'PYMODULE-2'),
  ('requests',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-2'),
  ('requests.__version__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-2'),
  ('requests._internal_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-2'),
  ('requests.adapters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-2'),
  ('requests.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\api.py',
   'PYMODULE-2'),
  ('requests.auth',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE-2'),
  ('requests.certs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE-2'),
  ('requests.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE-2'),
  ('requests.cookies',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-2'),
  ('requests.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-2'),
  ('requests.hooks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-2'),
  ('requests.models',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\models.py',
   'PYMODULE-2'),
  ('requests.packages',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE-2'),
  ('requests.sessions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-2'),
  ('requests.status_codes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-2'),
  ('requests.structures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE-2'),
  ('requests.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE-2'),
  ('runpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\runpy.py',
   'PYMODULE-2'),
  ('secrets',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\secrets.py',
   'PYMODULE-2'),
  ('selectors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE-2'),
  ('selenium',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE-2'),
  ('shlex',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shlex.py',
   'PYMODULE-2'),
  ('shutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shutil.py',
   'PYMODULE-2'),
  ('signal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\signal.py',
   'PYMODULE-2'),
  ('six',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\six.py',
   'PYMODULE-2'),
  ('socket',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py',
   'PYMODULE-2'),
  ('socketserver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socketserver.py',
   'PYMODULE-2'),
  ('socks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\socks.py',
   'PYMODULE-2'),
  ('soupsieve',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE-2'),
  ('soupsieve.__meta__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE-2'),
  ('soupsieve.css_match',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE-2'),
  ('soupsieve.css_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE-2'),
  ('soupsieve.css_types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE-2'),
  ('soupsieve.pretty',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE-2'),
  ('soupsieve.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE-2'),
  ('sqlite3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\__init__.py',
   'PYMODULE-2'),
  ('sqlite3.dbapi2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE-2'),
  ('sqlite3.dump',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\dump.py',
   'PYMODULE-2'),
  ('ssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ssl.py',
   'PYMODULE-2'),
  ('sspi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\sspi.py',
   'PYMODULE-2'),
  ('sspicon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\sspicon.py',
   'PYMODULE-2'),
  ('statistics',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE-2'),
  ('string',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\string.py',
   'PYMODULE-2'),
  ('stringprep',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE-2'),
  ('subprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE-2'),
  ('sysconfig',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sysconfig.py',
   'PYMODULE-2'),
  ('tarfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tarfile.py',
   'PYMODULE-2'),
  ('tempfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tempfile.py',
   'PYMODULE-2'),
  ('textwrap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\textwrap.py',
   'PYMODULE-2'),
  ('threading',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\threading.py',
   'PYMODULE-2'),
  ('tkinter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\__init__.py',
   'PYMODULE-2'),
  ('tkinter.commondialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE-2'),
  ('tkinter.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE-2'),
  ('tkinter.messagebox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE-2'),
  ('tkinter.ttk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\ttk.py',
   'PYMODULE-2'),
  ('token',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\token.py',
   'PYMODULE-2'),
  ('tokenize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tokenize.py',
   'PYMODULE-2'),
  ('tqdm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE-2'),
  ('tqdm._dist_ver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE-2'),
  ('tqdm._monitor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE-2'),
  ('tqdm._tqdm_pandas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE-2'),
  ('tqdm.cli',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE-2'),
  ('tqdm.gui',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE-2'),
  ('tqdm.notebook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE-2'),
  ('tqdm.std',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\std.py',
   'PYMODULE-2'),
  ('tqdm.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE-2'),
  ('tqdm.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\version.py',
   'PYMODULE-2'),
  ('tracemalloc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE-2'),
  ('tty',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tty.py',
   'PYMODULE-2'),
  ('typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\typing.py',
   'PYMODULE-2'),
  ('typing_extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE-2'),
  ('ui_theme',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - 副本\\ui_theme.py',
   'PYMODULE-2'),
  ('update_config',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - 副本\\update_config.py',
   'PYMODULE-2'),
  ('urllib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE-2'),
  ('urllib.error',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE-2'),
  ('urllib.parse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('urllib.request',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('urllib.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('urllib3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-2'),
  ('urllib3._base_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-2'),
  ('urllib3._collections',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-2'),
  ('urllib3._request_methods',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-2'),
  ('urllib3._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-2'),
  ('urllib3.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-2'),
  ('urllib3.connectionpool',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-2'),
  ('urllib3.contrib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.fetch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.request',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-2'),
  ('urllib3.contrib.pyopenssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-2'),
  ('urllib3.contrib.socks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-2'),
  ('urllib3.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3.fields',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-2'),
  ('urllib3.filepost',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-2'),
  ('urllib3.http2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.http2.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-2'),
  ('urllib3.http2.probe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-2'),
  ('urllib3.poolmanager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-2'),
  ('urllib3.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-2'),
  ('urllib3.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.util.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-2'),
  ('urllib3.util.proxy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-2'),
  ('urllib3.util.request',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-2'),
  ('urllib3.util.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-2'),
  ('urllib3.util.retry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-2'),
  ('urllib3.util.ssltransport',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-2'),
  ('urllib3.util.timeout',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-2'),
  ('urllib3.util.url',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-2'),
  ('urllib3.util.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-2'),
  ('urllib3.util.wait',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-2'),
  ('uu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uu.py',
   'PYMODULE-2'),
  ('uuid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uuid.py',
   'PYMODULE-2'),
  ('webbrowser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE-2'),
  ('win32com',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE-2'),
  ('win32com.client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE-2'),
  ('win32com.client.CLSIDToClass',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE-2'),
  ('win32com.client.build',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE-2'),
  ('win32com.client.dynamic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE-2'),
  ('win32com.client.gencache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE-2'),
  ('win32com.client.genpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE-2'),
  ('win32com.client.makepy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE-2'),
  ('win32com.client.selecttlb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE-2'),
  ('win32com.client.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE-2'),
  ('win32com.server',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE-2'),
  ('win32com.server.dispatcher',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE-2'),
  ('win32com.server.exception',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE-2'),
  ('win32com.server.policy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE-2'),
  ('win32com.server.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE-2'),
  ('win32com.universal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE-2'),
  ('win32com.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE-2'),
  ('win32con',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE-2'),
  ('win32traceutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE-2'),
  ('winerror',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE-2'),
  ('wmi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\wmi.py',
   'PYMODULE-2'),
  ('xlsxwriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE-2'),
  ('xlsxwriter.app',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_area',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_bar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_column',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_doughnut',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_line',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_pie',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_radar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_scatter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_stock',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE-2'),
  ('xlsxwriter.chartsheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.color',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE-2'),
  ('xlsxwriter.comments',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE-2'),
  ('xlsxwriter.contenttypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE-2'),
  ('xlsxwriter.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE-2'),
  ('xlsxwriter.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE-2'),
  ('xlsxwriter.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE-2'),
  ('xlsxwriter.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE-2'),
  ('xlsxwriter.feature_property_bag',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE-2'),
  ('xlsxwriter.format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE-2'),
  ('xlsxwriter.image',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE-2'),
  ('xlsxwriter.metadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE-2'),
  ('xlsxwriter.packager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE-2'),
  ('xlsxwriter.relationships',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_rel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_structure',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE-2'),
  ('xlsxwriter.shape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE-2'),
  ('xlsxwriter.sharedstrings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE-2'),
  ('xlsxwriter.styles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE-2'),
  ('xlsxwriter.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE-2'),
  ('xlsxwriter.theme',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE-2'),
  ('xlsxwriter.url',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE-2'),
  ('xlsxwriter.utility',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE-2'),
  ('xlsxwriter.vml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE-2'),
  ('xlsxwriter.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE-2'),
  ('xlsxwriter.worksheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.xmlwriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE-2'),
  ('xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\__init__.py',
   'PYMODULE-2'),
  ('xml.dom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\__init__.py',
   'PYMODULE-2'),
  ('xml.dom.NodeFilter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-2'),
  ('xml.dom.domreg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\domreg.py',
   'PYMODULE-2'),
  ('xml.dom.expatbuilder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-2'),
  ('xml.dom.minicompat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE-2'),
  ('xml.dom.minidom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minidom.py',
   'PYMODULE-2'),
  ('xml.dom.pulldom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE-2'),
  ('xml.dom.xmlbuilder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-2'),
  ('xml.etree',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml.parsers.expat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.sax',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE-2'),
  ('xml.sax._exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.expatreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.handler',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('xmlrpc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE-2'),
  ('xmlrpc.client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE-2'),
  ('yaml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE-2'),
  ('yaml.composer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE-2'),
  ('yaml.constructor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE-2'),
  ('yaml.cyaml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE-2'),
  ('yaml.dumper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE-2'),
  ('yaml.emitter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE-2'),
  ('yaml.error',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE-2'),
  ('yaml.events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE-2'),
  ('yaml.loader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE-2'),
  ('yaml.nodes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE-2'),
  ('yaml.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE-2'),
  ('yaml.reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE-2'),
  ('yaml.representer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE-2'),
  ('yaml.resolver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE-2'),
  ('yaml.scanner',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE-2'),
  ('yaml.serializer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE-2'),
  ('yaml.tokens',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE-2'),
  ('zipfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py',
   'PYMODULE-2'),
  ('zipimport',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE-2')])
