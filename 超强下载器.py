#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超强下载器 - 解决所有下载问题的终极方案
"""

import requests
import os
import time
import hashlib
import threading
import tkinter as tk
from tkinter import ttk, messagebox
import tempfile
import subprocess
import sys

class SuperDownloader:
    """超强下载器"""
    
    def __init__(self, url, params, expected_size=0, expected_hash=""):
        self.url = url
        self.params = params
        self.expected_size = expected_size
        self.expected_hash = expected_hash
        
        # 下载配置
        self.chunk_size = 8192  # 8KB chunks
        self.max_retries = 5
        self.timeout = (30, 300)  # 连接超时30s，读取超时300s
        self.retry_delay = 2  # 重试延迟
        
        # 状态
        self.downloaded_size = 0
        self.total_size = 0
        self.is_cancelled = False
        self.progress_callback = None
        self.log_callback = None
        
    def set_progress_callback(self, callback):
        """设置进度回调"""
        self.progress_callback = callback
        
    def set_log_callback(self, callback):
        """设置日志回调"""
        self.log_callback = callback
        
    def log(self, message, level="INFO"):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message, level)
        else:
            print(f"[{level}] {message}")
    
    def download_with_resume(self, output_file):
        """支持断点续传的下载"""
        self.log("开始下载，支持断点续传...")
        
        # 检查已存在的文件
        resume_pos = 0
        if os.path.exists(output_file):
            resume_pos = os.path.getsize(output_file)
            self.log(f"发现已存在文件，从 {resume_pos:,} 字节处继续下载")
        
        for attempt in range(self.max_retries):
            try:
                self.log(f"尝试下载 (第 {attempt + 1}/{self.max_retries} 次)")
                
                # 设置请求头
                headers = {}
                if resume_pos > 0:
                    headers['Range'] = f'bytes={resume_pos}-'
                    self.log(f"使用断点续传: Range=bytes={resume_pos}-")
                
                # 发送请求
                response = requests.get(
                    self.url, 
                    params=self.params, 
                    headers=headers,
                    stream=True, 
                    timeout=self.timeout
                )
                
                # 检查响应状态
                if response.status_code == 206:  # Partial Content
                    self.log("服务器支持断点续传", "SUCCESS")
                elif response.status_code == 200:  # OK
                    if resume_pos > 0:
                        self.log("服务器不支持断点续传，重新下载", "WARNING")
                        resume_pos = 0
                        if os.path.exists(output_file):
                            os.remove(output_file)
                else:
                    raise Exception(f"HTTP错误: {response.status_code}")
                
                # 获取总大小
                if 'Content-Length' in response.headers:
                    content_length = int(response.headers['Content-Length'])
                    if response.status_code == 206:
                        self.total_size = resume_pos + content_length
                    else:
                        self.total_size = content_length
                else:
                    self.total_size = self.expected_size
                
                self.log(f"文件总大小: {self.total_size:,} 字节")
                
                # 开始下载
                mode = 'ab' if resume_pos > 0 else 'wb'
                with open(output_file, mode) as f:
                    self.downloaded_size = resume_pos
                    last_update_time = time.time()
                    
                    for chunk in response.iter_content(chunk_size=self.chunk_size):
                        if self.is_cancelled:
                            self.log("下载已取消", "WARNING")
                            return False
                        
                        if chunk:
                            f.write(chunk)
                            f.flush()  # 立即刷新缓冲区
                            self.downloaded_size += len(chunk)
                            
                            # 更新进度 (每100ms更新一次)
                            current_time = time.time()
                            if current_time - last_update_time >= 0.1:
                                if self.total_size > 0:
                                    progress = (self.downloaded_size / self.total_size) * 100
                                    if self.progress_callback:
                                        self.progress_callback(progress)
                                last_update_time = current_time
                    
                    # 强制同步到磁盘
                    f.flush()
                    os.fsync(f.fileno())
                
                # 验证下载完整性
                actual_size = os.path.getsize(output_file)
                self.log(f"下载完成，文件大小: {actual_size:,} 字节")
                
                # 大小验证
                if self.total_size > 0:
                    size_diff = abs(actual_size - self.total_size)
                    size_tolerance = max(1024, self.total_size * 0.001)  # 1KB或0.1%的容差
                    
                    if size_diff <= size_tolerance:
                        self.log("文件大小验证通过", "SUCCESS")
                    else:
                        raise Exception(f"文件大小不匹配: 实际{actual_size}, 预期{self.total_size}, 差异{size_diff}")
                
                # MD5验证 (如果提供了hash)
                if self.expected_hash:
                    self.log("验证文件MD5...")
                    actual_hash = self.calculate_md5(output_file)
                    if actual_hash.lower() == self.expected_hash.lower():
                        self.log("MD5验证通过", "SUCCESS")
                    else:
                        raise Exception(f"MD5不匹配: 实际{actual_hash}, 预期{self.expected_hash}")
                
                # 最终进度更新
                if self.progress_callback:
                    self.progress_callback(100.0)
                
                self.log("下载成功完成!", "SUCCESS")
                return True
                
            except Exception as e:
                self.log(f"下载失败: {e}", "ERROR")
                
                if attempt < self.max_retries - 1:
                    self.log(f"等待 {self.retry_delay} 秒后重试...", "WARNING")
                    time.sleep(self.retry_delay)
                    self.retry_delay *= 2  # 指数退避
                else:
                    self.log("所有重试都失败了", "ERROR")
                    return False
        
        return False
    
    def calculate_md5(self, file_path):
        """计算文件MD5"""
        md5_hash = hashlib.md5()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
        return md5_hash.hexdigest()
    
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True

class DownloadDialog:
    """下载对话框"""
    
    def __init__(self, parent=None):
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.root.title("🚀 超强下载器")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 居中显示
        self.root.transient(parent)
        self.root.grab_set()
        
        self.downloader = None
        self.download_thread = None
        
        self.create_gui()
        
    def create_gui(self):
        """创建GUI"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="🚀 超强下载器",
            font=("微软雅黑", 16, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=10)
        
        # 状态标签
        self.status_label = tk.Label(
            self.root,
            text="准备下载...",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        self.status_label.pack(pady=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            maximum=100,
            length=400
        )
        self.progress_bar.pack(pady=10)
        
        # 进度文本
        self.progress_text = tk.Label(
            self.root,
            text="0.0%",
            font=("微软雅黑", 12, "bold"),
            fg="#27ae60"
        )
        self.progress_text.pack(pady=5)
        
        # 详细信息
        self.detail_label = tk.Label(
            self.root,
            text="",
            font=("微软雅黑", 9),
            fg="#666666"
        )
        self.detail_label.pack(pady=5)
        
        # 日志区域
        log_frame = tk.LabelFrame(self.root, text="下载日志", font=("微软雅黑", 9))
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.log_text = tk.Text(log_frame, height=8, font=("Consolas", 8))
        scrollbar = tk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 按钮
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        self.cancel_button = tk.Button(
            button_frame,
            text="取消下载",
            command=self.cancel_download,
            font=("微软雅黑", 10),
            bg="#e74c3c",
            fg="white",
            state=tk.DISABLED
        )
        self.cancel_button.pack(side=tk.LEFT, padx=10)
        
        self.close_button = tk.Button(
            button_frame,
            text="关闭",
            command=self.close_dialog,
            font=("微软雅黑", 10),
            bg="#95a5a6",
            fg="white"
        )
        self.close_button.pack(side=tk.LEFT, padx=10)
    
    def start_download(self, url, params, expected_size=0, expected_hash="", output_file=None):
        """开始下载"""
        if not output_file:
            output_file = os.path.join(tempfile.gettempdir(), "super_download.tmp")
        
        self.output_file = output_file
        self.downloader = SuperDownloader(url, params, expected_size, expected_hash)
        self.downloader.set_progress_callback(self.update_progress)
        self.downloader.set_log_callback(self.log_message)
        
        self.cancel_button.config(state=tk.NORMAL)
        self.status_label.config(text="正在下载...")
        
        def download_thread():
            success = self.downloader.download_with_resume(output_file)
            
            # 在主线程中更新UI
            self.root.after(0, lambda: self.download_finished(success))
        
        self.download_thread = threading.Thread(target=download_thread, daemon=True)
        self.download_thread.start()
    
    def update_progress(self, progress):
        """更新进度"""
        self.root.after(0, lambda: self._update_progress_ui(progress))
    
    def _update_progress_ui(self, progress):
        """在主线程中更新进度UI"""
        self.progress_var.set(progress)
        self.progress_text.config(text=f"{progress:.1f}%")
        
        if hasattr(self.downloader, 'downloaded_size') and hasattr(self.downloader, 'total_size'):
            downloaded = self.downloader.downloaded_size
            total = self.downloader.total_size
            if total > 0:
                self.detail_label.config(
                    text=f"{downloaded:,} / {total:,} 字节 ({downloaded/1024/1024:.1f} / {total/1024/1024:.1f} MB)"
                )
    
    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        self.root.after(0, lambda: self._log_message_ui(message, level))
    
    def _log_message_ui(self, message, level):
        """在主线程中更新日志UI"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "ERROR":
            prefix = "❌"
        elif level == "SUCCESS":
            prefix = "✅"
        elif level == "WARNING":
            prefix = "⚠️"
        else:
            prefix = "ℹ️"
        
        log_line = f"[{timestamp}] {prefix} {message}\n"
        self.log_text.insert(tk.END, log_line)
        self.log_text.see(tk.END)
    
    def download_finished(self, success):
        """下载完成"""
        self.cancel_button.config(state=tk.DISABLED)
        
        if success:
            self.status_label.config(text="下载完成!", fg="#27ae60")
            messagebox.showinfo("下载完成", f"文件已成功下载到:\n{self.output_file}")
        else:
            self.status_label.config(text="下载失败!", fg="#e74c3c")
            messagebox.showerror("下载失败", "下载过程中出现错误，请查看日志了解详情。")
    
    def cancel_download(self):
        """取消下载"""
        if self.downloader:
            self.downloader.cancel()
            self.status_label.config(text="正在取消...", fg="#f39c12")
    
    def close_dialog(self):
        """关闭对话框"""
        if self.downloader and not self.downloader.is_cancelled:
            if messagebox.askyesno("确认", "下载正在进行中，确定要关闭吗？"):
                self.cancel_download()
                self.root.destroy()
        else:
            self.root.destroy()

def test_super_downloader():
    """测试超强下载器"""
    # 创建测试对话框
    dialog = DownloadDialog()
    
    # 测试下载
    url = "http://198.23.135.176:5000/update/download"
    params = {
        'key': 'ADMIN_BYPASS',
        'device_id': 'ADMIN-DEVICE-001',
        'version': '2.1.1'
    }
    
    output_file = "test_download.exe"
    dialog.start_download(url, params, output_file=output_file)
    
    dialog.root.mainloop()

if __name__ == "__main__":
    test_super_downloader()
