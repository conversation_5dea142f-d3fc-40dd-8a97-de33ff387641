#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复服务器版本信息文件
"""

import paramiko
import json
import os
from datetime import datetime

def create_version_info_file():
    """在服务器上创建版本信息文件"""
    print("🔧 修复服务器版本信息文件")
    print("=" * 50)
    
    # 服务器连接信息
    server_info = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0'
    }
    
    # 版本信息内容
    version_info = {
        "version": "2.1.0",
        "release_date": datetime.now().isoformat(),
        "description": "亚马逊蓝图工具 v2.1.0",
        "file_size": 0,
        "sha256": "",
        "upload_time": datetime.now().isoformat(),
        "download_url": "/update/download?version=2.1.0"
    }
    
    try:
        print("🔗 连接到服务器...")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(**server_info)
        
        print("✅ SSH连接成功")
        
        # 创建必要的目录
        print("📁 创建更新目录...")
        commands = [
            "mkdir -p /opt/license_manager/updates",
            "mkdir -p /opt/license_manager/updates/files"
        ]
        
        for cmd in commands:
            stdin, stdout, stderr = ssh.exec_command(cmd)
            exit_status = stdout.channel.recv_exit_status()
            if exit_status == 0:
                print(f"✅ 执行成功: {cmd}")
            else:
                error = stderr.read().decode()
                print(f"⚠️ 执行警告: {cmd} - {error}")
        
        # 创建版本信息文件
        print("📄 创建版本信息文件...")
        version_json = json.dumps(version_info, ensure_ascii=False, indent=2)
        
        # 使用echo命令写入文件
        create_file_cmd = f'echo \'{version_json}\' > /opt/license_manager/updates/version_info.json'
        stdin, stdout, stderr = ssh.exec_command(create_file_cmd)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("✅ 版本信息文件创建成功")
        else:
            error = stderr.read().decode()
            print(f"❌ 创建文件失败: {error}")
            return False
        
        # 验证文件是否创建成功
        print("🔍 验证文件创建...")
        stdin, stdout, stderr = ssh.exec_command('cat /opt/license_manager/updates/version_info.json')
        file_content = stdout.read().decode()
        
        if file_content.strip():
            print("✅ 文件内容验证成功")
            print(f"📄 文件内容预览:\n{file_content[:200]}...")
        else:
            print("❌ 文件内容为空")
            return False
        
        # 设置文件权限
        print("🔐 设置文件权限...")
        chmod_cmd = "chmod 644 /opt/license_manager/updates/version_info.json"
        stdin, stdout, stderr = ssh.exec_command(chmod_cmd)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("✅ 文件权限设置成功")
        else:
            print("⚠️ 文件权限设置失败，但不影响使用")
        
        # 重启服务
        print("🔄 重启license-manager服务...")
        restart_cmd = "systemctl restart license-manager"
        stdin, stdout, stderr = ssh.exec_command(restart_cmd)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("✅ 服务重启成功")
        else:
            error = stderr.read().decode()
            print(f"⚠️ 服务重启失败: {error}")
        
        ssh.close()
        print("✅ SSH连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

def test_update_api_after_fix():
    """修复后测试更新API"""
    print("\n" + "=" * 50)
    print("🧪 测试修复后的更新API")
    print("=" * 50)
    
    import requests
    import time
    
    # 等待服务重启
    print("⏰ 等待服务重启...")
    time.sleep(3)
    
    server_url = "http://**************:5000"
    
    test_cases = [
        {
            "name": "管理员权限测试",
            "params": {
                "current_version": "2.0.0",
                "key": "ADMIN_BYPASS", 
                "device_id": "ADMIN-DEVICE-001"
            }
        },
        {
            "name": "普通用户测试",
            "params": {
                "current_version": "2.0.0",
                "key": "83R2AXQK-20250725-67c80d8d",
                "device_id": "20cc47fd9ca63e67"
            }
        }
    ]
    
    for test in test_cases:
        print(f"\n🧪 {test['name']}")
        
        try:
            response = requests.get(
                f"{server_url}/update/check",
                params=test['params'],
                timeout=10
            )
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API调用成功")
                print(f"📄 成功: {data.get('success', False)}")
                print(f"📄 有更新: {data.get('has_update', False)}")
                print(f"📄 当前版本: {data.get('current_version', 'Unknown')}")
                print(f"📄 最新版本: {data.get('latest_version', 'Unknown')}")
                
            elif response.status_code == 401:
                data = response.json()
                print(f"🚫 认证失败: {data.get('message', 'Unknown')}")
                
            elif response.status_code == 500:
                print(f"❌ 仍然是500错误")
                print(f"📄 错误响应: {response.text[:200]}...")
                
            else:
                print(f"⚠️ 其他状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🎯 修复服务器500错误")
    print("🔧 问题: /opt/license_manager/updates/version_info.json 文件不存在")
    print("💡 解决: 创建版本信息文件并重启服务")
    print()
    
    if create_version_info_file():
        print("\n🎉 版本信息文件修复成功！")
        test_update_api_after_fix()
        
        print("\n" + "=" * 50)
        print("📋 修复总结:")
        print("✅ 创建了 /opt/license_manager/updates/ 目录")
        print("✅ 创建了 version_info.json 文件")
        print("✅ 设置了正确的文件权限")
        print("✅ 重启了 license-manager 服务")
        
        print("\n🚀 现在license_client.py应该可以正常工作了！")
        print("💡 不会再出现500错误")
        
    else:
        print("\n❌ 修复失败，请检查:")
        print("1. 服务器连接是否正常")
        print("2. SSH权限是否足够")
        print("3. 磁盘空间是否充足")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
