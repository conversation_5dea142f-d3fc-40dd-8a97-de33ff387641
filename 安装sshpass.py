#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动安装sshpass工具
解决Windows系统缺少sshpass的问题
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path

def download_sshpass():
    """下载sshpass工具"""
    print("📥 下载sshpass工具...")
    
    # sshpass下载链接
    sshpass_url = "https://github.com/PowerShell/Win32-OpenSSH/releases/download/v8.1.0.0p1-Beta/OpenSSH-Win64.zip"
    
    try:
        # 创建临时目录
        temp_dir = Path("temp_sshpass")
        temp_dir.mkdir(exist_ok=True)
        
        zip_file = temp_dir / "openssh.zip"
        
        print(f"📥 正在下载: {sshpass_url}")
        urllib.request.urlretrieve(sshpass_url, zip_file)
        print("✅ 下载完成")
        
        # 解压文件
        print("📦 解压文件...")
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # 查找ssh.exe和scp.exe
        openssh_dir = temp_dir / "OpenSSH-Win64"
        if openssh_dir.exists():
            ssh_exe = openssh_dir / "ssh.exe"
            scp_exe = openssh_dir / "scp.exe"
            
            if ssh_exe.exists() and scp_exe.exists():
                # 复制到当前目录
                shutil.copy2(ssh_exe, "ssh.exe")
                shutil.copy2(scp_exe, "scp.exe")
                print("✅ SSH工具安装完成")
                
                # 清理临时文件
                shutil.rmtree(temp_dir)
                return True
        
        print("❌ 未找到SSH工具")
        return False
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def create_sshpass_alternative():
    """创建sshpass替代方案"""
    print("🔧 创建sshpass替代方案...")
    
    # 创建简单的SSH连接脚本
    ssh_script = '''@echo off
set HOST=%1
set USER=%2
set PASS=%3
set CMD=%4

echo 正在连接到 %USER%@%HOST%...
echo %CMD% | ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null %USER%@%HOST%
'''
    
    with open("ssh_connect.bat", "w", encoding="utf-8") as f:
        f.write(ssh_script)
    
    print("✅ SSH连接脚本创建完成")

def install_git_bash_ssh():
    """使用Git Bash的SSH工具"""
    print("🔍 检查Git Bash...")
    
    # 常见的Git安装路径
    git_paths = [
        r"C:\Program Files\Git\usr\bin",
        r"C:\Program Files (x86)\Git\usr\bin",
        r"C:\Git\usr\bin"
    ]
    
    for git_path in git_paths:
        ssh_path = os.path.join(git_path, "ssh.exe")
        scp_path = os.path.join(git_path, "scp.exe")
        
        if os.path.exists(ssh_path) and os.path.exists(scp_path):
            print(f"✅ 找到Git SSH工具: {git_path}")
            
            # 复制到当前目录
            try:
                shutil.copy2(ssh_path, "ssh.exe")
                shutil.copy2(scp_path, "scp.exe")
                print("✅ SSH工具复制完成")
                return True
            except Exception as e:
                print(f"❌ 复制失败: {e}")
    
    print("❌ 未找到Git SSH工具")
    return False

def test_ssh_tools():
    """测试SSH工具"""
    print("🧪 测试SSH工具...")
    
    try:
        # 测试ssh命令
        result = subprocess.run(["ssh", "-V"], capture_output=True, text=True)
        if result.returncode == 0 or "OpenSSH" in result.stderr:
            print("✅ SSH工具可用")
            return True
        else:
            print("❌ SSH工具不可用")
            return False
    except FileNotFoundError:
        print("❌ 找不到SSH命令")
        return False

def create_python_ssh_solution():
    """创建Python SSH解决方案"""
    print("🐍 创建Python SSH解决方案...")
    
    try:
        # 尝试安装paramiko
        subprocess.run([sys.executable, "-m", "pip", "install", "paramiko"], check=True)
        print("✅ paramiko安装成功")
        
        # 创建Python SSH脚本
        python_ssh_script = '''#!/usr/bin/env python3
import paramiko
import sys
import os

def ssh_connect(host, username, password, command):
    """使用paramiko进行SSH连接"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        
        client.close()
        
        print(output)
        if error:
            print(error, file=sys.stderr)
        
        return stdout.channel.recv_exit_status() == 0
        
    except Exception as e:
        print(f"SSH连接失败: {e}", file=sys.stderr)
        return False

if __name__ == "__main__":
    if len(sys.argv) != 5:
        print("用法: python ssh_python.py <host> <username> <password> <command>")
        sys.exit(1)
    
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    command = sys.argv[4]
    
    success = ssh_connect(host, username, password, command)
    sys.exit(0 if success else 1)
'''
        
        with open("ssh_python.py", "w", encoding="utf-8") as f:
            f.write(python_ssh_script)
        
        print("✅ Python SSH脚本创建完成")
        return True
        
    except subprocess.CalledProcessError:
        print("❌ paramiko安装失败")
        return False

def main():
    """主函数"""
    print("🔧 sshpass工具安装器")
    print("=" * 50)
    print("解决Windows系统缺少sshpass的问题")
    print()
    
    # 检查当前是否有SSH工具
    if test_ssh_tools():
        print("✅ SSH工具已可用，无需安装")
        return True
    
    print("❌ 未找到SSH工具，开始安装...")
    print()
    
    # 方案1: 使用Git Bash的SSH
    print("🔍 方案1: 使用Git Bash SSH工具")
    if install_git_bash_ssh():
        if test_ssh_tools():
            print("🎉 Git Bash SSH工具安装成功！")
            return True
    
    # 方案2: 下载OpenSSH
    print("\n📥 方案2: 下载OpenSSH工具")
    if download_sshpass():
        if test_ssh_tools():
            print("🎉 OpenSSH工具安装成功！")
            return True
    
    # 方案3: Python SSH解决方案
    print("\n🐍 方案3: Python SSH解决方案")
    if create_python_ssh_solution():
        print("🎉 Python SSH解决方案创建成功！")
        print("\n📋 使用说明:")
        print("部署工具将自动使用Python SSH方案")
        return True
    
    # 方案4: 手动指导
    print("\n📋 方案4: 手动安装指导")
    print("=" * 30)
    print("请手动安装SSH工具:")
    print("1. 安装Git for Windows (包含SSH工具)")
    print("   下载地址: https://git-scm.com/download/win")
    print("2. 或者安装Windows OpenSSH")
    print("   在Windows功能中启用OpenSSH客户端")
    print("3. 或者使用WSL (Windows Subsystem for Linux)")
    print()
    
    return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 SSH工具安装完成！")
        print("现在可以使用部署工具了")
    else:
        print("\n❌ SSH工具安装失败")
        print("请按照手动安装指导进行操作")
    
    input("\n按回车键退出...")
