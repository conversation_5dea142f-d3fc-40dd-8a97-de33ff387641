@echo off
chcp 65001 >nul
title 🔥 开放端口5000

echo.
echo ==========================================
echo 🔥 开放端口5000并测试网络访问
echo ==========================================
echo.
echo 🔍 问题分析:
echo • ✅ Flask服务正常运行
echo • ✅ 端口5000本地监听正常
echo • ✅ 本地连接测试成功
echo • ❌ 外部网络无法访问
echo.
echo 🎯 解决方案:
echo • 🔥 开放防火墙端口5000
echo • 🧪 测试外部网络访问
echo • 🔧 如果失败，设置nginx代理到端口80
echo.

REM 检查requests模块
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少requests模块，正在安装...
    python -m pip install requests
    if errorlevel 1 (
        echo ❌ requests安装失败
        pause
        exit /b 1
    )
    echo ✅ requests安装完成
    echo.
)

echo 🔥 开始开放端口并测试...
echo.

REM 运行脚本
python "开放端口5000.py"

echo.
echo 👋 处理完成
pause
