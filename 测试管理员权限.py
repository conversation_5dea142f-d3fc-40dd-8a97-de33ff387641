#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试管理员权限 - 验证管理员密钥是否可以访问所有API
"""

import requests
import json

def test_admin_permissions():
    """测试管理员权限"""
    print("🔑 测试管理员权限")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    admin_key = "V09M4HCC-20350729-def2f7"
    device_id = "ADMIN-DEVICE-001"
    
    test_cases = [
        {
            "name": "检查更新API",
            "url": f"{server_url}/update/check",
            "method": "GET",
            "params": {
                'current_version': '1.0.0',
                'key': admin_key,
                'device_id': device_id
            },
            "expected_status": [200, 400]
        },
        {
            "name": "更新统计API",
            "url": f"{server_url}/update/stats",
            "method": "GET",
            "params": {
                'key': admin_key,
                'device_id': device_id
            },
            "expected_status": [200, 404]
        },
        {
            "name": "下载更新API",
            "url": f"{server_url}/update/download",
            "method": "GET",
            "params": {
                'version': '1.0.0',
                'key': admin_key,
                'device_id': device_id
            },
            "expected_status": [200, 404]
        },
        {
            "name": "授权检查API",
            "url": f"{server_url}/license/check",
            "method": "POST",
            "json": {
                'key': admin_key,
                'device_id': device_id
            },
            "expected_status": [200]
        }
    ]
    
    print("🌐 开始测试管理员权限...")
    print()
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test in enumerate(test_cases, 1):
        print(f"📋 测试 {i}/{total_count}: {test['name']}")
        print(f"   🌐 URL: {test['url']}")
        
        try:
            if test['method'] == 'GET':
                response = requests.get(
                    test['url'],
                    params=test.get('params', {}),
                    timeout=10
                )
            else:
                response = requests.post(
                    test['url'],
                    json=test.get('json', {}),
                    timeout=10
                )
            
            status_code = response.status_code
            print(f"   📊 状态码: {status_code}")
            
            # 检查状态码是否符合预期
            expected = test['expected_status']
            if status_code in expected:
                success_count += 1
                print(f"   ✅ 权限验证通过")
                
                # 尝试解析响应
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = response.json()
                        
                        if isinstance(data, dict):
                            if 'success' in data:
                                print(f"   📄 成功: {data['success']}")
                            if 'message' in data:
                                message = data['message'][:50]
                                print(f"   📄 消息: {message}...")
                            if 'permissions' in data:
                                print(f"   🔒 权限: {data['permissions']}")
                                
                except Exception as e:
                    print(f"   📄 响应: {response.text[:100]}...")
                    
            else:
                print(f"   ❌ 权限验证失败 (期望: {expected})")
                if status_code == 401:
                    print(f"   🚫 未授权 - 密钥可能无效")
                elif status_code == 403:
                    print(f"   🚫 禁止访问 - 权限不足")
                else:
                    print(f"   📄 错误: {response.text[:100]}...")
            
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        
        print()
    
    # 总结
    print("=" * 50)
    print(f"🎯 测试完成: {success_count}/{total_count} 个API权限正常")
    
    if success_count == total_count:
        print("🎉 所有管理员权限测试通过！")
        return True
    elif success_count >= total_count * 0.75:
        print("✅ 大部分管理员权限正常")
        return True
    else:
        print("⚠️ 多个API权限有问题，需要检查密钥")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 测试管理员密钥权限")
        print("🔑 密钥: V09M4HCC-20350729-def2f7")
        print("📱 设备: ADMIN-DEVICE-001")
        print()
        
        if test_admin_permissions():
            print("\n✅ 管理员权限验证成功！")
            print("\n📋 现在你可以:")
            print("1. 启动exe文件管理工具")
            print("2. 使用所有管理功能")
            print("3. 上传和下载exe文件")
        else:
            print("\n⚠️ 部分权限验证失败")
            print("建议检查服务器配置和密钥状态")
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
