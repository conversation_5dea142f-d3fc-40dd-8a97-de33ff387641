#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Flask路由问题
添加基本的路由和测试页面
"""

import paramiko
import sys
import time

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def fix_flask_routes():
    """修复Flask路由"""
    print("🔧 修复Flask路由问题")
    print("=" * 40)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 步骤1: 检查当前路由
    print("🔍 步骤1: 检查当前路由...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"grep -n '@app.route' {config['deploy_path']}/license_server.py | head -10")
    if success:
        print("当前路由:")
        for line in output.split('\n'):
            if line.strip():
                print(f"   {line}")
    print()
    
    # 步骤2: 添加基本路由
    print("🔧 步骤2: 添加基本路由...")
    
    # 创建路由补丁
    route_patch = '''
# 添加基本路由
@app.route('/')
def index():
    """主页"""
    return jsonify({
        "status": "success",
        "message": "亚马逊蓝图工具授权服务器",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "endpoints": [
            "/",
            "/health",
            "/api/check_update",
            "/api/status"
        ]
    })

@app.route('/health')
def health():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/status')
def api_status():
    """API状态"""
    return jsonify({
        "api_status": "running",
        "server": "license_manager",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/test')
def test():
    """测试页面"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>亚马逊蓝图工具授权服务器</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>🎉 授权服务器运行正常</h1>
        <p>服务器时间: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''</p>
        <h2>可用接口:</h2>
        <ul>
            <li><a href="/">/</a> - 主页</li>
            <li><a href="/health">/health</a> - 健康检查</li>
            <li><a href="/api/status">/api/status</a> - API状态</li>
            <li><a href="/api/check_update">/api/check_update</a> - 检查更新</li>
            <li><a href="/test">/test</a> - 测试页面</li>
        </ul>
    </body>
    </html>
    '''
'''
    
    # 步骤3: 备份并修改文件
    print("📝 步骤3: 备份并修改文件...")
    
    # 停止服务
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl stop license-manager")
    print("   🛑 服务已停止")
    
    # 备份原文件
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"cp {config['deploy_path']}/license_server.py {config['deploy_path']}/license_server.py.backup2")
    print("   💾 文件已备份")
    
    # 在文件末尾添加路由（在if __name__ == "__main__":之前）
    command = f"""cd {config['deploy_path']} && \
sed -i '/if __name__ == "__main__":/i\\{route_patch}' license_server.py && \
echo "路由已添加"
"""
    
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], command)
    if success:
        print("   ✅ 路由已添加")
    else:
        print(f"   ❌ 路由添加失败: {error}")
        # 尝试另一种方法
        print("   🔄 尝试另一种方法...")
        
        # 直接在文件末尾添加
        command2 = f"""cd {config['deploy_path']} && \
python3 -c "
with open('license_server.py', 'r') as f:
    content = f.read()

# 在if __name__ == '__main__':之前添加路由
if 'if __name__ == \"__main__\":' in content:
    parts = content.split('if __name__ == \"__main__\":')
    new_content = parts[0] + '''{route_patch}''' + 'if __name__ == \"__main__\":' + parts[1]
    with open('license_server.py', 'w') as f:
        f.write(new_content)
    print('路由添加成功')
else:
    print('未找到main函数')
"
"""
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], command2)
        if success2:
            print("   ✅ 路由添加成功")
        else:
            print(f"   ❌ 仍然失败: {error2}")
    print()
    
    # 步骤4: 启动服务
    print("🚀 步骤4: 启动服务...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl start license-manager")
    if success:
        print("   ✅ 服务已启动")
    else:
        print(f"   ❌ 服务启动失败: {error}")
        return False
    
    # 等待服务启动
    time.sleep(5)
    print()
    
    # 步骤5: 测试路由
    print("🧪 步骤5: 测试路由...")
    
    test_urls = [
        ("主页", "http://localhost:44285/"),
        ("健康检查", "http://localhost:44285/health"),
        ("API状态", "http://localhost:44285/api/status"),
        ("测试页面", "http://localhost:44285/test")
    ]
    
    success_count = 0
    for name, url in test_urls:
        success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                           f"curl -s {url}")
        if success and output.strip():
            print(f"   ✅ {name}: 正常")
            success_count += 1
        else:
            print(f"   ❌ {name}: 失败")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_urls)} 个路由正常")
    
    return success_count > 0

def main():
    """主函数"""
    try:
        print("🔍 当前问题: 访问根路径显示 'Not Found'")
        print("🎯 解决方案: 添加基本路由和测试页面")
        print()
        
        success = fix_flask_routes()
        
        if success:
            print("\n🎉 路由修复成功！")
            print("\n🌐 现在可以访问:")
            print("• 主页: http://**************:44285/")
            print("• 健康检查: http://**************:44285/health")
            print("• API状态: http://**************:44285/api/status")
            print("• 测试页面: http://**************:44285/test")
            print("• 检查更新: http://**************:44285/api/check_update")
        else:
            print("\n❌ 路由修复失败")
            print("💡 建议手动检查服务状态")
            
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
