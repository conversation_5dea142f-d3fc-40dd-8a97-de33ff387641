@echo off
chcp 65001 >nul
echo 🔄 外部更新器启动...
echo.

REM 等待主程序完全退出
echo ⏳ 等待主程序退出...
timeout /t 3 /nobreak >nul

REM 检查新文件是否存在
if not exist "%~1" (
    echo ❌ 错误: 新文件不存在 %~1
    pause
    exit /b 1
)

REM 检查目标文件是否存在
if not exist "%~2" (
    echo ❌ 错误: 目标文件不存在 %~2
    pause
    exit /b 1
)

echo 📁 备份原文件...
copy "%~2" "%~2.backup" >nul
if errorlevel 1 (
    echo ❌ 备份失败
    pause
    exit /b 1
)

echo 🔄 替换文件...
copy "%~1" "%~2" >nul
if errorlevel 1 (
    echo ❌ 文件替换失败，恢复备份...
    copy "%~2.backup" "%~2" >nul
    del "%~2.backup" >nul
    pause
    exit /b 1
)

echo ✅ 更新成功！
echo 🚀 重新启动程序...

REM 清理临时文件
del "%~1" >nul 2>&1
del "%~2.backup" >nul 2>&1

REM 重新启动程序
start "" "%~2"

echo 🎉 更新完成！
timeout /t 2 /nobreak >nul
exit /b 0
