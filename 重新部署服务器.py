#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新部署服务器 - 上传修改后的license_server.py并重启服务
"""

import paramiko
import os

def deploy_server():
    """重新部署服务器"""
    print("🚀 重新部署服务器")
    print("=" * 50)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    # 检查本地文件
    local_file = "license_server.py"
    if not os.path.exists(local_file):
        print(f"❌ 本地文件不存在: {local_file}")
        return False
    
    print(f"✅ 找到本地文件: {local_file}")
    print(f"📊 文件大小: {os.path.getsize(local_file)} 字节")
    
    try:
        # 建立SSH连接
        print("\n🔗 建立SSH连接...")
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(config['host'], username=config['username'], 
                      password=config['password'], timeout=30)
        
        # 建立SFTP连接
        sftp = client.open_sftp()
        
        # 步骤1: 停止服务
        print("\n🛑 步骤1: 停止服务...")
        stdin, stdout, stderr = client.exec_command("systemctl stop license-manager")
        stdout.channel.recv_exit_status()
        print("   ✅ 服务已停止")
        
        # 步骤2: 备份现有文件
        print("\n📁 步骤2: 备份现有文件...")
        from datetime import datetime
        backup_name = f"license_server.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        stdin, stdout, stderr = client.exec_command(
            f"cp {config['deploy_path']}/license_server.py {config['deploy_path']}/{backup_name}"
        )
        stdout.channel.recv_exit_status()
        print(f"   ✅ 已备份到: {backup_name}")
        
        # 步骤3: 上传新文件
        print("\n📤 步骤3: 上传新文件...")
        remote_file = f"{config['deploy_path']}/license_server.py"
        sftp.put(local_file, remote_file)
        print("   ✅ 文件上传完成")
        
        # 步骤4: 设置文件权限
        print("\n🔐 步骤4: 设置文件权限...")
        stdin, stdout, stderr = client.exec_command(f"chmod +x {remote_file}")
        stdout.channel.recv_exit_status()
        print("   ✅ 权限设置完成")
        
        # 步骤5: 验证文件
        print("\n🔍 步骤5: 验证文件...")
        stdin, stdout, stderr = client.exec_command(f"python3 -m py_compile {remote_file}")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ Python语法检查通过")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ Python语法错误: {error}")
            return False
        
        # 步骤6: 创建更新目录
        print("\n📁 步骤6: 创建更新目录...")
        stdin, stdout, stderr = client.exec_command(f"mkdir -p {config['deploy_path']}/updates/files")
        stdout.channel.recv_exit_status()
        print("   ✅ 更新目录已创建")
        
        # 步骤7: 启动服务
        print("\n🚀 步骤7: 启动服务...")
        stdin, stdout, stderr = client.exec_command("systemctl start license-manager")
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务启动成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 服务启动失败: {error}")
            return False
        
        # 等待服务启动
        import time
        print("\n⏳ 等待服务完全启动...")
        time.sleep(10)
        
        # 步骤8: 检查服务状态
        print("\n🔄 步骤8: 检查服务状态...")
        stdin, stdout, stderr = client.exec_command("systemctl is-active license-manager")
        status = stdout.read().decode('utf-8').strip()
        
        if status == "active":
            print("   ✅ 服务运行正常")
        else:
            print(f"   ❌ 服务状态异常: {status}")
            
            # 查看错误日志
            stdin, stdout, stderr = client.exec_command("journalctl -u license-manager --since '1 minute ago' --no-pager")
            log_output = stdout.read().decode('utf-8')
            print(f"   错误日志:\n{log_output}")
            return False
        
        # 步骤9: 检查端口监听
        print("\n🌐 步骤9: 检查端口监听...")
        stdin, stdout, stderr = client.exec_command("netstat -tlnp | grep :5000")
        port_output = stdout.read().decode('utf-8')
        
        if port_output.strip():
            print(f"   ✅ 端口5000已监听:\n{port_output}")
        else:
            print("   ❌ 端口5000未监听")
            
            # 检查其他端口
            stdin, stdout, stderr = client.exec_command("netstat -tlnp | grep python")
            python_ports = stdout.read().decode('utf-8')
            if python_ports.strip():
                print(f"   Python监听的端口:\n{python_ports}")
        
        # 步骤10: 测试API
        print("\n🧪 步骤10: 测试API...")
        import requests
        
        test_urls = [
            "http://**************:5000",
            "http://**************:5000/update/stats"
        ]
        
        success_count = 0
        for url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code in [200, 404]:  # 404也算连接成功
                    print(f"   ✅ {url}: HTTP {response.status_code}")
                    success_count += 1
                else:
                    print(f"   ❌ {url}: HTTP {response.status_code}")
            except Exception as e:
                print(f"   ❌ {url}: 连接失败 - {e}")
        
        # 关闭连接
        sftp.close()
        client.close()
        
        if success_count > 0:
            print("\n🎉 部署成功！")
            print("🌐 服务器地址: http://**************:5000")
            print("📋 可用API:")
            print("  • 检查更新: /update/check")
            print("  • 下载更新: /update/download")
            print("  • 上传更新: /update/upload")
            print("  • 获取统计: /update/stats")
            return True
        else:
            print("\n⚠️ 部署完成，但API测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 部署过程异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("🎯 目标: 重新部署license_server.py到服务器")
        print("🔧 方案: 停止服务 → 上传文件 → 启动服务 → 测试API")
        print()
        
        if deploy_server():
            print("\n✅ 部署成功！现在可以使用自动更新功能了")
        else:
            print("\n❌ 部署失败，请检查错误信息")
        
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
