# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件和子模块
fake_useragent_datas = collect_data_files('fake_useragent')
openpyxl_datas = collect_data_files('openpyxl')
fake_useragent_hiddenimports = collect_submodules('fake_useragent')
openpyxl_hiddenimports = collect_submodules('openpyxl')

# 合并所有数据文件
datas = []
datas += fake_useragent_datas
datas += openpyxl_datas

# 添加图标文件
if os.path.exists('icon.ico'):
    datas += [('icon.ico', '.')]

# 隐藏导入列表
hiddenimports = [
    # 核心依赖
    'cryptography',
    'cryptography.hazmat.primitives.ciphers',
    'cryptography.hazmat.backends.openssl',
    'cryptography.fernet',
    
    # Excel处理 - 完整支持
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.workbook.workbook',
    'openpyxl.worksheet',
    'openpyxl.worksheet.worksheet',
    'openpyxl.cell',
    'openpyxl.cell.cell',
    'openpyxl.styles',
    'openpyxl.utils',
    'openpyxl.reader.excel',
    'openpyxl.writer.excel',
    'openpyxl.xml.functions',
    'openpyxl.xml.constants',
    'xlsxwriter',
    'xlsxwriter.workbook',
    'xlsxwriter.worksheet',
    
    # Pandas Excel支持
    'pandas',
    'pandas.io.excel',
    'pandas.io.excel._base',
    'pandas.io.excel._openpyxl',
    'pandas.io.excel._xlsxwriter',
    'pandas.io.common',
    'pandas.io.parsers',
    
    # Web相关
    'selenium',
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.chrome.options',
    'webdriver_manager',
    'webdriver_manager.chrome',
    
    # fake_useragent完整支持
    'fake_useragent',
    'fake_useragent.fake',
    'fake_useragent.utils',
    'fake_useragent.settings',
    'fake_useragent.errors',
    
    # requests完整支持
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.models',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    
    # BeautifulSoup支持
    'beautifulsoup4',
    'bs4',
    'bs4.builder',
    'bs4.builder._html5lib',
    'bs4.builder._htmlparser',
    'bs4.builder._lxml',
    
    # lxml支持
    'lxml',
    'lxml.etree',
    'lxml.html',
    
    # 其他依赖
    'psutil',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    
    # 系统相关
    'platform',
    'socket',
    'threading',
    'queue',
    'json',
    'base64',
    'hashlib',
    'datetime',
    'time',
    'os',
    'sys',
    'subprocess',
    'tempfile',
    'shutil',
    'pathlib',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'ssl',
]

# 添加收集到的隐藏导入
hiddenimports += fake_useragent_hiddenimports
hiddenimports += openpyxl_hiddenimports

# 排除的模块（减小文件大小）
excludes = [
    'matplotlib',
    'numpy.distutils',
    'scipy',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',
    'unittest',
    'doctest',
    'pydoc',
    'xml.etree.cElementTree',
]

block_cipher = None

a = Analysis(
    ['license_client.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='亚马逊蓝图工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
