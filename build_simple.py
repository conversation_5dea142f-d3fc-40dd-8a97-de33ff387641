#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的优化构建脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        "license_client.py",
        "采集8.py.encrypted",
        "筛品终极版1.py.encrypted", 
        "历史价格7.py.encrypted",
        "专利1.py.encrypted",
        "icon.ico"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"缺少必要文件: {missing_files}")
        return False
    
    logger.info("所有必要文件检查通过")
    return True

def cleanup_old_files():
    """清理旧的构建文件"""
    logger.info("清理旧的构建文件")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            logger.info(f"已删除目录: {dir_name}")
    
    # 删除旧的spec文件
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        logger.info(f"已删除spec文件: {spec_file}")

def build_optimized():
    """构建优化版本"""
    logger.info("开始构建优化版本")
    
    # PyInstaller命令
    cmd = [
        sys.executable, "-m", "pyinstaller",
        "--onefile",                          # 单文件模式
        "--windowed",                         # 无控制台窗口
        "--clean",                            # 清理临时文件
        "--optimize=2",                       # Python字节码优化
        "--strip",                            # 去除调试信息
        "--noupx",                            # 禁用UPX压缩
        "--icon=icon.ico",                    # 设置图标
        "--name=亚马逊蓝图工具",                # 输出文件名
        
        # 添加数据文件
        "--add-data=采集8.py.encrypted;.",
        "--add-data=筛品终极版1.py.encrypted;.",
        "--add-data=历史价格7.py.encrypted;.",
        "--add-data=专利1.py.encrypted;.",
        "--add-data=icon.ico;.",
        "--add-data=auto_updater.py;.",
        
        # 排除不必要的模块以减小文件大小
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.distutils", 
        "--exclude-module=scipy",
        "--exclude-module=IPython",
        "--exclude-module=jupyter",
        "--exclude-module=notebook",
        "--exclude-module=tkinter.test",
        "--exclude-module=test",
        "--exclude-module=unittest",
        "--exclude-module=doctest",
        "--exclude-module=pydoc",
        "--exclude-module=sqlite3",
        "--exclude-module=bz2",
        "--exclude-module=lzma",
        
        # 核心依赖
        "--hidden-import=cryptography",
        "--hidden-import=cryptography.hazmat.primitives.ciphers",
        "--hidden-import=cryptography.hazmat.backends.openssl",
        "--hidden-import=cryptography.fernet",
        
        # Excel处理相关
        "--hidden-import=openpyxl",
        "--hidden-import=openpyxl.workbook",
        "--hidden-import=openpyxl.worksheet",
        "--hidden-import=xlsxwriter",
        
        # Pandas相关
        "--hidden-import=pandas",
        "--hidden-import=pandas.io.excel",
        "--hidden-import=pandas.io.common",
        
        # Web相关
        "--hidden-import=selenium",
        "--hidden-import=selenium.webdriver",
        "--hidden-import=selenium.webdriver.chrome",
        "--hidden-import=selenium.webdriver.common",
        "--hidden-import=requests",
        "--hidden-import=beautifulsoup4",
        "--hidden-import=bs4",
        
        # GUI相关
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        
        "license_client.py"                   # 主程序
    ]
    
    logger.info(f"执行构建命令...")
    logger.info("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 执行构建
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
        logger.info("构建成功完成")
        
        # 检查输出文件
        dist_dir = Path("dist")
        exe_files = list(dist_dir.glob("*.exe"))
        
        if exe_files:
            exe_file = exe_files[0]
            file_size = exe_file.stat().st_size
            size_mb = file_size / (1024 * 1024)
            
            logger.info(f"生成的exe文件: {exe_file}")
            logger.info(f"文件大小: {size_mb:.2f} MB")
            
            return True, exe_file, size_mb
        else:
            logger.error("未找到生成的exe文件")
            return False, None, 0
            
    except subprocess.CalledProcessError as e:
        logger.error(f"构建失败: {e}")
        if e.stderr:
            logger.error(f"错误输出: {e.stderr}")
        return False, None, 0
    except Exception as e:
        logger.error(f"构建过程出错: {e}")
        return False, None, 0

def create_portable_version(exe_file):
    """创建便携版本"""
    logger.info("创建便携版本")
    
    try:
        portable_dir = Path("portable")
        portable_dir.mkdir(exist_ok=True)
        
        # 复制exe文件
        portable_exe = portable_dir / exe_file.name
        shutil.copy2(exe_file, portable_exe)
        
        # 创建配置文件
        config_content = """# 亚马逊蓝图工具 - 便携版配置
# 此文件存在表示程序运行在便携模式

[Settings]
portable_mode=true
data_dir=./data
cache_dir=./cache
log_dir=./logs

[Update]
auto_check=true
server_url=https://your-server.com/updates/
"""
        
        config_file = portable_dir / "portable.ini"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        # 创建必要的目录
        for dir_name in ['data', 'cache', 'logs']:
            (portable_dir / dir_name).mkdir(exist_ok=True)
        
        # 创建说明文件
        readme_content = """# 亚马逊蓝图工具 - 便携版

## 使用说明

1. 直接运行 "亚马逊蓝图工具.exe"
2. 所有数据和配置文件都保存在当前目录
3. 可以将整个文件夹复制到其他电脑使用

## 目录说明

- data/: 程序数据文件
- cache/: 缓存文件
- logs/: 日志文件
- portable.ini: 便携模式配置文件

## 注意事项

- 请确保有足够的磁盘空间
- 建议定期清理cache目录
- 如有问题请查看logs目录中的日志文件
"""
        
        readme_file = portable_dir / "使用说明.txt"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        logger.info(f"便携版本创建完成: {portable_dir}")
        return True
        
    except Exception as e:
        logger.error(f"创建便携版本失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 亚马逊蓝图工具 - 简化优化构建器")
    print("=" * 50)
    
    # 检查必要文件
    if not check_files():
        print("❌ 文件检查失败！")
        return 1
    
    # 清理旧文件
    cleanup_old_files()
    
    # 构建优化版本
    success, exe_file, size_mb = build_optimized()
    
    if success:
        print(f"✅ 构建成功！")
        print(f"📁 输出文件: {exe_file}")
        print(f"📊 文件大小: {size_mb:.2f} MB")
        
        # 创建便携版本
        if create_portable_version(exe_file):
            print("✅ 便携版本创建成功！")
        
        print("\n💡 优化效果:")
        if size_mb < 50:
            print("  🎉 文件大小已优化到50MB以下")
        elif size_mb < 70:
            print("  👍 文件大小适中，在可接受范围内")
        else:
            print("  ⚠️  文件仍然较大，可能需要进一步优化")
        
        print("\n📦 输出目录:")
        print(f"  📁 标准版本: dist/")
        print(f"  📁 便携版本: portable/")
        
        print("\n🎯 下一步:")
        print("  1. 测试生成的exe文件")
        print("  2. 使用update_server_manager.py上传到服务器")
        print("  3. 更新version_info.json文件")
        
    else:
        print("❌ 构建失败！")
        print("\n🔍 故障排除:")
        print("  1. 检查是否安装了PyInstaller: pip install pyinstaller")
        print("  2. 检查是否缺少依赖包")
        print("  3. 查看上面的错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
