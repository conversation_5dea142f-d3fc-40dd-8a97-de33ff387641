#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化更新功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_silent_update():
    """测试静默更新"""
    print("🧪 测试静默更新功能")
    
    try:
        from auto_updater import check_and_update_silent
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("更新测试")
        root.geometry("300x200")
        
        def start_update():
            """开始更新测试"""
            result = check_and_update_silent(
                parent_window=root,
                current_version="2.1.0",
                license_key="ADMIN_BYPASS",
                device_id="ADMIN-DEVICE-001"
            )
            
            if result:
                messagebox.showinfo("成功", "更新完成！")
            else:
                messagebox.showinfo("信息", "没有可用更新或更新失败")
        
        # 创建测试按钮
        test_button = tk.Button(
            root,
            text="🔄 测试静默更新",
            command=start_update,
            font=("微软雅黑", 12),
            bg="#3498db",
            fg="white",
            padx=20,
            pady=10
        )
        test_button.pack(expand=True)
        
        info_label = tk.Label(
            root,
            text="点击按钮测试静默更新功能\n只会显示进度条，无其他弹窗",
            font=("微软雅黑", 10),
            fg="#666666"
        )
        info_label.pack(pady=10)
        
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        messagebox.showerror("错误", f"导入更新模块失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        messagebox.showerror("错误", f"测试失败: {e}")

def test_license_client_update():
    """测试license_client的更新功能"""
    print("🧪 测试license_client更新功能")
    
    try:
        # 检查文件是否存在
        if not os.path.exists("license_client.py"):
            print("❌ 未找到license_client.py文件")
            return
        
        print("✅ 找到license_client.py文件")
        
        # 检查导入是否正常
        try:
            from auto_updater import check_and_update_silent
            print("✅ 成功导入check_and_update_silent")
        except ImportError as e:
            print(f"❌ 导入失败: {e}")
            return
        
        # 检查简化更新器是否存在
        if os.path.exists("简化更新器.py"):
            print("✅ 找到简化更新器.py文件")
        else:
            print("❌ 未找到简化更新器.py文件")
            return
        
        print("\n🎉 所有组件检查通过！")
        print("💡 现在可以运行license_client.py并测试更新功能")
        print("📋 更新特点:")
        print("   - 只显示进度条")
        print("   - 无其他弹窗或终端输出")
        print("   - 更新完成后自动重启")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🔧 简化更新功能测试")
    print("=" * 50)
    
    # 选择测试模式
    root = tk.Tk()
    root.title("更新测试选择")
    root.geometry("400x300")
    
    title_label = tk.Label(
        root,
        text="选择测试模式",
        font=("微软雅黑", 16, "bold"),
        pady=20
    )
    title_label.pack()
    
    # 测试按钮1
    test1_button = tk.Button(
        root,
        text="🧪 测试静默更新功能",
        command=lambda: [root.destroy(), test_silent_update()],
        font=("微软雅黑", 12),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10,
        width=20
    )
    test1_button.pack(pady=10)
    
    # 测试按钮2
    test2_button = tk.Button(
        root,
        text="🔍 检查组件完整性",
        command=lambda: [root.destroy(), test_license_client_update()],
        font=("微软雅黑", 12),
        bg="#27ae60",
        fg="white",
        padx=20,
        pady=10,
        width=20
    )
    test2_button.pack(pady=10)
    
    # 启动license_client按钮
    launch_button = tk.Button(
        root,
        text="🚀 启动license_client",
        command=lambda: [root.destroy(), os.system("python license_client.py")],
        font=("微软雅黑", 12),
        bg="#e74c3c",
        fg="white",
        padx=20,
        pady=10,
        width=20
    )
    launch_button.pack(pady=10)
    
    info_label = tk.Label(
        root,
        text="简化更新特点:\n• 只显示进度条\n• 无终端输出\n• 自动重启程序",
        font=("微软雅黑", 10),
        fg="#666666",
        justify=tk.LEFT
    )
    info_label.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    main()
