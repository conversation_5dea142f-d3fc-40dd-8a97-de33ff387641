@echo off
chcp 65001 >nul
title 🔍 深度诊断CentOS服务器

echo.
echo ==========================================
echo 🔍 深度诊断CentOS服务器问题
echo ==========================================
echo.
echo 🎯 诊断内容:
echo • 🔧 服务状态详情
echo • 📋 服务日志分析
echo • 🐍 Python进程检查
echo • 🔌 端口监听详情
echo • 📄 配置文件检查
echo • 🧪 手动运行测试
echo • 🔥 防火墙状态
echo • 💻 系统资源
echo.
echo 🔧 自动修复:
echo • 📝 更新Flask配置
echo • 🔄 重启服务
echo • 🧪 测试连接
echo.

echo 🔍 开始深度诊断...
echo.

REM 运行诊断脚本
python "深度诊断服务.py"

echo.
echo 👋 诊断完成
pause
