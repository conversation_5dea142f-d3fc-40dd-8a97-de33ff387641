#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键部署界面工具
带图形界面的服务器部署工具
"""

import os
import sys
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import subprocess
import threading
from pathlib import Path
import time

class DeploymentGUI:
    """部署界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 一键部署到CentOS服务器")
        self.root.geometry("800x750")
        
        # 默认配置
        self.config = {
            "host": "**************",
            "port": 22,
            "username": "root",
            "password": "l39XNqJG24JmXc2za0",
            "deploy_path": "/opt/license_manager"
        }
        
        # 加载保存的配置
        self.load_config()
        
        # 创建界面
        self.create_widgets()
        
        # 部署状态
        self.deploying = False
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 服务器配置标签页
        self.create_server_config_tab(notebook)
        
        # 部署选项标签页
        self.create_deploy_options_tab(notebook)
        
        # 部署日志标签页
        self.create_deploy_log_tab(notebook)
    
    def create_server_config_tab(self, notebook):
        """创建服务器配置标签页"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="🖥️ 服务器配置")
        
        # 主框架
        main_frame = ttk.Frame(config_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 服务器信息
        server_frame = ttk.LabelFrame(main_frame, text="服务器连接信息", padding="15")
        server_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 服务器地址
        ttk.Label(server_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 8))
        self.host_var = tk.StringVar(value=self.config["host"])
        ttk.Entry(server_frame, textvariable=self.host_var, width=30, font=("Consolas", 10)).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # SSH端口
        ttk.Label(server_frame, text="SSH端口:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 8))
        self.port_var = tk.StringVar(value=str(self.config["port"]))
        ttk.Entry(server_frame, textvariable=self.port_var, width=30, font=("Consolas", 10)).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 用户名
        ttk.Label(server_frame, text="用户名:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 8))
        self.username_var = tk.StringVar(value=self.config["username"])
        ttk.Entry(server_frame, textvariable=self.username_var, width=30, font=("Consolas", 10)).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 密码
        ttk.Label(server_frame, text="密码:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 8))
        self.password_var = tk.StringVar(value=self.config["password"])
        password_entry = ttk.Entry(server_frame, textvariable=self.password_var, width=30, show="*", font=("Consolas", 10))
        password_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 显示/隐藏密码
        self.show_password_var = tk.BooleanVar()
        ttk.Checkbutton(server_frame, text="显示密码", variable=self.show_password_var, 
                       command=lambda: password_entry.config(show="" if self.show_password_var.get() else "*")).grid(row=3, column=2, padx=(10, 0), pady=(0, 8))
        
        # 部署路径
        ttk.Label(server_frame, text="部署路径:").grid(row=4, column=0, sticky=tk.W, padx=(0, 10))
        self.deploy_path_var = tk.StringVar(value=self.config["deploy_path"])
        ttk.Entry(server_frame, textvariable=self.deploy_path_var, width=30, font=("Consolas", 10)).grid(row=4, column=1, sticky=(tk.W, tk.E))
        
        server_frame.columnconfigure(1, weight=1)
        
        # 连接测试
        test_frame = ttk.Frame(main_frame)
        test_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(test_frame, text="🔗 测试连接", command=self.test_connection).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_frame, text="� 系统检测", command=self.system_detection).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_frame, text="�💾 保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_frame, text="📁 加载配置", command=self.load_config_file).pack(side=tk.LEFT)
        
        # 连接状态显示
        self.connection_status_label = ttk.Label(main_frame, text="连接状态: 未测试", foreground="gray")
        self.connection_status_label.pack(anchor=tk.W, pady=(0, 15))
        
        # 部署信息
        info_frame = ttk.LabelFrame(main_frame, text="部署信息", padding="15")
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        info_text = """📦 将要部署的组件:

✅ 授权服务器 (license_server.py)
✅ 自动更新器 (auto_updater.py)
✅ 版本管理系统 (update_server/)
✅ systemd服务配置
✅ nginx反向代理配置
✅ 防火墙安全配置 (firewalld)

🔧 系统依赖 (CentOS 7.4):
• Python 3.x 和 pip3
• Flask Web框架
• nginx Web服务器
• firewalld防火墙
• EPEL软件源

⚠️ 注意事项:
• 适配CentOS 7.4系统
• 使用yum包管理器
• 部署过程需要几分钟时间
• 需要稳定的网络连接
• 服务器需要root权限
• 部署完成后服务将自动启动"""
        
        info_label = tk.Label(info_frame, text=info_text, justify=tk.LEFT, anchor=tk.NW, 
                             font=("Microsoft YaHei", 9), foreground="darkblue")
        info_label.pack(fill=tk.BOTH, expand=True)
    
    def create_deploy_options_tab(self, notebook):
        """创建部署选项标签页"""
        options_frame = ttk.Frame(notebook)
        notebook.add(options_frame, text="⚙️ 部署选项")
        
        main_frame = ttk.Frame(options_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 部署选项
        deploy_frame = ttk.LabelFrame(main_frame, text="部署选项", padding="15")
        deploy_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.install_epel_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(deploy_frame, text="安装EPEL软件源", variable=self.install_epel_var).pack(anchor=tk.W, pady=2)

        self.update_system_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(deploy_frame, text="更新yum缓存", variable=self.update_system_var).pack(anchor=tk.W, pady=2)

        self.install_python3_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(deploy_frame, text="安装Python3和开发工具", variable=self.install_python3_var).pack(anchor=tk.W, pady=2)

        self.install_nginx_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(deploy_frame, text="安装和配置nginx", variable=self.install_nginx_var).pack(anchor=tk.W, pady=2)

        self.setup_firewall_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(deploy_frame, text="配置防火墙 (firewalld)", variable=self.setup_firewall_var).pack(anchor=tk.W, pady=2)

        self.auto_start_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(deploy_frame, text="设置服务自动启动", variable=self.auto_start_var).pack(anchor=tk.W, pady=2)

        self.backup_existing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(deploy_frame, text="备份现有文件", variable=self.backup_existing_var).pack(anchor=tk.W, pady=2)
        
        # 文件选择
        files_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="15")
        files_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 检查必要文件
        required_files = [
            "license_server.py",
            "auto_updater.py", 
            "update_server",
            "version_info.json"
        ]
        
        self.file_status = {}
        for file_name in required_files:
            frame = ttk.Frame(files_frame)
            frame.pack(fill=tk.X, pady=2)
            
            exists = os.path.exists(file_name)
            status = "✅" if exists else "❌"
            color = "green" if exists else "red"
            
            label = ttk.Label(frame, text=f"{status} {file_name}")
            label.pack(side=tk.LEFT)
            
            if exists:
                label.configure(foreground=color)
            
            self.file_status[file_name] = exists
        
        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="📊 部署进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # 当前步骤显示
        self.current_step_label = ttk.Label(progress_frame, text="准备就绪", font=("Arial", 10, "bold"))
        self.current_step_label.pack(anchor=tk.W, pady=(0, 5))

        # 步骤详情
        self.step_detail_label = ttk.Label(progress_frame, text="等待开始部署...", foreground="gray")
        self.step_detail_label.pack(anchor=tk.W)

        # 部署按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        self.deploy_button = ttk.Button(button_frame, text="🚀 开始部署",
                                       command=self.start_deployment, style="Accent.TButton")
        self.deploy_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="🔍 检查服务器状态",
                  command=self.check_server_status).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="📋 生成部署脚本", 
                  command=self.generate_deploy_script).pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.pack(fill=tk.X, pady=(15, 0))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备就绪", foreground="blue")
        self.status_label.pack(anchor=tk.W, pady=(5, 0))
    
    def create_deploy_log_tab(self, notebook):
        """创建部署日志标签页"""
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="📋 部署日志")
        
        main_frame = ttk.Frame(log_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 日志显示
        log_label_frame = ttk.LabelFrame(main_frame, text="部署日志", padding="10")
        log_label_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.log_text = tk.Text(log_label_frame, wrap=tk.WORD, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_label_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志控制
        log_control_frame = ttk.Frame(main_frame)
        log_control_frame.pack(fill=tk.X)
        
        ttk.Button(log_control_frame, text="🗑️ 清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_control_frame, text="💾 保存日志", 
                  command=self.save_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_control_frame, text="📋 复制日志", 
                  command=self.copy_log).pack(side=tk.LEFT)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()
    
    def test_connection(self):
        """测试服务器连接"""
        def test_thread():
            try:
                self.connection_status_label.config(text="连接状态: 测试中...", foreground="orange")
                self.log_message("开始测试服务器连接...")
                
                # 这里可以添加实际的连接测试逻辑
                import socket
                
                host = self.host_var.get().strip()
                port = int(self.port_var.get().strip())
                
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    self.connection_status_label.config(text="连接状态: ✅ 连接成功", foreground="green")
                    self.log_message(f"服务器 {host}:{port} 连接成功")
                    messagebox.showinfo("成功", "服务器连接测试成功！")
                else:
                    self.connection_status_label.config(text="连接状态: ❌ 连接失败", foreground="red")
                    self.log_message(f"服务器 {host}:{port} 连接失败")
                    messagebox.showerror("失败", "服务器连接测试失败！")
                    
            except Exception as e:
                self.connection_status_label.config(text="连接状态: ❌ 测试异常", foreground="red")
                self.log_message(f"连接测试异常: {e}", "ERROR")
                messagebox.showerror("错误", f"连接测试异常: {e}")
        
        threading.Thread(target=test_thread, daemon=True).start()

    def system_detection(self):
        """系统检测"""
        try:
            if os.path.exists("CentOS系统检测.py"):
                self.log_message("启动CentOS系统检测工具...")
                subprocess.Popen([sys.executable, "CentOS系统检测.py"])
            else:
                messagebox.showwarning("警告", "找不到CentOS系统检测工具")
        except Exception as e:
            messagebox.showerror("错误", f"启动系统检测工具失败: {e}")
    
    def start_deployment(self):
        """开始部署"""
        if self.deploying:
            messagebox.showwarning("警告", "部署正在进行中，请等待完成")
            return
        
        # 检查必要文件
        missing_files = [f for f, exists in self.file_status.items() if not exists]
        if missing_files:
            messagebox.showerror("错误", f"缺少必要文件:\n{chr(10).join(missing_files)}")
            return
        
        # 确认部署
        config_info = f"""服务器: {self.host_var.get()}
用户: {self.username_var.get()}
路径: {self.deploy_path_var.get()}

确认开始部署吗？"""
        
        if not messagebox.askyesno("确认部署", config_info):
            return
        
        # 开始部署
        self.deploying = True
        self.deploy_button.config(state="disabled", text="部署中...")
        self.progress_var.set(0)
        self.status_label.config(text="正在部署...", foreground="orange")
        
        # 在后台线程中执行部署
        threading.Thread(target=self.deploy_thread, daemon=True).start()
    
    def deploy_thread(self):
        """部署线程 - 带详细进度显示"""
        try:
            self.log_message("=" * 60)
            self.log_message("🚀 开始CentOS服务器自动化部署")
            self.log_message("=" * 60)

            # 定义部署步骤
            steps = [
                ("准备配置", self.step_prepare_config, 5),
                ("测试连接", self.step_test_connection, 15),
                ("创建目录", self.step_create_directories, 25),
                ("上传文件", self.step_upload_files, 40),
                ("安装依赖", self.step_install_dependencies, 60),
                ("配置服务", self.step_setup_services, 75),
                ("配置防火墙", self.step_setup_firewall, 85),
                ("启动服务", self.step_start_services, 95),
                ("验证部署", self.step_verify_deployment, 100)
            ]

            # 执行每个步骤
            for step_name, step_func, progress in steps:
                self.update_progress(progress, f"正在执行: {step_name}", f"执行步骤: {step_name}")
                self.log_message(f"📋 开始执行: {step_name}")

                try:
                    success = step_func()
                    if success:
                        self.log_message(f"✅ {step_name} 完成")
                    else:
                        self.log_message(f"❌ {step_name} 失败", "ERROR")
                        self.update_progress(progress, f"❌ 部署失败: {step_name}", "部署失败，请查看日志")
                        messagebox.showerror("部署失败", f"步骤 '{step_name}' 执行失败\n\n请查看日志了解详情")
                        return

                except Exception as e:
                    self.log_message(f"❌ {step_name} 异常: {e}", "ERROR")
                    self.update_progress(progress, f"❌ 部署异常: {step_name}", f"步骤异常: {e}")
                    messagebox.showerror("部署异常", f"步骤 '{step_name}' 执行异常:\n\n{e}")
                    return

            # 部署成功
            self.update_progress(100, "✅ 部署成功完成", "所有步骤已完成")
            self.log_message("🎉 CentOS服务器部署成功完成！")
            self.log_message(f"🌐 服务地址: http://{self.host_var.get()}/")
            messagebox.showinfo("部署成功", f"CentOS服务器部署成功完成！\n\n🌐 服务地址: http://{self.host_var.get()}/")

        except Exception as e:
            self.update_progress(0, "❌ 部署异常", f"部署过程异常: {e}")
            self.log_message(f"❌ 部署过程异常: {e}", "ERROR")
            messagebox.showerror("部署异常", f"部署过程异常:\n\n{e}")

        finally:
            self.deploying = False
            self.deploy_button.config(state="normal", text="🚀 开始部署")

    def update_progress(self, progress, current_step, detail):
        """更新进度显示"""
        self.progress_var.set(progress)
        self.current_step_label.config(text=current_step)
        self.step_detail_label.config(text=detail)
        self.root.update_idletasks()
    
    def update_config(self):
        """更新配置"""
        self.config = {
            "host": self.host_var.get().strip(),
            "port": int(self.port_var.get().strip()),
            "username": self.username_var.get().strip(),
            "password": self.password_var.get().strip(),
            "deploy_path": self.deploy_path_var.get().strip()
        }
    
    def generate_deploy_script_internal(self):
        """内部生成部署脚本"""
        # 更新deploy_to_server.py中的配置
        if os.path.exists("deploy_to_server.py"):
            with open("deploy_to_server.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # 替换服务器配置
            new_config = f'''SERVER_CONFIG = {{
    "host": "{self.config['host']}",
    "port": {self.config['port']},
    "username": "{self.config['username']}",
    "password": "{self.config['password']}",
    "deploy_path": "{self.config['deploy_path']}"
}}'''
            
            # 使用正则表达式替换配置
            import re
            pattern = r'SERVER_CONFIG\s*=\s*\{[^}]*\}'
            content = re.sub(pattern, new_config, content, flags=re.DOTALL)
            
            with open("deploy_to_server.py", "w", encoding="utf-8") as f:
                f.write(content)
            
            self.log_message("部署脚本配置已更新")
    
    def generate_deploy_script(self):
        """生成部署脚本"""
        try:
            self.update_config()
            self.generate_deploy_script_internal()
            messagebox.showinfo("成功", "部署脚本已生成并更新配置")
        except Exception as e:
            messagebox.showerror("错误", f"生成部署脚本失败: {e}")
    
    def check_server_status(self):
        """检查服务器状态"""
        try:
            if os.path.exists("检查服务器状态.py"):
                subprocess.Popen([sys.executable, "检查服务器状态.py"])
            else:
                messagebox.showwarning("警告", "找不到服务器状态检查工具")
        except Exception as e:
            messagebox.showerror("错误", f"启动状态检查工具失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            self.update_config()
            with open("deploy_config.json", "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            messagebox.showinfo("成功", "配置已保存到 deploy_config.json")
            self.log_message("配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists("deploy_config.json"):
                with open("deploy_config.json", "r", encoding="utf-8") as f:
                    saved_config = json.load(f)
                self.config.update(saved_config)
                
                # 更新界面
                self.host_var.set(self.config.get("host", ""))
                self.port_var.set(str(self.config.get("port", 22)))
                self.username_var.set(self.config.get("username", ""))
                self.password_var.set(self.config.get("password", ""))
                self.deploy_path_var.set(self.config.get("deploy_path", ""))
                
        except Exception as e:
            self.log_message(f"加载配置失败: {e}", "ERROR")
    
    def load_config_file(self):
        """从文件加载配置"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择配置文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if file_path:
                with open(file_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                
                self.config.update(config)
                
                # 更新界面
                self.host_var.set(self.config.get("host", ""))
                self.port_var.set(str(self.config.get("port", 22)))
                self.username_var.set(self.config.get("username", ""))
                self.password_var.set(self.config.get("password", ""))
                self.deploy_path_var.set(self.config.get("deploy_path", ""))
                
                messagebox.showinfo("成功", f"配置已从 {file_path} 加载")
                self.log_message(f"配置已从 {file_path} 加载")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {e}")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def save_log(self):
        """保存日志"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if file_path:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到 {file_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {e}")
    
    def copy_log(self):
        """复制日志到剪贴板"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.log_text.get(1.0, tk.END))
            messagebox.showinfo("成功", "日志已复制到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"复制日志失败: {e}")

    def run_ssh_command(self, command, timeout=300):
        """执行SSH命令 - 自动选择可用的SSH方法"""

        # 方法1: 尝试使用sshpass
        try:
            ssh_cmd = [
                "sshpass", "-p", self.config['password'],
                "ssh", "-o", "ConnectTimeout=30", "-o", "StrictHostKeyChecking=no",
                f"{self.config['username']}@{self.config['host']}",
                command
            ]
            result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=timeout)
            return result.returncode == 0, result.stdout, result.stderr
        except FileNotFoundError:
            pass  # sshpass不可用，尝试其他方法

        # 方法2: 尝试使用本地SSH (Git Bash或系统SSH)
        try:
            # 创建临时的SSH配置
            ssh_cmd = [
                "ssh", "-o", "ConnectTimeout=30", "-o", "StrictHostKeyChecking=no",
                "-o", "UserKnownHostsFile=/dev/null",
                f"{self.config['username']}@{self.config['host']}",
                command
            ]

            # 使用expect脚本自动输入密码 (如果可用)
            expect_script = f'''#!/usr/bin/expect -f
set timeout 30
spawn {' '.join(ssh_cmd)}
expect "password:"
send "{self.config['password']}\\r"
expect eof
'''

            # 尝试使用expect
            try:
                with open("temp_ssh.exp", "w") as f:
                    f.write(expect_script)

                result = subprocess.run(["expect", "temp_ssh.exp"],
                                      capture_output=True, text=True, timeout=timeout)
                os.remove("temp_ssh.exp")
                return result.returncode == 0, result.stdout, result.stderr
            except FileNotFoundError:
                pass  # expect不可用

        except FileNotFoundError:
            pass  # SSH不可用

        # 方法3: 使用Python paramiko
        try:
            import paramiko

            client = paramiko.SSHClient()
            client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            client.connect(
                self.config['host'],
                username=self.config['username'],
                password=self.config['password'],
                timeout=30
            )

            stdin, stdout, stderr = client.exec_command(command, timeout=timeout)

            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            exit_status = stdout.channel.recv_exit_status()

            client.close()

            return exit_status == 0, output, error

        except ImportError:
            self.log_message("❌ 缺少paramiko模块，正在尝试安装...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "paramiko"],
                             check=True, capture_output=True)
                self.log_message("✅ paramiko安装成功，请重试")
                return False, "", "请重新开始部署"
            except:
                pass
        except Exception as e:
            return False, "", f"SSH连接异常: {e}"

        # 所有方法都失败
        return False, "", "无可用的SSH工具，请安装sshpass、Git Bash或paramiko"

    def upload_file(self, local_file, remote_file):
        """上传文件 - 自动选择可用的SCP方法"""

        # 方法1: 尝试使用sshpass + scp
        try:
            scp_cmd = [
                "sshpass", "-p", self.config['password'],
                "scp", "-o", "StrictHostKeyChecking=no",
                local_file, f"{self.config['username']}@{self.config['host']}:{remote_file}"
            ]
            result = subprocess.run(scp_cmd, capture_output=True, text=True, timeout=120)
            return result.returncode == 0
        except FileNotFoundError:
            pass  # sshpass不可用

        # 方法2: 使用Python paramiko SFTP
        try:
            import paramiko

            transport = paramiko.Transport((self.config['host'], self.config['port']))
            transport.connect(username=self.config['username'], password=self.config['password'])

            sftp = paramiko.SFTPClient.from_transport(transport)
            sftp.put(local_file, remote_file)
            sftp.close()
            transport.close()

            return True

        except ImportError:
            self.log_message("❌ 缺少paramiko模块，无法上传文件")
            return False
        except Exception as e:
            self.log_message(f"❌ 文件上传失败: {e}")
            return False

    def step_prepare_config(self):
        """步骤1: 准备配置"""
        self.update_config()
        self.log_message(f"📋 服务器: {self.config['host']}")
        self.log_message(f"📋 用户: {self.config['username']}")
        self.log_message(f"📋 路径: {self.config['deploy_path']}")
        return True

    def step_test_connection(self):
        """步骤2: 测试连接"""
        self.log_message("🔗 测试SSH连接...")
        success, output, error = self.run_ssh_command("echo 'SSH连接测试成功' && uname -a")

        if success:
            self.log_message("✅ SSH连接正常")
            self.log_message(f"系统信息: {output.strip()}")
            return True
        else:
            self.log_message(f"❌ SSH连接失败: {error}")
            return False

    def step_create_directories(self):
        """步骤3: 创建目录"""
        self.log_message("📁 创建目录结构...")

        command = f"""
mkdir -p {self.config['deploy_path']} && \
mkdir -p {self.config['deploy_path']}/update_server/files && \
mkdir -p {self.config['deploy_path']}/update_server/versions && \
mkdir -p {self.config['deploy_path']}/logs && \
chmod 755 {self.config['deploy_path']} && \
echo '目录创建完成'
"""

        success, output, error = self.run_ssh_command(command)
        if success:
            self.log_message("✅ 目录结构创建完成")
            return True
        else:
            self.log_message(f"❌ 目录创建失败: {error}")
            return False

    def step_upload_files(self):
        """步骤4: 上传文件"""
        self.log_message("📤 上传应用文件...")

        files_to_upload = [
            ("license_server.py", f"{self.config['deploy_path']}/license_server.py"),
            ("auto_updater.py", f"{self.config['deploy_path']}/auto_updater.py"),
            ("version_info.json", f"{self.config['deploy_path']}/version_info.json")
        ]

        for local_file, remote_file in files_to_upload:
            if os.path.exists(local_file):
                self.log_message(f"📤 上传 {local_file}...")
                if self.upload_file(local_file, remote_file):
                    self.log_message(f"✅ {local_file} 上传成功")
                else:
                    self.log_message(f"❌ {local_file} 上传失败")
                    return False
            else:
                self.log_message(f"⚠️ 文件不存在: {local_file}")
                return False

        # 设置文件权限
        success, output, error = self.run_ssh_command(f"chmod +x {self.config['deploy_path']}/*.py")
        if success:
            self.log_message("✅ 文件权限设置完成")

        return True

    def step_install_dependencies(self):
        """步骤5: 安装依赖"""
        self.log_message("📦 安装系统依赖 (CentOS 7.4)...")

        command = f"""
# 安装EPEL源
yum install -y epel-release && \
# 更新yum缓存
yum makecache fast && \
# 安装Python3和开发工具
yum install -y python3 python3-pip python3-devel gcc gcc-c++ make && \
# 安装nginx
yum install -y nginx && \
# 安装其他必要工具
yum install -y curl wget git firewalld && \
# 升级pip3
python3 -m pip install --upgrade pip && \
# 安装Python包
pip3 install flask requests cryptography && \
echo "依赖安装完成"
"""

        success, output, error = self.run_ssh_command(command, timeout=1200)
        if success:
            self.log_message("✅ 依赖安装完成")
            return True
        else:
            self.log_message(f"❌ 依赖安装失败: {error}")
            return False

    def step_setup_services(self):
        """步骤6: 配置服务"""
        self.log_message("⚙️ 配置系统服务...")

        # 创建systemd服务文件
        service_content = f"""[Unit]
Description=License Manager Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={self.config['deploy_path']}
ExecStart=/usr/bin/python3 {self.config['deploy_path']}/license_server.py
Restart=always
RestartSec=10
Environment=PYTHONPATH={self.config['deploy_path']}

[Install]
WantedBy=multi-user.target"""

        command = f"""
# 创建systemd服务
cat > /etc/systemd/system/license-manager.service << 'EOF'
{service_content}
EOF

# 重新加载systemd
systemctl daemon-reload && \
# 启用服务
systemctl enable license-manager && \
systemctl enable nginx && \
echo "服务配置完成"
"""

        success, output, error = self.run_ssh_command(command)
        if success:
            self.log_message("✅ 服务配置完成")
            return True
        else:
            self.log_message(f"❌ 服务配置失败: {error}")
            return False

    def step_setup_firewall(self):
        """步骤7: 配置防火墙"""
        self.log_message("🔥 配置防火墙 (firewalld)...")

        command = """
# 启动firewalld
systemctl start firewalld && \
systemctl enable firewalld && \
# 添加HTTP服务
firewall-cmd --permanent --add-service=http && \
firewall-cmd --permanent --add-service=https && \
# 添加SSH服务
firewall-cmd --permanent --add-service=ssh && \
# 重新加载防火墙规则
firewall-cmd --reload && \
echo "防火墙配置完成"
"""

        success, output, error = self.run_ssh_command(command)
        if success:
            self.log_message("✅ 防火墙配置完成")
            return True
        else:
            self.log_message(f"❌ 防火墙配置失败: {error}")
            return False

    def step_start_services(self):
        """步骤8: 启动服务"""
        self.log_message("🚀 启动服务...")

        command = """
# 启动license-manager服务
systemctl start license-manager && \
# 启动nginx服务
systemctl start nginx && \
# 检查服务状态
systemctl is-active license-manager && \
systemctl is-active nginx && \
echo "服务启动完成"
"""

        success, output, error = self.run_ssh_command(command)
        if success:
            self.log_message("✅ 服务启动成功")
            return True
        else:
            self.log_message(f"❌ 服务启动失败: {error}")
            return False

    def step_verify_deployment(self):
        """步骤9: 验证部署 - 分步骤详细检查"""
        self.log_message("🔍 开始详细验证部署结果...")

        verification_passed = 0
        total_checks = 5

        # 检查1: 文件是否存在
        self.log_message("📁 检查1: 验证文件部署...")
        success, output, error = self.run_ssh_command(f"ls -la {self.config['deploy_path']}/")
        if success:
            self.log_message("✅ 文件部署检查通过")
            for line in output.split('\n')[:5]:  # 只显示前5行
                if line.strip():
                    self.log_message(f"   {line}")
            verification_passed += 1
        else:
            self.log_message(f"❌ 文件部署检查失败: {error}")

        # 检查2: license-manager服务状态
        self.log_message("🔧 检查2: 验证license-manager服务...")
        success, output, error = self.run_ssh_command("systemctl is-active license-manager")
        if success and "active" in output:
            self.log_message("✅ license-manager服务运行正常")
            verification_passed += 1
        else:
            self.log_message(f"❌ license-manager服务异常")
            # 获取详细状态
            success2, output2, error2 = self.run_ssh_command("systemctl status license-manager")
            if output2:
                self.log_message(f"   状态详情: {output2[:200]}...")

        # 检查3: nginx服务状态
        self.log_message("🌐 检查3: 验证nginx服务...")
        success, output, error = self.run_ssh_command("systemctl is-active nginx")
        if success and "active" in output:
            self.log_message("✅ nginx服务运行正常")
            verification_passed += 1
        else:
            self.log_message(f"❌ nginx服务异常")
            # 获取详细状态
            success2, output2, error2 = self.run_ssh_command("systemctl status nginx")
            if output2:
                self.log_message(f"   状态详情: {output2[:200]}...")

        # 检查4: 端口监听
        self.log_message("🔌 检查4: 验证端口监听...")
        success, output, error = self.run_ssh_command("netstat -tlnp | grep -E ':(80|5000)'")
        if success and output.strip():
            self.log_message("✅ 端口监听检查通过")
            for line in output.split('\n'):
                if line.strip():
                    self.log_message(f"   {line}")
            verification_passed += 1
        else:
            self.log_message("❌ 端口监听检查失败")
            # 尝试其他端口检查命令
            success2, output2, error2 = self.run_ssh_command("ss -tlnp | grep -E ':(80|5000)'")
            if success2 and output2.strip():
                self.log_message("✅ 端口监听检查通过 (使用ss命令)")
                verification_passed += 1

        # 检查5: HTTP服务测试
        self.log_message("🌍 检查5: 验证HTTP服务...")
        success, output, error = self.run_ssh_command("curl -s -o /dev/null -w 'HTTP状态码: %{http_code}' http://localhost:5000/ || echo '连接失败'")
        if success and "200" in output:
            self.log_message("✅ HTTP服务测试通过")
            self.log_message(f"   {output}")
            verification_passed += 1
        else:
            self.log_message("❌ HTTP服务测试失败")
            self.log_message(f"   响应: {output}")
            # 尝试检查进程
            success2, output2, error2 = self.run_ssh_command("ps aux | grep python3 | grep license_server")
            if output2:
                self.log_message(f"   进程状态: {output2}")

        # 总结验证结果
        self.log_message(f"📊 验证完成: {verification_passed}/{total_checks} 项检查通过")

        if verification_passed >= 4:
            self.log_message("🎉 部署验证基本成功！")
            self.log_message(f"🌐 服务地址: http://{self.config['host']}:5000/")
            return True
        elif verification_passed >= 2:
            self.log_message("⚠️ 部署部分成功，但存在一些问题")
            self.log_message("💡 建议手动检查服务状态")
            return True  # 部分成功也算通过
        else:
            self.log_message("❌ 部署验证失败，需要手动修复")
            return False

def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置主题样式
    style = ttk.Style()
    if "vista" in style.theme_names():
        style.theme_use("vista")
    elif "clam" in style.theme_names():
        style.theme_use("clam")
    
    # 创建应用
    app = DeploymentGUI(root)
    
    # 启动消息
    app.log_message("🚀 一键部署工具已启动")
    app.log_message("请配置服务器信息后开始部署")
    
    root.mainloop()

if __name__ == "__main__":
    main()
