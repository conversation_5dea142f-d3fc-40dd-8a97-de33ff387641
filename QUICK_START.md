# 快速开始指南

## 遇到依赖冲突？按以下步骤解决：

### 🔧 第一步：修复依赖冲突

```bash
python fix_dependencies.py
```

这个脚本会：
- 自动卸载冲突的包
- 分步骤重新安装所有依赖
- 处理版本冲突问题
- 验证安装结果
- 测试Excel功能

### 🎯 第二步：启动构建工具

**方法1：使用图形界面（推荐）**
```bash
python build_gui.py
```

**方法2：如果遇到编码问题**
```bash
python start_gui_fixed.py
```

**方法3：直接构建**
```bash
python build_license_system.py
```

### 📋 依赖冲突说明

**问题原因：**
- `amazoncaptcha` 库与现代版本的 `requests` 和 `pillow` 不兼容
- 该库要求较旧版本的依赖，与其他库冲突

**解决方案：**
- 暂时移除 `amazoncaptcha` 依赖
- 如果确实需要验证码功能，可以手动安装兼容版本
- 主要功能（Excel处理、Web爬虫、数据分析）不受影响

### ✅ 验证安装

运行以下命令验证依赖是否正确安装：

```bash
python test_dependencies.py
```

或者测试编码问题：

```bash
python test_encoding.py
```

### 🚀 构建流程

1. **检查依赖** - 确保所有必需的库都已安装
2. **测试Excel** - 验证openpyxl功能正常
3. **开始构建** - 生成可执行文件
4. **检查输出** - 在dist目录中找到生成的exe文件

### 📁 生成的文件

构建成功后，您会得到：

- `dist/亚马逊授权客户端.exe` - 主程序
- `server_files/` - 服务器端文件
  - `license_server.py` - 授权服务器
  - `*.py.encrypted` - 加密的业务脚本

### ⚠️ 注意事项

1. **首次运行可能较慢** - 需要下载浏览器驱动
2. **exe文件较大** - 包含所有依赖，约100-200MB
3. **杀毒软件误报** - 可能需要添加白名单
4. **验证码功能** - 暂时不可用，等待库更新

### 🆘 遇到问题？

1. **依赖冲突** → 运行 `python fix_dependencies.py`
2. **编码错误** → 使用 `python start_gui_fixed.py`
3. **Excel不工作** → 检查openpyxl安装
4. **构建失败** → 查看日志文件，清理临时文件后重试

### 📞 技术支持

如果仍有问题，请提供：
- 错误信息的完整日志
- Python版本信息
- 操作系统版本
- 已安装的包列表 (`pip list`)

---

**快速命令总结：**

```bash
# 修复依赖
python fix_dependencies.py

# 启动GUI
python build_gui.py

# 直接构建
python build_license_system.py

# 测试功能
python test_dependencies.py
```
