@echo off
chcp 65001 >nul
echo ========================================
echo    亚马逊授权系统构建工具
echo ========================================
echo.

echo 正在启动图形界面构建工具...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查tkinter是否可用
python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo 错误：tkinter不可用，请检查Python安装
    pause
    exit /b 1
)

REM 启动GUI
python build_gui.py

echo.
echo 构建工具已关闭
pause
