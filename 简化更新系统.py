#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化更新系统 - 只显示进度，自动重启
"""

import tkinter as tk
from tkinter import ttk
import requests
import os
import tempfile
import subprocess
import sys
import threading
import time
import hashlib

class SilentUpdater:
    """静默更新器 - 只显示进度"""
    
    def __init__(self):
        self.license_key = "ADMIN_BYPASS"
        self.device_id = "ADMIN-DEVICE-001"
        self.server_url = "http://198.23.135.176:5000"
        self.progress_window = None
        self.progress_var = None
        self.status_var = None
        
    def check_and_update_silent(self, current_version="2.1.0"):
        """静默检查并更新"""
        # 1. 检查更新
        update_info = self._check_update(current_version)
        if not update_info:
            return False
        
        # 2. 显示进度窗口并开始下载
        self._show_progress_window()
        self._start_download(update_info)
        
        return True
    
    def _check_update(self, current_version):
        """检查更新（静默）"""
        try:
            url = f"{self.server_url}/update/check"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'current_version': current_version
            }
            
            response = requests.get(url, params=params, timeout=15)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('has_update'):
                    return data.get('update_info')
            return None
        except:
            return None
    
    def _show_progress_window(self):
        """显示进度窗口"""
        self.progress_window = tk.Toplevel()
        self.progress_window.title("正在更新")
        self.progress_window.geometry("400x120")
        self.progress_window.resizable(False, False)
        
        # 设置图标
        try:
            self.progress_window.iconbitmap("icon.ico")
        except:
            pass
        
        # 居中显示
        self.progress_window.geometry("+%d+%d" % (
            (self.progress_window.winfo_screenwidth() // 2) - 200,
            (self.progress_window.winfo_screenheight() // 2) - 60
        ))
        
        # 禁用关闭按钮
        self.progress_window.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # 状态标签
        self.status_var = tk.StringVar(value="正在检查更新...")
        status_label = tk.Label(
            self.progress_window,
            textvariable=self.status_var,
            font=("微软雅黑", 10)
        )
        status_label.pack(pady=10)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            self.progress_window,
            variable=self.progress_var,
            maximum=100,
            length=350
        )
        progress_bar.pack(pady=10)
        
        # 百分比标签
        self.percent_var = tk.StringVar(value="0%")
        percent_label = tk.Label(
            self.progress_window,
            textvariable=self.percent_var,
            font=("微软雅黑", 9),
            fg="#666666"
        )
        percent_label.pack()
        
        # 保持窗口在最前面
        self.progress_window.attributes('-topmost', True)
        self.progress_window.focus_force()
    
    def _start_download(self, update_info):
        """开始下载（在后台线程）"""
        def download_thread():
            try:
                self._update_status("正在下载更新...")
                
                # 下载文件
                temp_file = self._download_file(update_info)
                if not temp_file:
                    self._update_status("下载失败")
                    time.sleep(2)
                    self._close_progress()
                    return
                
                self._update_status("正在安装更新...")
                self._update_progress(95)
                
                # 应用更新
                if self._apply_update(temp_file):
                    self._update_status("更新完成，正在重启...")
                    self._update_progress(100)
                    time.sleep(1)
                    self._restart_application()
                else:
                    self._update_status("安装失败")
                    time.sleep(2)
                    self._close_progress()
                    
            except Exception as e:
                self._update_status("更新失败")
                time.sleep(2)
                self._close_progress()
        
        # 启动下载线程
        thread = threading.Thread(target=download_thread, daemon=True)
        thread.start()
    
    def _download_file(self, update_info):
        """下载文件（支持断点续传）"""
        try:
            version = update_info.get('version')
            file_size = update_info.get('file_size', 0)
            
            # 创建临时文件
            temp_file = os.path.join(tempfile.gettempdir(), f"amazon_blueprint_update_{version}.exe")
            
            # 检查是否有未完成的下载
            downloaded_size = 0
            if os.path.exists(temp_file):
                downloaded_size = os.path.getsize(temp_file)
            
            url = f"{self.server_url}/update/download"
            params = {
                'key': self.license_key,
                'device_id': self.device_id,
                'version': version
            }
            
            max_retries = 5
            retry_count = 0
            
            while retry_count < max_retries:
                try:
                    # 设置断点续传头
                    headers = {}
                    if downloaded_size > 0:
                        headers['Range'] = f'bytes={downloaded_size}-'
                    
                    response = requests.get(
                        url, 
                        params=params, 
                        headers=headers,
                        stream=True,
                        timeout=(60, 1800)  # 30分钟读取超时
                    )
                    
                    if response.status_code not in [200, 206]:
                        raise Exception(f"HTTP错误: {response.status_code}")
                    
                    # 获取文件总大小
                    if response.status_code == 200:
                        actual_file_size = int(response.headers.get('Content-Length', file_size))
                        downloaded_size = 0
                        mode = 'wb'
                    else:  # 206 Partial Content
                        content_range = response.headers.get('Content-Range', '')
                        if content_range:
                            actual_file_size = int(content_range.split('/')[-1])
                        else:
                            actual_file_size = downloaded_size + int(response.headers.get('Content-Length', 0))
                        mode = 'ab'
                    
                    # 下载文件
                    with open(temp_file, mode) as f:
                        chunk_size = 64 * 1024  # 64KB chunks
                        
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)
                                
                                # 更新进度
                                if actual_file_size > 0:
                                    progress = (downloaded_size / actual_file_size) * 90  # 下载占90%
                                    self._update_progress(progress)
                    
                    # 检查下载是否完成
                    if downloaded_size >= actual_file_size:
                        return temp_file
                    else:
                        retry_count += 1
                        time.sleep(5)
                        
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        return None
                    time.sleep(retry_count * 5)  # 递增等待时间
            
            return None
            
        except Exception as e:
            return None
    
    def _apply_update(self, update_file):
        """应用更新"""
        try:
            # 获取当前程序路径
            if getattr(sys, 'frozen', False):
                current_exe = sys.executable
            else:
                current_exe = os.path.abspath(sys.argv[0])
            
            # 创建更新脚本
            update_script = self._create_update_script(update_file, current_exe)
            
            # 启动更新脚本
            subprocess.Popen([sys.executable, update_script], 
                           creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
            
            return True
        except:
            return False
    
    def _create_update_script(self, new_file, current_file):
        """创建更新脚本"""
        script_content = f'''
import os
import shutil
import time
import subprocess
import sys

try:
    # 等待主程序退出
    time.sleep(2)
    
    # 备份当前文件
    backup_file = "{current_file}.backup"
    if os.path.exists("{current_file}"):
        shutil.copy2("{current_file}", backup_file)
    
    # 替换文件
    shutil.copy2("{new_file}", "{current_file}")
    
    # 删除临时文件
    os.remove("{new_file}")
    
    # 启动新程序
    subprocess.Popen(["{current_file}"], cwd=os.path.dirname("{current_file}"))
    
except Exception as e:
    # 如果更新失败，恢复备份
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, "{current_file}")
'''
        
        script_path = os.path.join(tempfile.gettempdir(), "update_script.py")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    def _restart_application(self):
        """重启应用程序"""
        try:
            # 关闭进度窗口
            if self.progress_window:
                self.progress_window.destroy()
            
            # 退出当前程序
            sys.exit(0)
        except:
            pass
    
    def _update_status(self, status):
        """更新状态"""
        if self.status_var:
            self.status_var.set(status)
            if self.progress_window:
                self.progress_window.update()
    
    def _update_progress(self, progress):
        """更新进度"""
        if self.progress_var:
            self.progress_var.set(progress)
            if self.percent_var:
                self.percent_var.set(f"{progress:.1f}%")
            if self.progress_window:
                self.progress_window.update()
    
    def _close_progress(self):
        """关闭进度窗口"""
        if self.progress_window:
            self.progress_window.destroy()

# 全局更新器实例
_silent_updater = SilentUpdater()

def check_and_update_silent(current_version="2.1.0"):
    """静默更新入口函数"""
    return _silent_updater.check_and_update_silent(current_version)

def test_silent_update():
    """测试静默更新"""
    import tkinter as tk
    
    root = tk.Tk()
    root.title("测试静默更新")
    root.geometry("300x150")
    
    def start_update():
        check_and_update_silent("2.1.0")
    
    tk.Label(root, text="点击按钮测试静默更新", font=("微软雅黑", 12)).pack(pady=20)
    tk.Button(root, text="开始更新", command=start_update, font=("微软雅黑", 10)).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_silent_update()
