@echo off
chcp 65001 >nul
title 🔍 检查并修复服务器404问题

echo.
echo ==========================================
echo 🔍 检查并修复服务器404问题
echo ==========================================
echo.
echo 🔍 问题确认:
echo • ❌ 所有API接口返回404错误
echo • ✅ 服务器端口5000可访问
echo • ❌ 路由配置可能有问题
echo • ❌ license_manager.py无法连接
echo.
echo 🎯 检查内容:
echo • 🔍 检查systemd服务状态
echo • 🔌 检查端口5000监听状态
echo • 📋 查看服务启动日志
echo • 🧪 测试本地API连接
echo • 🐍 检查Python进程状态
echo • 📄 验证license_server.py文件
echo • 🔄 尝试重启服务
echo.
echo 🔧 修复方案:
echo • 🔄 自动重启服务
echo • 🐍 手动启动Python脚本
echo • ⚙️ 修复systemd配置
echo • 📋 提供手动修复步骤
echo.

echo 🔍 开始检查并修复服务器...
echo.

REM 运行检查修复脚本
python "检查并修复服务器.py"

echo.
echo 👋 检查完成
pause
