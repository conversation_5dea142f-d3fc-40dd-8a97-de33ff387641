#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速语法检查 - 验证auto_updater.py语法是否正确
"""

import ast
import sys

def check_syntax(filename):
    """检查Python文件语法"""
    print(f"🔍 检查 {filename} 语法...")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析语法
        ast.parse(content)
        print(f"✅ {filename} 语法检查通过!")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"   错误: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ 检查 {filename} 时出错: {e}")
        return False

def test_import():
    """测试导入模块"""
    print("\n🧪 测试导入模块...")
    
    try:
        import auto_updater
        print("✅ auto_updater 模块导入成功!")
        
        # 检查关键类和函数是否存在
        if hasattr(auto_updater, 'AutoUpdater'):
            print("✅ AutoUpdater 类存在")
        else:
            print("❌ AutoUpdater 类不存在")
            return False
            
        if hasattr(auto_updater, 'check_and_update'):
            print("✅ check_and_update 函数存在")
        else:
            print("❌ check_and_update 函数不存在")
            return False
            
        if hasattr(auto_updater, 'check_and_update_silent'):
            print("✅ check_and_update_silent 函数存在")
        else:
            print("❌ check_and_update_silent 函数不存在")
            return False
        
        return True
        
    except SyntaxError as e:
        print(f"❌ 导入时语法错误: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_license_client():
    """测试license_client.py导入"""
    print("\n🧪 测试 license_client.py 导入...")
    
    try:
        # 尝试导入license_client中使用的部分
        from auto_updater import check_and_update_silent as check_and_update
        print("✅ license_client.py 导入语句正常")
        return True
        
    except Exception as e:
        print(f"❌ license_client.py 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 快速语法检查工具")
    print("=" * 50)
    
    results = []
    
    # 1. 检查auto_updater.py语法
    results.append(check_syntax("auto_updater.py"))
    
    # 2. 测试模块导入
    results.append(test_import())
    
    # 3. 测试license_client导入
    results.append(test_license_client())
    
    print("\n" + "=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print("🎉 所有检查都通过!")
        print("\n✅ 现在可以正常使用:")
        print("  - auto_updater.py 语法正确")
        print("  - 模块导入正常")
        print("  - license_client.py 可以正常运行")
        
        print("\n🚀 建议:")
        print("1. 重启 license_client.py 程序")
        print("2. 测试更新功能")
        print("3. 观察下载进度是否正常")
        
    else:
        print(f"⚠️ 检查结果: {success_count}/{total_count} 项通过")
        print("请查看上述错误信息并修复")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
