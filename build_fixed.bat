@echo off
echo 🔧 开始构建修复版exe
echo ================================

echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo 📋 检查PyInstaller...
python -m pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 📥 安装PyInstaller...
    python -m pip install pyinstaller
)

echo 🗑️ 清理旧构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo 🚀 开始构建...
python -m PyInstaller build_fixed.spec

if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成！
echo 📁 输出文件位于 dist 目录

echo 🧪 测试exe文件...
if exist "dist\亚马逊蓝图工具_修复版.exe" (
    echo ✅ exe文件生成成功
    echo 📊 文件大小:
    dir "dist\亚马逊蓝图工具_修复版.exe" | find ".exe"
) else (
    echo ❌ exe文件未找到
)

echo ================================
echo 🎉 构建流程完成
pause
