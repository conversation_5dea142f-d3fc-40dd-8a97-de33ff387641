#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新API - 验证服务器API是否正常工作
"""

import requests
import json

def test_update_apis():
    """测试更新相关的API"""
    print("🧪 测试更新API")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    # 测试API列表
    test_cases = [
        {
            "name": "检查更新",
            "url": f"{server_url}/update/check",
            "method": "GET",
            "params": {"current_version": "1.0.0"}
        },
        {
            "name": "获取统计",
            "url": f"{server_url}/update/stats",
            "method": "GET"
        },
        {
            "name": "基本连接",
            "url": server_url,
            "method": "GET"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}/{total_count}: {test_case['name']}")
        print(f"🌐 URL: {test_case['url']}")
        
        try:
            if test_case['method'] == 'GET':
                response = requests.get(
                    test_case['url'],
                    params=test_case.get('params', {}),
                    timeout=10
                )
            else:
                response = requests.post(test_case['url'], timeout=10)
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 请求成功")
                success_count += 1
                
                # 尝试解析JSON响应
                try:
                    data = response.json()
                    if isinstance(data, dict):
                        if data.get('success'):
                            print("✅ API响应成功")
                        else:
                            print(f"⚠️ API返回失败: {data.get('message', '未知错误')}")
                        
                        # 显示部分响应数据
                        if 'current_version' in data:
                            version_info = data['current_version']
                            if version_info:
                                print(f"📦 当前版本: {version_info.get('version', 'N/A')}")
                        
                        if 'has_update' in data:
                            print(f"🔄 有更新: {data.get('has_update', False)}")
                    else:
                        print("📄 响应格式: 非JSON对象")
                except json.JSONDecodeError:
                    print("📄 响应格式: 非JSON")
                    print(f"📝 响应内容: {response.text[:200]}...")
            
            elif response.status_code == 404:
                print("❌ 接口不存在 (404)")
            elif response.status_code == 500:
                print("❌ 服务器内部错误 (500)")
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectTimeout:
            print("❌ 连接超时")
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败 - 服务器可能未运行")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 个API正常")
    
    if success_count == total_count:
        print("🎉 所有API测试通过！")
        return True
    elif success_count > 0:
        print("⚠️ 部分API正常，可能需要检查服务器配置")
        return False
    else:
        print("❌ 所有API测试失败，请检查服务器状态")
        return False

def test_specific_endpoints():
    """测试特定的端点"""
    print("\n🔍 详细测试特定端点")
    print("=" * 30)
    
    server_url = "http://198.23.135.176:5000"
    
    # 测试检查更新API的详细功能
    print("\n📋 详细测试检查更新API:")
    try:
        response = requests.get(
            f"{server_url}/update/check",
            params={
                "current_version": "1.0.0",
                "key": "test_key",  # 这会失败，但可以看到错误信息
                "device_id": "test_device"
            },
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code in [200, 400, 401]:
            data = response.json()
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"测试异常: {e}")

def main():
    """主函数"""
    try:
        print("🎯 目标: 测试服务器更新API功能")
        print("🌐 服务器: http://198.23.135.176:5000")
        print()
        
        # 基本API测试
        api_ok = test_update_apis()
        
        # 详细测试
        if api_ok:
            test_specific_endpoints()
        
        print("\n💡 说明:")
        print("• 如果所有测试通过，说明服务器API正常")
        print("• 如果部分失败，可能需要检查服务器配置")
        print("• 如果全部失败，请确认服务器是否正在运行")
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
