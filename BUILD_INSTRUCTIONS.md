# 亚马逊授权系统构建说明

## 概述

本项目是一个亚马逊相关的授权系统，包含采集、筛品、价格分析、专利查询等功能。`build_license_system.py` 是主要的构建脚本，用于将整个系统打包成可执行文件。

## 改进内容

### 1. 完整的依赖管理
- 新增 `requirements.txt` 文件，包含所有必需的依赖库
- 特别添加了 `openpyxl` 和 `xlsxwriter` 用于Excel文件处理
- 包含了所有Web爬虫、数据处理、加密等相关依赖

### 2. 增强的PyInstaller配置
- 添加了更多的 `--hidden-import` 选项
- 确保pandas、openpyxl、selenium等关键库被正确打包
- 包含了cryptography加密库的所有子模块

### 3. 依赖检查和测试
- 自动检查并安装缺失的依赖
- 添加了Excel功能测试
- 改进的错误处理和日志记录

## 使用方法

### 方法一：使用图形界面构建工具（推荐）

**标准启动**：
```bash
python build_gui.py
```

**如果遇到编码错误，使用修复版**：
```bash
python start_gui_fixed.py
```

这个图形界面工具提供：
1. 可视化依赖管理 - 添加、删除、编辑依赖包
2. 构建选项配置 - 单文件模式、无控制台等
3. 实时日志显示 - 查看构建过程
4. 依赖检查和安装 - 一键检查和安装缺失依赖
5. Excel功能测试 - 验证openpyxl是否正常工作
6. 配置保存和加载 - 保存您的自定义设置
7. 自动处理编码问题 - 支持中文字符显示

### 方法二：解决依赖冲突的安装脚本

```bash
python install_dependencies.py
```

这个脚本会：
1. 分步安装依赖，避免版本冲突
2. 特别处理pillow和amazoncaptcha的冲突
3. 验证所有依赖是否正确安装
4. 测试Excel功能

### 方法三：使用增强版构建脚本

```bash
python build_enhanced.py
```

### 方法四：传统方式

```bash
# 使用修复版requirements文件
pip install -r requirements_fixed.txt

# 然后运行构建
python build_license_system.py
```

## 依赖库说明

### 核心依赖
- **selenium**: Web自动化框架
- **pandas**: 数据处理和分析
- **openpyxl**: Excel文件读写（.xlsx格式）
- **xlsxwriter**: Excel文件写入
- **requests**: HTTP请求库
- **beautifulsoup4**: HTML解析
- **webdriver_manager**: 浏览器驱动管理

### 加密和安全
- **cryptography**: 高级加密库
- **fake_useragent**: 用户代理伪装

### 其他工具
- **amazoncaptcha**: 亚马逊验证码处理
- **psutil**: 系统进程管理
- **lxml**: XML/HTML解析
- **pillow**: 图像处理

## 构建输出

构建成功后，会生成以下文件：

1. **dist/亚马逊授权客户端.exe** - 主要的可执行文件
2. **server_files/** - 服务器端文件目录
   - license_server.py - 授权服务器
   - *.py.encrypted - 加密的业务脚本

## 部署说明

### 客户端部署
1. 将 `dist/亚马逊授权客户端.exe` 分发给用户
2. 用户可以直接运行，程序会自动检查并安装缺失的依赖

### 服务器端部署
1. 将 `server_files/` 目录中的文件上传到服务器
2. 运行 `license_server.py` 启动授权服务

## 故障排除

### 常见问题

1. **依赖冲突错误（pillow和amazoncaptcha）**
   ```
   ERROR: Cannot install amazoncaptcha==0.5.11 and pillow>=10.0.0 because these package versions have conflicting dependencies.
   ```
   **解决方案**：
   - 使用 `install_dependencies.py` 脚本自动解决
   - 或手动安装兼容版本：
     ```bash
     pip install "pillow>=9.0.1,<9.6.0"
     pip install amazoncaptcha>=0.5.0
     ```

2. **Excel功能不工作**
   - 确保安装了 openpyxl: `pip install openpyxl`
   - 检查pandas版本: `pip install pandas>=2.0.0`
   - 使用GUI工具的"测试Excel"功能验证

3. **加密功能失败**
   - 安装cryptography: `pip install cryptography`
   - 在Windows上可能需要Visual C++构建工具

4. **Selenium无法启动**
   - 安装webdriver_manager: `pip install webdriver_manager`
   - 确保Chrome浏览器已安装

5. **打包失败**
   - 升级PyInstaller: `pip install pyinstaller --upgrade`
   - 清理缓存: 删除 build/ 和 dist/ 目录后重试
   - 使用GUI工具的"清理文件"功能

6. **编码错误（'gbk' codec can't decode）**
   ```
   'gbk' codec can't decode byte 0x80 in position 35: illegal multibyte sequence
   ```
   **解决方案**：
   - 使用修复版启动脚本：`python start_gui_fixed.py`
   - 或手动设置环境变量：
     ```bash
     set PYTHONIOENCODING=utf-8
     set PYTHONLEGACYWINDOWSSTDIO=1
     python build_gui.py
     ```
   - 测试编码修复：`python test_encoding.py`

7. **GUI界面无法启动**
   - 确保tkinter已安装（Python标准库）
   - 在Linux上可能需要: `sudo apt-get install python3-tk`

### 日志文件
- `build.log` - 主构建日志
- `build_enhanced.log` - 增强构建日志
- `encrypt.log` - 加密操作日志

## 技术特性

1. **文件加密**: 使用AES-256-GCM加密业务脚本
2. **依赖自动安装**: 运行时自动检查并安装缺失依赖
3. **Excel支持**: 完整的Excel读写功能
4. **跨平台**: 支持Windows、Linux、macOS
5. **单文件部署**: 打包成单个exe文件，便于分发

## 注意事项

1. 确保Python版本 >= 3.8
2. 建议在虚拟环境中进行构建
3. 首次运行可能需要下载浏览器驱动
4. 生成的exe文件较大（约100-200MB），这是正常的
5. 在某些杀毒软件中可能被误报，需要添加白名单
