#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开放端口5000并测试网络访问
"""

import paramiko
import sys
import time
import requests

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def open_port_5000():
    """开放端口5000"""
    print("🔥 开放端口5000")
    print("=" * 30)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print()
    
    # 步骤1: 添加防火墙规则
    print("🔧 步骤1: 添加防火墙规则...")
    commands = [
        "firewall-cmd --permanent --add-port=5000/tcp",
        "firewall-cmd --reload"
    ]
    
    for cmd in commands:
        success, output, error = ssh_connect(config['host'], config['username'], config['password'], cmd)
        print(f"   执行: {cmd}")
        if success:
            print(f"   ✅ 成功: {output.strip()}")
        else:
            print(f"   ❌ 失败: {error.strip()}")
    print()
    
    # 步骤2: 验证防火墙规则
    print("🔍 步骤2: 验证防火墙规则...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "firewall-cmd --list-ports")
    print(f"   开放端口: {output.strip()}")
    
    if "5000/tcp" in output:
        print("   ✅ 端口5000已开放")
    else:
        print("   ❌ 端口5000未开放")
        return False
    print()
    
    # 步骤3: 测试本地连接
    print("🧪 步骤3: 测试本地连接...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "curl -s http://localhost:5000/")
    if success and output.strip():
        print("   ✅ 本地连接正常")
        print(f"   响应: {output[:100]}...")
    else:
        print("   ❌ 本地连接失败")
        return False
    print()
    
    return True

def test_external_access():
    """测试外部访问"""
    print("🌍 测试外部网络访问")
    print("=" * 30)
    
    host = '**************'
    port = 5000
    
    test_urls = [
        f"http://{host}:{port}/",
        f"http://{host}:{port}/health",
        f"http://{host}:{port}/test"
    ]
    
    print(f"🌐 测试服务器: {host}:{port}")
    print()
    
    success_count = 0
    for url in test_urls:
        try:
            print(f"🔗 测试: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ 成功 (状态码: {response.status_code})")
                print(f"   📄 内容: {response.text[:100]}...")
                success_count += 1
            else:
                print(f"   ❌ 失败 (状态码: {response.status_code})")
                
        except requests.exceptions.ConnectTimeout:
            print(f"   ❌ 连接超时")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接错误")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        print()
    
    print(f"📊 测试结果: {success_count}/{len(test_urls)} 个URL可访问")
    
    if success_count == 0:
        print("\n💡 可能的问题:")
        print("• 🔥 防火墙仍然阻止端口5000")
        print("• 🌐 ISP或网络限制")
        print("• 🔒 服务器安全组设置")
        print("• 📡 DNS解析问题")
        
        print("\n🔧 建议解决方案:")
        print("• 使用VPN或代理访问")
        print("• 联系服务器提供商检查安全组")
        print("• 尝试使用其他端口(如80)")
        
    return success_count > 0

def setup_nginx_proxy():
    """设置nginx反向代理到端口80"""
    print("🔧 设置nginx反向代理")
    print("=" * 30)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0'
    }
    
    # nginx配置
    nginx_config = '''server {
    listen 80;
    server_name **************;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}'''
    
    print("📝 创建nginx配置...")
    
    # 创建nginx配置文件
    command = f"""cat > /etc/nginx/conf.d/license-manager.conf << 'EOF'
{nginx_config}
EOF
nginx -t && systemctl reload nginx && echo "nginx配置成功"
"""
    
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], command)
    
    if success:
        print("   ✅ nginx配置成功")
        print("   🌐 现在可以通过端口80访问: http://**************/")
        return True
    else:
        print(f"   ❌ nginx配置失败: {error}")
        return False

def main():
    """主函数"""
    try:
        print("🔍 问题分析: 服务正常运行，但外部无法访问")
        print("🎯 解决方案: 开放防火墙端口并测试网络访问")
        print()
        
        # 开放端口
        if open_port_5000():
            print("✅ 防火墙配置完成")
        else:
            print("❌ 防火墙配置失败")
            return
        
        print("\n" + "="*50)
        
        # 测试外部访问
        if test_external_access():
            print("\n🎉 外部访问成功！")
            print("🌐 您现在可以访问: http://**************:5000/")
        else:
            print("\n❌ 外部访问仍然失败")
            
            # 提供nginx代理方案
            print("\n🔧 尝试nginx反向代理方案...")
            choice = input("是否设置nginx代理到端口80？(y/n): ").lower().strip()
            
            if choice == 'y' or choice == 'yes':
                if setup_nginx_proxy():
                    print("\n🎉 nginx代理设置成功！")
                    print("🌐 请访问: http://**************/")
                    
                    # 测试端口80
                    print("\n🧪 测试端口80访问...")
                    try:
                        response = requests.get("http://**************/", timeout=10)
                        if response.status_code == 200:
                            print("✅ 端口80访问成功！")
                        else:
                            print(f"❌ 端口80访问失败 (状态码: {response.status_code})")
                    except Exception as e:
                        print(f"❌ 端口80访问异常: {e}")
                else:
                    print("❌ nginx代理设置失败")
            
    except Exception as e:
        print(f"❌ 处理过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
