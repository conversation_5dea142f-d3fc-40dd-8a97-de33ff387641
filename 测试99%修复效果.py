#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试99%修复效果
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox, ttk
import threading
import time

def test_fix():
    """测试修复效果"""
    
    # 创建GUI
    root = tk.Tk()
    root.title("测试99%修复效果")
    root.geometry("800x600")
    
    # 设置图标
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 标题
    title_label = tk.Label(
        root,
        text="🔧 测试99%下载修复效果",
        font=("微软雅黑", 16, "bold"),
        pady=10
    )
    title_label.pack()
    
    # 进度条
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(
        root,
        variable=progress_var,
        maximum=100,
        length=600
    )
    progress_bar.pack(pady=10)
    
    # 进度标签
    progress_label = tk.Label(
        root,
        text="0.00%",
        font=("微软雅黑", 12),
        fg="#2c3e50"
    )
    progress_label.pack()
    
    # 日志区域
    log_frame = tk.Frame(root)
    log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    log_text = scrolledtext.ScrolledText(log_frame, height=20, width=90)
    log_text.pack(fill=tk.BOTH, expand=True)
    
    def log(message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        log_text.insert(tk.END, log_message + "\n")
        log_text.see(tk.END)
        root.update()
        print(log_message)
    
    def update_progress(progress):
        """更新进度"""
        progress_var.set(progress)
        progress_label.config(text=f"{progress:.2f}%")
        
        # 特别关注高进度
        if progress >= 99.0:
            progress_label.config(fg="#e74c3c")  # 红色
            log(f"🔍 高进度: {progress:.6f}%")
        elif progress >= 95.0:
            progress_label.config(fg="#f39c12")  # 橙色
        else:
            progress_label.config(fg="#27ae60")  # 绿色
        
        root.update()
    
    def test_original_updater():
        """测试原始更新器"""
        log("🧪 测试修复后的auto_updater...")
        
        def test_thread():
            try:
                from auto_updater import AutoUpdater
                
                updater = AutoUpdater(
                    current_version="2.1.0",
                    license_server_url="http://198.23.135.176:5000/",
                    license_key="ADMIN_BYPASS",
                    device_id="ADMIN-DEVICE-001"
                )
                
                log("✅ 更新器创建成功")
                
                # 检查更新
                update_info = updater.check_for_updates()
                if not update_info:
                    log("ℹ️ 没有可用更新")
                    return
                
                log(f"📊 版本: {update_info.get('version')}")
                log(f"📦 大小: {update_info.get('file_size', 0)/1024/1024:.2f}MB")
                
                # 下载测试
                log("📥 开始下载测试...")
                
                temp_file = updater.download_update(update_info, update_progress)
                
                if temp_file:
                    log(f"✅ 下载成功: {temp_file}")
                    
                    import os
                    if os.path.exists(temp_file):
                        file_size = os.path.getsize(temp_file)
                        expected_size = update_info.get('file_size', 0)
                        
                        log(f"📁 文件大小: {file_size:,} 字节")
                        log(f"📋 预期大小: {expected_size:,} 字节")
                        
                        if file_size == expected_size:
                            log("🎉 文件完整!")
                            update_progress(100.0)
                            messagebox.showinfo("成功", "下载完整！99%问题已修复！")
                        else:
                            completion = (file_size / expected_size) * 100
                            log(f"⚠️ 文件不完整: {completion:.6f}%")
                            if completion >= 99.9:
                                log("✅ 接近完整，可能是正常的文件大小差异")
                                messagebox.showinfo("接近成功", f"下载{completion:.3f}%完成，基本正常")
                            else:
                                messagebox.showwarning("部分成功", f"下载{completion:.3f}%完成")
                else:
                    log("❌ 下载失败")
                    messagebox.showerror("失败", "下载失败")
                
            except Exception as e:
                log(f"❌ 测试失败: {e}")
                import traceback
                log(f"📄 详细错误: {traceback.format_exc()}")
        
        # 在后台线程运行测试
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
    
    def test_enhanced_downloader():
        """测试增强版下载器"""
        log("🚀 测试增强版下载器...")
        
        def test_thread():
            try:
                from enhanced_downloader import EnhancedDownloader
                
                downloader = EnhancedDownloader(
                    "http://198.23.135.176:5000/",
                    "ADMIN_BYPASS",
                    "ADMIN-DEVICE-001"
                )
                
                log("✅ 增强版下载器创建成功")
                
                # 模拟更新信息
                update_info = {
                    'version': '2.1.1',
                    'file_size': 57000000  # 约57MB
                }
                
                log(f"📊 测试版本: {update_info['version']}")
                log(f"📦 测试大小: {update_info['file_size']/1024/1024:.2f}MB")
                
                # 下载测试
                log("📥 开始增强版下载测试...")
                
                temp_file = downloader.enhanced_download_update(update_info, update_progress)
                
                if temp_file:
                    log(f"✅ 增强版下载成功: {temp_file}")
                    
                    import os
                    if os.path.exists(temp_file):
                        file_size = os.path.getsize(temp_file)
                        expected_size = update_info['file_size']
                        
                        log(f"📁 文件大小: {file_size:,} 字节")
                        log(f"📋 预期大小: {expected_size:,} 字节")
                        
                        completion = (file_size / expected_size) * 100
                        log(f"📊 完成度: {completion:.6f}%")
                        
                        if completion >= 99.9:
                            log("🎉 增强版下载成功!")
                            update_progress(100.0)
                            messagebox.showinfo("成功", "增强版下载成功！")
                        else:
                            messagebox.showwarning("部分成功", f"增强版下载{completion:.3f}%完成")
                else:
                    log("❌ 增强版下载失败")
                    messagebox.showerror("失败", "增强版下载失败")
                
            except Exception as e:
                log(f"❌ 增强版测试失败: {e}")
                import traceback
                log(f"📄 详细错误: {traceback.format_exc()}")
        
        # 在后台线程运行测试
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
    
    def clear_log():
        """清空日志"""
        log_text.delete(1.0, tk.END)
        progress_var.set(0)
        progress_label.config(text="0.00%", fg="#2c3e50")
    
    # 按钮区域
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    # 测试修复后的更新器
    test1_button = tk.Button(
        button_frame,
        text="🔧 测试修复后更新器",
        command=test_original_updater,
        font=("微软雅黑", 12),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10
    )
    test1_button.pack(side=tk.LEFT, padx=10)
    
    # 测试增强版下载器
    test2_button = tk.Button(
        button_frame,
        text="🚀 测试增强版下载器",
        command=test_enhanced_downloader,
        font=("微软雅黑", 12),
        bg="#e74c3c",
        fg="white",
        padx=20,
        pady=10
    )
    test2_button.pack(side=tk.LEFT, padx=10)
    
    # 清空日志按钮
    clear_button = tk.Button(
        button_frame,
        text="🗑️ 清空日志",
        command=clear_log,
        font=("微软雅黑", 12),
        bg="#95a5a6",
        fg="white",
        padx=20,
        pady=10
    )
    clear_button.pack(side=tk.LEFT, padx=10)
    
    # 说明
    info_label = tk.Label(
        root,
        text="修复内容：文件缓冲区刷新、实际文件大小验证、进度显示优化",
        font=("微软雅黑", 9),
        fg="#666666"
    )
    info_label.pack(pady=5)
    
    log("🔧 99%下载修复测试工具已启动")
    log("💡 点击按钮测试修复效果")
    log("🎯 重点观察进度是否能达到100%")
    
    root.mainloop()

if __name__ == "__main__":
    test_fix()
