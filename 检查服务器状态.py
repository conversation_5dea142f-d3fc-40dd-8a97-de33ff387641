#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查服务器状态
监控部署的服务器运行状态
"""

import requests
import json
import time
from datetime import datetime
import subprocess
import sys

# 服务器配置
SERVER_CONFIG = {
    "host": "**************",
    "username": "root",
    "deploy_path": "/opt/license_manager"
}

def check_http_service():
    """检查HTTP服务状态"""
    print("🌐 检查HTTP服务...")
    
    test_urls = [
        f"http://{SERVER_CONFIG['host']}/",
        f"http://{SERVER_CONFIG['host']}/update/check"
    ]
    
    for url in test_urls:
        try:
            if "update/check" in url:
                # 测试更新检查API
                response = requests.get(url, params={
                    'key': 'test',
                    'device_id': 'test',
                    'current_version': '1.0.0'
                }, timeout=10)
            else:
                response = requests.get(url, timeout=10)
            
            print(f"✅ {url} - 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   响应时间: {response.elapsed.total_seconds():.2f}秒")
            
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - 连接失败")
        except requests.exceptions.Timeout:
            print(f"⚠️ {url} - 请求超时")
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")

def check_ssh_connection():
    """检查SSH连接"""
    print("\n🔗 检查SSH连接...")
    
    try:
        # 尝试SSH连接测试
        result = subprocess.run([
            "ssh", "-o", "ConnectTimeout=10", "-o", "BatchMode=yes",
            f"{SERVER_CONFIG['username']}@{SERVER_CONFIG['host']}", 
            "echo 'SSH连接正常'"
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ SSH连接正常")
        else:
            print("❌ SSH连接失败")
            print(f"   错误: {result.stderr.strip()}")
            
    except subprocess.TimeoutExpired:
        print("⚠️ SSH连接超时")
    except FileNotFoundError:
        print("⚠️ SSH客户端未找到，跳过SSH测试")
    except Exception as e:
        print(f"❌ SSH测试失败: {e}")

def check_service_status():
    """检查服务状态"""
    print("\n🔧 检查服务状态...")
    
    try:
        # 检查systemd服务状态
        result = subprocess.run([
            "ssh", "-o", "ConnectTimeout=10",
            f"{SERVER_CONFIG['username']}@{SERVER_CONFIG['host']}", 
            "systemctl is-active license-manager"
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and "active" in result.stdout:
            print("✅ license-manager 服务运行正常")
        else:
            print("❌ license-manager 服务未运行")
            
        # 检查nginx状态
        result = subprocess.run([
            "ssh", "-o", "ConnectTimeout=10",
            f"{SERVER_CONFIG['username']}@{SERVER_CONFIG['host']}", 
            "systemctl is-active nginx"
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and "active" in result.stdout:
            print("✅ nginx 服务运行正常")
        else:
            print("❌ nginx 服务未运行")
            
    except subprocess.TimeoutExpired:
        print("⚠️ 服务状态检查超时")
    except FileNotFoundError:
        print("⚠️ SSH客户端未找到，跳过服务状态检查")
    except Exception as e:
        print(f"❌ 服务状态检查失败: {e}")

def check_version_info():
    """检查版本信息"""
    print("\n📄 检查版本信息...")
    
    try:
        # 尝试获取版本信息
        response = requests.get(f"http://{SERVER_CONFIG['host']}/updates/version_info.json", timeout=10)
        
        if response.status_code == 200:
            version_info = response.json()
            print("✅ 版本信息文件可访问")
            print(f"   当前版本: {version_info.get('version', '未知')}")
            print(f"   发布日期: {version_info.get('release_date', '未知')}")
            print(f"   文件大小: {version_info.get('file_size', 0) / (1024*1024):.1f} MB")
        else:
            print(f"❌ 版本信息文件不可访问 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"❌ 获取版本信息失败: {e}")

def check_update_files():
    """检查更新文件"""
    print("\n📦 检查更新文件...")
    
    try:
        # 检查文件目录
        response = requests.get(f"http://{SERVER_CONFIG['host']}/updates/files/", timeout=10)
        
        if response.status_code == 200:
            print("✅ 更新文件目录可访问")
            
            # 尝试解析目录列表
            if "amazon_blueprint" in response.text:
                print("   找到exe文件")
            else:
                print("   ⚠️ 未找到exe文件")
        else:
            print(f"❌ 更新文件目录不可访问 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"❌ 检查更新文件失败: {e}")

def get_server_info():
    """获取服务器信息"""
    print("\n💻 获取服务器信息...")
    
    try:
        commands = [
            ("系统信息", "uname -a"),
            ("Python版本", "python3 --version"),
            ("磁盘使用", "df -h /opt"),
            ("内存使用", "free -h"),
            ("运行时间", "uptime")
        ]
        
        for name, command in commands:
            result = subprocess.run([
                "ssh", "-o", "ConnectTimeout=10",
                f"{SERVER_CONFIG['username']}@{SERVER_CONFIG['host']}", 
                command
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print(f"✅ {name}: {result.stdout.strip()}")
            else:
                print(f"❌ {name}: 获取失败")
                
    except subprocess.TimeoutExpired:
        print("⚠️ 服务器信息获取超时")
    except FileNotFoundError:
        print("⚠️ SSH客户端未找到，跳过服务器信息获取")
    except Exception as e:
        print(f"❌ 获取服务器信息失败: {e}")

def check_logs():
    """检查服务日志"""
    print("\n📋 检查服务日志...")
    
    try:
        # 获取最近的服务日志
        result = subprocess.run([
            "ssh", "-o", "ConnectTimeout=10",
            f"{SERVER_CONFIG['username']}@{SERVER_CONFIG['host']}", 
            "journalctl -u license-manager --since '1 hour ago' --no-pager -n 5"
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ 服务日志 (最近5条):")
            for line in result.stdout.strip().split('\n')[-5:]:
                if line.strip():
                    print(f"   {line}")
        else:
            print("❌ 无法获取服务日志")
            
    except subprocess.TimeoutExpired:
        print("⚠️ 日志获取超时")
    except FileNotFoundError:
        print("⚠️ SSH客户端未找到，跳过日志检查")
    except Exception as e:
        print(f"❌ 日志检查失败: {e}")

def run_comprehensive_check():
    """运行综合检查"""
    print("🔍 服务器状态综合检查")
    print("=" * 60)
    print(f"服务器: {SERVER_CONFIG['host']}")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    checks = [
        ("HTTP服务", check_http_service),
        ("SSH连接", check_ssh_connection),
        ("服务状态", check_service_status),
        ("版本信息", check_version_info),
        ("更新文件", check_update_files),
        ("服务器信息", get_server_info),
        ("服务日志", check_logs)
    ]
    
    for check_name, check_func in checks:
        try:
            check_func()
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
        
        time.sleep(1)  # 避免请求过于频繁
    
    print("\n" + "=" * 60)
    print("📊 检查完成")
    print("=" * 60)

def show_management_commands():
    """显示管理命令"""
    print("\n💡 常用管理命令:")
    print("=" * 40)
    
    commands = [
        ("查看服务状态", f"ssh root@{SERVER_CONFIG['host']} 'systemctl status license-manager'"),
        ("重启服务", f"ssh root@{SERVER_CONFIG['host']} 'systemctl restart license-manager'"),
        ("查看实时日志", f"ssh root@{SERVER_CONFIG['host']} 'journalctl -u license-manager -f'"),
        ("查看nginx状态", f"ssh root@{SERVER_CONFIG['host']} 'systemctl status nginx'"),
        ("测试API", f"curl 'http://{SERVER_CONFIG['host']}/update/check?key=test&device_id=test&current_version=1.0.0'")
    ]
    
    for desc, cmd in commands:
        print(f"📌 {desc}:")
        print(f"   {cmd}")
        print()

def main():
    """主函数"""
    try:
        run_comprehensive_check()
        show_management_commands()
        
        print("🔄 是否需要持续监控？(y/n): ", end="")
        response = input().lower().strip()
        
        if response in ['y', 'yes', '是']:
            print("🔄 开始持续监控 (按Ctrl+C停止)...")
            
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 检查服务状态...")
                check_http_service()
                time.sleep(30)  # 每30秒检查一次
                
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
    except Exception as e:
        print(f"\n❌ 检查异常: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
