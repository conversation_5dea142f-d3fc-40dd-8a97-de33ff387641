# 亚马逊授权系统依赖包 - 修复版本冲突
# 核心依赖
selenium>=4.15.0
pandas>=2.0.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0
requests>=2.31.0
beautifulsoup4>=4.12.0
webdriver_manager>=4.0.0
psutil>=5.9.0
cryptography>=41.0.0
fake_useragent>=1.4.0

# Web爬虫和数据处理
lxml>=4.9.0

# 图像处理 - 使用兼容amazoncaptcha的版本
pillow>=9.0.1,<9.6.0

# 亚马逊验证码处理 - 在pillow之后安装
amazoncaptcha>=0.5.0

# 开发和构建工具
pyinstaller>=6.0.0

# 可选依赖
numpy>=1.24.0
urllib3>=2.0.0
certifi>=2023.0.0
charset-normalizer>=3.0.0
idna>=3.4

# 系统依赖（Windows特定）
# wmi>=1.5.1; sys_platform == "win32"

# 注意事项：
# 1. pillow和amazoncaptcha有版本冲突，使用兼容版本
# 2. 如果遇到冲突，可以先安装pillow，再安装amazoncaptcha
# 3. 建议在虚拟环境中安装以避免冲突
