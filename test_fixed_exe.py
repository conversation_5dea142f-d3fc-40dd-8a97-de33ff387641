#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试构建的exe是否包含修复
"""

import subprocess
import os
import time
import json

def test_exe_version():
    """测试exe版本信息"""
    exe_path = "dist/亚马逊蓝图工具_修复版.exe"
    
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在")
        return False
    
    print(f"🧪 测试exe文件: {exe_path}")
    
    # 检查文件大小
    file_size = os.path.getsize(exe_path) / (1024 * 1024)
    print(f"📊 文件大小: {file_size:.1f} MB")
    
    # 尝试启动exe（快速测试）
    try:
        print("🚀 测试exe启动...")
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待2秒看是否能正常启动
        time.sleep(2)
        
        if process.poll() is None:
            print("✅ exe启动成功")
            process.terminate()
            return True
        else:
            print("❌ exe启动失败")
            return False
            
    except Exception as e:
        print(f"❌ exe测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 exe修复验证测试")
    print("=" * 40)
    
    if test_exe_version():
        print("✅ exe测试通过")
    else:
        print("❌ exe测试失败")
