#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查客户端服务器配置问题
"""

import requests
import json

def check_server_ports():
    """检查不同端口的服务器状态"""
    print("🔍 检查服务器端口配置")
    print("=" * 50)
    
    server_ip = "**************"
    ports_to_check = [5000, 44285, 80, 8080]
    
    for port in ports_to_check:
        print(f"\n📡 检查端口 {port}:")
        server_url = f"http://{server_ip}:{port}"
        
        try:
            # 检查根路径
            response = requests.get(f"{server_url}/", timeout=5)
            print(f"   ✅ 根路径 (/): HTTP {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   📄 响应: {data.get('message', 'No message')}")
                    print(f"   🔢 版本: {data.get('version', 'Unknown')}")
                except:
                    print(f"   📄 响应: {response.text[:100]}...")
            
            # 检查license API
            try:
                license_response = requests.get(f"{server_url}/license/check", timeout=5)
                print(f"   🔑 License API: HTTP {license_response.status_code}")
            except:
                print(f"   🔑 License API: 连接失败")
            
            # 检查update API
            try:
                update_response = requests.get(f"{server_url}/update/stats", timeout=5)
                print(f"   🔄 Update API: HTTP {update_response.status_code}")
            except:
                print(f"   🔄 Update API: 连接失败")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 端口 {port}: 连接失败")
        except requests.exceptions.Timeout:
            print(f"   ⏰ 端口 {port}: 连接超时")
        except Exception as e:
            print(f"   ❌ 端口 {port}: {e}")

def check_license_client_config():
    """检查license_client.py中的服务器配置"""
    print("\n" + "=" * 50)
    print("📋 检查license_client.py配置")
    print("=" * 50)
    
    try:
        with open("license_client.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 查找服务器URL配置
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'server_url' in line and '=' in line:
                print(f"   第{i+1}行: {line.strip()}")
                
        # 检查当前配置的服务器
        if 'self.server_url = "http://**************:44285"' in content:
            print("\n⚠️ 发现问题:")
            print("   客户端配置的端口是 44285")
            print("   但服务器实际运行在端口 5000")
            print("\n💡 解决方案:")
            print("   需要将客户端的服务器地址改为:")
            print("   self.server_url = \"http://**************:5000\"")
            
    except Exception as e:
        print(f"❌ 读取license_client.py失败: {e}")

def test_correct_server():
    """测试正确的服务器地址"""
    print("\n" + "=" * 50)
    print("🧪 测试正确的服务器地址")
    print("=" * 50)
    
    server_url = "http://**************:5000"
    
    # 测试基本连接
    try:
        response = requests.get(f"{server_url}/", timeout=5)
        print(f"✅ 服务器连接正常: HTTP {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📄 服务器信息: {data.get('message', 'Unknown')}")
            print(f"🔢 版本: {data.get('version', 'Unknown')}")
            
            # 测试license API
            test_response = requests.post(
                f"{server_url}/license/check",
                json={
                    "key": "TEST-KEY",
                    "device_id": "TEST-DEVICE"
                },
                timeout=5
            )
            print(f"🔑 License API测试: HTTP {test_response.status_code}")
            
    except Exception as e:
        print(f"❌ 服务器测试失败: {e}")

def main():
    """主函数"""
    print("🎯 诊断license_client.py的404错误")
    print("🔧 检查服务器端口配置问题")
    print()
    
    check_server_ports()
    check_license_client_config()
    test_correct_server()
    
    print("\n" + "=" * 50)
    print("📋 诊断总结:")
    print("1. 检查客户端配置的服务器端口是否正确")
    print("2. 确认服务器在正确端口上运行")
    print("3. 测试API端点的可访问性")
    print("4. 如果端口不匹配，需要修改客户端配置")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
