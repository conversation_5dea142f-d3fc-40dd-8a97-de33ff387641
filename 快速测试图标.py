#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试更新对话框图标
"""

import tkinter as tk
import os
import sys

def test_icon_setting():
    """测试图标设置"""
    print("🧪 测试图标设置")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("亚马逊蓝图工具")
    root.geometry("400x300")
    
    # 设置主窗口图标
    if os.path.exists("icon.ico"):
        try:
            root.iconbitmap("icon.ico")
            print("✅ 主窗口图标设置成功")
        except Exception as e:
            print(f"❌ 主窗口图标设置失败: {e}")
    else:
        print("❌ 未找到icon.ico文件")
    
    def show_simple_update_dialog():
        """显示简化更新对话框"""
        try:
            from 简化更新器 import SimpleUpdateDialog
            
            update_info = {
                'version': '2.1.1',
                'has_update': True
            }
            
            dialog = SimpleUpdateDialog(root, update_info)
            
            # 手动创建测试对话框
            test_dialog = tk.Toplevel(root)
            test_dialog.title("正在更新")
            test_dialog.geometry("400x150")
            test_dialog.transient(root)
            
            # 设置图标
            if os.path.exists("icon.ico"):
                try:
                    test_dialog.iconbitmap("icon.ico")
                    print("✅ 更新对话框图标设置成功")
                except Exception as e:
                    print(f"❌ 更新对话框图标设置失败: {e}")
            
            # 添加内容
            label = tk.Label(
                test_dialog,
                text="🔄 正在更新到最新版本\n\n检查窗口标题栏是否有图标",
                font=("微软雅黑", 11),
                pady=30
            )
            label.pack()
            
            close_btn = tk.Button(
                test_dialog,
                text="关闭",
                command=test_dialog.destroy,
                font=("微软雅黑", 10)
            )
            close_btn.pack(pady=10)
            
        except Exception as e:
            print(f"❌ 创建更新对话框失败: {e}")
            tk.messagebox.showerror("错误", f"创建更新对话框失败: {e}")
    
    def show_standard_update_dialog():
        """显示标准更新对话框"""
        try:
            from auto_updater import UpdateDialog
            
            update_info = {
                'version': '2.1.1',
                'current_version': '2.1.0',
                'changelog': '• 修复了已知问题\n• 优化了程序性能\n• 增加了新功能',
                'has_update': True
            }
            
            dialog = UpdateDialog(root, update_info)
            print("✅ 标准更新对话框创建成功")
            
        except Exception as e:
            print(f"❌ 创建标准更新对话框失败: {e}")
            tk.messagebox.showerror("错误", f"创建标准更新对话框失败: {e}")
    
    # 创建界面
    title_label = tk.Label(
        root,
        text="图标测试",
        font=("微软雅黑", 16, "bold"),
        pady=20
    )
    title_label.pack()
    
    info_label = tk.Label(
        root,
        text="检查主窗口标题栏是否显示图标",
        font=("微软雅黑", 10),
        fg="#666666"
    )
    info_label.pack(pady=10)
    
    # 测试按钮
    simple_btn = tk.Button(
        root,
        text="🧪 测试简化更新对话框图标",
        command=show_simple_update_dialog,
        font=("微软雅黑", 11),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10
    )
    simple_btn.pack(pady=10)
    
    standard_btn = tk.Button(
        root,
        text="🧪 测试标准更新对话框图标",
        command=show_standard_update_dialog,
        font=("微软雅黑", 11),
        bg="#27ae60",
        fg="white",
        padx=20,
        pady=10
    )
    standard_btn.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_icon_setting()
