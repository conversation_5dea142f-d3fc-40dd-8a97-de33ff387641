#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用spec文件构建exe，解决复杂依赖问题
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_spec.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_spec_file():
    """创建详细的spec文件来处理复杂依赖"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件和子模块
fake_useragent_datas = collect_data_files('fake_useragent')
openpyxl_datas = collect_data_files('openpyxl')
fake_useragent_hiddenimports = collect_submodules('fake_useragent')
openpyxl_hiddenimports = collect_submodules('openpyxl')

# 合并所有数据文件
datas = []
datas += fake_useragent_datas
datas += openpyxl_datas

# 添加图标文件
if os.path.exists('icon.ico'):
    datas += [('icon.ico', '.')]

# 隐藏导入列表
hiddenimports = [
    # 核心依赖
    'cryptography',
    'cryptography.hazmat.primitives.ciphers',
    'cryptography.hazmat.backends.openssl',
    'cryptography.fernet',
    
    # Excel处理 - 完整支持
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.workbook.workbook',
    'openpyxl.worksheet',
    'openpyxl.worksheet.worksheet',
    'openpyxl.cell',
    'openpyxl.cell.cell',
    'openpyxl.styles',
    'openpyxl.utils',
    'openpyxl.reader.excel',
    'openpyxl.writer.excel',
    'openpyxl.xml.functions',
    'openpyxl.xml.constants',
    'xlsxwriter',
    'xlsxwriter.workbook',
    'xlsxwriter.worksheet',
    
    # Pandas Excel支持
    'pandas',
    'pandas.io.excel',
    'pandas.io.excel._base',
    'pandas.io.excel._openpyxl',
    'pandas.io.excel._xlsxwriter',
    'pandas.io.common',
    'pandas.io.parsers',
    
    # Web相关
    'selenium',
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.chrome.options',
    'webdriver_manager',
    'webdriver_manager.chrome',
    
    # fake_useragent完整支持
    'fake_useragent',
    'fake_useragent.fake',
    'fake_useragent.utils',
    'fake_useragent.settings',
    'fake_useragent.errors',
    
    # requests完整支持
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.models',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    
    # BeautifulSoup支持
    'beautifulsoup4',
    'bs4',
    'bs4.builder',
    'bs4.builder._html5lib',
    'bs4.builder._htmlparser',
    'bs4.builder._lxml',
    
    # lxml支持
    'lxml',
    'lxml.etree',
    'lxml.html',
    
    # 其他依赖
    'psutil',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    
    # 系统相关
    'platform',
    'socket',
    'threading',
    'queue',
    'json',
    'base64',
    'hashlib',
    'datetime',
    'time',
    'os',
    'sys',
    'subprocess',
    'tempfile',
    'shutil',
    'pathlib',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'ssl',
]

# 添加收集到的隐藏导入
hiddenimports += fake_useragent_hiddenimports
hiddenimports += openpyxl_hiddenimports

# 排除的模块（减小文件大小）
excludes = [
    'matplotlib',
    'numpy.distutils',
    'scipy',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',
    'unittest',
    'doctest',
    'pydoc',
    'xml.etree.cElementTree',
]

block_cipher = None

a = Analysis(
    ['license_client.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='亚马逊蓝图工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    # 写入spec文件
    spec_file = "amazon_blueprint.spec"
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    logger.info(f"创建spec文件: {spec_file}")
    return spec_file

def build_with_spec():
    """使用spec文件构建exe"""
    
    logger.info("🚀 开始使用spec文件构建exe...")
    
    # 1. 检查依赖
    try:
        import PyInstaller
        logger.info(f"PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        logger.error("PyInstaller未安装，请运行: pip install pyinstaller")
        return False
    
    # 2. 创建spec文件
    spec_file = create_spec_file()
    
    # 3. 清理旧文件
    if os.path.exists("dist"):
        import shutil
        shutil.rmtree("dist")
        logger.info("清理旧的dist目录")
    
    if os.path.exists("build"):
        import shutil
        shutil.rmtree("build")
        logger.info("清理旧的build目录")
    
    # 4. 执行PyInstaller
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            spec_file
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        if result.returncode == 0:
            logger.info("✅ PyInstaller构建成功")
            
            # 检查生成的文件
            exe_path = os.path.join("dist", "亚马逊蓝图工具.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path)
                logger.info(f"生成的exe文件: {exe_path}")
                logger.info(f"文件大小: {file_size / (1024*1024):.1f}MB")
                return True
            else:
                logger.error("exe文件未生成")
                return False
        else:
            logger.error(f"PyInstaller构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"构建过程出错: {e}")
        return False

def test_dependencies():
    """测试关键依赖是否可以导入"""
    
    logger.info("🧪 测试关键依赖...")
    
    dependencies = [
        'fake_useragent',
        'openpyxl',
        'pandas',
        'requests',
        'selenium',
        'bs4',  # beautifulsoup4的导入名称是bs4
        'cryptography'
    ]
    
    failed_deps = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            logger.info(f"✅ {dep} - 导入成功")
        except ImportError as e:
            logger.error(f"❌ {dep} - 导入失败: {e}")
            failed_deps.append(dep)
    
    if failed_deps:
        logger.error(f"以下依赖导入失败: {failed_deps}")
        logger.error("请安装缺失的依赖: pip install " + " ".join(failed_deps))
        return False
    
    logger.info("✅ 所有关键依赖测试通过")
    return True

def main():
    """主函数"""
    
    logger.info("=" * 60)
    logger.info("🔧 亚马逊蓝图工具 - Spec文件构建器")
    logger.info("=" * 60)
    
    # 1. 测试依赖
    if not test_dependencies():
        logger.error("依赖测试失败，请先安装缺失的依赖")
        return False
    
    # 2. 构建exe
    if build_with_spec():
        logger.info("🎉 构建完成！")
        logger.info("请测试生成的exe文件是否能正常运行")
        return True
    else:
        logger.error("构建失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
