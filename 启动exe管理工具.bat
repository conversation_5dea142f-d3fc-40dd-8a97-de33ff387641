@echo off
chcp 65001 >nul
title 🔧 亚马逊蓝图工具 - exe文件管理

echo.
echo ==========================================
echo 🔧 亚马逊蓝图工具 - exe文件管理
echo ==========================================
echo.
echo 📋 功能介绍:
echo • 📤 上传新版本exe文件到服务器
echo • 📥 下载服务器上的exe文件
echo • 📊 管理版本信息和更新说明
echo • 🧪 测试自动更新API功能
echo • 🔄 刷新版本列表
echo.
echo 🌐 服务器信息:
echo • 地址: http://198.23.135.176:5000
echo • 存储路径: /opt/license_manager/updates/
echo • API接口: /api/check_update, /api/download_update
echo.
echo 💡 使用说明:
echo 1. 首先运行"添加自动更新API到license_server.py"
echo 2. 使用此工具上传exe文件到服务器
echo 3. 客户端exe会自动检查并下载更新
echo.

echo 🚀 启动exe文件管理工具...
echo.

REM 运行管理工具
python "exe文件管理工具.py"

echo.
echo 👋 管理工具已关闭
pause
