#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复SSH连接问题
自动安装paramiko并测试连接
"""

import subprocess
import sys
import os

def install_paramiko():
    """安装paramiko模块"""
    print("📦 安装paramiko模块...")
    
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip升级完成")
        
        # 安装paramiko
        subprocess.run([sys.executable, "-m", "pip", "install", "paramiko"], 
                      check=True, capture_output=True)
        print("✅ paramiko安装完成")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False

def test_paramiko():
    """测试paramiko连接"""
    print("🧪 测试paramiko连接...")
    
    try:
        import paramiko
        print("✅ paramiko模块导入成功")
        
        # 测试连接配置
        host = "**************"
        username = "root"
        password = "l39XNqJG24JmXc2za0"
        
        print(f"🔗 测试连接到 {username}@{host}...")
        
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        # 执行测试命令
        stdin, stdout, stderr = client.exec_command("echo 'SSH连接测试成功' && uname -a")
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        
        client.close()
        
        if output:
            print("✅ SSH连接测试成功")
            print(f"服务器信息: {output.strip()}")
            return True
        else:
            print(f"❌ SSH连接失败: {error}")
            return False
            
    except ImportError:
        print("❌ paramiko模块导入失败")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def create_test_files():
    """创建测试用的必要文件"""
    print("📝 创建测试文件...")
    
    # 创建version_info.json
    if not os.path.exists("version_info.json"):
        import json
        version_info = {
            "version": "1.0.0",
            "build_date": "2024-01-01",
            "description": "测试版本"
        }
        with open("version_info.json", "w", encoding="utf-8") as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        print("✅ 创建 version_info.json")
    
    # 创建简单的license_server.py
    if not os.path.exists("license_server.py"):
        license_server_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权服务器
"""

from flask import Flask, jsonify, request
import os

app = Flask(__name__)

@app.route('/')
def index():
    return jsonify({
        "status": "running",
        "message": "授权服务器运行中",
        "version": "1.0.0"
    })

@app.route('/api/check_update')
def check_update():
    return jsonify({
        "has_update": False,
        "version": "1.0.0",
        "message": "当前版本是最新的"
    })

if __name__ == "__main__":
    port = int(os.environ.get('PORT', 5000))
    app.run(host="0.0.0.0", port=port, debug=False)
'''
        with open("license_server.py", "w", encoding="utf-8") as f:
            f.write(license_server_content)
        print("✅ 创建 license_server.py")
    
    # 创建简单的auto_updater.py
    if not os.path.exists("auto_updater.py"):
        auto_updater_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动更新器
"""

import json
import os

def check_update():
    """检查更新"""
    return {
        "has_update": False,
        "version": "1.0.0",
        "message": "当前版本是最新的"
    }

def get_version_info():
    """获取版本信息"""
    try:
        with open("version_info.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except:
        return {"version": "1.0.0", "description": "默认版本"}

if __name__ == "__main__":
    print("自动更新器运行中")
    print(f"当前版本: {get_version_info()}")
'''
        with open("auto_updater.py", "w", encoding="utf-8") as f:
            f.write(auto_updater_content)
        print("✅ 创建 auto_updater.py")

def main():
    """主函数"""
    print("🔧 SSH连接问题快速修复工具")
    print("=" * 50)
    
    # 创建必要文件
    create_test_files()
    
    # 安装paramiko
    print("\n📦 步骤1: 安装paramiko模块")
    if not install_paramiko():
        print("❌ paramiko安装失败")
        print("\n📋 手动安装方法:")
        print("1. 打开命令提示符")
        print("2. 运行: pip install paramiko")
        print("3. 或者: python -m pip install paramiko")
        return False
    
    # 测试连接
    print("\n🧪 步骤2: 测试SSH连接")
    if not test_paramiko():
        print("❌ SSH连接测试失败")
        print("\n📋 可能的原因:")
        print("1. 服务器地址或端口错误")
        print("2. 用户名或密码错误")
        print("3. 网络连接问题")
        print("4. 服务器SSH服务未启动")
        return False
    
    print("\n🎉 SSH连接修复完成！")
    print("现在可以正常使用部署工具了")
    
    # 启动部署工具
    print("\n🚀 启动部署工具...")
    try:
        subprocess.Popen([sys.executable, "一键部署界面工具.py"])
        print("✅ 部署工具启动成功")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 修复完成！")
    else:
        print("\n❌ 修复失败，请手动处理")
    
    input("\n按回车键退出...")
