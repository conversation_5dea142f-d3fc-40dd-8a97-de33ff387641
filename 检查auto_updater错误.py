#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查auto_updater.py中的错误处理
"""

import ast
import traceback

def analyze_auto_updater():
    """分析auto_updater.py文件"""
    print("🔍 分析auto_updater.py文件")
    print("=" * 50)
    
    try:
        with open("auto_updater.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 文件大小: {len(content)} 字符")
        print(f"📄 文件行数: {len(content.splitlines())} 行")
        
        # 检查语法错误
        try:
            ast.parse(content)
            print("✅ 语法检查通过")
        except SyntaxError as e:
            print(f"❌ 语法错误: {e}")
            print(f"📍 错误位置: 第{e.lineno}行")
            return False
        
        # 检查关键函数是否存在
        functions_to_check = [
            'check_update',
            'download_update',
            '_verify_file_hash',
            'apply_update',
            'check_and_update'
        ]
        
        print("\n🔍 检查关键函数:")
        for func_name in functions_to_check:
            if f"def {func_name}" in content:
                print(f"✅ {func_name} - 存在")
            else:
                print(f"❌ {func_name} - 缺失")
        
        # 检查导入语句
        print("\n🔍 检查导入语句:")
        required_imports = [
            'requests',
            'tkinter',
            'tempfile',
            'hashlib',
            'os',
            'sys',
            'time',
            'json'
        ]
        
        for import_name in required_imports:
            if f"import {import_name}" in content or f"from {import_name}" in content:
                print(f"✅ {import_name} - 已导入")
            else:
                print(f"❌ {import_name} - 未导入")
        
        # 检查异常处理
        print("\n🔍 检查异常处理:")
        exception_patterns = [
            'except Exception as e:',
            'except requests.exceptions',
            'except (requests.exceptions.ChunkedEncodingError',
            'except requests.exceptions.Timeout',
            'except requests.exceptions.ConnectionError'
        ]
        
        for pattern in exception_patterns:
            count = content.count(pattern)
            print(f"📊 {pattern}: {count} 次")
        
        # 检查可能的问题
        print("\n🔍 检查可能的问题:")
        
        # 检查print语句（可能被静默处理）
        print_count = content.count('print(')
        print(f"📊 print语句数量: {print_count}")
        
        # 检查return None语句
        return_none_count = content.count('return None')
        print(f"📊 return None语句数量: {return_none_count}")
        
        # 检查messagebox错误显示
        messagebox_error_count = content.count('messagebox.showerror')
        print(f"📊 messagebox.showerror数量: {messagebox_error_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        traceback.print_exc()
        return False

def test_import_auto_updater():
    """测试导入auto_updater"""
    print("\n🧪 测试导入auto_updater")
    print("=" * 50)
    
    try:
        import auto_updater
        print("✅ auto_updater导入成功")
        
        # 检查类和函数
        if hasattr(auto_updater, 'AutoUpdater'):
            print("✅ AutoUpdater类存在")
            
            # 创建实例测试
            try:
                updater = auto_updater.AutoUpdater(
                    license_key="ADMIN_BYPASS",
                    device_id="ADMIN-DEVICE-001"
                )
                print("✅ AutoUpdater实例创建成功")
                
                # 测试方法
                if hasattr(updater, 'check_update'):
                    print("✅ check_update方法存在")
                else:
                    print("❌ check_update方法不存在")
                
                if hasattr(updater, 'download_update'):
                    print("✅ download_update方法存在")
                else:
                    print("❌ download_update方法不存在")
                    
            except Exception as e:
                print(f"❌ AutoUpdater实例创建失败: {e}")
        else:
            print("❌ AutoUpdater类不存在")
        
        if hasattr(auto_updater, 'check_and_update'):
            print("✅ check_and_update函数存在")
        else:
            print("❌ check_and_update函数不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ auto_updater导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        traceback.print_exc()
        return False

def create_simple_test():
    """创建简单的更新测试"""
    print("\n🧪 创建简单的更新测试")
    print("=" * 50)
    
    test_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的更新测试
"""

def test_simple_update():
    """简单的更新测试"""
    print("🧪 开始简单更新测试")
    
    try:
        # 导入模块
        print("📦 导入auto_updater...")
        from auto_updater import check_and_update
        print("✅ 导入成功")
        
        # 调用更新函数
        print("🚀 调用check_and_update...")
        result = check_and_update(
            parent_window=None,
            current_version="2.1.0",
            license_key="ADMIN_BYPASS",
            device_id="ADMIN-DEVICE-001"
        )
        
        print(f"📊 更新结果: {result}")
        
        if result:
            print("✅ 更新成功")
        else:
            print("ℹ️ 没有更新或用户取消")
            
    except Exception as e:
        print(f"❌ 更新测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_update()
'''
    
    with open("简单更新测试.py", 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print("✅ 已创建 简单更新测试.py")

def main():
    """主函数"""
    print("🔧 检查auto_updater错误")
    print("=" * 60)
    
    # 1. 分析文件
    if not analyze_auto_updater():
        print("❌ 文件分析失败")
        return
    
    # 2. 测试导入
    if not test_import_auto_updater():
        print("❌ 导入测试失败")
        return
    
    # 3. 创建简单测试
    create_simple_test()
    
    print("\n" + "=" * 60)
    print("📊 检查完成")
    print("💡 建议:")
    print("1. 运行 python 详细诊断更新问题.py 进行详细诊断")
    print("2. 运行 python 简单更新测试.py 进行简单测试")
    print("3. 查看控制台输出的详细错误信息")

if __name__ == "__main__":
    main()
