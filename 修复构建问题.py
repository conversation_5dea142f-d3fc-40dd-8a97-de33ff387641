#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复构建问题 - 确保exe包含所有修复
"""

import os
import shutil
import json
from pathlib import Path

def check_build_files():
    """检查构建所需的文件是否包含修复"""
    print("🔍 检查构建文件状态")
    print("=" * 50)
    
    files_to_check = {
        "license_client.py": "主程序文件",
        "update_config.py": "更新配置文件", 
        "auto_updater.py": "自动更新模块"
    }
    
    issues = []
    
    for filename, description in files_to_check.items():
        if not os.path.exists(filename):
            issues.append(f"❌ {filename} 不存在")
            continue
            
        print(f"✅ {filename} ({description}) - 存在")
        
        # 检查关键修复是否存在
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if filename == "license_client.py":
            if "检查是否刚刚完成更新" in content:
                print(f"   ✅ 包含更新状态检查修复")
            else:
                issues.append(f"   ❌ 缺少更新状态检查修复")
                
        elif filename == "update_config.py":
            if "auto_check_enabled" in content and "True" in content:
                print(f"   ✅ 自动更新已启用")
            else:
                issues.append(f"   ❌ 自动更新配置有问题")
                
            if "update_version" in content:
                print(f"   ✅ 包含版本更新函数")
            else:
                issues.append(f"   ❌ 缺少版本更新函数")
                
        elif filename == "auto_updater.py":
            if "apply_update" in content and "new_version" in content:
                print(f"   ✅ 包含版本参数修复")
            else:
                issues.append(f"   ❌ 缺少版本参数修复")
    
    if issues:
        print("\n⚠️ 发现问题:")
        for issue in issues:
            print(issue)
        return False
    else:
        print("\n✅ 所有构建文件检查通过")
        return True

def create_build_spec():
    """创建优化的构建规格文件"""
    print("\n📝 创建优化的构建规格")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 分析主程序
a = Analysis(
    ['license_client.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('update_config.py', '.'),  # 确保包含配置文件
        ('auto_updater.py', '.'),   # 确保包含更新模块
    ],
    hiddenimports=[
        'fake_useragent',
        'fake_useragent.fake',
        'fake_useragent.utils',
        'openpyxl',
        'openpyxl.workbook.workbook',
        'openpyxl.worksheet.worksheet',
        'pandas',
        'requests',
        'selenium',
        'bs4',
        'lxml',
        'cryptography',
        'PIL',
        'update_config',  # 确保包含我们的配置模块
        'auto_updater',   # 确保包含我们的更新模块
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'numpy.distutils',
        'jupyter',
        'notebook',
        'IPython',
        'pytest',
        'unittest'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集数据
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 创建exe
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='亚马逊蓝图工具_修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('build_fixed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建 build_fixed.spec 文件")

def verify_version_config():
    """验证版本配置"""
    print("\n🔍 验证版本配置")
    
    try:
        from update_config import get_config
        config = get_config()
        
        current_version = config.get("current_version", "unknown")
        auto_update = config.get("auto_check_enabled", False)
        
        print(f"📊 当前版本: {current_version}")
        print(f"📊 自动更新: {'启用' if auto_update else '禁用'}")
        
        if current_version == "unknown":
            print("⚠️ 版本号未正确设置")
            return False
            
        if not auto_update:
            print("⚠️ 自动更新被禁用")
            return False
            
        print("✅ 版本配置正确")
        return True
        
    except Exception as e:
        print(f"❌ 版本配置检查失败: {e}")
        return False

def create_build_script():
    """创建修复版构建脚本"""
    print("\n📝 创建修复版构建脚本")
    
    build_script = '''@echo off
echo 🔧 开始构建修复版exe
echo ================================

echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo 📋 检查PyInstaller...
python -m pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 📥 安装PyInstaller...
    python -m pip install pyinstaller
)

echo 🗑️ 清理旧构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo 🚀 开始构建...
python -m PyInstaller build_fixed.spec

if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成！
echo 📁 输出文件位于 dist 目录

echo 🧪 测试exe文件...
if exist "dist\\亚马逊蓝图工具_修复版.exe" (
    echo ✅ exe文件生成成功
    echo 📊 文件大小:
    dir "dist\\亚马逊蓝图工具_修复版.exe" | find ".exe"
) else (
    echo ❌ exe文件未找到
)

echo ================================
echo 🎉 构建流程完成
pause
'''
    
    with open('build_fixed.bat', 'w', encoding='utf-8') as f:
        f.write(build_script)
    
    print("✅ 已创建 build_fixed.bat 构建脚本")

def create_test_script():
    """创建exe测试脚本"""
    print("\n📝 创建exe测试脚本")
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试构建的exe是否包含修复
"""

import subprocess
import os
import time
import json

def test_exe_version():
    """测试exe版本信息"""
    exe_path = "dist/亚马逊蓝图工具_修复版.exe"
    
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在")
        return False
    
    print(f"🧪 测试exe文件: {exe_path}")
    
    # 检查文件大小
    file_size = os.path.getsize(exe_path) / (1024 * 1024)
    print(f"📊 文件大小: {file_size:.1f} MB")
    
    # 尝试启动exe（快速测试）
    try:
        print("🚀 测试exe启动...")
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待2秒看是否能正常启动
        time.sleep(2)
        
        if process.poll() is None:
            print("✅ exe启动成功")
            process.terminate()
            return True
        else:
            print("❌ exe启动失败")
            return False
            
    except Exception as e:
        print(f"❌ exe测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 exe修复验证测试")
    print("=" * 40)
    
    if test_exe_version():
        print("✅ exe测试通过")
    else:
        print("❌ exe测试失败")
'''
    
    with open('test_fixed_exe.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 已创建 test_fixed_exe.py 测试脚本")

def main():
    """主函数"""
    print("🔧 修复构建问题工具")
    print("=" * 60)
    
    # 1. 检查构建文件
    if not check_build_files():
        print("\n❌ 构建文件检查失败，请先修复相关问题")
        return
    
    # 2. 验证版本配置
    if not verify_version_config():
        print("\n❌ 版本配置有问题，请检查update_config.py")
        return
    
    # 3. 创建构建文件
    create_build_spec()
    create_build_script()
    create_test_script()
    
    print("\n" + "=" * 60)
    print("📋 修复完成！接下来的步骤:")
    print("1. 运行 build_fixed.bat 构建修复版exe")
    print("2. 运行 python test_fixed_exe.py 测试exe")
    print("3. 测试新的exe是否还会重复更新")
    print("\n🎯 新的exe文件将包含所有修复，不会再重复更新！")

if __name__ == "__main__":
    main()
