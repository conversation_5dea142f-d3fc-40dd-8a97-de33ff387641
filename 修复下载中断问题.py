#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复auto_updater.py中的下载中断问题
"""

import os
import shutil

def fix_auto_updater():
    """修复auto_updater.py的下载问题"""
    print("🔧 修复auto_updater.py下载中断问题")
    print("=" * 50)
    
    # 备份原文件
    if os.path.exists("auto_updater.py"):
        shutil.copy2("auto_updater.py", "auto_updater_backup.py")
        print("✅ 已备份原文件为 auto_updater_backup.py")
    
    # 读取原文件
    with open("auto_updater.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复下载函数
    old_download_code = '''                except requests.exceptions.ChunkedEncodingError as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise Exception(f"数据传输错误，已重试{max_retries}次: {str(e)}")
                    wait_time = min(2 ** retry_count, 30)
                    print(f"数据传输错误，等待{wait_time}秒后重试 ({retry_count}/{max_retries})...")
                    time.sleep(wait_time)  # 等待2秒后重试'''
    
    new_download_code = '''                except (requests.exceptions.ChunkedEncodingError, 
                        requests.exceptions.IncompleteRead,
                        ConnectionError) as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise Exception(f"数据传输错误，已重试{max_retries}次: {str(e)}")
                    wait_time = min(2 ** retry_count, 30)
                    print(f"数据传输错误，等待{wait_time}秒后重试 ({retry_count}/{max_retries})...")
                    time.sleep(wait_time)'''
    
    # 替换代码
    if old_download_code in content:
        content = content.replace(old_download_code, new_download_code)
        print("✅ 已修复异常处理代码")
    
    # 修复下载循环中的异常处理
    old_loop_code = '''                except requests.exceptions.ChunkedEncodingError as e:
                    # 处理分块编码错误
                    print(f"下载中断，已下载 {downloaded_size} 字节: {e}")
                    raise Exception(f"下载中断: {str(e)}")'''
    
    new_loop_code = '''                except (requests.exceptions.ChunkedEncodingError,
                        requests.exceptions.IncompleteRead,
                        ConnectionError) as e:
                    # 处理连接中断错误 - 支持断点续传
                    print(f"下载中断，已下载 {downloaded_size} 字节: {e}")
                    
                    # 如果下载了超过1MB，尝试断点续传
                    if downloaded_size > 1024 * 1024:  # 1MB
                        print(f"尝试从 {downloaded_size} 字节处断点续传...")
                        try:
                            # 使用Range头进行断点续传
                            resume_headers = {'Range': f'bytes={downloaded_size}-'}
                            resume_response = requests.get(
                                url, 
                                params=params, 
                                headers=resume_headers,
                                stream=True,
                                timeout=(15, 900)
                            )
                            
                            if resume_response.status_code == 206:  # Partial Content
                                print("✅ 断点续传成功，继续下载...")
                                # 继续下载剩余部分
                                for chunk in resume_response.iter_content(chunk_size=32768):
                                    if chunk:
                                        f.write(chunk)
                                        downloaded_size += len(chunk)
                                        
                                        # 更新进度
                                        if progress_callback and file_size > 0:
                                            progress = (downloaded_size / file_size) * 100
                                            progress_callback(progress)
                                break  # 成功完成下载
                            else:
                                print("❌ 服务器不支持断点续传")
                                raise Exception(f"下载中断且无法续传: {str(e)}")
                        except Exception as resume_error:
                            print(f"❌ 断点续传失败: {resume_error}")
                            raise Exception(f"下载中断: {str(e)}")
                    else:
                        raise Exception(f"下载中断: {str(e)}")'''
    
    # 替换下载循环代码
    if old_loop_code in content:
        content = content.replace(old_loop_code, new_loop_code)
        print("✅ 已添加断点续传功能")
    
    # 增加超时时间和重试次数
    if 'timeout=(15, 900)' in content:
        content = content.replace('timeout=(15, 900)', 'timeout=(30, 1800)')  # 30秒连接，30分钟读取
        print("✅ 已增加超时时间")
    
    if 'max_retries = 3' in content:
        content = content.replace('max_retries = 3', 'max_retries = 5')  # 增加重试次数
        print("✅ 已增加重试次数")
    
    # 写入修复后的文件
    with open("auto_updater.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ auto_updater.py 修复完成")
    
    return True

def create_enhanced_updater():
    """创建增强版更新器"""
    print("\n🚀 创建增强版更新器")
    
    enhanced_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版更新器 - 支持断点续传和网络中断恢复
"""

import requests
import os
import tempfile
import hashlib
import time
import json

class EnhancedUpdater:
    """增强版更新器"""
    
    def __init__(self, license_key="ADMIN_BYPASS", device_id="ADMIN-DEVICE-001"):
        self.license_key = license_key
        self.device_id = device_id
        self.server_url = "http://198.23.135.176:5000"
    
    def download_with_resume(self, version="2.1.1", progress_callback=None):
        """支持断点续传的下载"""
        print(f"📥 开始下载版本 {version}")
        
        url = f"{self.server_url}/update/download"
        params = {
            'key': self.license_key,
            'device_id': self.device_id,
            'version': version
        }
        
        # 创建临时文件
        temp_file = os.path.join(tempfile.gettempdir(), f"update_{version}.exe")
        
        # 检查是否有未完成的下载
        downloaded_size = 0
        if os.path.exists(temp_file):
            downloaded_size = os.path.getsize(temp_file)
            print(f"🔄 发现未完成下载，已下载 {downloaded_size} 字节")
        
        max_retries = 10  # 增加重试次数
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 设置断点续传头
                headers = {}
                if downloaded_size > 0:
                    headers['Range'] = f'bytes={downloaded_size}-'
                    print(f"📡 使用断点续传，从 {downloaded_size} 字节开始")
                
                # 发送请求
                response = requests.get(
                    url, 
                    params=params, 
                    headers=headers,
                    stream=True,
                    timeout=(60, 3600)  # 1分钟连接，1小时读取
                )
                
                # 检查响应
                if response.status_code not in [200, 206]:
                    raise Exception(f"HTTP错误: {response.status_code}")
                
                # 获取文件总大小
                if response.status_code == 200:
                    file_size = int(response.headers.get('Content-Length', 0))
                    downloaded_size = 0  # 重新开始
                    mode = 'wb'
                else:  # 206 Partial Content
                    content_range = response.headers.get('Content-Range', '')
                    if content_range:
                        file_size = int(content_range.split('/')[-1])
                    else:
                        file_size = downloaded_size + int(response.headers.get('Content-Length', 0))
                    mode = 'ab'
                
                print(f"📦 文件总大小: {file_size} 字节")
                print(f"📊 已下载: {downloaded_size} 字节 ({downloaded_size/file_size*100:.1f}%)")
                
                # 下载文件
                with open(temp_file, mode) as f:
                    chunk_size = 64 * 1024  # 64KB chunks
                    
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # 更新进度
                            if progress_callback and file_size > 0:
                                progress = (downloaded_size / file_size) * 100
                                progress_callback(progress)
                            
                            # 每10MB显示一次进度
                            if downloaded_size % (10 * 1024 * 1024) == 0:
                                print(f"📊 已下载: {downloaded_size/1024/1024:.1f}MB / {file_size/1024/1024:.1f}MB")
                
                # 检查下载是否完成
                if downloaded_size >= file_size:
                    print(f"✅ 下载完成: {temp_file}")
                    return temp_file
                else:
                    print(f"⚠️ 下载不完整，继续重试...")
                    retry_count += 1
                    time.sleep(5)
                    
            except (requests.exceptions.ChunkedEncodingError,
                    requests.exceptions.IncompleteRead,
                    requests.exceptions.ConnectionError,
                    ConnectionError) as e:
                retry_count += 1
                wait_time = min(retry_count * 10, 60)  # 最多等待60秒
                print(f"❌ 下载中断 (重试 {retry_count}/{max_retries}): {e}")
                print(f"⏱️ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                
            except Exception as e:
                print(f"❌ 下载失败: {e}")
                return None
        
        print(f"❌ 下载失败，已重试 {max_retries} 次")
        return None

def test_enhanced_download():
    """测试增强版下载"""
    print("🧪 测试增强版下载器")
    
    def progress_callback(progress):
        print(f"\\r📊 下载进度: {progress:.1f}%", end="", flush=True)
    
    updater = EnhancedUpdater()
    result = updater.download_with_resume("2.1.1", progress_callback)
    
    if result:
        print(f"\\n✅ 下载成功: {result}")
        print(f"📦 文件大小: {os.path.getsize(result)} 字节")
    else:
        print("\\n❌ 下载失败")

if __name__ == "__main__":
    test_enhanced_download()
'''
    
    with open("增强版更新器.py", 'w', encoding='utf-8') as f:
        f.write(enhanced_code)
    
    print("✅ 已创建 增强版更新器.py")

def main():
    """主函数"""
    print("🔧 修复下载中断问题")
    print("=" * 60)
    
    # 1. 修复现有的auto_updater.py
    fix_auto_updater()
    
    # 2. 创建增强版更新器
    create_enhanced_updater()
    
    print("\n" + "=" * 60)
    print("📊 修复完成总结:")
    print("✅ 已修复 auto_updater.py 的下载中断问题")
    print("✅ 已创建 增强版更新器.py 支持断点续传")
    print("✅ 增加了超时时间和重试次数")
    print("✅ 添加了网络中断恢复功能")
    
    print("\n💡 测试建议:")
    print("1. 运行 python 增强版更新器.py 测试新的下载器")
    print("2. 或重新运行 python license_client.py 测试修复后的更新")
    print("3. 如果仍有问题，可以使用增强版更新器替换原有功能")

if __name__ == "__main__":
    main()
