#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试普通用户自动更新功能
"""

import requests
import json

def test_normal_user_update():
    """测试普通用户的自动更新功能"""
    print("🧪 测试普通用户自动更新功能")
    print("=" * 50)
    
    server_url = "http://**************:5000"
    
    # 模拟普通用户的认证信息
    test_cases = [
        {
            "name": "管理员密钥测试",
            "key": "ADMIN_BYPASS",
            "device_id": "ADMIN-DEVICE-001",
            "description": "使用管理员密钥（应该成功）"
        },
        {
            "name": "有效激活码测试",
            "key": "V09M4HCC-20350729-def2f7",
            "device_id": "ADMIN-DEVICE-001",
            "description": "使用之前创建的有效激活码"
        },
        {
            "name": "无效激活码测试",
            "key": "INVALID-KEY-123",
            "device_id": "TEST-DEVICE-001",
            "description": "使用无效激活码（应该失败）"
        },
        {
            "name": "空参数测试",
            "key": None,
            "device_id": None,
            "description": "不提供认证参数（应该失败）"
        }
    ]
    
    print("🔍 测试不同用户的更新API访问权限...")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📋 测试 {i}/{len(test_cases)}: {test_case['name']}")
        print(f"   📝 描述: {test_case['description']}")
        print(f"   🔑 密钥: {test_case['key']}")
        print(f"   📱 设备ID: {test_case['device_id']}")
        
        try:
            # 测试检查更新API
            params = {}
            if test_case['key'] and test_case['device_id']:
                params = {
                    'current_version': '2.0.0',
                    'key': test_case['key'],
                    'device_id': test_case['device_id']
                }
            elif test_case['key']:
                params = {
                    'current_version': '2.0.0',
                    'key': test_case['key']
                }
            elif test_case['device_id']:
                params = {
                    'current_version': '2.0.0',
                    'device_id': test_case['device_id']
                }
            else:
                params = {'current_version': '2.0.0'}
            
            response = requests.get(
                f"{server_url}/update/check",
                params=params,
                timeout=10
            )
            
            print(f"   📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 检查更新成功")
                print(f"   📄 成功: {result.get('success', False)}")
                if result.get('has_update'):
                    print(f"   🔄 有可用更新: {result.get('latest_version', 'Unknown')}")
                else:
                    print(f"   ✅ 已是最新版本")
                    
            elif response.status_code == 400:
                error_data = response.json()
                print(f"   ⚠️ 参数错误: {error_data.get('message', 'Unknown')}")
                
            elif response.status_code == 401:
                error_data = response.json()
                print(f"   🚫 认证失败: {error_data.get('message', 'Unknown')}")
                
            else:
                print(f"   ❌ 其他错误: HTTP {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   📄 错误信息: {error_data.get('message', 'Unknown')}")
                except:
                    print(f"   📄 响应内容: {response.text[:100]}...")
            
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        
        print()
    
    # 总结
    print("=" * 50)
    print("📋 测试总结:")
    print()
    print("✅ 管理员密钥 (ADMIN_BYPASS) - 可以无限制访问更新API")
    print("🔑 有效激活码 - 需要在数据库中存在且未过期")
    print("❌ 无效激活码 - 会被服务器拒绝")
    print("⚠️ 缺少参数 - 会返回参数错误")
    print()
    print("💡 结论:")
    print("- 普通用户需要有效的激活码才能使用自动更新功能")
    print("- 激活码必须存在于服务器数据库中且未过期")
    print("- 设备ID必须与激活码绑定的设备匹配")
    print("- 管理员可以使用ADMIN_BYPASS绕过所有检查")

def main():
    """主函数"""
    try:
        print("🎯 目标: 测试普通用户的自动更新功能")
        print("🔧 功能: 验证不同认证情况下的API访问")
        print("💡 服务器: **************:5000")
        print()
        
        test_normal_user_update()
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
