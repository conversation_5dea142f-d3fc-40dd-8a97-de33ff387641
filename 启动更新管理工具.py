#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动更新管理工具
检查依赖并启动更新管理界面
"""

import sys
import os
import subprocess
import importlib.util

def check_dependencies():
    """检查必要的依赖"""
    required_packages = ['tkinter', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖"""
    for package in packages:
        print(f"正在安装 {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    return True

def check_update_manager_file():
    """检查更新管理工具文件是否存在"""
    manager_file = "更新管理工具.py"
    if not os.path.exists(manager_file):
        print(f"❌ 找不到 {manager_file}")
        print("请确保文件在当前目录中")
        return False
    return True

def main():
    """主函数"""
    print("🔄 启动更新管理工具")
    print("=" * 50)
    
    # 检查更新管理工具文件
    if not check_update_manager_file():
        input("按回车键退出...")
        return
    
    # 检查依赖
    print("📦 检查依赖...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"⚠️ 缺少依赖: {', '.join(missing_packages)}")
        
        # 询问是否自动安装
        response = input("是否自动安装缺失的依赖？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是', '']:
            if install_dependencies(missing_packages):
                print("✅ 所有依赖安装完成")
            else:
                print("❌ 依赖安装失败")
                input("按回车键退出...")
                return
        else:
            print("❌ 无法启动，缺少必要依赖")
            input("按回车键退出...")
            return
    else:
        print("✅ 所有依赖已满足")
    
    # 启动更新管理工具
    print("🚀 启动更新管理工具...")
    try:
        # 导入并运行更新管理工具
        spec = importlib.util.spec_from_file_location("update_manager", "更新管理工具.py")
        update_manager = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(update_manager)
        
        # 运行主函数
        update_manager.main()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n详细错误信息:")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
