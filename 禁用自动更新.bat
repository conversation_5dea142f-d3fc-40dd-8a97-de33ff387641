@echo off
chcp 65001 >nul
title 禁用自动更新工具

echo.
echo ========================================
echo           自动更新控制工具
echo ========================================
echo.
echo 如果您的程序一直重复下载更新，
echo 请选择以下操作：
echo.
echo [1] 禁用自动更新（推荐）
echo [2] 启用自动更新
echo [3] 打开图形界面
echo [4] 退出
echo.

:menu
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto disable
if "%choice%"=="2" goto enable
if "%choice%"=="3" goto gui
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:disable
echo.
echo 正在禁用自动更新...
python "禁用自动更新.py" disable
if %errorlevel%==0 (
    echo.
    echo ✅ 自动更新已成功禁用！
    echo.
    echo 程序将不再自动下载更新。
    echo 如需更新，请使用程序内的"检查更新"按钮。
) else (
    echo.
    echo ❌ 禁用失败，请检查Python环境或以管理员身份运行
)
echo.
pause
goto exit

:enable
echo.
echo 正在启用自动更新...
python "禁用自动更新.py" enable
if %errorlevel%==0 (
    echo.
    echo ✅ 自动更新已重新启用！
) else (
    echo.
    echo ❌ 启用失败，请检查Python环境或以管理员身份运行
)
echo.
pause
goto exit

:gui
echo.
echo 正在打开图形界面...
python "禁用自动更新.py" gui
goto exit

:exit
exit
