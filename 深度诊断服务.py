#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断CentOS服务器问题
详细检查license_server.py的运行状态
"""

import paramiko
import sys
import time

def ssh_connect(host, username, password, command):
    """SSH连接并执行命令"""
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        client.connect(host, username=username, password=password, timeout=30)
        
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        exit_status = stdout.channel.recv_exit_status()
        
        client.close()
        
        return exit_status == 0, output, error
        
    except Exception as e:
        return False, "", str(e)

def deep_diagnosis():
    """深度诊断服务问题"""
    print("🔍 深度诊断CentOS服务器问题")
    print("=" * 50)
    
    # 服务器配置
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    print(f"🌐 服务器: {config['host']}")
    print(f"📁 路径: {config['deploy_path']}")
    print()
    
    # 检查1: 服务状态详情
    print("🔧 检查1: 服务状态详情...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "systemctl status license-manager -l --no-pager")
    print("服务状态:")
    for line in output.split('\n'):
        if line.strip():
            print(f"   {line}")
    print()
    
    # 检查2: 服务日志
    print("📋 检查2: 服务日志 (最近20行)...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "journalctl -u license-manager --no-pager -n 20")
    print("服务日志:")
    for line in output.split('\n')[-15:]:
        if line.strip():
            print(f"   {line}")
    print()
    
    # 检查3: Python进程
    print("🐍 检查3: Python进程状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "ps aux | grep python3")
    print("Python进程:")
    for line in output.split('\n'):
        if 'python3' in line and 'grep' not in line:
            print(f"   {line}")
    print()
    
    # 检查4: 端口监听详情
    print("🔌 检查4: 端口监听详情...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "netstat -tlnp")
    print("所有监听端口:")
    for line in output.split('\n'):
        if 'LISTEN' in line:
            print(f"   {line}")
    print()
    
    # 检查5: license_server.py内容
    print("📄 检查5: license_server.py配置...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"head -30 {config['deploy_path']}/license_server.py")
    print("脚本开头内容:")
    for line in output.split('\n'):
        if line.strip():
            print(f"   {line}")
    print()
    
    # 检查6: 手动运行测试
    print("🧪 检查6: 手动运行测试...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       f"cd {config['deploy_path']} && timeout 10 python3 license_server.py")
    print("手动运行结果:")
    if output:
        for line in output.split('\n'):
            if line.strip():
                print(f"   输出: {line}")
    if error:
        for line in error.split('\n'):
            if line.strip():
                print(f"   错误: {line}")
    print()
    
    # 检查7: 防火墙状态
    print("🔥 检查7: 防火墙状态...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "firewall-cmd --list-all")
    print("防火墙配置:")
    for line in output.split('\n'):
        if line.strip():
            print(f"   {line}")
    print()
    
    # 检查8: 系统资源
    print("💻 检查8: 系统资源...")
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], 
                                       "free -h && df -h /opt")
    print("系统资源:")
    for line in output.split('\n'):
        if line.strip():
            print(f"   {line}")
    print()
    
    return True

def fix_flask_config():
    """修复Flask配置"""
    print("🔧 修复Flask应用配置")
    print("=" * 30)
    
    config = {
        'host': '**************',
        'username': 'root',
        'password': 'l39XNqJG24JmXc2za0',
        'deploy_path': '/opt/license_manager'
    }
    
    # 创建修复版本的license_server.py
    fixed_server_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权服务器 - 修复版本
"""

from flask import Flask, jsonify, request
import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/')
def index():
    logger.info("收到根路径请求")
    return jsonify({
        "status": "running",
        "message": "授权服务器运行中",
        "version": "1.0.0",
        "path": os.getcwd()
    })

@app.route('/api/check_update')
def check_update():
    logger.info("收到更新检查请求")
    return jsonify({
        "has_update": False,
        "version": "1.0.0",
        "message": "当前版本是最新的"
    })

@app.route('/health')
def health():
    return jsonify({"status": "healthy"})

if __name__ == "__main__":
    logger.info("启动授权服务器...")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info(f"Python版本: {sys.version}")
    
    # 确保绑定到所有接口
    port = int(os.environ.get('PORT', 5000))
    logger.info(f"监听端口: {port}")
    
    app.run(
        host="0.0.0.0",  # 绑定所有接口
        port=port,
        debug=False,
        threaded=True
    )
'''
    
    # 备份原文件并创建新文件
    commands = [
        f"cd {config['deploy_path']}",
        f"cp license_server.py license_server.py.backup",
        f"cat > license_server.py << 'EOF'\\n{fixed_server_content}\\nEOF",
        "chmod +x license_server.py"
    ]
    
    command = " && ".join(commands)
    
    success, output, error = ssh_connect(config['host'], config['username'], config['password'], command)
    
    if success:
        print("✅ license_server.py已更新")
        
        # 重启服务
        print("🔄 重启服务...")
        success2, output2, error2 = ssh_connect(config['host'], config['username'], config['password'], 
                                               "systemctl restart license-manager")
        if success2:
            print("✅ 服务重启成功")
            
            # 等待服务启动
            time.sleep(5)
            
            # 测试服务
            print("🧪 测试服务...")
            success3, output3, error3 = ssh_connect(config['host'], config['username'], config['password'], 
                                                   "curl -s http://localhost:5000/health")
            if success3:
                print(f"✅ 服务测试成功: {output3}")
                return True
            else:
                print(f"❌ 服务测试失败: {error3}")
        else:
            print(f"❌ 服务重启失败: {error2}")
    else:
        print(f"❌ 文件更新失败: {error}")
    
    return False

def main():
    """主函数"""
    try:
        # 深度诊断
        deep_diagnosis()
        
        print("\n" + "="*50)
        print("🔧 尝试修复Flask配置...")
        
        # 修复配置
        if fix_flask_config():
            print("\n🎉 修复成功！")
            print("🌐 请访问: http://**************:5000/")
            print("🔍 健康检查: http://**************:5000/health")
        else:
            print("\n❌ 修复失败，请查看诊断信息")
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
