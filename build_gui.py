#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图形界面构建工具 - 亚马逊授权系统
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import subprocess
import sys
import os
import json
from pathlib import Path
import logging

class BuildGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("亚马逊授权系统构建工具")
        self.root.geometry("800x600")
        
        # 配置文件路径
        self.config_file = "build_config.json"
        self.load_config()
        
        # 设置日志
        self.setup_logging()
        
        # 创建界面
        self.create_widgets()
        
        # 加载默认依赖列表
        self.load_default_dependencies()
    
    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger("BuildGUI")
        self.logger.setLevel(logging.INFO)
        
        # 创建日志处理器
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def load_config(self):
        """加载配置"""
        self.config = {
            "dependencies": [],
            "build_options": {
                "onefile": True,
                "windowed": True,
                "clean": True,
                "icon": "icon.ico",
                "name": "亚马逊授权客户端"
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config.update(json.load(f))
            except Exception as e:
                self.log_message(f"加载配置失败: {str(e)}")
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="亚马逊授权系统构建工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 依赖管理区域
        deps_frame = ttk.LabelFrame(main_frame, text="依赖管理", padding="10")
        deps_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        deps_frame.columnconfigure(0, weight=1)
        
        # 依赖列表
        deps_list_frame = ttk.Frame(deps_frame)
        deps_list_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        deps_list_frame.columnconfigure(0, weight=1)
        
        ttk.Label(deps_list_frame, text="当前依赖列表:").grid(row=0, column=0, sticky=tk.W)
        
        # 依赖列表框
        self.deps_listbox = tk.Listbox(deps_list_frame, height=8)
        self.deps_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 依赖操作按钮
        deps_btn_frame = ttk.Frame(deps_list_frame)
        deps_btn_frame.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        ttk.Button(deps_btn_frame, text="添加依赖", 
                  command=self.add_dependency).pack(pady=2, fill=tk.X)
        ttk.Button(deps_btn_frame, text="删除依赖", 
                  command=self.remove_dependency).pack(pady=2, fill=tk.X)
        ttk.Button(deps_btn_frame, text="编辑依赖", 
                  command=self.edit_dependency).pack(pady=2, fill=tk.X)
        ttk.Button(deps_btn_frame, text="重置默认", 
                  command=self.reset_dependencies).pack(pady=2, fill=tk.X)
        
        # 构建选项
        options_frame = ttk.LabelFrame(deps_frame, text="构建选项", padding="5")
        options_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 构建选项变量
        self.onefile_var = tk.BooleanVar(value=self.config["build_options"]["onefile"])
        self.windowed_var = tk.BooleanVar(value=self.config["build_options"]["windowed"])
        self.clean_var = tk.BooleanVar(value=self.config["build_options"]["clean"])
        
        ttk.Checkbutton(options_frame, text="单文件模式 (--onefile)", 
                       variable=self.onefile_var).grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Checkbutton(options_frame, text="无控制台窗口 (--windowed)", 
                       variable=self.windowed_var).grid(row=0, column=1, sticky=tk.W, padx=5)
        ttk.Checkbutton(options_frame, text="清理临时文件 (--clean)", 
                       variable=self.clean_var).grid(row=0, column=2, sticky=tk.W, padx=5)
        
        # 图标和名称设置
        icon_frame = ttk.Frame(options_frame)
        icon_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        icon_frame.columnconfigure(1, weight=1)
        icon_frame.columnconfigure(3, weight=1)
        
        ttk.Label(icon_frame, text="图标文件:").grid(row=0, column=0, padx=5)
        self.icon_var = tk.StringVar(value=self.config["build_options"]["icon"])
        ttk.Entry(icon_frame, textvariable=self.icon_var).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        
        ttk.Label(icon_frame, text="程序名称:").grid(row=0, column=2, padx=5)
        self.name_var = tk.StringVar(value=self.config["build_options"]["name"])
        ttk.Entry(icon_frame, textvariable=self.name_var).grid(row=0, column=3, sticky=(tk.W, tk.E), padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="构建日志", padding="10")
        log_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 操作按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(btn_frame, text="检查依赖", command=self.check_dependencies).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="安装依赖", command=self.install_dependencies).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="测试Excel", command=self.test_excel).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="开始构建", command=self.start_build).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清理文件", command=self.cleanup_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
    
    def load_default_dependencies(self):
        """加载默认依赖列表"""
        default_deps = [
            "selenium>=4.15.0",
            "pandas>=2.0.0", 
            "openpyxl>=3.1.0",
            "xlsxwriter>=3.1.0",
            "requests>=2.31.0",
            "beautifulsoup4>=4.12.0",
            "webdriver_manager>=4.0.0",
            "psutil>=5.9.0",
            "cryptography>=41.0.0",
            "fake_useragent>=1.4.0",
            "lxml>=4.9.0",
            "pillow>=9.0.1,<9.6.0",  # 兼容amazoncaptcha
            "amazoncaptcha>=0.5.0",
            "pyinstaller>=6.0.0"
        ]
        
        if not self.config["dependencies"]:
            self.config["dependencies"] = default_deps
        
        self.update_deps_listbox()
    
    def update_deps_listbox(self):
        """更新依赖列表框"""
        self.deps_listbox.delete(0, tk.END)
        for dep in self.config["dependencies"]:
            self.deps_listbox.insert(tk.END, dep)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()
    
    def add_dependency(self):
        """添加依赖"""
        dialog = DependencyDialog(self.root, "添加依赖")
        if dialog.result:
            self.config["dependencies"].append(dialog.result)
            self.update_deps_listbox()
            self.log_message(f"添加依赖: {dialog.result}")
    
    def remove_dependency(self):
        """删除依赖"""
        selection = self.deps_listbox.curselection()
        if selection:
            index = selection[0]
            dep = self.config["dependencies"][index]
            self.config["dependencies"].pop(index)
            self.update_deps_listbox()
            self.log_message(f"删除依赖: {dep}")
        else:
            messagebox.showwarning("警告", "请选择要删除的依赖")
    
    def edit_dependency(self):
        """编辑依赖"""
        selection = self.deps_listbox.curselection()
        if selection:
            index = selection[0]
            current_dep = self.config["dependencies"][index]
            dialog = DependencyDialog(self.root, "编辑依赖", current_dep)
            if dialog.result:
                self.config["dependencies"][index] = dialog.result
                self.update_deps_listbox()
                self.log_message(f"编辑依赖: {current_dep} -> {dialog.result}")
        else:
            messagebox.showwarning("警告", "请选择要编辑的依赖")
    
    def reset_dependencies(self):
        """重置为默认依赖"""
        if messagebox.askyesno("确认", "确定要重置为默认依赖列表吗？"):
            self.config["dependencies"] = []
            self.load_default_dependencies()
            self.log_message("已重置为默认依赖列表")
    
    def check_dependencies(self):
        """检查依赖"""
        self.log_message("开始检查依赖...")
        
        def check_thread():
            missing_deps = []
            for dep in self.config["dependencies"]:
                package_name = dep.split(">=")[0].split("==")[0].split("<")[0]
                try:
                    __import__(package_name.replace("-", "_"))
                    self.log_message(f"✓ {package_name} 已安装")
                except ImportError:
                    missing_deps.append(dep)
                    self.log_message(f"✗ {package_name} 未安装")
            
            if missing_deps:
                self.log_message(f"发现 {len(missing_deps)} 个缺失的依赖")
            else:
                self.log_message("所有依赖都已安装")
        
        threading.Thread(target=check_thread, daemon=True).start()
    
    def install_dependencies(self):
        """安装依赖"""
        self.log_message("开始安装依赖...")
        
        def install_thread():
            try:
                # 创建临时requirements文件
                temp_req = "temp_requirements.txt"
                with open(temp_req, 'w', encoding='utf-8') as f:
                    for dep in self.config["dependencies"]:
                        f.write(f"{dep}\n")
                
                # 设置环境变量确保正确编码
                import os
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

                # 安装依赖
                cmd = [sys.executable, "-m", "pip", "install", "-r", temp_req]
                process = subprocess.Popen(cmd,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.STDOUT,
                                         text=True,
                                         encoding='utf-8',
                                         errors='replace',
                                         env=env)

                for line in process.stdout:
                    self.log_message(line.strip())
                
                process.wait()
                
                if process.returncode == 0:
                    self.log_message("依赖安装完成")
                else:
                    self.log_message("依赖安装失败")
                
                # 清理临时文件
                if os.path.exists(temp_req):
                    os.remove(temp_req)
                    
            except Exception as e:
                self.log_message(f"安装依赖时出错: {str(e)}")
        
        threading.Thread(target=install_thread, daemon=True).start()

    def test_excel(self):
        """测试Excel功能"""
        self.log_message("开始测试Excel功能...")

        def test_thread():
            try:
                import pandas as pd
                import openpyxl

                # 创建测试数据
                test_data = pd.DataFrame({
                    'Product': ['Test Product 1', 'Test Product 2', 'Test Product 3'],
                    'Price': [19.99, 29.99, 39.99],
                    'Stock': [100, 200, 300],
                    'Available': [True, False, True]
                })

                # 测试写入Excel
                test_file = 'excel_test.xlsx'
                test_data.to_excel(test_file, index=False, engine='openpyxl')
                self.log_message("✓ Excel写入测试成功")

                # 测试读取Excel
                read_data = pd.read_excel(test_file, engine='openpyxl')
                self.log_message("✓ Excel读取测试成功")

                # 验证数据一致性
                if len(read_data) == len(test_data) and list(read_data.columns) == list(test_data.columns):
                    self.log_message("✓ Excel数据一致性验证成功")
                    self.log_message("🎉 Excel功能测试全部通过！")
                    messagebox.showinfo("成功", "Excel功能测试全部通过！")
                else:
                    self.log_message("✗ Excel数据不一致")
                    messagebox.showwarning("警告", "Excel数据不一致，请检查openpyxl安装")

                # 清理测试文件
                if os.path.exists(test_file):
                    os.remove(test_file)
                    self.log_message("清理测试文件完成")

            except ImportError as e:
                missing_lib = str(e).split("'")[1] if "'" in str(e) else "未知库"
                self.log_message(f"✗ 缺少依赖库: {missing_lib}")
                messagebox.showerror("错误", f"缺少依赖库: {missing_lib}\n请先安装依赖")
            except Exception as e:
                self.log_message(f"✗ Excel功能测试失败: {str(e)}")
                messagebox.showerror("错误", f"Excel功能测试失败: {str(e)}")

        threading.Thread(target=test_thread, daemon=True).start()

    def start_build(self):
        """开始构建"""
        # 更新配置
        self.config["build_options"]["onefile"] = self.onefile_var.get()
        self.config["build_options"]["windowed"] = self.windowed_var.get()
        self.config["build_options"]["clean"] = self.clean_var.get()
        self.config["build_options"]["icon"] = self.icon_var.get()
        self.config["build_options"]["name"] = self.name_var.get()
        
        self.log_message("开始构建过程...")
        
        def build_thread():
            try:
                # 设置环境变量确保正确编码
                import os
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

                # 运行构建脚本
                cmd = [sys.executable, "build_license_system.py"]
                process = subprocess.Popen(cmd,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.STDOUT,
                                         text=True,
                                         encoding='utf-8',
                                         errors='replace',
                                         env=env)

                for line in process.stdout:
                    self.log_message(line.strip())

                process.wait()

                if process.returncode == 0:
                    self.log_message("构建完成！")
                    messagebox.showinfo("成功", "构建完成！请检查dist目录。")
                else:
                    self.log_message("构建失败")
                    messagebox.showerror("失败", "构建失败，请检查日志。")

            except Exception as e:
                self.log_message(f"构建时出错: {str(e)}")
                messagebox.showerror("错误", f"构建时出错: {str(e)}")
        
        threading.Thread(target=build_thread, daemon=True).start()
    
    def cleanup_files(self):
        """清理文件"""
        self.log_message("清理临时文件...")
        
        dirs_to_clean = ['build', '__pycache__', 'temp_embed']
        files_to_clean = ['*.spec', '*.log']
        
        cleaned = 0
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                import shutil
                shutil.rmtree(dir_name)
                self.log_message(f"删除目录: {dir_name}")
                cleaned += 1
        
        import glob
        for pattern in files_to_clean:
            for file in glob.glob(pattern):
                try:
                    os.remove(file)
                    self.log_message(f"删除文件: {file}")
                    cleaned += 1
                except:
                    pass
        
        self.log_message(f"清理完成，删除了 {cleaned} 个文件/目录")


class DependencyDialog:
    def __init__(self, parent, title, initial_value=""):
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # 创建界面
        frame = ttk.Frame(self.dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="依赖包名称 (例如: pandas>=2.0.0):").pack(anchor=tk.W, pady=(0, 10))
        
        self.entry = ttk.Entry(frame, width=50)
        self.entry.pack(fill=tk.X, pady=(0, 20))
        self.entry.insert(0, initial_value)
        self.entry.focus()
        
        # 按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.pack()
        
        ttk.Button(btn_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        self.entry.bind('<Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def ok_clicked(self):
        value = self.entry.get().strip()
        if value:
            self.result = value
        self.dialog.destroy()
    
    def cancel_clicked(self):
        self.dialog.destroy()


def main():
    root = tk.Tk()
    app = BuildGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
