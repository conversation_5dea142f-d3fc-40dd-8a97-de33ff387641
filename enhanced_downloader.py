#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版下载器 - 解决99%停止问题
"""

import requests
import os

class EnhancedDownloader:
    def __init__(self, server_url, license_key, device_id):
        self.server_url = server_url
        self.license_key = license_key
        self.device_id = device_id
    
def enhanced_download_update(self, update_info, progress_callback=None):
    """
    增强版下载更新文件 - 解决99%停止问题
    """
    try:
        version = update_info.get('version')
        file_size = update_info.get('file_size', 0)
        
        if not version:
            return None
        
        import tempfile
        temp_file = os.path.join(tempfile.gettempdir(), f"amazon_blueprint_update_{version}.exe")
        
        # 删除旧文件
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
            except:
                pass
        
        from urllib.parse import urljoin
        url = urljoin(self.server_url, "/update/download")
        params = {
            'key': self.license_key,
            'device_id': self.device_id,
            'version': version
        }
        
        max_retries = 3
        for retry in range(max_retries):
            try:
                response = requests.get(
                    url, 
                    params=params,
                    stream=True,
                    timeout=(60, 7200)  # 2小时超时
                )
                
                if response.status_code != 200:
                    raise Exception(f"HTTP错误: {response.status_code}")
                
                actual_file_size = int(response.headers.get('Content-Length', file_size))
                downloaded_size = 0
                last_progress = -1
                chunk_count = 0
                
                with open(temp_file, 'wb') as f:
                    chunk_size = 64 * 1024  # 64KB chunks
                    
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            chunk_count += 1
                            
                            # 每100个chunk刷新一次
                            if chunk_count % 100 == 0:
                                f.flush()
                            
                            # 更新进度
                            if progress_callback and actual_file_size > 0:
                                progress = (downloaded_size / actual_file_size) * 100
                                if progress - last_progress >= 0.5:  # 每0.5%更新一次
                                    display_progress = min(progress, 99.5)  # 最大显示99.5%
                                    progress_callback(display_progress)
                                    last_progress = progress
                    
                    # 最终刷新
                    f.flush()
                    os.fsync(f.fileno())
                
                # 验证文件
                if os.path.exists(temp_file):
                    actual_size = os.path.getsize(temp_file)
                    if actual_size >= actual_file_size * 0.999:  # 99.9%完整性
                        if progress_callback:
                            progress_callback(100.0)  # 最终显示100%
                        return temp_file
                    else:
                        raise Exception(f"文件不完整: {actual_size}/{actual_file_size}")
                else:
                    raise Exception("文件不存在")
                    
            except Exception as e:
                if retry == max_retries - 1:
                    return None
                
                # 删除损坏文件
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                
                import time
                time.sleep((retry + 1) * 5)
        
        return None
        
    except Exception as e:
        return None

# 测试代码
if __name__ == "__main__":
    downloader = EnhancedDownloader(
        "http://198.23.135.176:5000/",
        "ADMIN_BYPASS",
        "ADMIN-DEVICE-001"
    )
    
    # 模拟更新信息
    update_info = {
        'version': '2.1.1',
        'file_size': 57000000  # 约57MB
    }
    
    def progress_callback(progress):
        print(f"下载进度: {progress:.2f}%")
    
    result = downloader.enhanced_download_update(update_info, progress_callback)
    if result:
        print(f"下载成功: {result}")
    else:
        print("下载失败")
