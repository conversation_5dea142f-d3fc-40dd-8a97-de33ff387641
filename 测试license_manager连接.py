#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试license_manager.py与服务器的连接
"""

import requests
import json
from datetime import datetime

def test_server_connection():
    """测试服务器连接"""
    print("🧪 测试license_manager.py与服务器连接")
    print("=" * 50)
    
    server_url = "http://198.23.135.176:5000"
    
    print(f"🌐 服务器地址: {server_url}")
    print()
    
    # 测试1: 基本连接
    print("🔗 测试1: 基本连接...")
    try:
        response = requests.get(f"{server_url}/", timeout=10)
        if response.status_code == 200:
            print("   ✅ 基本连接成功")
            try:
                data = response.json()
                print(f"   📄 响应: {data.get('message', '未知')}")
            except:
                print(f"   📄 响应: {response.text[:100]}...")
        else:
            print(f"   ❌ 基本连接失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"   ❌ 基本连接异常: {e}")
        return False
    
    # 测试2: 健康检查
    print("\n🏥 测试2: 健康检查...")
    try:
        response = requests.get(f"{server_url}/health", timeout=10)
        if response.status_code == 200:
            print("   ✅ 健康检查成功")
        else:
            print(f"   ❌ 健康检查失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
    
    # 测试3: 许可证列表接口
    print("\n📋 测试3: 许可证列表接口...")
    try:
        response = requests.get(f"{server_url}/license/list", timeout=10)
        if response.status_code == 200:
            print("   ✅ 许可证列表接口成功")
            try:
                data = response.json()
                if data.get("success"):
                    licenses = data.get("licenses", [])
                    print(f"   📊 当前激活码数量: {len(licenses)}")
                else:
                    print(f"   ⚠️ 接口返回失败: {data.get('message', '未知错误')}")
            except:
                print(f"   📄 响应: {response.text[:100]}...")
        else:
            print(f"   ❌ 许可证列表接口失败 (状态码: {response.status_code})")
            print(f"   📄 错误响应: {response.text[:200]}...")
    except Exception as e:
        print(f"   ❌ 许可证列表接口异常: {e}")
    
    # 测试4: 生成激活码接口
    print("\n🔑 测试4: 生成激活码接口...")
    try:
        test_data = {
            "expire_days": 1,
            "quantity": 1,
            "permission_level": 1
        }
        response = requests.post(f"{server_url}/license/generate", 
                               json=test_data, timeout=10)
        if response.status_code == 200:
            print("   ✅ 生成激活码接口成功")
            try:
                data = response.json()
                if data.get("success"):
                    keys = data.get("keys", [])
                    print(f"   🔑 生成的测试激活码: {keys[0] if keys else '无'}")
                else:
                    print(f"   ⚠️ 生成失败: {data.get('message', '未知错误')}")
            except:
                print(f"   📄 响应: {response.text[:100]}...")
        else:
            print(f"   ❌ 生成激活码接口失败 (状态码: {response.status_code})")
            print(f"   📄 错误响应: {response.text[:200]}...")
    except Exception as e:
        print(f"   ❌ 生成激活码接口异常: {e}")
    
    # 测试5: 模拟license_manager.py的完整流程
    print("\n🔄 测试5: 模拟license_manager.py完整流程...")
    try:
        # 5.1 获取激活码列表
        print("   📋 5.1 获取激活码列表...")
        response = requests.get(f"{server_url}/license/list", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("     ✅ 列表获取成功")
                licenses = data.get("licenses", [])
                
                if licenses:
                    # 5.2 查询第一个激活码的详细信息
                    test_key = licenses[0]["key"]
                    print(f"   🔍 5.2 查询激活码详情: {test_key[:10]}...")
                    
                    response = requests.get(f"{server_url}/license/info", 
                                          params={"key": test_key}, timeout=10)
                    if response.status_code == 200:
                        info_data = response.json()
                        if info_data.get("success"):
                            print("     ✅ 激活码查询成功")
                            info = info_data.get("info", {})
                            print(f"     📊 状态: {info.get('status', '未知')}")
                            print(f"     📅 过期日期: {info.get('expire_date', '未知')}")
                            print(f"     🔐 权限级别: {info.get('permission_level', '未知')}")
                        else:
                            print(f"     ❌ 激活码查询失败: {info_data.get('message')}")
                    else:
                        print(f"     ❌ 激活码查询请求失败 (状态码: {response.status_code})")
                else:
                    print("     ℹ️ 当前没有激活码，跳过详情查询")
            else:
                print(f"     ❌ 列表获取失败: {data.get('message')}")
        else:
            print(f"     ❌ 列表请求失败 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"   ❌ 完整流程测试异常: {e}")
    
    return True

def provide_troubleshooting():
    """提供故障排除建议"""
    print("\n🔧 故障排除建议")
    print("=" * 30)
    
    print("如果license_manager.py仍然无法连接:")
    print()
    print("1. 🔍 检查端口配置:")
    print("   • 确认license_manager.py中的端口是5000")
    print("   • 检查防火墙是否开放端口5000")
    print()
    print("2. 🔄 重启服务:")
    print("   • SSH到服务器执行: systemctl restart license-manager")
    print("   • 查看服务状态: systemctl status license-manager")
    print()
    print("3. 📋 查看服务日志:")
    print("   • 执行: journalctl -u license-manager -f")
    print("   • 查看是否有错误信息")
    print()
    print("4. 🧪 手动测试:")
    print("   • 在服务器上执行: curl http://localhost:5000/license/list")
    print("   • 检查本地响应是否正常")
    print()
    print("5. 🔧 数据库初始化:")
    print("   • 可能需要创建licenses.json文件")
    print("   • 或者初始化数据库结构")

def main():
    """主函数"""
    try:
        print("🎯 目标: 验证license_manager.py能否正常连接服务器")
        print("📋 测试内容: 所有许可证管理API接口")
        print()
        
        # 执行连接测试
        if test_server_connection():
            print("\n🎉 连接测试完成！")
            print("💡 现在请尝试运行license_manager.py")
            print("🌐 如果仍有问题，请查看下面的故障排除建议")
            
            # 提供故障排除建议
            provide_troubleshooting()
        else:
            print("\n❌ 基本连接失败")
            print("🔧 请检查服务器状态和网络连接")
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
