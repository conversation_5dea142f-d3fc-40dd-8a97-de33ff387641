#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本 - 解决版本冲突问题
"""

import subprocess
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def install_package(package, description=""):
    """安装单个包"""
    try:
        logger.info(f"安装 {package} {description}")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        logger.info(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ {package} 安装失败: {e}")
        return False

def install_dependencies_step_by_step():
    """分步安装依赖，避免冲突"""
    
    # 第一步：安装基础依赖
    logger.info("=== 第一步：安装基础依赖 ===")
    basic_deps = [
        "requests>=2.31.0",
        "urllib3>=2.0.0", 
        "certifi>=2023.0.0",
        "charset-normalizer>=3.0.0",
        "idna>=3.4",
        "numpy>=1.24.0",
        "python-dateutil>=2.8.2",
        "pytz>=2020.1",
        "tzdata>=2022.7"
    ]
    
    for dep in basic_deps:
        install_package(dep)
    
    # 第二步：安装数据处理依赖
    logger.info("\n=== 第二步：安装数据处理依赖 ===")
    data_deps = [
        "pandas>=2.0.0",
        "openpyxl>=3.1.0", 
        "xlsxwriter>=3.1.0",
        "lxml>=4.9.0"
    ]
    
    for dep in data_deps:
        install_package(dep)
    
    # 第三步：安装Web相关依赖
    logger.info("\n=== 第三步：安装Web相关依赖 ===")
    web_deps = [
        "beautifulsoup4>=4.12.0",
        "selenium>=4.15.0",
        "webdriver_manager>=4.0.0",
        "fake_useragent>=1.4.0"
    ]
    
    for dep in web_deps:
        install_package(dep)
    
    # 第四步：安装系统依赖
    logger.info("\n=== 第四步：安装系统依赖 ===")
    system_deps = [
        "psutil>=5.9.0",
        "cryptography>=41.0.0"
    ]
    
    for dep in system_deps:
        install_package(dep)
    
    # 第五步：处理冲突的依赖（pillow和amazoncaptcha）
    logger.info("\n=== 第五步：处理冲突依赖 ===")
    
    # 先安装兼容版本的pillow
    logger.info("安装兼容版本的pillow...")
    if install_package("pillow>=9.0.1,<9.6.0", "(兼容amazoncaptcha)"):
        # 然后安装amazoncaptcha
        logger.info("安装amazoncaptcha...")
        install_package("amazoncaptcha>=0.5.0")
    else:
        # 如果失败，尝试降级pillow
        logger.info("尝试降级pillow...")
        install_package("pillow==9.5.0")
        install_package("amazoncaptcha>=0.5.0")
    
    # 第六步：安装构建工具
    logger.info("\n=== 第六步：安装构建工具 ===")
    build_deps = [
        "pyinstaller>=6.0.0"
    ]
    
    for dep in build_deps:
        install_package(dep)
    
    logger.info("\n=== 安装完成 ===")

def verify_installation():
    """验证安装"""
    logger.info("\n=== 验证安装 ===")
    
    packages_to_verify = [
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('selenium', 'selenium'),
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('cryptography', 'cryptography'),
        ('amazoncaptcha', 'amazoncaptcha'),
        ('pillow', 'PIL'),
        ('pyinstaller', 'PyInstaller')
    ]
    
    failed_imports = []
    
    for package_name, import_name in packages_to_verify:
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'Unknown')
            logger.info(f"✓ {package_name} ({version})")
        except ImportError:
            logger.error(f"✗ {package_name} 导入失败")
            failed_imports.append(package_name)
    
    if failed_imports:
        logger.error(f"以下包导入失败: {', '.join(failed_imports)}")
        return False
    else:
        logger.info("所有包验证成功！")
        return True

def test_excel_functionality():
    """测试Excel功能"""
    logger.info("\n=== 测试Excel功能 ===")
    
    try:
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Product': ['Test1', 'Test2'],
            'Price': [10.99, 20.99]
        })
        
        # 测试Excel写入
        test_file = 'dependency_test.xlsx'
        test_data.to_excel(test_file, index=False, engine='openpyxl')
        
        # 测试Excel读取
        read_data = pd.read_excel(test_file, engine='openpyxl')
        
        # 验证
        if len(read_data) == len(test_data):
            logger.info("✓ Excel读写功能正常")
            success = True
        else:
            logger.error("✗ Excel数据不一致")
            success = False
        
        # 清理
        import os
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return success
        
    except Exception as e:
        logger.error(f"✗ Excel功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始安装亚马逊授权系统依赖...")
    
    # 升级pip
    logger.info("升级pip...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
    
    # 分步安装依赖
    install_dependencies_step_by_step()
    
    # 验证安装
    if verify_installation():
        # 测试Excel功能
        if test_excel_functionality():
            logger.info("\n🎉 所有依赖安装并验证成功！")
            logger.info("现在可以运行构建脚本了。")
        else:
            logger.warning("Excel功能测试失败，但其他功能应该正常")
    else:
        logger.error("部分依赖安装失败，请检查错误信息")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
