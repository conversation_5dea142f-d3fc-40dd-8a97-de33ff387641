#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复更新对话框按钮显示问题
"""

import os
import sys

def fix_update_dialog():
    """修复更新对话框"""
    print("🔧 修复更新对话框按钮显示问题")
    print("=" * 50)
    
    # 检查auto_updater.py文件
    if not os.path.exists("auto_updater.py"):
        print("❌ 未找到auto_updater.py文件")
        return False
    
    try:
        # 读取文件
        with open("auto_updater.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ 读取auto_updater.py文件成功")
        
        # 检查是否已经修复
        if "🚀 立即更新" in content and "button_container" in content:
            print("✅ 更新对话框已经包含修复")
            return True
        
        # 如果没有修复，进行修复
        print("🔧 正在修复更新对话框...")
        
        # 查找并替换对话框大小
        if 'geometry("400x300")' in content:
            content = content.replace('geometry("400x300")', 'geometry("450x400")')
            print("✅ 修复对话框大小")
        
        # 查找并替换按钮部分
        old_button_code = '''        update_button = tk.Button(
            button_frame,
            text="立即更新",
            command=self.on_update,
            bg="#27ae60",
            fg="white",
            font=("微软雅黑", 10, "bold"),
            padx=20
        )
        update_button.pack(side=tk.LEFT, padx=(0, 10))'''
        
        new_button_code = '''        # 创建按钮容器，居中显示
        button_container = tk.Frame(button_frame, bg="#ffffff")
        button_container.pack(expand=True)
        
        update_button = tk.Button(
            button_container,
            text="🚀 立即更新",
            command=self.on_update,
            bg="#27ae60",
            fg="white",
            font=("微软雅黑", 11, "bold"),
            padx=25,
            pady=8,
            relief=tk.RAISED,
            bd=2,
            cursor="hand2"
        )
        update_button.pack(side=tk.LEFT, padx=(0, 15))'''
        
        if old_button_code in content:
            content = content.replace(old_button_code, new_button_code)
            print("✅ 修复立即更新按钮")
        
        # 写回文件
        with open("auto_updater.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_simple_update_test():
    """创建简单的更新测试"""
    print("\n🧪 创建更新功能测试")
    print("=" * 30)
    
    test_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的更新功能测试
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time

def show_update_dialog():
    """显示更新对话框"""
    # 创建对话框
    dialog = tk.Toplevel()
    dialog.title("发现新版本")
    dialog.geometry("450x400")
    dialog.resizable(False, False)
    dialog.grab_set()
    
    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
    y = (dialog.winfo_screenheight() // 2) - (400 // 2)
    dialog.geometry(f"+{x}+{y}")
    
    # 标题
    title_label = tk.Label(
        dialog,
        text="🎉 发现新版本！",
        font=("微软雅黑", 14, "bold"),
        fg="#2c3e50"
    )
    title_label.pack(pady=20)
    
    # 版本信息
    info_frame = tk.Frame(dialog)
    info_frame.pack(fill=tk.X, padx=20, pady=10)
    
    current_label = tk.Label(
        info_frame,
        text="当前版本: v2.1.0",
        font=("微软雅黑", 10)
    )
    current_label.pack(anchor='w')
    
    latest_label = tk.Label(
        info_frame,
        text="最新版本: v2.1.1",
        font=("微软雅黑", 10, "bold"),
        fg="#e74c3c"
    )
    latest_label.pack(anchor='w')
    
    # 更新说明
    changelog_label = tk.Label(
        info_frame,
        text="更新内容:",
        font=("微软雅黑", 10, "bold")
    )
    changelog_label.pack(anchor='w', pady=(10, 5))
    
    changelog_text = tk.Text(
        info_frame,
        height=8,
        width=45,
        font=("微软雅黑", 9),
        wrap=tk.WORD,
        bg="#f8f9fa",
        relief=tk.SUNKEN,
        bd=1
    )
    changelog_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    changelog_text.insert(tk.END, "• 修复了已知问题\\n• 优化了程序性能\\n• 增加了新功能")
    changelog_text.config(state=tk.DISABLED)
    
    # 按钮区域
    button_frame = tk.Frame(dialog, bg="#ffffff")
    button_frame.pack(fill=tk.X, padx=20, pady=(10, 20), side=tk.BOTTOM)
    
    # 按钮容器
    button_container = tk.Frame(button_frame, bg="#ffffff")
    button_container.pack(expand=True)
    
    def on_update():
        messagebox.showinfo("更新", "开始下载更新...")
        dialog.destroy()
    
    def on_later():
        messagebox.showinfo("提醒", "稍后提醒更新")
        dialog.destroy()
    
    # 立即更新按钮
    update_button = tk.Button(
        button_container,
        text="🚀 立即更新",
        command=on_update,
        bg="#27ae60",
        fg="white",
        font=("微软雅黑", 11, "bold"),
        padx=25,
        pady=8,
        relief=tk.RAISED,
        bd=2,
        cursor="hand2"
    )
    update_button.pack(side=tk.LEFT, padx=(0, 15))
    
    # 稍后提醒按钮
    later_button = tk.Button(
        button_container,
        text="⏰ 稍后提醒",
        command=on_later,
        bg="#95a5a6",
        fg="white",
        font=("微软雅黑", 10),
        padx=25,
        pady=8,
        relief=tk.RAISED,
        bd=2,
        cursor="hand2"
    )
    later_button.pack(side=tk.LEFT)

def main():
    """主函数"""
    root = tk.Tk()
    root.title("更新功能测试")
    root.geometry("300x200")
    
    # 测试按钮
    test_button = tk.Button(
        root,
        text="🧪 测试更新对话框",
        command=show_update_dialog,
        font=("微软雅黑", 12),
        bg="#3498db",
        fg="white",
        padx=20,
        pady=10
    )
    test_button.pack(expand=True)
    
    root.mainloop()

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("测试更新对话框简单版.py", 'w', encoding='utf-8') as f:
            f.write(test_code)
        print("✅ 创建测试文件: 测试更新对话框简单版.py")
        return True
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 更新对话框修复工具")
    print("=" * 60)
    
    try:
        # 修复更新对话框
        fix_success = fix_update_dialog()
        
        # 创建测试文件
        test_success = create_simple_update_test()
        
        print("\n" + "=" * 60)
        print("📊 修复结果:")
        print(f"   - 对话框修复: {'✅' if fix_success else '❌'}")
        print(f"   - 测试文件创建: {'✅' if test_success else '❌'}")
        
        if fix_success:
            print("\n🎉 修复完成！")
            print("💡 现在更新对话框应该正确显示按钮了")
            print("\n📋 修复内容:")
            print("   1. 增加了对话框大小 (450x400)")
            print("   2. 改进了按钮布局")
            print("   3. 增强了按钮样式")
            print("   4. 添加了图标和视觉效果")
            
            if test_success:
                print("\n🧪 测试建议:")
                print("   1. 运行: python 测试更新对话框简单版.py")
                print("   2. 检查按钮是否正确显示")
                print("   3. 测试按钮功能是否正常")
        else:
            print("\n⚠️ 修复可能不完整，请手动检查")
            
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")

if __name__ == "__main__":
    main()
