#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁用自动更新工具
用于永久禁用程序的自动更新功能
"""

import os
import sys
import json
import tkinter as tk
from tkinter import messagebox

def disable_auto_update():
    """禁用自动更新"""
    try:
        # 修改配置文件
        config_file = "update_config.py"
        if os.path.exists(config_file):
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换自动更新设置
            content = content.replace(
                '"auto_check_enabled": True,',
                '"auto_check_enabled": False,  # 已禁用自动更新'
            )
            content = content.replace(
                '"auto_check_enabled": False,  # 默认禁用自动更新',
                '"auto_check_enabled": False,  # 已禁用自动更新'
            )
            
            # 写回配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已成功禁用自动更新功能")
            return True
        else:
            print("❌ 未找到配置文件")
            return False
            
    except Exception as e:
        print(f"❌ 禁用自动更新失败: {str(e)}")
        return False

def create_user_config():
    """创建用户配置文件"""
    try:
        config_file = os.path.join(os.path.expanduser("~"), ".amazon_update_config.json")
        
        user_config = {
            "enable_auto_update": False,
            "enable_manual_update": True,
            "update_check_interval": 24,
            "last_update_check": None,
            "disabled_by_user": True,
            "disable_reason": "用户手动禁用自动更新"
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(user_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已创建用户配置文件: {config_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建用户配置失败: {str(e)}")
        return False

def show_gui():
    """显示图形界面"""
    root = tk.Tk()
    root.title("自动更新控制工具")
    root.geometry("400x300")
    root.resizable(False, False)
    
    # 设置图标
    try:
        if os.path.exists("icon.ico"):
            root.iconbitmap("icon.ico")
    except:
        pass
    
    # 标题
    title_label = tk.Label(
        root,
        text="🛠️ 自动更新控制工具",
        font=("微软雅黑", 16, "bold"),
        fg="#2c3e50"
    )
    title_label.pack(pady=20)
    
    # 说明文字
    info_text = """如果您的程序一直重复下载更新，
可以使用此工具禁用自动更新功能。

禁用后，程序将不会自动检查和下载更新，
但您仍可以通过程序内的"检查更新"按钮
手动检查更新。"""
    
    info_label = tk.Label(
        root,
        text=info_text,
        font=("微软雅黑", 10),
        fg="#7f8c8d",
        justify=tk.LEFT
    )
    info_label.pack(pady=20, padx=20)
    
    # 按钮框架
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    def on_disable():
        """禁用自动更新"""
        try:
            success1 = disable_auto_update()
            success2 = create_user_config()
            
            if success1 or success2:
                messagebox.showinfo(
                    "成功",
                    "✅ 自动更新已成功禁用！\n\n程序将不再自动下载更新。\n如需更新，请使用程序内的\"检查更新\"按钮。"
                )
                root.quit()
            else:
                messagebox.showerror(
                    "失败", 
                    "❌ 禁用自动更新失败！\n\n请确保程序文件没有被占用，\n或以管理员身份运行此工具。"
                )
        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {str(e)}")
    
    def on_enable():
        """启用自动更新"""
        try:
            # 修改配置文件
            config_file = "update_config.py"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                content = content.replace(
                    '"auto_check_enabled": False,  # 已禁用自动更新',
                    '"auto_check_enabled": True,'
                )
                content = content.replace(
                    '"auto_check_enabled": False,  # 默认禁用自动更新',
                    '"auto_check_enabled": True,'
                )
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            # 更新用户配置
            config_file = os.path.join(os.path.expanduser("~"), ".amazon_update_config.json")
            user_config = {
                "enable_auto_update": True,
                "enable_manual_update": True,
                "disabled_by_user": False
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(user_config, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo(
                "成功", 
                "✅ 自动更新已重新启用！\n\n程序将恢复自动检查更新。"
            )
            root.quit()
            
        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {str(e)}")
    
    # 禁用按钮
    disable_button = tk.Button(
        button_frame,
        text="🚫 禁用自动更新",
        command=on_disable,
        bg="#e74c3c",
        fg="white",
        font=("微软雅黑", 12, "bold"),
        padx=20,
        pady=10,
        cursor="hand2"
    )
    disable_button.pack(side=tk.LEFT, padx=10)
    
    # 启用按钮
    enable_button = tk.Button(
        button_frame,
        text="✅ 启用自动更新",
        command=on_enable,
        bg="#27ae60",
        fg="white",
        font=("微软雅黑", 12, "bold"),
        padx=20,
        pady=10,
        cursor="hand2"
    )
    enable_button.pack(side=tk.LEFT, padx=10)
    
    # 退出按钮
    exit_button = tk.Button(
        root,
        text="❌ 退出",
        command=root.quit,
        bg="#95a5a6",
        fg="white",
        font=("微软雅黑", 10),
        padx=15,
        pady=5,
        cursor="hand2"
    )
    exit_button.pack(pady=20)
    
    # 居中显示窗口
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (400 // 2)
    y = (root.winfo_screenheight() // 2) - (300 // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == "disable":
            success1 = disable_auto_update()
            success2 = create_user_config()
            if success1 or success2:
                print("✅ 自动更新已禁用")
            else:
                print("❌ 禁用失败")
        elif command == "gui":
            show_gui()
        else:
            print("用法:")
            print("  python 禁用自动更新.py disable  # 禁用自动更新")
            print("  python 禁用自动更新.py gui     # 显示图形界面")
    else:
        # 默认显示图形界面
        show_gui()

if __name__ == "__main__":
    main()
