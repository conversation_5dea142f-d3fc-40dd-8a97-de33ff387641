# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 分析主程序
a = Analysis(
    ['license_client.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('update_config.py', '.'),  # 确保包含配置文件
        ('auto_updater.py', '.'),   # 确保包含更新模块
    ],
    hiddenimports=[
        'fake_useragent',
        'fake_useragent.fake',
        'fake_useragent.utils',
        'openpyxl',
        'openpyxl.workbook.workbook',
        'openpyxl.worksheet.worksheet',
        'pandas',
        'requests',
        'selenium',
        'bs4',
        'lxml',
        'cryptography',
        'PIL',
        'update_config',  # 确保包含我们的配置模块
        'auto_updater',   # 确保包含我们的更新模块
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'numpy.distutils',
        'jupyter',
        'notebook',
        'IPython',
        'pytest',
        'unittest'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集数据
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 创建exe
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='亚马逊蓝图工具_修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
