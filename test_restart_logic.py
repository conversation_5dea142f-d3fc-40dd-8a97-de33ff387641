#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重启逻辑修复
"""

import os
import sys
import tempfile
import subprocess

def test_update_script_generation():
    """测试更新脚本生成"""
    print("🧪 测试更新脚本生成")
    print("=" * 50)
    
    try:
        from auto_updater import AutoUpdater
        
        # 创建测试实例
        updater = AutoUpdater("2.1.2", "http://test.com", "test_key", "test_device")
        
        # 模拟文件路径
        new_file = "C:\\temp\\new_version.exe"
        current_file = "C:\\current\\app.exe"
        new_version = "2.1.3"
        
        # 生成更新脚本
        script_path = updater._create_update_script(new_file, current_file, new_version)
        
        print(f"✅ 更新脚本生成成功: {script_path}")
        
        # 读取脚本内容
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查关键内容
        if "os.startfile" in script_content:
            print("✅ 脚本包含重启逻辑")
        else:
            print("❌ 脚本缺少重启逻辑")
            
        if new_version in script_content:
            print("✅ 脚本包含版本号更新")
        else:
            print("❌ 脚本缺少版本号更新")
            
        if "shutil.copy2" in script_content:
            print("✅ 脚本包含文件替换逻辑")
        else:
            print("❌ 脚本缺少文件替换逻辑")
            
        # 清理临时文件
        os.remove(script_path)
        print("✅ 更新脚本测试完成")
        
    except Exception as e:
        print(f"❌ 更新脚本测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_restart_flow():
    """测试重启流程"""
    print("\n🧪 测试重启流程")
    print("=" * 50)
    
    try:
        # 检查download_and_install函数
        from auto_updater import download_and_install
        import inspect
        
        source = inspect.getsource(download_and_install)
        
        # 检查是否移除了双重重启
        if "restart_application" not in source:
            print("✅ 已移除程序内部重启逻辑")
        else:
            print("❌ 仍然存在程序内部重启逻辑")
            
        if "sys.exit(0)" in source:
            print("✅ 程序会正确退出让更新脚本接管")
        else:
            print("❌ 程序退出逻辑可能有问题")
            
        print("✅ 重启流程测试完成")
        
    except Exception as e:
        print(f"❌ 重启流程测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_version_update_in_script():
    """测试脚本中的版本号更新逻辑"""
    print("\n🧪 测试脚本中的版本号更新逻辑")
    print("=" * 50)
    
    try:
        from auto_updater import AutoUpdater
        
        # 创建测试实例
        updater = AutoUpdater("2.1.2", "http://test.com", "test_key", "test_device")
        
        # 生成更新脚本
        script_path = updater._create_update_script("new.exe", "current.exe", "2.1.3")
        
        # 读取脚本内容
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查外部配置文件逻辑
        if "AppData" in script_content and "Local" in script_content:
            print("✅ 脚本使用外部配置文件")
        else:
            print("❌ 脚本未使用外部配置文件")
            
        if "version_config.json" in script_content:
            print("✅ 脚本更新版本配置文件")
        else:
            print("❌ 脚本未更新版本配置文件")
            
        if "current_version" in script_content:
            print("✅ 脚本包含版本号更新逻辑")
        else:
            print("❌ 脚本缺少版本号更新逻辑")
            
        # 清理
        os.remove(script_path)
        print("✅ 版本号更新逻辑测试完成")
        
    except Exception as e:
        print(f"❌ 版本号更新逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_restart_mechanism():
    """分析重启机制"""
    print("\n🔍 分析重启机制")
    print("=" * 50)
    
    print("📋 修复后的重启流程:")
    print("1. 用户选择更新")
    print("2. 下载新版本文件")
    print("3. 调用apply_update()创建更新脚本")
    print("4. 更新脚本启动（独立进程）")
    print("5. 主程序退出（sys.exit(0)）")
    print("6. 更新脚本等待主程序退出")
    print("7. 更新脚本替换exe文件")
    print("8. 更新脚本更新版本配置文件")
    print("9. 更新脚本启动新版本（os.startfile）")
    print("10. 新版本启动，读取新的版本号")
    
    print("\n✅ 关键修复点:")
    print("- 移除了程序内部的restart_application()函数")
    print("- 只有更新脚本负责重启，避免双重启动")
    print("- 更新脚本会正确启动新版本exe文件")
    print("- 版本号通过外部配置文件正确更新")

def main():
    """主测试函数"""
    print("🚀 开始重启逻辑修复测试")
    print("=" * 60)
    
    # 运行所有测试
    test_update_script_generation()
    test_restart_flow()
    test_version_update_in_script()
    analyze_restart_mechanism()
    
    print("\n" + "=" * 60)
    print("🎉 重启逻辑修复测试完成")
    print("\n📋 修复总结:")
    print("1. ✅ 移除了双重重启机制")
    print("2. ✅ 只有更新脚本负责重启新版本")
    print("3. ✅ 程序正确退出让更新脚本接管")
    print("4. ✅ 版本号通过外部配置文件正确更新")
    
    print("\n🎯 预期效果:")
    print("- 更新后会启动新版本而不是旧版本")
    print("- 不会出现双重启动问题")
    print("- 版本号会正确更新到新版本")
    print("- 不会再出现无限更新循环")

if __name__ == "__main__":
    main()
