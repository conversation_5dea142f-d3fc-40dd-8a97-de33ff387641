#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复exe更新问题
"""

import os
import shutil
import tempfile
import glob

def create_external_updater():
    """创建外部更新器批处理文件"""
    updater_content = """@echo off
chcp 65001 >nul
echo 🔄 外部更新器启动...
echo.

REM 等待主程序完全退出
echo ⏳ 等待主程序退出...
timeout /t 3 /nobreak >nul

REM 检查新文件是否存在
if not exist "%~1" (
    echo ❌ 错误: 新文件不存在 %~1
    pause
    exit /b 1
)

REM 检查目标文件是否存在
if not exist "%~2" (
    echo ❌ 错误: 目标文件不存在 %~2
    pause
    exit /b 1
)

echo 📁 备份原文件...
copy "%~2" "%~2.backup" >nul
if errorlevel 1 (
    echo ❌ 备份失败
    pause
    exit /b 1
)

echo 🔄 替换文件...
copy "%~1" "%~2" >nul
if errorlevel 1 (
    echo ❌ 文件替换失败，恢复备份...
    copy "%~2.backup" "%~2" >nul
    del "%~2.backup" >nul
    pause
    exit /b 1
)

echo ✅ 更新成功！
echo 🚀 重新启动程序...

REM 清理临时文件
del "%~1" >nul 2>&1
del "%~2.backup" >nul 2>&1

REM 重新启动程序
start "" "%~2"

echo 🎉 更新完成！
timeout /t 2 /nobreak >nul
exit /b 0
"""
    
    with open('external_updater.bat', 'w', encoding='utf-8') as f:
        f.write(updater_content)
    
    print("✅ 已创建外部更新器: external_updater.bat")

def create_temp_cleaner():
    """创建临时目录清理工具"""
    cleaner_content = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import tempfile
import glob

def clean_temp():
    try:
        temp_dir = tempfile.gettempdir()
        print(f"🧹 清理临时目录: {temp_dir}")
        
        # 清理MEI目录
        mei_dirs = glob.glob(os.path.join(temp_dir, "_MEI*"))
        cleaned = 0
        
        for mei_dir in mei_dirs:
            try:
                if os.path.exists(mei_dir):
                    print(f"🗑️ 删除: {mei_dir}")
                    shutil.rmtree(mei_dir, ignore_errors=True)
                    cleaned += 1
            except:
                pass
        
        print(f"✅ 清理完成，删除了 {cleaned} 个临时目录")
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧹 临时目录清理工具")
    clean_temp()
    input("按回车键退出...")
"""
    
    with open('temp_cleaner.py', 'w', encoding='utf-8') as f:
        f.write(cleaner_content)
    
    print("✅ 已创建临时目录清理工具: temp_cleaner.py")

def clean_temp_now():
    """立即清理临时目录"""
    try:
        temp_dir = tempfile.gettempdir()
        print(f"🧹 清理临时目录: {temp_dir}")
        
        # 清理MEI目录
        mei_dirs = glob.glob(os.path.join(temp_dir, "_MEI*"))
        cleaned = 0
        
        for mei_dir in mei_dirs:
            try:
                if os.path.exists(mei_dir):
                    print(f"🗑️ 删除: {mei_dir}")
                    shutil.rmtree(mei_dir, ignore_errors=True)
                    cleaned += 1
            except Exception as e:
                print(f"⚠️ 无法删除 {mei_dir}: {e}")
        
        print(f"✅ 清理完成，删除了 {cleaned} 个临时目录")
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def fix_auto_updater_simple():
    """简单修复auto_updater.py"""
    try:
        if not os.path.exists('auto_updater.py'):
            print("❌ auto_updater.py 文件不存在")
            return False
        
        # 备份
        shutil.copy2('auto_updater.py', 'auto_updater.py.backup_simple')
        print("✅ 已备份auto_updater.py")
        
        # 读取内容
        with open('auto_updater.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单替换重启逻辑
        old_restart = 'os.execv(sys.executable, [sys.executable] + sys.argv)'
        new_restart = '''# 使用外部更新器安全重启
                if getattr(sys, 'frozen', False) and os.path.exists('external_updater.bat'):
                    import subprocess
                    subprocess.Popen(['external_updater.bat', temp_file, sys.executable], shell=True)
                    sys.exit(0)
                else:
                    os.execv(sys.executable, [sys.executable] + sys.argv)'''
        
        if old_restart in content:
            content = content.replace(old_restart, new_restart)
            print("✅ 已修复重启逻辑")
        
        # 保存
        with open('auto_updater.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ auto_updater.py 修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ 快速修复exe更新问题")
    print("=" * 50)
    print("问题: Failed to start embedded python interpreter!")
    print("原因: PyInstaller exe文件自我更新失败")
    print("=" * 50)
    
    print("\n🔧 创建修复工具...")
    create_external_updater()
    create_temp_cleaner()
    
    print("\n🧹 清理临时目录...")
    clean_temp_now()
    
    print("\n🔧 修复更新机制...")
    fix_auto_updater_simple()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成！")
    print("=" * 50)
    
    print("\n📋 解决方案:")
    print("1. ✅ 创建了外部更新器 (external_updater.bat)")
    print("2. ✅ 创建了临时目录清理工具 (temp_cleaner.py)")
    print("3. ✅ 清理了现有的临时目录")
    print("4. ✅ 修复了auto_updater.py的重启机制")
    
    print("\n🚀 下一步:")
    print("1. 重新构建exe文件")
    print("2. 测试更新功能")
    print("3. 如果还有问题，运行 temp_cleaner.py")
    
    print("\n💡 原理:")
    print("- 使用外部批处理文件替换exe")
    print("- 避免exe文件自我替换的问题")
    print("- 自动清理PyInstaller临时目录")

if __name__ == "__main__":
    main()
